package com.md.flight.lh.utils;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.md.flight.lh.domain.dto.LhBaseReq;
import com.md.flight.lh.enums.LhReqMassageEnum;
import com.md.flight.lh.processor.LhBaseProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 联合工具类
 */
@Slf4j
@Component
public class LhUtils {

    /**
     * 判断消息是否是联合飞机的消息
     *
     * @param payload
     * @return
     */
    public LhBaseProcessor judgmentMessage(String payload, String topic) {
        // 消息转换
        LhBaseReq lhBaseReq = JSONObject.parseObject(payload, LhBaseReq.class);
        if (ObjectUtil.isEmpty(lhBaseReq)) {
            log.error("消息转换失败, 消息:{}", payload);
            return null;
        }

        //获取主题方法消息
        int lastSlashIndex = topic.lastIndexOf('/');
        String topicInfo = (lastSlashIndex != -1) ? topic.substring(lastSlashIndex + 1) : null;

        int index = topicInfo.indexOf('_');
        String topicMethod = topicInfo.substring(index + 1);

        // 获取处理器并处理消息
        LhBaseProcessor processor = LhReqMassageEnum.getProcessor(topicMethod + "_" + lhBaseReq.getMethod());
        if (ObjectUtil.isEmpty(processor)) {
            //            log.error("消息处理失败, 找不到对应的处理器, 消息:{}",payload);
            return null;
        }
        return processor;
    }
}
