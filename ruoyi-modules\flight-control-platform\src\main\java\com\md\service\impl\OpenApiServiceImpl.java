package com.md.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.constant.TaskCallbackConstants;
import com.md.domain.bo.FlightLineBo;
import com.md.domain.dto.FlightLineDto;
import com.md.domain.dto.OpenApiDto;
import com.md.domain.po.Flyer;
import com.md.domain.po.UavInfo;
import com.md.domain.vo.DroneStatusVo;
import com.md.domain.vo.FlightLineVo;
import com.md.enums.DataSourceEnum;
import com.md.mapper.FlyerMapper;
import com.md.mapper.UavInfoMapper;
import com.md.service.IDroneStatusService;
import com.md.service.IFlightLineService;
import com.md.service.IFlightTaskService;
import com.md.service.IFlyerService;
import com.md.service.IOpenApiService;
import com.md.service.IUavInfoService;
import com.md.utils.EncryptUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * 开放接口实现类
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OpenApiServiceImpl implements IOpenApiService {

    private final IUavInfoService uavInfoService;
    private final IFlightLineService flightLineService;
    private final IFlightTaskService flightTaskService;
    private final IDroneStatusService droneStatusService;
    private final EncryptUtils encryptUtils;
    private final UavInfoMapper uavInfoMapper;
    private final IFlyerService flyerService;
    private final FlyerMapper flyerMapper;

    /**
     * 查询航线列表
     */
    @Override
    public String flightLineList(String openUrl, String decrypt, String openTenantId, String apiTenantId) {
        try {
            // 解析参数
            OpenApiDto openApiDto = JSONObject.parseObject(decrypt, OpenApiDto.class);

            // 检查参数
            if (ObjectUtil.isEmpty(openApiDto)) {
                log.error("查询航线列表失败: 参数为空");
                return null;
            }

            // 创建FlightLineVO参数对象
            FlightLineBo flightLineBo = new FlightLineBo();
            flightLineBo.setFlightControlNo(openApiDto.getFlightControlNo());
            flightLineBo.setTenantId(openTenantId);

            // 调用服务获取航线列表
            List<FlightLineVo> voList = flightLineService.selectFlightLineListExclude(flightLineBo);

            // 转换结果为DTO
            List<FlightLineDto> dtoList = new ArrayList<>();
            for (FlightLineVo vo : voList) {
                FlightLineDto dto = new FlightLineDto();
                BeanUtils.copyProperties(vo, dto);
                dtoList.add(dto);
            }

            // 加密结果
            String rsaParam = encryptUtils.parseAESEncrypt(openUrl, JSON.toJSONString(dtoList), openTenantId,apiTenantId);
            return rsaParam;

        } catch (Exception e) {
            log.error("查询航线列表失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查询无人机列表
     *
     * @param openApiDto
     * @return
     */
    @Override
    public String uavList(String openUrl, OpenApiDto openApiDto, String openTenantId, String apiTenantId) {

        //获取无人机信息
        try {
            if (ObjectUtil.isEmpty(openApiDto)) {
                return null;
            }
            List<UavInfo> uavInfo = uavInfoService.list(Wrappers.<UavInfo>lambdaQuery().eq(UavInfo::getDelFlag, 0)
                .eq(UavInfo::getFlightControlNo, openApiDto.getFlightControlNo()).eq(UavInfo::getTenantId, openTenantId));
            //数据加密
            String rsaParam = encryptUtils.parseAESEncrypt(openUrl, JSON.toJSONString(uavInfo), openTenantId,apiTenantId);

            return JSON.toJSONString(rsaParam);
        } catch (Exception e) {
            log.error("=========UavList==========->" + e.getMessage());
            return null;
        }
    }

    /**
     * 根据航线创建任务（包含平台编码）
     *
     * @param openUrl      开放接口URL
     * @param param        加密的请求参数
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @param platformCode 平台编码
     * @return 加密后的创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTaskFromLine(String openUrl, String param, String openTenantId, String apiTenantId, String platformCode) {
        if (ObjectUtil.isEmpty(param)) {
            log.error("创建航线任务失败: 参数为空");
            return null;
        }

        try {
            // 解密参数
            String decrypt = encryptUtils.parseOpenDecrypt(openUrl, param, openTenantId,apiTenantId);
            JSONObject jsonObject = JSONObject.parseObject(decrypt);

            // 获取参数
            String lineId = jsonObject.getString("lineId");
            String uavCode = jsonObject.getString("uavCode");
            Boolean executeImmediately = jsonObject.getBoolean("executeImmediately");

            // 参数校验
            if (ObjectUtil.isEmpty(lineId) || ObjectUtil.isEmpty(uavCode)) {
                log.error("创建航线任务失败: 航线ID或无人机编码为空");
                JSONObject errorResult = new JSONObject();
                errorResult.put("code", "500");
                errorResult.put("message", "航线ID或无人机编码不能为空");
                return encryptUtils.parseAESEncrypt(openUrl, errorResult.toString(), openTenantId,apiTenantId);
            }

            // 设置默认值
            if (executeImmediately == null) {
                executeImmediately = false; // 默认不立即执行
            }

            log.info("开放接口创建航线任务: lineId={}, uavCode={}, executeImmediately={}", lineId, uavCode,
                executeImmediately);

            // 调用业务服务创建任务，传递租户ID
            String taskId = flightTaskService.createTaskFromLine(lineId, uavCode, executeImmediately, openTenantId);
            log.info("开放接口创建航线任务成功: taskId={}", taskId);

            // 存储taskId和platformCode的映射关系到Redis
            if (StringUtils.isNotEmpty(taskId) && StringUtils.isNotEmpty(platformCode)) {
                // 将taskId和platformCode的映射存入Redis，设置60分钟有效期
                RedisUtils.setCacheObject(TaskCallbackConstants.TASK_PLATFORM_CODE_KEY_PREFIX + taskId, platformCode,
                    Duration.ofMinutes(60));

                // 将taskId和tenantId的映射存入Redis，与platformCode保持相同的有效期
                RedisUtils.setCacheObject(TaskCallbackConstants.TASK_TENANT_ID_KEY_PREFIX + taskId, openTenantId,
                    Duration.ofMinutes(60));

                log.info("已将任务ID[{}]与平台编码[{}]及租户ID[{}]的映射保存到Redis", taskId, platformCode, openTenantId);
            }

            // 构造返回结果
            JSONObject result = new JSONObject();
            result.put("code", "200");
            result.put("taskId", taskId);
            result.put("message", "创建航线任务执行成功");

            // 返回加密结果
            return encryptUtils.parseAESEncrypt(openUrl, result.toString(), openTenantId,apiTenantId);
        } catch (Exception e) {
            log.error("开放接口创建航线任务失败: {}", e.getMessage(), e);
            // 构造错误返回
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", "500");
            errorResult.put("message", "创建航线任务执行失败: " + e.getMessage());
            return encryptUtils.parseAESEncrypt(openUrl, errorResult.toString(), openTenantId,apiTenantId);
        }
    }

    /**
     * 获取无人机最新状态数据
     *
     * @param param    加密的无人机ID参数
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @return 加密后的状态数据JSON字符串，无数据时返回null
     */
    @Override
    public String getDroneStatus(String openUrl, String param, String openTenantId, String apiTenantId) {
        log.info("开放接口获取无人机状态");
        try {
            if (ObjectUtil.isEmpty(param)) {
                log.error("获取无人机状态参数错误: 参数为空");
                return null;
            }

            // 解密参数
            //            String decrypt = encryptUtils.parseAESDecrypt(param, tenantId);
            String decrypt = encryptUtils.parseOpenDecrypt(openUrl, param, openTenantId,apiTenantId);

            JSONObject jsonObject = JSONObject.parseObject(decrypt);

            // 获取无人机ID
            String droneId = jsonObject.getString("droneId");

            // 参数校验
            if (ObjectUtil.isEmpty(droneId)) {
                log.error("获取无人机状态参数错误: 无人机ID为空");
                JSONObject errorResult = new JSONObject();
                errorResult.put("code", "500");
                errorResult.put("message", "无人机ID不能为空");
                return encryptUtils.parseAESEncrypt(openUrl, errorResult.toString(), openTenantId,apiTenantId);
            }

            log.info("开放接口获取无人机状态: droneId={}", droneId);

            // 调用状态服务获取无人机状态
            DroneStatusVo droneStatusVo = droneStatusService.getDroneStatus(droneId);

            // 无状态数据时返回错误信息
            if (droneStatusVo == null) {
                log.info("无人机不在线或无状态数据: droneId={}", droneId);
                JSONObject errorResult = new JSONObject();
                errorResult.put("success", false);
                errorResult.put("message", "无人机不在线或无状态数据");
                return encryptUtils.parseAESEncrypt(openUrl, errorResult.toString(), openTenantId,apiTenantId);
            }

            // 获取状态数据
            String droneStatus = droneStatusVo.getStatusData();

            // 如果状态数据为空，也返回错误信息
            if (droneStatus == null) {
                log.info("无人机状态数据为空: droneId={}", droneId);
                JSONObject errorResult = new JSONObject();
                errorResult.put("success", false);
                errorResult.put("message", "无人机状态数据为空");
                return encryptUtils.parseAESEncrypt(openUrl, errorResult.toString(), openTenantId,apiTenantId);
            }

            // 将原始状态数据包装成标准响应格式
            JSONObject statusObj = JSON.parseObject(droneStatus);
            JSONObject result = new JSONObject();
            result.put("code", "200");
            result.put("data", statusObj);

            // 数据加密
            String encryptedStatus = encryptUtils.parseAESEncrypt(openUrl, result.toString(), openTenantId,apiTenantId);
            log.debug("无人机状态数据获取成功: droneId={}", droneId);

            return encryptedStatus;
        } catch (Exception e) {
            log.error("开放接口获取无人机状态失败: {}", e.getMessage(), e);
            // 构造错误返回
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", "500");
            errorResult.put("message", "获取无人机状态失败: " + e.getMessage());
            return encryptUtils.parseAESEncrypt(openUrl, errorResult.toString(), openTenantId,apiTenantId);
        }
    }

    /**
     * 获取飞手列表
     *
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @return 加密后的飞手列表JSON字符串
     */
    @Override
    public String queryFlyerList(String openUrl, String decrypt, String openTenantId, String apiTenantId) {
        try {
            // 解析参数
            OpenApiDto openApiDto = JSONObject.parseObject(decrypt, OpenApiDto.class);

            // 检查参数
            if (ObjectUtil.isEmpty(openApiDto)) {
                log.error("查询飞手列表失败: 参数为空");
                return null;
            }

            // 调用服务获取飞手列表
            List<Flyer> voList = flyerMapper.selectList(Wrappers.<Flyer>lambdaQuery()
                .eq(Flyer::getPlateformId, openApiDto.getFlightControlNo())
                .eq(Flyer::getDataSource, DataSourceEnum.SYNC.getName()));

            // 加密结果
            String rsaParam = encryptUtils.parseAESEncrypt(openUrl, JSON.toJSONString(voList), openTenantId,apiTenantId);
            return rsaParam;

        } catch (Exception e) {
            log.error("查询飞手列表失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
