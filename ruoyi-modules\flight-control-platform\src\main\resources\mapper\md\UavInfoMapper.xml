<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.UavInfoMapper">
    <resultMap type="com.md.domain.po.UavInfo" id="UavInfoResult">
        <result property="id" column="id"/>
        <result property="uavCode" column="uav_code"/>
        <result property="categoryCode" column="category_code"/>
        <result property="manufacturerName" column="manufacturer_name"/>
        <result property="flightControlNo" column="flight_control_no"/>
        <result property="maxLoad" column="max_load"/>
        <result property="dataSource" column="data_source"/>
        <result property="uavName" column="uav_name"/>
        <result property="deviceModel" column="device_model"/>
        <result property="batterySn" column="battery_sn"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="batchId" column="batch_id"/>
        <result property="syncTime" column="sync_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="httpEndpoint" column="http_endpoint"/>
        <result property="mqttEndpoint" column="mqtt_endpoint"/>
        <result property="mavlinkIp" column="mavlink_ip"/>
        <result property="mavlinkPort" column="mavlink_port"/>
    </resultMap>

    <sql id="selectFkUavInfoVo">
        select id,
               uav_code,
               category_code,
               manufacturer_name,
               flight_control_no,
               max_load,
               data_source,
               uav_name,
               device_model,
               battery_sn,
               status,
               del_flag,
               remark,
               batch_id,
               sync_time,
               create_by,
               create_time,
               update_by,
               update_time,
               http_endpoint,
               mqtt_endpoint,
               mavlink_ip,
               mavlink_port
        from uav_info
    </sql>

    <select id="selectFkUavInfoList" parameterType="com.md.domain.po.UavInfo" resultMap="UavInfoResult">
        <include refid="selectFkUavInfoVo"/>
        <where>
            <if test="uavCode != null  and uavCode != ''">
                and uav_code = #{uavCode}
            </if>
            <if test="categoryCode != null  and categoryCode != ''">
                and category_code = #{categoryCode}
            </if>
            <if test="manufacturerName != null  and manufacturerName != ''">
                and manufacturer_name like concat('%', #{manufacturerName}, '%')
            </if>
            <if test="flightControlNo != null  and flightControlNo != ''">
                and flight_control_no like concat('%', #{flightControlNo}, '%')
            </if>
            <if test="maxLoad != null">
                and max_load = #{maxLoad}
            </if>
            <if test="dataSource != null  and dataSource != ''">
                and data_source = #{dataSource}
            </if>
            <if test="uavName != null  and uavName != ''">
                and uav_name like concat('%', #{uavName}, '%')
            </if>
            <if test="deviceModel != null  and deviceModel != ''">
                and device_model = #{deviceModel}
            </if>
            <if test="batterySn != null  and batterySn != ''">
                and battery_sn = #{batterySn}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="batchId != null  and batchId != ''">
                and batch_id = #{batchId}
            </if>
            <if test="syncTime != null">
                and sync_time = #{syncTime}
            </if>
            <if test="createBy != null">
                and create_by like concat('%', #{createBy}, '%')
            </if>
            <if test="startTime != null">
                and create_time <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[ < ]]> DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
            <if test="mavlinkIp != null and mavlinkIp != ''">
                and mavlink_ip = #{mavlinkIp}
            </if>
            <if test="mavlinkPort != null">
                and mavlink_port = #{mavlinkPort}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectFkUavInfoById" parameterType="Long" resultMap="UavInfoResult">
        <include refid="selectFkUavInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertFkUavInfo" parameterType="com.md.domain.po.UavInfo" useGeneratedKeys="true" keyProperty="id">
        insert into uav_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uavCode != null">
                uav_code,
            </if>
            <if test="categoryCode != null">
                category_code,
            </if>
            <if test="manufacturerName != null">
                manufacturer_name,
            </if>
            <if test="flightControlNo != null">
                flight_control_no,
            </if>
            <if test="maxLoad != null">
                max_load,
            </if>
            <if test="dataSource != null">
                data_source,
            </if>
            <if test="uavName != null">
                uav_name,
            </if>
            <if test="deviceModel != null">
                device_model,
            </if>
            <if test="batterySn != null">
                battery_sn,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="batchId != null">
                batch_id,
            </if>
            <if test="syncTime != null">
                sync_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="httpEndpoint != null">
                http_endpoint,
            </if>
            <if test="mqttEndpoint != null">
                mqtt_endpoint,
            </if>
            <if test="mavlinkIp != null">
                mavlink_ip,
            </if>
            <if test="mavlinkPort != null">
                mavlink_port,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uavCode != null">
                #{uavCode},
            </if>
            <if test="categoryCode != null">
                #{categoryCode},
            </if>
            <if test="manufacturerName != null">
                #{manufacturerName},
            </if>
            <if test="flightControlNo != null">
                #{flightControlNo},
            </if>
            <if test="maxLoad != null">
                #{maxLoad},
            </if>
            <if test="dataSource != null">
                #{dataSource},
            </if>
            <if test="uavName != null">
                #{uavName},
            </if>
            <if test="deviceModel != null">
                #{deviceModel},
            </if>
            <if test="batterySn != null">
                #{batterySn},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="batchId != null">
                #{batchId},
            </if>
            <if test="syncTime != null">
                #{syncTime},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="httpEndpoint != null">
                #{httpEndpoint},
            </if>
            <if test="mqttEndpoint != null">
                #{mqttEndpoint},
            </if>
            <if test="mavlinkIp != null">
                #{mavlinkIp},
            </if>
            <if test="mavlinkPort != null">
                #{mavlinkPort},
            </if>
        </trim>
    </insert>

    <update id="updateFkUavInfo" parameterType="com.md.domain.po.UavInfo">
        update uav_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="uavCode != null">
                uav_code = #{uavCode},
            </if>
            <if test="categoryCode != null">
                category_code = #{categoryCode},
            </if>
            <if test="manufacturerName != null">
                manufacturer_name = #{manufacturerName},
            </if>
            <if test="flightControlNo != null">
                flight_control_no = #{flightControlNo},
            </if>
            <if test="maxLoad != null">
                max_load = #{maxLoad},
            </if>
            <if test="dataSource != null">
                data_source = #{dataSource},
            </if>
            <if test="uavName != null">
                uav_name = #{uavName},
            </if>
            <if test="deviceModel != null">
                device_model = #{deviceModel},
            </if>
            <if test="batterySn != null">
                battery_sn = #{batterySn},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="batchId != null">
                batch_id = #{batchId},
            </if>
            <if test="syncTime != null">
                sync_time = #{syncTime},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="httpEndpoint != null">
                http_endpoint = #{httpEndpoint},
            </if>
            <if test="mqttEndpoint != null">
                mqtt_endpoint = #{mqttEndpoint},
            </if>
            <if test="mavlinkIp != null">
                mavlink_ip = #{mavlinkIp},
            </if>
            <if test="mavlinkPort != null">
                mavlink_port = #{mavlinkPort},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFkUavInfoById" parameterType="Long">
        delete from uav_info where id = #{id}
    </delete>

    <delete id="deleteFkUavInfoByIds" parameterType="String">
        delete from uav_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据无人机编码列表批量查询无人机信息 -->
    <select id="selectByUavCodes" resultType="UavInfo">
        select *
        from uav_info
        where uav_code in
        <foreach collection="uavCodes" item="uavCode" open="(" separator="," close=")">
            #{uavCode}
        </foreach>
    </select>
</mapper>
