package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 航点动作实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("flight_point_action")
public class FlightPointAction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 航点ID
     */
    private String pointId;

    /**
     * 动作编号
     */
    private Integer actionIndex;

    /**
     * 飞行器悬停等待时间
     */
    private Integer hoverTime;

    /**
     * 飞行器目标偏航角
     */
    private Double aircraftHeading;

    /**
     * 是否使用全局拍照模式
     */
    private Integer useGlobalImageFormat;

    /**
     * 拍照模式
     */
    private String imageFormat;

    /**
     * 云台偏航角
     */
    private Double gimbalYawRotateAngle;

    /**
     * 云台俯仰角
     */
    private Double gimbalPitchRotateAngle;

    /**
     * 变焦焦距
     */
    private Double zoom;

    /**
     * 录像状态：1：开始录像 0：结束录像
     */
    private Integer recordStatus;

    /**
     * 等时触发 单位秒
     */
    private int multipleTiming;

    /**
     * 等距触发 单位米
     */
    private int multipleDistance;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 动作类型
     */
    private String type;
}