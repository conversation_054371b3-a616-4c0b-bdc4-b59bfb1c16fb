package com.md.flight.lh.service;

import com.md.flight.lh.domain.dto.HomePoint;

/**
 * 联合飞机控制服务接口
 */
public interface LhDroneControlService {
    /**
     * 执行起飞命令
     *
     * @param droneId  无人机ID
     * @param mode     高度模式 0：绝对高度 1：相对高度(默认)
     * @param altitude 高度值
     * @return 是否成功收到响应
     */
    boolean takeoff(String droneId, int mode, float altitude);

    /**
     * 执行降落命令
     *
     * @param droneId 无人机ID
     * @param mode    降落模式 0：直接降落 1：精准降落
     * @param code    降落二维码编号（可选）
     * @return 是否成功收到响应
     */
    boolean land(String droneId, int mode, String code);

    /**
     * 执行返航命令（扩展版，支持指定返航点）
     *
     * @param droneId      无人机ID
     * @param mode         返航模式 1：安全高度返航 2：直线返航 3：原路返航
     * @param safeAltitude 安全返航高度（相对起飞点)，单位米，当mode=1时有效
     * @param speed        返航速度，单位米/秒
     * @param specificHome 是否指定返航点 true:是 false:否
     * @param homePoint    返航点坐标，当specificHome=true时有效
     * @return 是否成功收到响应
     */
    boolean goHomeExtended(String droneId, int mode, float safeAltitude, float speed, boolean specificHome,
        HomePoint homePoint);

    /**
     * 执行航线暂停
     *
     * @param droneId 无人机ID
     */
    boolean executePauseMission(String droneId);

    /**
     * 执行航线继续
     *
     * @param droneId 无人机ID
     */
    boolean executeResumeMission(String droneId);

    /**
     * 启用虚拟摇杆控制模式
     *
     * @param droneId 无人机ID
     */
    void startVirtualStick(String droneId);

    /**
     * 停用虚拟摇杆控制模式
     *
     * @param droneId 无人机ID
     */
    void stopVirtualStick(String droneId);
}
