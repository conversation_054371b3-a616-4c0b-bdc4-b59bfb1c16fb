<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.PlatformInfoMapper">
    <resultMap type="com.md.domain.po.PlatformInfo" id="PlatformInfoResult">
        <result property="id" column="id"/>
        <result property="manufacturerName" column="manufacturer_name"/>
        <result property="flightControlNo" column="flight_control_no"/>
        <result property="networkAddress" column="network_address"/>
        <result property="account" column="account"/>
        <result property="password" column="password"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="aesKey" column="aes_key"/>
    </resultMap>

    <sql id="selectFkPlatformInfoVo">
        select id,
               manufacturer_name,
               flight_control_no,
               network_address,
               account,
               password,
               remark,
               create_by,
               create_time,
               update_by,
               update_time,
               dept_id,
               user_id,
               aes_key
        from platform_info
    </sql>

    <select id="selectFkPlatformInfoList" parameterType="com.md.domain.po.PlatformInfo" resultMap="PlatformInfoResult">
        <include refid="selectFkPlatformInfoVo"/>
        <where>
            <if test="manufacturerName != null  and manufacturerName != ''">
                and manufacturer_name like concat('%', #{manufacturerName}, '%')
            </if>
            <if test="flightControlNo != null  and flightControlNo != ''">
                and flight_control_no = #{flightControlNo}
            </if>
            <if test="networkAddress != null  and networkAddress != ''">
                and network_address = #{networkAddress}
            </if>
            <if test="account != null  and account != ''">
                and account = #{account}
            </if>
            <if test="password != null  and password != ''">
                and password = #{password}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectFkPlatformInfoById" parameterType="Long" resultMap="PlatformInfoResult">
        <include refid="selectFkPlatformInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertFkPlatformInfo" parameterType="com.md.domain.po.PlatformInfo" useGeneratedKeys="true"
            keyProperty="id">
        insert into platform_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="manufacturerName != null and manufacturerName != ''">
                manufacturer_name,
            </if>
            <if test="flightControlNo != null and flightControlNo != ''">
                flight_control_no,
            </if>
            <if test="networkAddress != null and networkAddress != ''">
                network_address,
            </if>
            <if test="account != null">
                account,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="manufacturerName != null and manufacturerName != ''">
                #{manufacturerName},
            </if>
            <if test="flightControlNo != null and flightControlNo != ''">
                #{flightControlNo},
            </if>
            <if test="networkAddress != null and networkAddress != ''">
                #{networkAddress},
            </if>
            <if test="account != null">
                #{account},
            </if>
            <if test="password != null">
                #{password},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="deptId != null">
                #{deptId},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
        </trim>
    </insert>

    <update id="updateFkPlatformInfo" parameterType="com.md.domain.po.PlatformInfo">
        update platform_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="manufacturerName != null and manufacturerName != ''">
                manufacturer_name = #{manufacturerName},
            </if>
            <if test="flightControlNo != null and flightControlNo != ''">
                flight_control_no = #{flightControlNo},
            </if>
            <if test="networkAddress != null and networkAddress != ''">
                network_address = #{networkAddress},
            </if>
            <if test="account != null">
                account = #{account},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFkPlatformInfoById" parameterType="Long">
        delete from platform_info where id = #{id}
    </delete>

    <delete id="deleteFkPlatformInfoByIds" parameterType="String">
        delete from platform_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectPlatformtByCode" resultType="com.md.domain.po.PlatformInfo">
        <include refid="selectFkPlatformInfoVo"/>
        where flight_control_no = #{code}
    </select>

    <select id="checkFlightControlNoUnique" parameterType="String" resultMap="PlatformInfoResult">
        select id, flight_control_no
        from platform_info
        where flight_control_no = #{flightControlNo}
        limit 1
    </select>
</mapper>