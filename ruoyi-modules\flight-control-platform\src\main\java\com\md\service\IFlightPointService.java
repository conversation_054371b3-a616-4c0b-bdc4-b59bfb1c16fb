package com.md.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.md.domain.po.FlightPoint;

/**
 * 航点Service接口
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public interface IFlightPointService extends IService<FlightPoint> {
    /**
     * 查询航点
     *
     * @param id 航点主键
     * @return 航点
     */
    public FlightPoint selectFlightPointById(Long id);

    /**
     * 查询航点列表
     *
     * @param flightPoint 航点
     * @return 航点集合
     */
    public List<FlightPoint> selectFlightPointList(FlightPoint flightPoint);

    /**
     * 新增航点
     *
     * @param flightPoint 航点
     * @return 结果
     */
    public int insertFlightPoint(FlightPoint flightPoint);

    /**
     * 修改航点
     *
     * @param flightPoint 航点
     * @return 结果
     */
    public int updateFlightPoint(FlightPoint flightPoint);

    /**
     * 批量删除航点
     *
     * @param ids 需要删除的航点主键集合
     * @return 结果
     */
    public int deleteFlightPointByIds(Long[] ids);

    /**
     * 删除航点信息
     *
     * @param id 航点主键
     * @return 结果
     */
    public int deleteFlightPointById(Long id);
}