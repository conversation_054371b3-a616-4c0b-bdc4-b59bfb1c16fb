package com.md.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.md.domain.po.FlightTask;
import com.md.mapper.FlightLineTaskMapper;
import com.md.service.IFlightTaskStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 飞行任务状态管理服务实现
 * 专门负责任务状态的更新和管理
 * 
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FlightTaskStatusServiceImpl implements IFlightTaskStatusService {

    private final FlightLineTaskMapper flightLineTaskMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskStatus(String taskName, Integer status, String message) {
        try {
            log.info("更新任务状态: taskName={}, status={}, message={}", taskName, status, message);
            
            LambdaUpdateWrapper<FlightTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(FlightTask::getTaskName, taskName)
                        .set(FlightTask::getTaskStatus, status)
                        .set(FlightTask::getUpdateTime, DateUtils.getNowDate());
            
            if (message != null) {
                updateWrapper.set(FlightTask::getRemark, message);
            }
            
            int result = flightLineTaskMapper.update(null, updateWrapper);
            
            if (result > 0) {
                log.info("任务状态更新成功: taskName={}, status={}", taskName, status);
                return true;
            } else {
                log.warn("任务状态更新失败，未找到匹配的任务: taskName={}", taskName);
                return false;
            }
            
        } catch (Exception e) {
            log.error("更新任务状态异常: taskName={}, status={}", taskName, status, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskOperator(String taskName, Long operatorId) {
        try {
            log.info("更新任务操作人: taskName={}, operatorId={}", taskName, operatorId);
            
            LambdaUpdateWrapper<FlightTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(FlightTask::getTaskName, taskName)
                        .set(FlightTask::getOperatorId, operatorId)
                        .set(FlightTask::getUpdateTime, DateUtils.getNowDate());
            
            int result = flightLineTaskMapper.update(null, updateWrapper);
            
            if (result > 0) {
                log.info("任务操作人更新成功: taskName={}, operatorId={}", taskName, operatorId);
                return true;
            } else {
                log.warn("任务操作人更新失败，未找到匹配的任务: taskName={}", taskName);
                return false;
            }
            
        } catch (Exception e) {
            log.error("更新任务操作人异常: taskName={}, operatorId={}", taskName, operatorId, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskStatusById(String taskId, Integer status, String message) {
        try {
            log.info("根据ID更新任务状态: taskId={}, status={}, message={}", taskId, status, message);
            
            LambdaUpdateWrapper<FlightTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(FlightTask::getId, taskId)
                        .set(FlightTask::getTaskStatus, status)
                        .set(FlightTask::getUpdateTime, DateUtils.getNowDate());
            
            if (message != null) {
                updateWrapper.set(FlightTask::getRemark, message);
            }
            
            int result = flightLineTaskMapper.update(null, updateWrapper);
            
            if (result > 0) {
                log.info("任务状态更新成功: taskId={}, status={}", taskId, status);
                return true;
            } else {
                log.warn("任务状态更新失败，未找到匹配的任务: taskId={}", taskId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("更新任务状态异常: taskId={}, status={}", taskId, status, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskOperatorById(String taskId, Long operatorId) {
        try {
            log.info("根据ID更新任务操作人: taskId={}, operatorId={}", taskId, operatorId);
            
            LambdaUpdateWrapper<FlightTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(FlightTask::getId, taskId)
                        .set(FlightTask::getOperatorId, operatorId)
                        .set(FlightTask::getUpdateTime, DateUtils.getNowDate());
            
            int result = flightLineTaskMapper.update(null, updateWrapper);
            
            if (result > 0) {
                log.info("任务操作人更新成功: taskId={}, operatorId={}", taskId, operatorId);
                return true;
            } else {
                log.warn("任务操作人更新失败，未找到匹配的任务: taskId={}", taskId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("更新任务操作人异常: taskId={}, operatorId={}", taskId, operatorId, e);
            throw e;
        }
    }
}
