package com.md.flight.yx.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 亿讯航点对象 yx_route_point
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("yx_route_point")
public class YxRoutePoint {
    /** 亿讯航点表ID */
    private Long id;

    /** 航线顺序 */
    private Integer pointOrder;

    /** 航点名称 */
    private String pointName;

    /** 经度 */
    private Double longitude;

    /** 纬度 */
    private Double latitude;

    /** 高度 */
    private float height;

    /** 速度(单位:米/秒，范围0~15) */
    private Long speed;

    /** 批次号 */
    private String batch;

    /** 同步时间 */
    private Date syncTime;
}
