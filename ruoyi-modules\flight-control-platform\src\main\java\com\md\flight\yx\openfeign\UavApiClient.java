package com.md.flight.yx.openfeign;

import com.md.flight.yx.openfeign.fallback.UavApiFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 无人机API接口客户端 提供无人机、航线、飞手和任务管理等功能的远程调用接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@FeignClient(name = "uavApiClient", url = "${uav.api.base-url}",
        fallbackFactory = UavApiFallback.class)
public interface UavApiClient {

    /**
     * 查询租户全部无人机记录
     *
     * @param timestamp 请求时间戳
     * @param sign      请求签名
     * @return 无人机列表响应
     */
    @PostMapping("/uav/openapi/v1.0/drone/list")
    String getDroneList(@RequestBody String request,
                        @RequestHeader("timestamp") String timestamp,
                        @RequestHeader("sign") String sign,
                        @RequestHeader("appId") String appId);

    /**
     * 查询飞手列表
     *
     * @param timestamp
     * @param sign
     * @param appId
     * @return
     */
    @PostMapping("/uav/openapi/v1.0/flyer/list")
    String getFlyerList(@RequestBody String request,
                        @RequestHeader("timestamp") String timestamp,
                        @RequestHeader("sign") String sign,
                        @RequestHeader("appId") String appId);

    /**
     * 查询航线
     *
     * @param timestamp 请求时间戳
     * @param sign      请求签名
     * @return 任务状态响应
     */
    @PostMapping("/uav/openapi/v1.0/planeline/list")
    String getLineList(@RequestBody String request,
                       @RequestHeader("timestamp") String timestamp,
                       @RequestHeader("sign") String sign,
                       @RequestHeader("appId") String appId);

    /**
     * 查询航点
     *
     * @param timestamp 请求时间戳
     * @param sign      请求签名
     * @return 任务状态响应
     */
    @PostMapping("/uav/openapi/v1.0/planeline/detail")
    String getLineDatail(@RequestBody String request,
                         @RequestHeader("timestamp") String timestamp,
                         @RequestHeader("sign") String sign,
                         @RequestHeader("appId") String appId);
}