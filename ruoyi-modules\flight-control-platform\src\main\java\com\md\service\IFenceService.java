package com.md.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.md.domain.bo.FenceBo;
import com.md.domain.po.FenceInfo;
import com.md.domain.vo.FenceVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 电子围栏服务接口
 */
public interface IFenceService extends IService<FenceInfo> {

    /**
     * 查询围栏列表（分页）
     *
     * @param bo        围栏业务对象
     * @param pageQuery 分页参数
     * @return 分页围栏集合
     */
    TableDataInfo<FenceVo> selectFenceList(FenceBo bo, PageQuery pageQuery);

    /**
     * 查询围栏详细信息
     *
     * @param id 围栏ID
     * @return 围栏详细信息
     */
    FenceVo selectFenceById(String id);

    /**
     * 新增围栏
     *
     * @param bo 围栏业务对象
     * @return 围栏ID
     */
    String insertFence(FenceBo bo);

    /**
     * 修改围栏
     *
     * @param bo 围栏业务对象
     * @return 围栏ID
     */
    String updateFence(FenceBo bo);

    /**
     * 批量删除围栏
     *
     * @param ids 需要删除的围栏ID数组
     * @return 结果
     */
    int deleteFenceByIds(String[] ids);

    /**
     * 启用围栏
     *
     * @param id 围栏ID
     * @return 结果
     */
    boolean enableFence(String id);

    /**
     * 禁用围栏
     *
     * @param id 围栏ID
     * @return 结果
     */
    boolean disableFence(String id);
}
