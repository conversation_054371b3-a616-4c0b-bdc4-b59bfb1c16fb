package com.md.domain.bo;

import com.md.domain.dto.UavPositionDto;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 无人机位置业务对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UavPositionDto.class)
public class UavPositionBo extends BaseEntity {
    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 无人机编码
     */
    @NotBlank(message = "无人机编码不能为空")
    private String uavCode;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;

    /**
     * 高度(米)
     */
    @NotNull(message = "高度不能为空")
    private BigDecimal height;
}