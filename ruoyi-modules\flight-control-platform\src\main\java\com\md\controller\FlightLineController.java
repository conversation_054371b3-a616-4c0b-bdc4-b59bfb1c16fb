package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.domain.bo.FlightLineBo;
import com.md.domain.vo.FlightLineVo;
import com.md.enums.BaseInfoSyncEnum;
import com.md.service.IBaseInfoService;
import com.md.service.IFlightLineService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 航线管理
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/flight/line")
public class FlightLineController extends BaseController {

    private final IFlightLineService flightLineService;

    /**
     * 查询航线管理列表
     */
    @SaCheckPermission("flight:line:query")
    @GetMapping("/list")
    public TableDataInfo<FlightLineVo> list(FlightLineBo flightLineBo, PageQuery pageQuery) {
        return flightLineService.selectFlightLineList(flightLineBo, pageQuery);
    }

    /**
     * 导出航线管理列表
     */
    @SaCheckPermission("flight:line:export")
    @Log(title = "航线管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FlightLineBo flightLineBo) {
        List<FlightLineVo> list = flightLineService.selectFlightLineList(flightLineBo);
        ExcelUtil.exportExcel(list, "航线管理数据", FlightLineVo.class, response);
    }

    /**
     * 获取航线管理详细信息
     */
    @SaCheckPermission("flight:line:query")
    @GetMapping(value = "/{id}")
    public R<FlightLineVo> getInfo(@PathVariable("id") String id) {
        return R.ok(flightLineService.selectFlightLineById(id));
    }

    /**
     * 根据航线ID生成kmz文件(需要优化成多个版本，不局限于大疆的kmz航线文件)
     */
    @SaCheckPermission("flight:line:upload")
    @RepeatSubmit
    @GetMapping(value = "/buildKmzByFlightLine")
    public R<Void> buildKmzByFlightLine(String id) {
        // 1. 获取航线和航点信息
        FlightLineVo flightLine = flightLineService.selectFlightLineById(id);
        if (flightLine == null) {
            throw new ServiceException("航线不存在");
        }

        // 2. 根据飞控平台类型，调用对应的服务进行航线生成
        IBaseInfoService syncType = BaseInfoSyncEnum.getSyncType(flightLine.getFlightControlNo());
        if (syncType != null) {
           return toAjax(syncType.buildKmzByFlightLine(id));
        }
        return toAjax(false);
    }

    /**
     * 新增航线管理
     */
    @SaCheckPermission("flight:line:add")
    @Log(title = "航线管理", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping
    public R<String> add(@RequestBody FlightLineBo flightLineBo) {
        String flightLineId = flightLineService.insertFlightLine(flightLineBo);
        return R.ok(flightLineId);
    }

    /**
     * 修改航线管理
     */
    @SaCheckPermission("flight:line:edit")
    @RepeatSubmit
    @Log(title = "航线管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<String> edit(@RequestBody FlightLineBo flightLineBo) {
        String flightLineId = flightLineService.updateFlightLine(flightLineBo);
        return R.ok(flightLineId);
    }

    /**
     * 删除航线管理
     */
    @SaCheckPermission("flight:line:remove")
    @RepeatSubmit
    @Log(title = "航线管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable String[] ids) {
        return toAjax(flightLineService.deleteFlightLineByIds(ids));
    }

    /**
     * 同步航线详情
     */
    @SaCheckPermission("flight:line:sync")
    @RepeatSubmit
    @GetMapping("/sync")
    public R<Void> sync(@RequestParam("code") String code) {
        return toAjax(flightLineService.syncFlightLine(code));
    }

    /**
     * 导出航线KMZ文件
     */
    @SaCheckPermission("flight:line:export")
    @GetMapping("/export/kmz")
    public R<String> exportKmz(@RequestParam String lineName, HttpServletResponse response) {
        try {
            String fileUrl = flightLineService.exportKmz(lineName);
            return R.ok(fileUrl);
        } catch (Exception e) {
            log.error("获取航线[{}]下载地址失败", lineName, e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 更新航线关联的围栏
     *
     * @param lineId   航线ID
     * @param fenceIds 围栏ID列表，以逗号分隔
     * @return 操作结果
     */
    @SaCheckPermission("flight:line:edit")
    @PutMapping("/fences/{lineId}")
    public R<Void> updateLineFences(@PathVariable("lineId") String lineId, @RequestParam String fenceIds) {
        boolean result = flightLineService.updateLineFences(lineId, fenceIds);
        return result ? R.ok() : R.fail("更新航线围栏关联失败");
    }

    /**
     * 获取航线关联的围栏ID
     *
     * @param lineId 航线ID
     * @return 围栏ID列表，以逗号分隔
     */
    @SaCheckPermission("flight:line:query")
    @GetMapping("/fences/{lineId}")
    public R<String> getLineFences(@PathVariable("lineId") String lineId) {
        String fenceIds = flightLineService.getLineFences(lineId);
        return R.ok(fenceIds);
    }
}
