package com.md.utils;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 无人机控制锁工具类
 */
@Component
@Slf4j
public class DroneLockUtil {

    private static final String LOCK_PREFIX = "drone:lock:";

    private static final Integer LOCK_EXPIRE_TIME = 15 * 60; // 15分钟

    /**
     * 尝试获取无人机控制权
     *
     * @param uavCode    无人机编号
     * @param operatorId 操作员ID
     * @return 是否获取成功
     */
    public boolean tryLock(String uavCode, Long operatorId) {
        String lockKey = LOCK_PREFIX + uavCode;
        String lockValue = operatorId + ":" + System.currentTimeMillis();

        try {
            // 直接尝试原子获取锁（SET NX EX）
            Boolean success = TenantHelper.ignore(
                () -> RedisUtils.setObjectIfAbsent(lockKey, lockValue, Duration.ofSeconds(LOCK_EXPIRE_TIME)));

            if (Boolean.TRUE.equals(success)) {
                log.info("操作员[{}]成功获取无人机[{}]的控制权", operatorId, uavCode);
                return true;
            }

            // 获取失败，检查是否是当前操作员的锁（需要刷新）
            Object existValue = TenantHelper.ignore(() -> RedisUtils.getCacheObject(lockKey));
            if (existValue != null) {
                String existValueStr = existValue.toString();
                if (existValueStr.startsWith(operatorId + ":")) {
                    // 是当前操作员的锁，刷新过期时间
                    TenantHelper.ignore(() -> {
                        RedisUtils.setCacheObject(lockKey, lockValue, Duration.ofSeconds(LOCK_EXPIRE_TIME));
                        return null;
                    });
                    log.debug("操作员[{}]已持有无人机[{}]的控制权，刷新过期时间", operatorId, uavCode);
                    return true;
                } else {
                    log.debug("无人机[{}]已被其他操作员[{}]控制", uavCode, existValueStr.split(":")[0]);
                    return false;
                }
            }

            log.debug("无人机[{}]控制权获取失败，原因未知", uavCode);
            return false;
        } catch (Exception e) {
            log.error("获取无人机控制锁失败: uavCode={}, operatorId={}", uavCode, operatorId, e);
            return false;
        }
    }

    /**
     * 释放无人机控制权
     *
     * @param uavCode 无人机编号
     */
    public void unlock(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        TenantHelper.ignore(() -> {
            RedisUtils.deleteObject(lockKey);
            return null;
        });
        log.info("无人机[{}]的控制权已释放", uavCode);
    }

    /**
     * 检查无人机是否被锁定
     *
     * @param uavCode 无人机编号
     * @return 是否被锁定
     */
    public boolean isLocked(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        return TenantHelper.ignore(() -> RedisUtils.getCacheObject(lockKey) != null);
    }

    /**
     * 获取当前锁定操作员ID
     *
     * @param uavCode 无人机编号
     * @return 操作员ID，如果未锁定则返回null
     */
    public Long getLockedOperatorId(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        String value = TenantHelper.ignore(() -> RedisUtils.getCacheObject(lockKey));
        if (value != null) {
            return Long.parseLong(value.split(":")[0]);
        }
        return null;
    }
}