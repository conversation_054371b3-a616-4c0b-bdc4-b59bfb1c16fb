package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.md.command.constant.CommandType;
import com.md.command.constant.ExecutorType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 无人机指令日志实体
 */
@Data
@Accessors(chain = true)
@TableName("drone_command_log")
public class DroneCommandLog {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("command_id")
    private String commandId;
    
    @TableField("drone_id")
    private String droneId;
    
    @TableField("command_type")
    private CommandType commandType;
    
    @TableField("executor_type")
    private ExecutorType executorType;
    
    @TableField("parameters")
    private String parameters;
    
    @TableField("execution_status")
    private Integer executionStatus;
    
    @TableField("error_message")
    private String errorMessage;
    
    @TableField("response_data")
    private String responseData;
    
    @TableField("execution_time")
    private Long executionTime;
    
    @TableField("create_time")
    private Date createTime;
    
    @TableField("create_by")
    private String createBy;
    
    @TableField("update_time")
    private Date updateTime;
    
    @TableField("update_by")
    private String updateBy;
    
    @TableField("remark")
    private String remark;
} 