package com.md.flight.engine.impl;

import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.enums.TaskStatusEnum;
import com.md.flight.engine.TaskExecutionEngine;
import com.md.flight.mavlink.converter.FlightTaskConverter;
import com.md.flight.mavlink.model.Mission;
import com.md.flight.mavlink.service.MavlinkMissionService;
import com.md.mqtt.event.DroneType;
import com.md.mqtt.event.TaskExecutionEvent;
import com.md.service.IFlightTaskStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * MAVLink任务执行引擎
 * 使用MAVLink协议执行航线任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MavlinkExecutionEngine implements TaskExecutionEngine {

    @Lazy
    private final MavlinkMissionService mavlinkMissionService;
    private final FlightTaskConverter flightTaskConverter;
    private final IFlightTaskStatusService flightTaskStatusService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 厂商类型编码 - MD
     */
    private static final String MANUFACTURER_MD = "MD";

    /**
     * 协议类型编码 - MAVLINK
     */
    private static final String PROTOCOL_MAVLINK = "MAVLINK";

    @Override
    public CompletableFuture<Boolean> executeTask(FlightTask task, List<FlightTaskPoint> points) {
        try {
            log.info("使用MAVLink引擎执行航线任务: taskId={}, taskName={}", task.getId(), task.getTaskName());

            // 1. 检查无人机编码
            String uavCode = task.getUavCode();
            if (uavCode == null || uavCode.isEmpty()) {
                throw new ServiceException("任务未关联无人机");
            }

            // 2. 转换为MAVLink任务
            Mission mission = flightTaskConverter.convertToMavlinkMission(task, points);

            // 3. 上传MAVLink任务到无人机并等待完成
            CompletableFuture<Boolean> uploadResult = mavlinkMissionService.uploadMission(uavCode, mission);
            Boolean uploadSuccess = uploadResult.get(); // 阻塞等待上传完成

            if (!uploadSuccess) {
                throw new ServiceException("航线上传失败");
            }

            log.info("航线已成功上传到无人机: taskId={}, uavCode={}", task.getId(), uavCode);

            // 4. 发布任务执行事件，触发轨迹记录（在任务开始前发布，确保能够记录完整的轨迹）
            try {
                eventPublisher.publishEvent(new TaskExecutionEvent(uavCode, task.getId(), true, DroneType.MAVLINK));
                log.info("已发布MAVLink任务开始执行事件: taskId={}, uavCode={}", task.getId(), uavCode);
            } catch (Exception e) {
                log.error("发布MAVLink任务开始执行事件失败: taskId={}, uavCode={}", task.getId(), uavCode, e);
            }

            // 5. 上传成功后，开始执行任务
            mavlinkMissionService.startMission(uavCode, task.getFlightSpeed(), task.getTaskName());

            // 6. 任务开始执行后，更新任务状态为执行中
            flightTaskStatusService.updateTaskStatus(task.getTaskName(), TaskStatusEnum.EXECUTING.getCode(), null);

            // 7. 更新任务操作人（如果operatorId不为空）
            Long operatorId = LoginHelper.getUserId();
            if (operatorId != null) {
                flightTaskStatusService.updateTaskOperator(task.getTaskName(), operatorId);
                log.info("已更新任务[{}]的执飞人ID为: {}", task.getTaskName(), operatorId);
            } else {
                log.info("执飞人ID为空，跳过更新任务[{}]的执飞人", task.getTaskName());
            }

            return CompletableFuture.completedFuture(true);
        } catch (Exception e) {
            log.error("MAVLink执行航线任务失败: taskId={}", task.getId(), e);
            throw new ServiceException("执行航线失败: " + e.getMessage());
        }
    }

    @Override
    public String getSupportedManufacturer() {
        return MANUFACTURER_MD;
    }

    @Override
    public String getSupportedProtocol() {
        return PROTOCOL_MAVLINK;
    }
}