package com.md.flight.djauv.domain.kml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

@Data
@XStreamAlias("wpml:waypointHeadingParam")
public class KmlWaypointHeadingParam {

    @XStreamAlias("wpml:waypointHeadingMode")
    private String waypointHeadingMode;

    @XStreamAlias("wpml:waypointHeadingAngle")
    private String waypointHeadingAngle;

    @XStreamAlias("wpml:waypointPoiPoint")
    private String waypointPoiPoint;

    @XStreamAlias("wpml:waypointHeadingPathMode")
    private String waypointHeadingPathMode;


}
