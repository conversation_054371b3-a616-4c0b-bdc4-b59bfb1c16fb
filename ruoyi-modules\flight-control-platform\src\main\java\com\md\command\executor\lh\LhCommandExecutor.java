package com.md.command.executor.lh;

import cn.hutool.core.util.ObjectUtil;
import com.md.command.constant.CommandType;
import com.md.command.core.DroneCommand;
import com.md.command.core.DroneCommandExecutor;
import com.md.command.model.dto.CommandResult;
import com.md.flight.lh.domain.dto.HomePoint;
import com.md.flight.lh.service.LhDroneControlService;
import com.md.service.IFlightTaskExecutionService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 联合飞机命令执行器
 * 负责将通用命令转发给联合飞机服务执行
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LhCommandExecutor implements DroneCommandExecutor {

    private final LhDroneControlService lhDroneControlService;

    @Lazy
    private final IFlightTaskExecutionService flightTaskExecutionService;

    // 跟踪已开启虚拟摇杆的无人机
    private final Set<String> virtualStickEnabledDrones = ConcurrentHashMap.newKeySet();

    // 命令处理器映射
    private final Map<CommandType, CommandHandler> commandHandlers = new EnumMap<>(CommandType.class);

    private final String LH_FLIGHT_CODE = "lh:flight_code:";

    /**
     * 命令处理器函数式接口
     */
    @FunctionalInterface
    private interface CommandHandler {
        CommandResult handle(DroneCommand command) throws Exception;
    }

    /**
     * 初始化命令处理器映射
     */
    @PostConstruct
    private void initCommandHandlers() {
        // 航线任务相关
        commandHandlers.put(CommandType.EXECUTE_MINIO_MISSION, this::executeFlightTask);
        commandHandlers.put(CommandType.PAUSE_MISSION, this::executePauseMissionWrapper);
        commandHandlers.put(CommandType.RESUME_MISSION, this::executeResumeMissionWrapper);

        // 基本飞行控制
        commandHandlers.put(CommandType.GO_HOME, this::executeGoHomeWrapper);
        commandHandlers.put(CommandType.LAND, this::executeLandWrapper);
        commandHandlers.put(CommandType.VIRTUAL_STICK_LAND, this::executeLandWrapper);
        commandHandlers.put(CommandType.VIRTUAL_STICK_TAKEOFF, this::executeTakeoffWrapper);

        // 虚拟摇杆控制
        commandHandlers.put(CommandType.VIRTUAL_STICK_START, this::executeVirtualStickStartWrapper);
        commandHandlers.put(CommandType.VIRTUAL_STICK_STOP, this::executeVirtualStickStopWrapper);
    }

    @Override
    public CommandResult executeCommand(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            CommandType commandType = command.getCommandType();
            Map<String, Object> params = command.getParameters();

            log.info("收到联合飞机命令: droneId={}, commandType={}, params={}", droneId, commandType, params);

            // 查找对应的命令处理器
            CommandHandler handler = commandHandlers.get(commandType);
            if (ObjectUtil.isNotNull(handler)) {
                log.debug("开始执行联合飞机命令: droneId={}, commandType={}", droneId, commandType);
                CommandResult result = handler.handle(command);
                log.info("联合飞机命令执行完成: droneId={}, commandType={}, 结果={}", droneId, commandType,
                    result.isSuccess() ? "成功" : "失败");
                return result;
            }

            log.warn("不支持的联合飞机命令类型: droneId={}, commandType={}", droneId, commandType);
            return CommandResult.failed("不支持的联合飞机命令类型: " + commandType);
        } catch (Exception e) {
            log.error("联合飞机命令执行失败: command={}, error={}", command, e.getMessage(), e);
            return CommandResult.failed("联合飞机命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行起飞命令
     */
    private CommandResult executeTakeoff(String droneId, Map<String, Object> params) {
        try {
            // 检查虚拟摇杆是否已开启
            if (requiresVirtualStickMode(droneId)) {
                log.warn("无人机未开启虚拟摇杆控制模式: droneId={}", droneId);
                return CommandResult.failed("请先开启虚拟摇杆控制模式");
            }

            // 提取参数
            int mode = getIntParam(params, "mode", 1); // 默认使用相对高度
            float altitude = getFloatParam(params, "altitude", 10.0f); // 默认10米高度

            // 调用服务执行起飞
            boolean success = lhDroneControlService.takeoff(droneId, mode, altitude);

            // 根据执行结果返回不同的结果
            if (success) {
                return CommandResult.success("联合飞机起飞命令已发送并收到响应");
            } else {
                return CommandResult.failed("联合飞机起飞命令已发送但响应超时，请检查无人机状态");
            }
        } catch (Exception e) {
            log.error("联合飞机起飞命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("联合飞机起飞命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行降落命令
     */
    private CommandResult executeLand(String droneId, Map<String, Object> params) {
        try {
            // 删除飞行任务
            TenantHelper.ignore(() ->
                RedisUtils.deleteObject(LH_FLIGHT_CODE + droneId)
            );

            // 提取参数
            int mode = getIntParam(params, "mode", 0); // 默认直接降落
            String code = getStringParam(params, "code", ""); // 降落二维码编号（可选）

            log.info("执行联合飞机降落命令: droneId={}, mode={}, code={}", droneId, mode, code);

            // 调用服务执行降落
            boolean success = lhDroneControlService.land(droneId, mode, code);

            // 根据执行结果返回不同的结果
            if (success) {
                return CommandResult.success("联合飞机降落命令已发送并收到响应");
            } else {
                return CommandResult.failed("联合飞机降落命令已发送但响应超时，请检查无人机状态");
            }
        } catch (Exception e) {
            log.error("联合飞机降落命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("联合飞机降落命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行返航命令
     */
    private CommandResult executeGoHome(String droneId, Map<String, Object> params) {
        try {
            // 提取参数
            int mode = getIntParam(params, "mode", 1); // 默认安全高度返航
            float safeAltitude = getFloatParam(params, "safeAltitude", 120.0f); // 默认120米
            float speed = getFloatParam(params, "speed", 10.0f); // 默认10米/秒

            // 提取指定返航点参数
            boolean specificHome = getBooleanParam(params, "specificHome", false);
            HomePoint homePoint = null;

            if (specificHome) {
                // 从参数中提取返航点信息
                Map<String, Object> homePointMap = getMapParam(params, "homePoint", null);
                if (homePointMap != null) {
                    double latitude = getDoubleParam(homePointMap, "latitude", 0.0);
                    double longitude = getDoubleParam(homePointMap, "longitude", 0.0);
                    float altitude = getFloatParam(homePointMap, "altitude", 0.0f);
                    float heading = getFloatParam(homePointMap, "heading", 0.0f);

                    homePoint = new HomePoint((float) latitude, (float) longitude, altitude, heading);

                    log.info("执行联合飞机返航命令(指定返航点): droneId={}, mode={}, safeAltitude={}, speed={}, " +
                            "homePoint=[lat={}, lng={}, alt={}, heading={}]", droneId, mode, safeAltitude, speed, latitude,
                        longitude, altitude, heading);
                } else {
                    log.warn("指定了返航点但未提供有效的返航点参数，将使用默认返航点");
                    specificHome = false;
                }
            } else {
                log.info("执行联合飞机返航命令: droneId={}, mode={}, safeAltitude={}, speed={}", droneId, mode,
                    safeAltitude, speed);
            }

            // 调用服务执行返航
            boolean success =
                lhDroneControlService.goHomeExtended(droneId, mode, safeAltitude, speed, specificHome, homePoint);

            // 根据执行结果返回不同的结果
            if (success) {
                return CommandResult.success("联合飞机返航命令已发送并收到响应");
            } else {
                return CommandResult.failed("联合飞机返航命令已发送但响应超时，请检查无人机状态");
            }
        } catch (Exception e) {
            log.error("联合飞机返航命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("联合飞机返航命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行暂停任务命令
     */
    private CommandResult executePauseMission(String droneId, Map<String, Object> params) {
        try {
            log.info("执行联合飞机暂停任务命令: droneId={}, params={}", droneId, params);
            // 调用服务执行返航
            boolean success =
                lhDroneControlService.executePauseMission(droneId);

            // 根据执行结果返回不同的结果
            if (success) {
                return CommandResult.success("联合飞机暂停任务命令已发送并收到响应");
            } else {
                return CommandResult.failed("联合飞机暂停任务命令已发送但响应超时，请检查无人机状态");
            }
        } catch (Exception e) {
            log.error("联合飞机暂停任务命令命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("联合飞机暂停任务命令命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行恢复任务命令
     */
    private CommandResult executeResumeMission(String droneId, Map<String, Object> params) {
        try {
            log.info("执行联合飞机继续任务命令: droneId={}, params={}", droneId, params);
            // 调用服务执行返航
            boolean success =
                lhDroneControlService.executeResumeMission(droneId);

            // 根据执行结果返回不同的结果
            if (success) {
                return CommandResult.success("联合飞机继续任务命令已发送并收到响应");
            } else {
                return CommandResult.failed("联合飞机继续任务命令已发送但响应超时，请检查无人机状态");
            }
        } catch (Exception e) {
            log.error("联合飞机继续任务命令命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("联合飞机继续任务命令命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行开启虚拟摇杆控制命令
     */
    private CommandResult executeVirtualStickStart(String droneId, Map<String, Object> params) {
        try {
            log.info("执行联合飞机开启虚拟摇杆控制命令: droneId={}, params={}", droneId, params);

            // 调用服务执行开启虚拟摇杆
            lhDroneControlService.startVirtualStick(droneId);

            // 标记无人机已开启虚拟摇杆
            virtualStickEnabledDrones.add(droneId);
            log.info("已启用联合飞机虚拟摇杆控制模式: droneId={}", droneId);

            return CommandResult.success("联合飞机虚拟摇杆控制模式已启用");
        } catch (Exception e) {
            log.error("联合飞机启用虚拟摇杆控制模式失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("联合飞机启用虚拟摇杆控制模式失败: " + e.getMessage());
        }
    }

    /**
     * 执行停止虚拟摇杆控制命令
     */
    private CommandResult executeVirtualStickStop(String droneId, Map<String, Object> params) {
        try {
            log.info("执行联合飞机停止虚拟摇杆控制命令: droneId={}, params={}", droneId, params);

            // 调用服务执行停止虚拟摇杆
            lhDroneControlService.stopVirtualStick(droneId);

            // 移除无人机的虚拟摇杆状态
            virtualStickEnabledDrones.remove(droneId);
            log.info("已停用联合飞机虚拟摇杆控制模式: droneId={}", droneId);

            return CommandResult.success("联合飞机虚拟摇杆控制已停止");
        } catch (Exception e) {
            log.error("联合飞机停止虚拟摇杆控制失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("联合飞机停止虚拟摇杆控制失败: " + e.getMessage());
        } finally {
            // 确保在发生异常时也移除状态
            virtualStickEnabledDrones.remove(droneId);
        }
    }

    /**
     * 检查无人机是否需要先开启虚拟摇杆模式
     *
     * @param droneId 无人机ID
     * @return true-需要先开启虚拟摇杆模式，false-已开启虚拟摇杆模式
     */
    private boolean requiresVirtualStickMode(String droneId) {
        return !virtualStickEnabledDrones.contains(droneId);
    }

    /**
     * 获取整数参数
     */
    private int getIntParam(Map<String, Object> params, String name, int defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("参数{}解析为整数失败，使用默认值{}", name, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 获取浮点参数
     */
    private float getFloatParam(Map<String, Object> params, String name, float defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Number) {
            return ((Number) value).floatValue();
        }
        try {
            return Float.parseFloat(value.toString());
        } catch (NumberFormatException e) {
            log.warn("参数{}解析为浮点数失败，使用默认值{}", name, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 获取字符串参数
     */
    private String getStringParam(Map<String, Object> params, String name, String defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        return value.toString();
    }

    /**
     * 获取布尔参数
     */
    private boolean getBooleanParam(Map<String, Object> params, String name, boolean defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue() != 0;
        }
        String strValue = value.toString().toLowerCase();
        return "true".equals(strValue) || "1".equals(strValue) || "yes".equals(strValue);
    }

    /**
     * 获取Map参数
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getMapParam(Map<String, Object> params, String name, Map<String, Object> defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        log.warn("参数{}不是Map类型，使用默认值", name);
        return defaultValue;
    }

    /**
     * 获取双精度浮点参数
     */
    private double getDoubleParam(Map<String, Object> params, String name, double defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            log.warn("参数{}解析为双精度浮点数失败，使用默认值{}", name, defaultValue);
            return defaultValue;
        }
    }

    @Override
    public Set<String> getSupportedManufacturers() {
        return Set.of("LH");
    }

    @Override
    public String getSupportedProtocol() {
        return "MQTT";
    }

    @Override
    public int getPriority() {
        return 10; // 默认优先级
    }

    /**
     * 执行航线任务命令
     * 联合飞机直接通过executeFlightTask执行任务
     *
     * @param command 无人机命令
     * @return 命令执行结果
     */
    private CommandResult executeFlightTask(DroneCommand command) {
        try {
            String droneId = command.getDroneId();

            // 从命令中获取任务ID
            String taskId = getTaskIdFromCommand(command);
            if (taskId == null) {
                log.error("未找到任务ID: droneId={}", droneId);
                return CommandResult.failed("未找到任务ID");
            }

            return executeFlightTaskInternal(droneId, taskId);
        } catch (Exception e) {
            log.error("执行联合飞机航线任务命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行联合飞机航线任务命令失败: " + e.getMessage());
        }
    }

    /**
     * 内部执行航线任务的方法
     *
     * @param droneId 无人机ID
     * @param taskId  任务ID
     * @return 命令执行结果
     */
    private CommandResult executeFlightTaskInternal(String droneId, String taskId) {
        try {
            log.info("执行联合飞机航线任务命令: droneId={}, taskId={}", droneId, taskId);

            // 使用联合飞机任务执行引擎执行任务
            log.info("开始通过联合飞机任务执行引擎执行航线任务: taskId={}", taskId);
            CompletableFuture<Boolean> future = flightTaskExecutionService.executeFlightTask(taskId);

            // 异步处理执行结果
            future.whenComplete((success, throwable) -> {
                if (throwable != null) {
                    log.error("联合飞机航线任务执行失败: taskId={}, error={}", taskId, throwable.getMessage(),
                        throwable);
                } else if (success) {
                    log.info("联合飞机航线任务执行成功: taskId={}", taskId);
                } else {
                    log.warn("联合飞机航线任务执行失败: taskId={}", taskId);
                }
            });

            return CommandResult.success("联合飞机航线任务已开始执行");
        } catch (Exception e) {
            log.error("执行联合飞机航线任务命令失败: droneId={}, taskId={}, error={}", droneId, taskId, e.getMessage(),
                e);
            return CommandResult.failed("执行联合飞机航线任务命令失败: " + e.getMessage());
        }
    }

    /**
     * 从命令中获取任务ID
     * 优先从命令的taskId字段获取，如果没有再从参数中获取
     *
     * @param command 无人机命令
     * @return 任务ID，如果未找到则返回null
     */
    private String getTaskIdFromCommand(DroneCommand command) {
        // 优先从命令的taskId字段获取
        String taskId = command.getTaskId();
        if (taskId != null && !taskId.trim().isEmpty()) {
            return taskId;
        }

        // 如果命令级别没有taskId，再从参数中获取
        if (command.getParameters() != null) {
            Map<String, Object> params = command.getParameters();
            Object taskIdObj = params.get("taskId");
            if (taskIdObj != null) {
                return taskIdObj.toString();
            }
        }

        return null;
    }

    // 包装器方法，将DroneCommand转换为原有的方法调用
    private CommandResult executeGoHomeWrapper(DroneCommand command) {
        return executeGoHome(command.getDroneId(), command.getParameters());
    }

    private CommandResult executePauseMissionWrapper(DroneCommand command) {
        return executePauseMission(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeResumeMissionWrapper(DroneCommand command) {
        return executeResumeMission(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeLandWrapper(DroneCommand command) {
        return executeLand(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeTakeoffWrapper(DroneCommand command) {
        return executeTakeoff(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeVirtualStickStartWrapper(DroneCommand command) {
        return executeVirtualStickStart(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeVirtualStickStopWrapper(DroneCommand command) {
        return executeVirtualStickStop(command.getDroneId(), command.getParameters());
    }
}
