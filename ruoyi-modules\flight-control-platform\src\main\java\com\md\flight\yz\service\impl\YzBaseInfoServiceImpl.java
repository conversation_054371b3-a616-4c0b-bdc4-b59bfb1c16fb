package com.md.flight.yz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.domain.dto.YzBaseInfoDto;
import com.md.domain.po.*;
import com.md.enums.BaseInfoSyncEnum;
import com.md.enums.DataSourceEnum;
import com.md.flight.yz.domain.po.YzFlightRoute;
import com.md.flight.yz.domain.po.YzUAV;
import com.md.flight.yz.factory.YzBaseInfoFactory;
import com.md.flight.yz.service.IYzFlightRouteService;
import com.md.flight.yz.service.IYzUAVService;
import com.md.mapper.FlightPointMapper;
import com.md.service.IBaseInfoService;
import com.md.service.IFlightLineService;
import com.md.service.IPlatformInfoService;
import com.md.service.IUavInfoService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 亿航基本信息实现类
 */
@Service
@RequiredArgsConstructor
public class YzBaseInfoServiceImpl implements IBaseInfoService {

    private final YzBaseInfoFactory baseInfo;

    private final IUavInfoService uavInfoService;

    private final IPlatformInfoService platformInfoService;

    private final IYzUAVService iUAVService;

    private final IFlightLineService flightLineService;

    private final IYzFlightRouteService yzFlightRouteService;

    private final FlightPointMapper flightPointMapper;


    /**
     * 同步无人机信息
     */
    @Override
    public void syncUAV() {
        String syncType = BaseInfoSyncEnum.YZ_SYNC.getSyncType();
        YzBaseInfoDto yzBaseInfoDto = new YzBaseInfoDto();
        yzBaseInfoDto.setDataType("drone");
        yzBaseInfoDto.setLastTime("2024-01-01 00:00:00");
        yzBaseInfoDto.setChannelType("1");
        LambdaQueryWrapper<PlatformInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PlatformInfo::getFlightControlNo, syncType);
        PlatformInfo platform = platformInfoService.getOne(queryWrapper);

        Date nowDate = DateUtils.getNowDate();
        String simpleUUID = IdUtil.fastSimpleUUID();
        List<UavInfo> uavList = new ArrayList<>();
        List<YzUAV> baseInfoList = baseInfo.getBaseInfoList(yzBaseInfoDto, YzUAV.class);
        //获取原业务表中亿航数据
        List<UavInfo> uavs = uavInfoService.list(Wrappers.<UavInfo>lambdaQuery().eq(UavInfo::getManufacturerName, platform.getManufacturerName()));
        List<YzUAV> baseInfoListStream = baseInfoList.stream()
                .map(baseInfo -> {
                    UavInfo uav = new UavInfo();
                    uav.setUavCode(baseInfo.getDroneCode());
                    uav.setCategoryCode(baseInfo.getCategoryCode());
                    uav.setManufacturerName(platform.getManufacturerName());
                    uav.setFlightControlNo(platform.getFlightControlNo());
                    uav.setStatus(baseInfo.getIsEnabled() != null ? baseInfo.getIsEnabled() : 0);
                    uav.setDelFlag(baseInfo.getIsDeleted() != null ? baseInfo.getIsDeleted() : 0);
                    uav.setRemark(baseInfo.getRemarks());
                    uav.setBatchId(simpleUUID);
                    uav.setSyncTime(DateUtils.getNowDate());
                    uav.setCreateTime(baseInfo.getCreateTime());
                    uav.setCreateTime(nowDate);
                    uav.setCreateBy(LoginHelper.getUserId());
                    uav.setDataSource(DataSourceEnum.SYNC.getCode());
                    UavInfo tmpUav = uavs.stream().filter(u -> u.getUavCode().equals(baseInfo.getDroneCode())).findAny().orElse(null);
                    if (ObjectUtil.isNotEmpty(tmpUav)) {
                        uav.setRemark(tmpUav.getRemark());
                    }
                    uavList.add(uav);

                    baseInfo.setSyncTime(nowDate);
                    baseInfo.setBatchId(simpleUUID);
                    return baseInfo;
                })
                .collect(Collectors.toList());

        //添加数据到亿航日志表中
        iUAVService.saveBatch(baseInfoListStream);
        //删除业务表中亿航数据
        uavInfoService.remove(Wrappers.<UavInfo>lambdaQuery().eq(UavInfo::getManufacturerName, platform.getManufacturerName()));
        //在业务表插入无人机信息
        uavInfoService.saveBatch(uavList);
    }

    @Override
    public void syncFlyer() {
        System.out.println("亿航暂无同步飞手接口");
    }

    /**
     * 同步亿航航线数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncFlightLine() {
        // 1. 获取平台信息
        String syncType = BaseInfoSyncEnum.YZ_SYNC.getSyncType();
        LambdaQueryWrapper<PlatformInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PlatformInfo::getFlightControlNo, syncType);
        PlatformInfo platform = platformInfoService.getOne(queryWrapper);

        // 2. 获取原业务表中亿航数据
        List<FlightLine> existingLines = flightLineService.list(
                Wrappers.<FlightLine>lambdaQuery()
                        .eq(FlightLine::getFlightControlNo, platform.getFlightControlNo())
        );

        // 3. 获取亿航航线数据
        YzBaseInfoDto yzBaseInfoDto = new YzBaseInfoDto();
        yzBaseInfoDto.setDataType("airline");
        yzBaseInfoDto.setLastTime("2024-01-01 00:00:00");
        yzBaseInfoDto.setChannelType("1");
        List<YzFlightRoute> baseInfoList = baseInfo.getBaseInfoList(yzBaseInfoDto, YzFlightRoute.class);

        // 4. 构建新的航线和航点数据
        List<FlightLine> flightLines = new ArrayList<>();
        List<FlightPoint> allPoints = new ArrayList<>();
        Date nowDate = DateUtils.getNowDate();

        for (YzFlightRoute yzFlightRoute : baseInfoList) {
            // 4.1 生成航线ID和航线名称
            long snowflakeId = IdWorker.getId();
            String lineName = yzFlightRoute.getTakeoffApronName() + "--->" + yzFlightRoute.getLandApronName();

            // 4.2 构建航线数据
            FlightLine flightLine = new FlightLine();
            flightLine.setId(String.valueOf(snowflakeId));
            flightLine.setFlightControlNo(platform.getFlightControlNo());
            flightLine.setLineName(lineName);
            flightLine.setCreateTime(nowDate);
            flightLine.setCreateBy(LoginHelper.getUserId());
            flightLine.setFlightControlNo(platform.getFlightControlNo());
            flightLine.setDataSource(DataSourceEnum.SYNC.getCode());

            // 4.4 构建航点数据
            // 起飞点
            FlightPoint takeOffPoint = new FlightPoint();
            takeOffPoint.setId(String.valueOf(IdWorker.getId()));
            takeOffPoint.setLineId(String.valueOf(snowflakeId));
            takeOffPoint.setPointIndex(0L);
            takeOffPoint.setLatitude(new BigDecimal(Double.toString(yzFlightRoute.getTakeOffApronLatitude())));
            takeOffPoint.setLongitude(new BigDecimal(Double.toString(yzFlightRoute.getTakeOffApronLongitude())));
            takeOffPoint.setAltitude(new BigDecimal(Long.toString(yzFlightRoute.getTakeOffApronAltitude())));
            takeOffPoint.setCreateTime(nowDate);
            takeOffPoint.setCreateBy(LoginHelper.getUserId());

            // 降落点
            FlightPoint landPoint = new FlightPoint();
            landPoint.setId(String.valueOf(IdWorker.getId()));
            landPoint.setLineId(String.valueOf(snowflakeId));
            landPoint.setPointIndex(1L);
            landPoint.setLatitude(new BigDecimal(Double.toString(yzFlightRoute.getLandApronLatitude())));
            landPoint.setLongitude(new BigDecimal(Double.toString(yzFlightRoute.getLandApronLongitude())));
            landPoint.setAltitude(new BigDecimal(Long.toString(yzFlightRoute.getLandApronAltitude())));
            landPoint.setCreateTime(nowDate);
            landPoint.setCreateBy(LoginHelper.getUserId());

            allPoints.add(takeOffPoint);
            allPoints.add(landPoint);
            flightLines.add(flightLine);
        }

        // 5. 删除旧数据
        List<String> oldLineIds = flightLineService.list(
                Wrappers.<FlightLine>lambdaQuery()
                        .eq(FlightLine::getFlightControlNo, platform.getFlightControlNo())
                        .eq(FlightLine::getDataSource, DataSourceEnum.SYNC.getCode())
        ).stream().map(FlightLine::getId).collect(Collectors.toList());

        if (!oldLineIds.isEmpty()) {
            flightPointMapper.deletePointsByLineIds(oldLineIds);
        }

        flightLineService.remove(
                Wrappers.<FlightLine>lambdaQuery()
                        .eq(FlightLine::getFlightControlNo, platform.getFlightControlNo())
                        .eq(FlightLine::getDataSource, DataSourceEnum.SYNC.getCode())
        );

        // 6. 保存新数据
        if (!flightLines.isEmpty()) {
            flightLineService.saveBatch(flightLines);
            if (!allPoints.isEmpty()) {
                flightPointMapper.batchInsertPoints(allPoints);
            }
        }
    }

    @Override
    public boolean buildKmzByFlightLine(String routeId) {
        return true;
    }
}
