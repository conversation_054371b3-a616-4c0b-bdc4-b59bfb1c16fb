package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

/**
 * 接口管理表 sys_api_config
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_api_config", autoResultMap = true)
public class SysApiConfig extends TenantEntity {

    /**
     * 接口ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 目标平台
     */
    private String targetPlatform;

    /**
     * 平台编码
     */
    private String plateformCode;

    /**
     * 访问地址
     */
    private String url;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 配置信息
     */
    private String encryptInfo;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private String delFlag;

}
