package com.md.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 飞行器信息DTO
 */
@Data
@Accessors(chain = true)
public class UavInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 无人机驾驶航空器分类
     * 1，代表微型无人驾驶航空器
     * 2，代表轻型无人驾驶航空器
     * 3，代表小型无人驾驶航空器
     * 4，代表中型无人驾驶航空器
     * 5，代表大型无人驾驶航空器
     * 6，代表农用无人驾驶航空器
     */
    private Integer level;

    /**
     * 无人驾驶航空器类型
     * 0，代表固定翼
     * 1，代表旋翼航空器
     * 2，代表多桨或多轴航空器
     * 3，代表复合翼航空器
     * 4，代表飞艇
     * 5，代表其他
     */
    private String uavType;

    /**
     * 无人驾驶航空器产品序列号（SN码),机身码
     */
    private String sn;

    /**
     * 产品名称
     */
    private String lavName;

    /**
     * 产品型号
     */
    private String uavModel;

    /**
     * 生产厂商名称
     */
    private String uavManufacturer;

    /**
     * 空机重量
     */
    private String uavEmptyWeight;

    /**
     * 单位类型
     * 01，代表所有权人
     * 0101，代表营利法人
     * 010101，代表营利法人
     * 0102，代表非营利法人
     * 010201，代表事业单位
     * 010202，代表社会团体
     * 010203，代表基金会
     * 010204，代表社会服务机构
     * 010205，代表其他
     * 0103，代表特别法人
     * 010301，代表机关法人
     * 010302，代表农村集体经济组织法人
     * 010303，代表城镇农村的合作经济组织法人
     * 010304，代表基层群众性自治组织法人
     * 02，代表生产厂商
     * 0201，代表国内无人驾驶航空器生产厂商
     * 0202，代表国外无人驾驶航空器生产厂商
     * 说明：用户类型为非个人时才展示该字段
     */
    private String  uavUnitType;

    /**
     * 航空器属性
     * 0，无人机
     * 1，有人机（通航）
     */
    private Integer deviceType;
}
