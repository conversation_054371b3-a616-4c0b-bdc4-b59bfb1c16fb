<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.FenceInfoMapper">
    <resultMap type="com.md.domain.po.FenceInfo" id="FenceInfoResult">
        <id property="id" column="id"/>
        <result property="fenceName" column="fence_name"/>
        <result property="fenceType" column="fence_type"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectFenceInfoVo">
        select id,
               fence_name,
               fence_type,
               description,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from fence_info
    </sql>

    <select id="selectFenceInfoList" parameterType="com.md.domain.po.FenceInfo" resultMap="FenceInfoResult">
        <include refid="selectFenceInfoVo"/>
        <where>
            <if test="fenceName != null and fenceName != ''">
                AND fence_name like concat('%', #{fenceName}, '%')
            </if>
            <if test="fenceType != null">
                AND fence_type = #{fenceType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND create_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND create_time &lt;= #{params.endTime}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectFenceInfoById" parameterType="String" resultMap="FenceInfoResult">
        <include refid="selectFenceInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectActiveFences" resultMap="FenceInfoResult">
        <include refid="selectFenceInfoVo"/>
        where status = 1
    </select>

    <insert id="insertFenceInfo" parameterType="com.md.domain.po.FenceInfo">
        insert into fence_info (
        id,
        fence_name,
        fence_type,
        description,
        status,
        create_by,
        create_time,
        update_by,
        update_time,
        remark
        ) values (
        #{id},
        #{fenceName},
        #{fenceType},
        #{description},
        #{status},
        #{createBy},
        #{createTime},
        #{updateBy},
        #{updateTime},
        #{remark}
        )
    </insert>

    <update id="updateFenceInfo" parameterType="com.md.domain.po.FenceInfo">
        update fence_info
        <set>
            <if test="fenceName != null and fenceName != ''">
                fence_name = #{fenceName},
            </if>
            <if test="fenceType != null">
                fence_type = #{fenceType},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteFenceInfoById" parameterType="String">
        delete from fence_info where id = #{id}
    </delete>

    <delete id="deleteFenceInfoByIds" parameterType="String">
        delete from fence_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>