package com.md.flight.px4.service.impl;

import com.md.flight.execution.context.TaskExecutionInfo;
import com.md.enums.TaskStatusEnum;
import com.md.flight.px4.manager.Px4TaskStatusManager;
import com.md.flight.px4.service.Px4TaskStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * PX4任务状态服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class Px4TaskStatusServiceImpl implements Px4TaskStatusService {

    private final Px4TaskStatusManager taskStatusManager;

    @Override
    public String getCurrentTaskId(String droneId) {
        return taskStatusManager.getCurrentTaskId(droneId);
    }

    @Override
    public TaskExecutionInfo getTaskInfo(String taskId) {
        return taskStatusManager.getTaskInfo(taskId);
    }

    @Override
    public boolean isTaskExecuting(String droneId) {
        return taskStatusManager.isTaskExecuting(droneId);
    }

    @Override
    public TaskStatusEnum getTaskStatus(String taskId) {
        return taskStatusManager.getTaskStatus(taskId);
    }

    @Override
    public int getActiveTaskCount() {
        return taskStatusManager.getActiveTaskCount();
    }

    @Override
    public void forceStopTask(String droneId) {
        log.warn("强制停止PX4任务: droneId={}", droneId);
        taskStatusManager.forceStopTask(droneId);
    }
}
