package com.md.flight.px4.service.impl;

import com.md.flight.execution.context.TaskExecutionInfo;
import com.md.enums.TaskStatusEnum;
import com.md.flight.px4.service.Px4TaskStatusService;
import com.md.utils.TaskStatusRedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * PX4任务状态服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class Px4TaskStatusServiceImpl implements Px4TaskStatusService {

    private final TaskStatusRedisUtils taskStatusRedisUtils;
    private static final String DRONE_TYPE = "PX4";

    /**
     * 开始任务执行
     */
    public void startTask(String droneId, String taskId, String taskName, String commandId) {
        log.info("开始PX4任务执行: droneId={}, taskId={}, taskName={}, commandId={}", droneId, taskId, taskName,
            commandId);

        // 创建任务执行信息
        TaskExecutionInfo taskInfo = new TaskExecutionInfo(taskId, droneId, taskName);
        taskInfo.setStatus(TaskStatusEnum.EXECUTING);

        // 写入Redis
        taskStatusRedisUtils.setTaskInfo(DRONE_TYPE, taskId, taskInfo);
        taskStatusRedisUtils.setDroneTask(DRONE_TYPE, droneId, taskId);
        taskStatusRedisUtils.setDroneExecution(DRONE_TYPE, droneId, true);

        log.info("PX4任务执行状态已记录: droneId={}, taskId={}", droneId, taskId);
    }

    /**
     * 更新任务状态（使用Redisson分布式锁确保线程安全）
     *
     * @return true表示更新成功，false表示获取锁失败
     */
    public boolean updateTaskStatus(String taskId, TaskStatusEnum status, Integer currentWaypoint,
        Integer totalWaypoints, Double progress) {
        // 使用分布式锁确保任务状态更新的原子性
        boolean updated = taskStatusRedisUtils.tryUpdateTaskStatus(DRONE_TYPE, taskId, () -> {
            TaskExecutionInfo redisTaskInfo = taskStatusRedisUtils.getTaskInfo(DRONE_TYPE, taskId);
            if (redisTaskInfo != null) {
                redisTaskInfo.setStatus(status);
                if (currentWaypoint != null) {
                    redisTaskInfo.setCurrentWaypoint(currentWaypoint);
                }
                if (totalWaypoints != null) {
                    redisTaskInfo.setTotalWaypoints(totalWaypoints);
                }
                if (progress != null) {
                    redisTaskInfo.setProgress(progress);
                }
                taskStatusRedisUtils.setTaskInfo(DRONE_TYPE, taskId, redisTaskInfo);
            }
        });

        if (updated) {
            log.info("PX4任务状态已更新: taskId={}, status={}, waypoint={}/{}, progress={}%", taskId, status.getInfo(),
                currentWaypoint, totalWaypoints, progress);
        } else {
            log.warn("更新PX4任务状态失败，可能由于锁竞争: taskId={}", taskId);
        }

        return updated;
    }

    /**
     * 完成任务
     */
    public void completeTask(String droneId, TaskStatusEnum finalStatus) {
        String taskId = getCurrentTaskId(droneId);
        if (taskId != null) {
            TaskExecutionInfo taskInfo = getTaskInfo(taskId);
            if (taskInfo != null) {
                taskInfo.setStatus(finalStatus);
                taskStatusRedisUtils.setTaskInfo(DRONE_TYPE, taskId, taskInfo);
                log.info("PX4任务已完成: droneId={}, taskId={}, finalStatus={}", droneId, taskId,
                    finalStatus.getInfo());
            }

            // 清理Redis数据
            taskStatusRedisUtils.cleanupTaskData(DRONE_TYPE, droneId);
        }
    }

    @Override
    public String getCurrentTaskId(String droneId) {
        return taskStatusRedisUtils.getDroneTask(DRONE_TYPE, droneId);
    }

    @Override
    public TaskExecutionInfo getTaskInfo(String taskId) {
        return taskStatusRedisUtils.getTaskInfo(DRONE_TYPE, taskId);
    }

    @Override
    public boolean isTaskExecuting(String droneId) {
        return taskStatusRedisUtils.isDroneExecuting(DRONE_TYPE, droneId);
    }

    @Override
    public TaskStatusEnum getTaskStatus(String taskId) {
        TaskExecutionInfo taskInfo = getTaskInfo(taskId);
        return taskInfo != null ? taskInfo.getStatus() : null;
    }

    /**
     * 清理已完成的任务信息
     */
    public void cleanupCompletedTask(String taskId) {
        taskStatusRedisUtils.removeTaskInfo(DRONE_TYPE, taskId);
        TaskExecutionInfo taskInfo = taskStatusRedisUtils.getTaskInfo(DRONE_TYPE, taskId);
        if (taskInfo != null) {
            log.info("已清理PX4任务信息: taskId={}, droneId={}", taskId, taskInfo.getDroneId());
        }
    }

    @Override
    public int getActiveTaskCount() {
        return taskStatusRedisUtils.getAllActiveTasks(DRONE_TYPE).size();
    }

    @Override
    public void forceStopTask(String droneId) {
        String taskId = getCurrentTaskId(droneId);
        if (taskId != null) {
            TaskExecutionInfo taskInfo = getTaskInfo(taskId);
            if (taskInfo != null) {
                taskInfo.setStatus(TaskStatusEnum.FAILED);
                taskStatusRedisUtils.setTaskInfo(DRONE_TYPE, taskId, taskInfo);
                log.warn("强制停止PX4任务: droneId={}, taskId={}", droneId, taskId);
            }

            // 清理Redis数据
            taskStatusRedisUtils.cleanupTaskData(DRONE_TYPE, droneId);
        }
    }
}
