package com.md.flight.yx.util;

import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;

import java.nio.charset.StandardCharsets;

/**
 * 亿讯AES工具类
 */
public class YxAESUtil {

    /**
     * 解密函数
     *
     * @param source 被解密的字符串（密文）
     * @param key    AES解密密钥
     * @return 解密后的字符串
     */
    public static String decrypt(String source, String key) {
        return decrypt(source, key, key.substring(0, 16));
    }

    /**
     * 加密函数
     *
     * @param source 被加密的字符串（明文）
     * @param key    AES加密密钥
     * @return 密文
     */
    public static String encrypt(String source, String key) {
        return encrypt(source, key, key.substring(0, 16));
    }

    /**
     * 加密函数
     *
     * @param source 被加密的字符串（明文）
     * @param key    AES加密密钥
     * @param iv     AES密钥偏移量
     * @return 密文
     */
    public static String encrypt(String source, String key, String iv) {
        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, key.getBytes(StandardCharsets.UTF_8), iv.getBytes(StandardCharsets.UTF_8));
        String encrypt = aes.encryptBase64(source, StandardCharsets.UTF_8);
        return encrypt;
    }

    /**
     * 解密函数
     *
     * @param source 被加密的字符串（明文）
     * @param key    AES加密密钥
     * @param iv     AES密钥偏移量
     * @return 明文
     */
    private static String decrypt(String source, String key, String iv) {
        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, key.getBytes(StandardCharsets.UTF_8), iv.getBytes(StandardCharsets.UTF_8));
        String decrypt = aes.decryptStr(source, StandardCharsets.UTF_8);
        return decrypt;
    }
}
