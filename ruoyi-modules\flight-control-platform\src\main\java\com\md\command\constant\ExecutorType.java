package com.md.command.constant;

import lombok.Getter;

/**
 * 执行器类型枚举
 */
@Getter
public enum ExecutorType {
    MQTT("MQTT协议", "基于MQTT协议的实时消息传输"),
    HTTP("HTTP协议", "基于HTTP协议的请求响应模式"),
    WEBSOCKET("WebSocket协议", "基于WebSocket的双向实时通信"),
    MAVLINK("MAVLink协议", "基于MAVLink协议的无人机控制");

    private final String description;
    private final String detail;

    ExecutorType(String description, String detail) {
        this.description = description;
        this.detail = detail;
    }

    @Override
    public String toString() {
        return this.description;
    }
}