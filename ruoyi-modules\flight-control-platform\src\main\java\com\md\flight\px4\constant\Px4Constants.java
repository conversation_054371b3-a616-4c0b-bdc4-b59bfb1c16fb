package com.md.flight.px4.constant;

/**
 * PX4相关常量定义
 */
public class Px4Constants {

    /**
     * PX4厂商标识
     */
    public static final String MANUFACTURER_PX4 = "PX4";

    /**
     * MQTT协议标识
     */
    public static final String PROTOCOL_MQTT = "MQTT";

    /**
     * MQTT主题前缀
     */
    public static final String TOPIC_PREFIX = "px4/";

    /**
     * 主题后缀定义
     */
    public static final class TopicSuffix {
        public static final String COMMAND = "/command";
        public static final String VIRTUAL_STICK_COMMAND = "/virtual_stick/command";
        public static final String MISSION_COMMAND = "/mission/command";
        public static final String MISSION_STATUS = "/mission/status";
    }

    /**
     * PX4 MQTT命令类型（用于MQTT消息中的type字段）
     */
    public static final class MqttCommandType {
        // 基本控制命令
        public static final String ARM = "ARM";
        public static final String DISARM = "DISARM";
        public static final String TAKEOFF = "TAKEOFF";
        public static final String LAND = "LAND";
        public static final String GO_HOME = "GO_HOME";

        // 虚拟摇杆命令
        public static final String VIRTUAL_STICK_START = "VIRTUAL_STICK_START";
        public static final String VIRTUAL_STICK_STOP = "VIRTUAL_STICK_STOP";
        public static final String VIRTUAL_STICK_CONTROL = "VIRTUAL_STICK_CONTROL";

        // 航线任务命令
        public static final String EXECUTE_MISSION = "EXECUTE_MISSION";
        public static final String PAUSE_MISSION = "PAUSE_MISSION";
        public static final String RESUME_MISSION = "RESUME_MISSION";
        public static final String STOP_MISSION = "STOP_MISSION";
    }

    /**
     * 坐标系统类型
     */
    public static final class CoordinateSystem {
        public static final String BODY = "BODY";
        public static final String NED = "NED";
        public static final String ENU = "ENU";
    }

    /**
     * PX4任务状态类型（用于MQTT消息中的status字段）
     * 与MAVSDK机载程序完全一致
     */
    public static final class MissionStatus {
        public static final String PENDING = "PENDING";           // 待执行 - 任务已创建但尚未开始执行
        public static final String EXECUTING = "EXECUTING";       // 执行中 - 任务正在执行
        public static final String PAUSED = "PAUSED";             // 已暂停 - 任务执行被暂停
        public static final String RESUMED = "RESUMED";           // 已恢复 - 任务从暂停状态恢复（通常很快转为EXECUTING）
        public static final String COMPLETED = "COMPLETED";       // 已完成 - 任务已成功执行完毕
        public static final String FAILED = "FAILED";             // 失败 - 任务执行失败
        public static final String CANCELLED = "CANCELLED";       // 已取消 - 任务被取消
        public static final String UNKNOWN = "UNKNOWN";           // 未知状态 - 通常是初始状态或状态无法确定时
    }

    /**
     * PX4飞行器动作类型（用于MQTT消息中的vehicle_action字段）
     * 与MAVSDK机载程序完全一致
     */
    public static final class VehicleAction {
        public static final int NONE = 0;                         // 无动作
        public static final int TAKEOFF = 1;                      // 起飞
        public static final int LAND = 2;                         // 降落
    }

    /**
     * 默认参数值
     */
    public static final class DefaultValues {
        public static final float DEFAULT_TAKEOFF_ALTITUDE = 10.0f;
        public static final float DEFAULT_FLIGHT_SPEED = 5.0f;
        public static final float MAX_STICK_VALUE = 100.0f;
        public static final String DEFAULT_COORDINATE = CoordinateSystem.BODY;
        public static final int DEFAULT_MISSION_TIMEOUT = 300; // 任务超时时间（秒）
        public static final int DEFAULT_COMMAND_TIMEOUT = 30;  // 命令超时时间（秒）
    }
}
