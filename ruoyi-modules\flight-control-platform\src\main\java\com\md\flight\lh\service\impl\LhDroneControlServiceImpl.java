package com.md.flight.lh.service.impl;

import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.md.flight.lh.constant.LhTopicConstantReq;
import com.md.flight.lh.domain.dto.LhBaseReq;
import com.md.flight.lh.domain.dto.HomePoint;
import com.md.flight.lh.domain.dto.LhRouteExecuteDto;
import com.md.flight.lh.domain.dto.LhRoutePauseDto;
import com.md.flight.lh.service.LhDroneControlService;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tenant.helper.TenantHelper;
import org.redisson.api.RCountDownLatch;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 联合飞机控制服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LhDroneControlServiceImpl implements LhDroneControlService {

    private final MqttMessageHandler messageHandler;
    private final ObjectMapper objectMapper;
    private final RedissonClient redissonClient;

    // 存储bid和RCountDownLatch名称的映射关系
    public final Map<String, String> bidLatchNameMap = new ConcurrentHashMap<>();

    // 线程阻塞时间（秒）
    private final Integer awaitTime = 30;

    @Override
    public boolean takeoff(String droneId, int mode, float altitude) {
        try {
            log.info("执行联合飞机起飞命令: droneId={}, mode={}, altitude={}", droneId, mode, altitude);

            // 构建请求数据
            Map<String, Object> data = new HashMap<>();
            data.put("mode", mode);
            data.put("altitude", altitude);

            // 发送命令并等待响应
            boolean success = sendCommandAndWait(droneId, "takeoff", data);

            log.info("联合飞机起飞命令已完成: droneId={}, 响应状态={}", droneId, success ? "成功" : "超时");
            return success;
        } catch (Exception e) {
            log.error("联合飞机起飞命令发送失败: droneId={}, error={}", droneId, e.getMessage(), e);
            throw new RuntimeException("联合飞机起飞命令发送失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean land(String droneId, int mode, String code) {
        try {
            log.info("执行联合飞机降落命令: droneId={}, mode={}, code={}", droneId, mode, code);

            // 构建请求数据
            Map<String, Object> data = new HashMap<>();
            data.put("mode", mode);
            if (code != null && !code.isEmpty()) {
                data.put("code", code);
            }

            // 发送命令并等待响应
            boolean success = sendCommandAndWait(droneId, "land", data);

            log.info("联合飞机降落命令已完成: droneId={}, 响应状态={}", droneId, success ? "成功" : "超时");
            return success;
        } catch (Exception e) {
            log.error("联合飞机降落命令发送失败: droneId={}, error={}", droneId, e.getMessage(), e);
            throw new RuntimeException("联合飞机降落命令发送失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean goHomeExtended(String droneId, int mode, float safeAltitude, float speed, boolean specificHome,
        HomePoint homePoint) {
        try {
            log.info("执行联合飞机返航命令: droneId={}, mode={}, safeAltitude={}, speed={}, specificHome={}", droneId,
                mode, safeAltitude, speed, specificHome);

            if (specificHome && homePoint != null) {
                log.info("指定返航点: latitude={}, longitude={}, altitude={}, heading={}", homePoint.getLatitude(),
                    homePoint.getLongitude(), homePoint.getAltitude(), homePoint.getHeading());
            }

            // 构建请求数据
            Map<String, Object> data = new HashMap<>();
            data.put("mode", mode);
            data.put("safe_altitude", safeAltitude);
            data.put("speed", speed);

            // 如果指定了返航点，则添加相关参数
            if (specificHome && homePoint != null) {
                data.put("specific_home", 1);

                // 创建home_point对象
                Map<String, Object> homePointMap = new HashMap<>();
                homePointMap.put("latitude", homePoint.getLatitude());
                homePointMap.put("longitude", homePoint.getLongitude());
                homePointMap.put("altitude", homePoint.getAltitude());
                homePointMap.put("heading", homePoint.getHeading());

                data.put("home_point", homePointMap);
            }

            // 发送命令并等待响应
            boolean success = sendCommandAndWait(droneId, "go_home", data);

            log.info("联合飞机返航命令已完成: droneId={}, 响应状态={}", droneId, success ? "成功" : "超时");
            return success;
        } catch (Exception e) {
            log.error("联合飞机返航命令发送失败: droneId={}, error={}", droneId, e.getMessage(), e);
            throw new RuntimeException("联合飞机返航命令发送失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean executePauseMission(String droneId) {
        try {
            log.info("执行联合飞机航线暂停: droneId={}", droneId);

            // 构建请求数据
            Map<String, Object> data = new HashMap<>();
            data.put("value", 1);

            // 发送命令并等待响应
            boolean success = sendCommandAndWait(droneId, "route_pause", data);

            log.info("联合飞机航线暂停命令已完成: droneId={}, 响应状态={}", droneId, success ? "成功" : "超时");
            return success;
        } catch (Exception e) {
            log.error("联合飞机航线暂停命令发送失败: droneId={}, error={}", droneId, e.getMessage(), e);
            throw new RuntimeException("联合飞机航线暂停命令发送失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean executeResumeMission(String droneId) {
        try {
            log.info("执行联合飞机航线继续: droneId={}", droneId);

            // 构建请求数据
            Map<String, Object> data = new HashMap<>();
            data.put("value", 1);

            // 发送命令并等待响应
            boolean success = sendCommandAndWait(droneId, "route_resume", data);

            log.info("联合飞机航线继续命令已完成: droneId={}, 响应状态={}", droneId, success ? "成功" : "超时");
            return success;
        } catch (Exception e) {
            log.error("联合飞机航线继续命令发送失败: droneId={}, error={}", droneId, e.getMessage(), e);
            throw new RuntimeException("联合飞机航线继续命令发送失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void startVirtualStick(String droneId) {
        try {
            log.info("执行联合飞机启用虚拟摇杆控制模式命令: droneId={}", droneId);

            // 构建请求数据
            Map<String, Object> data = new HashMap<>();

            // 发送MQTT消息（不等待响应）
            sendCommandWithoutWaiting(droneId, "virtual_stick_start", data);

            log.info("联合飞机启用虚拟摇杆控制模式命令已发送: droneId={}", droneId);
        } catch (Exception e) {
            log.error("联合飞机启用虚拟摇杆控制模式命令发送失败: droneId={}, error={}", droneId, e.getMessage(), e);
            throw new RuntimeException("联合飞机启用虚拟摇杆控制模式命令发送失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void stopVirtualStick(String droneId) {
        try {
            log.info("执行联合飞机停用虚拟摇杆控制模式命令: droneId={}", droneId);

            // 构建请求数据
            Map<String, Object> data = new HashMap<>();

            // 发送MQTT消息（不等待响应）
            sendCommandWithoutWaiting(droneId, "virtual_stick_stop", data);

            log.info("联合飞机停用虚拟摇杆控制模式命令已发送: droneId={}", droneId);
        } catch (Exception e) {
            log.error("联合飞机停用虚拟摇杆控制模式命令发送失败: droneId={}, error={}", droneId, e.getMessage(), e);
            throw new RuntimeException("联合飞机停用虚拟摇杆控制模式命令发送失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发送命令并等待响应
     *
     * @param droneId 无人机ID
     * @param method  方法名
     * @param data    数据
     * @return 是否成功收到响应
     */
    private boolean sendCommandAndWait(String droneId, String method, Map<String, Object> data) throws Exception {
        // 生成bid
        String bid = UUID.randomUUID().toString();

        // 创建Redis中的RCountDownLatch，使用TenantHelper.ignore忽略租户信息
        String latchName = getLatchNameForBid(bid);
        RCountDownLatch latch = TenantHelper.ignore(() -> redissonClient.getCountDownLatch(latchName));
        TenantHelper.ignore(() -> {
            latch.trySetCount(1);
            return null;
        });

        // 存储latch名称与bid的映射关系
        bidLatchNameMap.put(bid, latchName);

        try {
            // 发送MQTT消息
            sendCommand(droneId, method, data, bid);

            // 等待响应，使用TenantHelper.ignore忽略租户信息
            boolean success = TenantHelper.ignore(() -> {
                try {
                    return latch.await(awaitTime, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("等待响应时被中断: droneId={}, bid={}, error={}", droneId, bid, e.getMessage(), e);
                    return false;
                }
            });
            if (!success) {
                log.warn("联合飞机{}命令响应超时: droneId={}, bid={}", method, droneId, bid);
            }
            return success;
        } finally {
            // 移除latch
            bidLatchNameMap.remove(bid);
            // 删除Redis中的RCountDownLatch，使用TenantHelper.ignore忽略租户信息
            TenantHelper.ignore(() -> {
                latch.delete();
                return null;
            });
        }
    }

    /**
     * 发送命令到联合飞机（不等待响应）
     *
     * @param droneId 无人机ID
     * @param method  方法名
     * @param data    数据
     */
    private void sendCommandWithoutWaiting(String droneId, String method, Map<String, Object> data) throws Exception {
        // 生成bid
        String bid = UUID.randomUUID().toString();

        // 发送MQTT消息
        sendCommand(droneId, method, data, bid);
    }

    /**
     * 发送命令到联合飞机
     *
     * @param droneId 无人机ID
     * @param method  方法名
     * @param data    数据
     * @param bid     业务ID
     */
    private void sendCommand(String droneId, String method, Map<String, Object> data, String bid) throws Exception {
        // 构建请求对象
        LhBaseReq<Map<String, Object>> request = new LhBaseReq<>();
        request.setTid(UUID.randomUUID().toString());
        request.setBid(bid);
        request.setTimestamp(System.currentTimeMillis());
        request.setGateway(droneId);
        request.setMethod(method);
        request.setNeed_reply(1);
        request.setData(data);

        // 转换为JSON
        String payload = objectMapper.writeValueAsString(request);

        // 构建主题
        String topic = String.format(LhTopicConstantReq.SERVICES, droneId);

        // 发送MQTT消息
        messageHandler.sendToMqtt(topic, payload);
    }

    /**
     * 根据bid生成RCountDownLatch的名称
     *
     * @param bid 业务ID
     * @return RCountDownLatch名称
     */
    public String getLatchNameForBid(String bid) {
        return "lh:latch:" + bid;
    }

    /**
     * 根据bid通知对应的等待线程
     */
    public void notifyResponse(String bid) {
        String latchName = bidLatchNameMap.get(bid);
        if (latchName != null) {
            TenantHelper.ignore(() -> {
                RCountDownLatch latch = redissonClient.getCountDownLatch(latchName);
                latch.countDown();
                return null;
            });
            log.debug("已通知bid={}的等待线程继续执行", bid);
        } else {
            // 如果在当前实例中找不到对应的latchName，尝试直接通过Redis操作
            String latchName2 = getLatchNameForBid(bid);
            TenantHelper.ignore(() -> {
                RCountDownLatch latch = redissonClient.getCountDownLatch(latchName2);
                latch.countDown();
                return null;
            });
            log.debug("已通知Redis中bid={}的等待线程继续执行", bid);
        }
    }
}
