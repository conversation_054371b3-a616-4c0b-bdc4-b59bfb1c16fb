package com.md.mqtt.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.constant.MqttConstants;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.flight.px4.converter.Px4StatusConverter;
import com.md.mqtt.exception.MqttProcessException;
import com.md.mqtt.listener.DroneStatusListener;
import com.md.utils.MessageRateLimiterUtils;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * PX4无人机状态消息处理器
 * 处理来自PX4无人机的状态消息
 */
@Component
@Slf4j
public class Px4StatusMessageProcessor extends AbstractMqttMessageProcessor {

    @Value("${mqtt.redis.expire-seconds:20}")  // 默认20秒
    private int expireSeconds;

    @Value("${mqtt.state.min-update-interval:500}")  // 默认500毫秒
    private long minUpdateInterval;

    @Autowired(required = false)
    private List<DroneStatusListener> statusListeners;

    @Autowired
    private Px4StatusConverter px4StatusConverter;

    @Autowired
    private MessageRateLimiterUtils rateLimiterUtils;

    // 状态更新限流器（保留作为fallback，主要使用分布式限流）
    private final Map<String, Long> lastUpdateTimeMap = new ConcurrentHashMap<>();

    @Override
    protected String getTopicPattern() {
        return "px4/[^/]+/status";  // 匹配 px4/{drone_id}/status 格式的主题
    }

    /**
     * 检查是否可以处理消息（分布式限流 + 内存fallback）
     */
    private boolean canProcessMessage(String droneId) {
        try {
            // 优先使用分布式限流（支持集群）
            if (!rateLimiterUtils.canProcessMessage("Px4StatusMessageProcessor", droneId, minUpdateInterval)) {
                return false;
            }
        } catch (Exception e) {
            log.warn("分布式限流检查失败，使用内存fallback: droneId={}, error={}", droneId, e.getMessage());

            // Fallback到内存限流
            long currentTime = System.currentTimeMillis();
            Long lastTime = lastUpdateTimeMap.get(droneId);
            if (lastTime != null && currentTime - lastTime < minUpdateInterval) {
                log.debug("PX4状态更新过于频繁（内存限流），跳过处理，无人机ID：{}", droneId);
                return false;
            }
            lastUpdateTimeMap.put(droneId, currentTime);
        }

        return true;
    }

    @Override
    protected void doProcessMessage(String topic, String payload) {
        try {
            // 从主题中提取无人机ID
            String droneId = extractDroneIdFromTopic(topic);
            if (droneId == null) {
                log.error("无法从主题中提取无人机ID: {}", topic);
                return;
            }

            // 解析PX4原始状态数据
            JSONObject px4StatusData = JSON.parseObject(payload);

            // 检查是否需要采样处理（优先使用分布式限流，内存作为fallback）
            if (!canProcessMessage(droneId)) {
                return;
            }

            // 转换为统一的DroneStatus格式
            DroneStatus unifiedStatus = px4StatusConverter.convertToUnifiedFormat(px4StatusData);

            // 验证转换后的数据
            if (!px4StatusConverter.validateConvertedData(unifiedStatus)) {
                log.error("PX4状态数据转换验证失败，跳过处理，无人机ID：{}", droneId);
                return;
            }

            // 检查之前的在线状态
            boolean wasOnline =
                TenantHelper.ignore(() -> RedisUtils.hasKey(MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId));

            // 存储统一格式的状态数据到Redis
            TenantHelper.ignore(() -> {
                String unifiedStatusJson = JSON.toJSONString(unifiedStatus);
                RedisUtils.setCacheObject(MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId, unifiedStatusJson,
                    Duration.ofSeconds(expireSeconds));
                return null;
            });

            // 触发状态变化事件
            if (statusListeners != null && !statusListeners.isEmpty()) {
                if (!wasOnline) {
                    // 之前离线，现在上线
                    String unifiedStatusJson = JSON.toJSONString(unifiedStatus);
                    for (DroneStatusListener listener : statusListeners) {
                        try {
                            listener.onDroneOnline(droneId, unifiedStatusJson);
                        } catch (Exception e) {
                            log.error("处理无人机上线事件失败", e);
                        }
                    }
                }
            }

            log.debug("处理PX4无人机状态消息成功，无人机ID：{}", droneId);
        } catch (Exception e) {
            log.error("处理PX4无人机状态消息失败", e);
            throw new MqttProcessException("处理PX4无人机状态消息失败", e);
        }
    }

    /**
     * 从主题中提取无人机ID
     * 例如：从 "px4/drone_001/status" 中提取 "drone_001"
     */
    private String extractDroneIdFromTopic(String topic) {
        try {
            String[] parts = topic.split("/");
            if (parts.length >= 3) {
                return parts[1];
            }
        } catch (Exception e) {
            log.error("提取无人机ID失败: {}", topic, e);
        }
        return null;
    }
}