package com.md.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.md.domain.bo.FlightTaskBo;
import com.md.domain.po.FlightTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025年01月13日 10:42
 */
@Mapper
public interface FlightLineTaskMapper extends BaseMapper<FlightTask> {
    /**
     * 查询航线任务列表
     */
    List<FlightTask> selectFlightLineTaskList(FlightTaskBo bo);

    /**
     * 查询航线任务详细信息
     */
    FlightTask selectFlightLineTaskById(Long id);

    /**
     * 根据围栏ID查询任务ID列表
     *
     * @param fenceId 围栏ID
     * @return 任务ID列表
     */
    List<String> selectTaskIdsByFenceId(@Param("fenceId") String fenceId);

    /**
     * 根据任务ID查询关联的围栏ID
     *
     * @param taskId 任务ID
     * @return 围栏ID
     */
    String selectFenceIdByTaskId(@Param("taskId") String taskId);

    /**
     * 批量查询任务名称
     *
     * @param ids 任务ID集合
     * @return 任务名称集合
     */
    Set<String> selectTaskNamesByIds(@Param("ids") List<String> ids);
}