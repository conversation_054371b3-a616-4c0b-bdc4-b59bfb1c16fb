package com.md.flight.lh.domain.dto;

import lombok.Data;

import java.util.Map;

/**
 * 航线上传航点信息dto
 */
@Data
public class LhRouteUploadPlacemarkDto {
    //是否危险点 正常(0) 危险(1)
    private int is_risky;

    //航线序号 由0开始
    private long index;

    //航点经纬度
    private Map<String, String> point;

    //航点执行高度
    private float execute_height;

    //航点执行速度 当use_global_speed为0时有效 (不使用全局速度)
    private float waypoint_speed;

    //是否使用全局速度  不使用（0）使用（1) 默认为不使用全局速度
    private int use_global_speed;
}
