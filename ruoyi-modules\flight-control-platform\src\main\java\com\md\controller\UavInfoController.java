package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.domain.bo.UavInfoBo;
import com.md.domain.po.PlatformInfo;
import com.md.domain.vo.UavInfoVo;
import com.md.service.IUavInfoService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 无人机
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/md/uav")
public class UavInfoController extends BaseController {

    private final IUavInfoService uavInfoService;

    /**
     * 查询无人机列表
     */
    @SaCheckPermission("md:uav:query")
    @GetMapping("/list")
    public TableDataInfo<UavInfoVo> list(UavInfoBo bo, PageQuery pageQuery) {
        return uavInfoService.selectUavInfoPage(bo, pageQuery);
    }

    /**
     * 导出无人机列表
     */
    @SaCheckPermission("md:uav:export")
    @Log(title = "无人机", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(UavInfoBo bo, HttpServletResponse response) {
        List<UavInfoVo> list = uavInfoService.selectFkUavInfoList(bo);
        ExcelUtil.exportExcel(list, "无人机数据", UavInfoVo.class, response);
    }

    /**
     * 获取无人机详细信息
     */
    @SaCheckPermission("md:uav:query")
    @GetMapping("/{id}")
    public R<UavInfoVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(uavInfoService.selectFkUavInfoById(id));
    }

    /**
     * 新增无人机
     */
    @SaCheckPermission("md:uav:add")
    @Log(title = "无人机", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody UavInfoBo bo) {
        return toAjax(uavInfoService.insertFkUavInfo(bo));
    }

    /**
     * 修改无人机
     */
    @SaCheckPermission("md:uav:edit")
    @Log(title = "无人机", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody UavInfoBo bo) {
        return toAjax(uavInfoService.updateFkUavInfo(bo));
    }

    /**
     * 删除无人机
     */
    @SaCheckPermission("md:uav:remove")
    @Log(title = "无人机", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(uavInfoService.deleteFkUavInfoByIds(ids));
    }

    /**
     * 同步无人机信息
     */
    @SaCheckPermission("md:uav:sync")
    @Log(title = "无人机", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    public R<Boolean> sync(@RequestBody PlatformInfo platform) {
        String code = platform.getFlightControlNo();
        return R.ok(uavInfoService.syncUAV(code));
    }
}
