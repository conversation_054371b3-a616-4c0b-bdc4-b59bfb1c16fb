package com.md.domain.dto;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 飞行动态DTO
 */
@Data
@Builder
public class FlightReportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 飞行记录编号
     */
    private String orderID;

    /**
     * 飞行计划编号
     */
    private String reqNo;

    /**
     * 飞行状态
     */
    private String flightStatus;

    /**
     * 制造商代码
     */
    private String manufacturerID;

    /**
     * 制造商名称
     */
    private String manufacturer;

    /**
     * 运营商代码
     */
    private String operatorID;

    /**
     * 运营商名称
     */
    private String operator;

    /**
     * 运营商logo
     */
    private String[] logo;

    /**
     * 实名登记号
     */
    private String uasID;


    /**
     * 当前时间
     */
    private String timeStamp;

    /**
     * 无人驾驶航空器产品序列号
     */
    private String SN;

    /**
     * 产品型号
     */
    private String uasModel;

    /**
     * 坐标系类型
     */
    private String coordinate;

    /**
     * 位置经度
     */
    private Double longitude;

    /**
     * 位置纬度
     */
    private Double latitude;

    /**
     * 高度类型
     */
    private Integer heightAltitype;

    /**
     * 真高
     */
    private Double height;

    /**
     * 高度
     */
    private Double altitude;

    /**
     * 垂直飞行速度值
     */
    private Double VS;

    /**
     * 水平飞行速度值
     */
    private Double GS;


    /**
     * 航迹角
     */
    private Double course;
}
