package com.md.flight.lh.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.md.constant.MqttConstants;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseBatteryData;
import com.md.flight.mavlink.model.DroneStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 联合电池数据响应处理器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LhBatteryProcessor implements LhBaseProcessor {

    @Value("${mqtt.redis.expire-seconds:20}")  // 默认20秒，与其他无人机配置一致
    private int expireSeconds;

    @Override
    public void processMessage(String payload) {
        try {
            // 消息转换
            LhResponse<LhResponseBatteryData> lhResponse = JSON.parseObject(payload, new TypeReference<>() {
            });

            String uavCode = lhResponse.getGateway();
            DroneStatus droneStatus;

            // 使用与大疆相同的存储结构
            String redisKey = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + uavCode;

            // 电池SN专用缓存键，用于持久化保存电池SN
            String batterySNKey = "battery_sn:" + uavCode;

            // 获取Redis中的数据
            String statusData = TenantHelper.ignore(() -> RedisUtils.getCacheObject(redisKey));

            if (statusData == null) {
                droneStatus = new DroneStatus();
                // 如果主缓存为空，尝试从电池SN专用缓存恢复
                String cachedBatterySN = TenantHelper.ignore(() -> RedisUtils.getCacheObject(batterySNKey));
                if (cachedBatterySN != null && !cachedBatterySN.isEmpty()) {
                    droneStatus.setBatterySN(cachedBatterySN);
                    log.debug("从专用缓存恢复联合飞机电池SN: droneId={}, batterySN={}", uavCode, cachedBatterySN);
                }
            } else {
                droneStatus = JSON.parseObject(statusData, DroneStatus.class);
            }

            // 执行转换
            droneStatusSet(droneStatus, lhResponse.getData());

            // 如果电池SN有值，保存到专用缓存（长期保存）
            String currentBatterySN = droneStatus.getBatterySN();
            if (currentBatterySN != null && !currentBatterySN.isEmpty()) {
                TenantHelper.ignore(() -> {
                    // 电池SN使用更长的过期时间（24小时），避免频繁丢失
                    RedisUtils.setCacheObject(batterySNKey, currentBatterySN, Duration.ofHours(24));
                    return null;
                });
                log.debug("保存联合飞机电池SN到专用缓存: droneId={}, batterySN={}", uavCode, currentBatterySN);
            }

            // 设置无人机序列号（如果尚未设置）
            if (droneStatus.getDroneSN() == null || droneStatus.getDroneSN().isEmpty()) {
                droneStatus.setDroneSN(uavCode);
            }

            // 转换为JSON字符串
            String jsonStatus = JSON.toJSONString(droneStatus);

            // 存储到Redis，设置过期时间与其他无人机保持一致
            TenantHelper.ignore(
                () -> RedisUtils.setCacheObject(redisKey, jsonStatus, Duration.ofSeconds(expireSeconds)));

        } catch (Exception e) {
            log.error("处理联合电池数据响应失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 将 LhResponseBatteryData 数据转换并设置到 DroneStatus 中（带非空校验）
     */
    public static void droneStatusSet(DroneStatus droneStatus, LhResponseBatteryData batteryData) {
        if (batteryData == null || droneStatus == null) {
            return;
        }

        // 设置在线状态 - 收到电池数据说明设备在线
        droneStatus.setOnline(true);

        // 获取或新建 BatteryInfo 对象
        DroneStatus.BatteryInfo batteryInfo = droneStatus.getBatteryInfo();
        if (batteryInfo == null) {
            batteryInfo = new DroneStatus.BatteryInfo();
        }

        // 设置电池电压（Float -> float 转换，注意 null 安全）
        if (batteryData.getVoltage() != null) {
            batteryInfo.setBatteryVoltage(batteryData.getVoltage());
        }

        // 设置剩余电量百分比（根据原始数据，charge_remaining就是百分比）
        if (batteryData.getCharge_remaining() != null) {
            batteryInfo.setChargeRemainingInPercent(batteryData.getCharge_remaining().intValue());
            // 如果有总容量，也可以计算剩余容量(mAh)
            if (batteryData.getFull_charge_capacity() != null && batteryData.getFull_charge_capacity() > 0) {
                int remainingCapacity =
                    (int)(batteryData.getFull_charge_capacity() * batteryData.getCharge_remaining() / 100.0);
                batteryInfo.setChargeRemaining(remainingCapacity);
            }
        }

        // 设置电池寿命（如果有health字段，可以映射到batteryLife）
        if (batteryData.getHealth() != null) {
            batteryInfo.setBatteryLife(batteryData.getHealth());
        }

        // 设置电池总容量（如果有full_charge_capacity字段）
        if (batteryData.getFull_charge_capacity() != null && batteryData.getFull_charge_capacity() > 0) {
            batteryInfo.setTotalCapacity(batteryData.getFull_charge_capacity());
        }

        // 设置电池序列号（uid字段对应电池SN）
        if (batteryData.getUid() != null && !batteryData.getUid().isEmpty()) {
            droneStatus.setBatterySN(batteryData.getUid());
        }

        // 更新 DroneStatus 的电池信息引用
        droneStatus.setBatteryInfo(batteryInfo);

        // 可选：更新最后更新时间
        droneStatus.setLastUpdateTime(System.currentTimeMillis());

    }

}
