package com.md.flight.lh.processor;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.md.flight.engine.impl.LhMqttExecutionEngine;
import com.md.flight.lh.constant.LhTopicConstantReq;
import com.md.flight.lh.constant.LhTopicConstantRsp;
import com.md.flight.lh.domain.dto.*;
import com.md.flight.lh.enums.LhStatusEnum;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 联合航线上传消息处理
 */
@Component
@Slf4j
public class LhRouteUploadProcessor implements LhBaseProcessor{
    @Autowired
    private MqttMessageHandler messageHandler;

    private final String LH_FLIGHT_LINE_info="lh:flight_line_info:";


    @Override
    public void processMessage(String payload) {
        try {
            log.info("收到联合航线上传消息处理消息: {}", payload);

            // 消息转换
            LhResponse<LhResponseRouteUpload> lhResponse = JSON.parseObject(
                payload,
                new TypeReference<>() {});

            // 获取航线
            Map<String,String> params = RedisUtils.getCacheObject(LH_FLIGHT_LINE_info+lhResponse.getBid());
            if (ObjectUtil.isEmpty(params)){
                return;
            }
            Integer status = lhResponse.getData().getOutput().getStatus();
            if (status == 0){
                log.info("状态为 OK，执行上传航线任务");
                // 构建主题
                String topic = String.format(LhTopicConstantReq.SERVICES, lhResponse.getGateway());

                // 构建请求参数
                LhBaseReq<LhRouteExecuteDto> lhBaseReq = new LhBaseReq<>();
                LhRouteExecuteDto lhRouteExecuteDto = new LhRouteExecuteDto();

                //  设置执行模式
                lhRouteExecuteDto.setMode(2);
                lhRouteExecuteDto.setSpecific_dock(0);
                lhRouteExecuteDto.setRoute_id(params.get("lineId"));

                // 设置航线请求信息
                lhBaseReq.setData(lhRouteExecuteDto);
                lhBaseReq.setMethod("route_execute");
                lhBaseReq.setTimestamp(System.currentTimeMillis());
                lhBaseReq.setTid(IdUtil.fastSimpleUUID());
                lhBaseReq.setBid(params.get("routeExecuteBid"));
                lhBaseReq.setNeed_reply(1);
                lhBaseReq.setGateway(params.get("uavCode"));

                // 发送起飞命令
                String lhBaseReqString = JSON.toJSONString(lhBaseReq);
                messageHandler.sendToMqtt(topic, lhBaseReqString);

                //  删除缓存
                RedisUtils.deleteObject(LH_FLIGHT_LINE_info+lhResponse.getBid());
            }else{
                log.warn("状态非 OK，{}", lhResponse.getData().getOutput().getMessage());
            }
        } catch (Exception e) {
            log.error("收到联合航线上传消息处理失败: {}", e.getMessage(), e);
        }
    }
}
