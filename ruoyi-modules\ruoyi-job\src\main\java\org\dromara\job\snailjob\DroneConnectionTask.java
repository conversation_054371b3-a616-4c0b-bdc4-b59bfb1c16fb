package org.dromara.job.snailjob;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.md.domain.bo.UavInfoBo;
import com.md.domain.vo.UavInfoVo;
import com.md.flight.mavlink.service.MavlinkConnectionService;
import com.md.service.IUavInfoService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 无人机连接状态维护任务
 * 用于自动检查和维护无人机的连接状态
 */
@Slf4j
@Component
@JobExecutor(name = "droneConnectionTask")
public class DroneConnectionTask {

    private static final String MANUFACTURER_MD = "MD";

    /**
     * SnailJob执行方法
     *
     * @param jobArgs 任务参数
     * @return 执行结果
     */
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        try {
            // 执行无人机连接维护
            maintainDroneConnection();
            return ExecuteResult.success("无人机连接状态维护任务执行成功");
        } catch (Exception e) {
            log.error("无人机连接状态维护任务执行失败: {}", e.getMessage(), e);
            return ExecuteResult.failure("无人机连接状态维护任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 检查并维护无人机连接
     * 每分钟执行一次，查询飞控平台为MD的无人机，检查连接状态并自动连接未连接的无人机
     */
    public void maintainDroneConnection() {
        log.info("定时任务触发 - 维护无人机连接状态");
        try {
            // 获取服务实例
            IUavInfoService uavInfoService = SpringUtils.getBean(IUavInfoService.class);
            MavlinkConnectionService connectionService = SpringUtils.getBean(MavlinkConnectionService.class);

            // 查询飞控平台为MD的无人机
            UavInfoBo queryParam = new UavInfoBo();
            queryParam.setFlightControlNo(MANUFACTURER_MD);
            List<UavInfoVo> mdDrones = uavInfoService.selectFkUavInfoList(queryParam);

            log.info("查询到{}架飞控平台为MD的无人机", mdDrones.size());

            // 检查并维护每架无人机的连接状态
            for (UavInfoVo drone : mdDrones) {
                // 跳过未配置MAVLink连接参数的无人机
                if (drone.getMavlinkIp() == null || drone.getMavlinkPort() == null) {
                    log.warn("无人机[{}](ID:{})未配置MAVLink连接参数，跳过连接检查", drone.getUavName(),
                        drone.getUavCode());
                    continue;
                }

                // 检查连接状态
                try {
                    String droneId = drone.getUavCode();

                    // 直接调用服务接口检查连接状态
                    boolean isConnected = connectionService.isConnected(droneId);

                    // 如果未连接，则尝试建立连接
                    if (!isConnected) {
                        log.info("无人机[{}](ID:{})未连接，尝试建立连接", drone.getUavName(), droneId);

                        // 直接调用服务接口建立连接
                        connectionService.connect(droneId, drone.getMavlinkIp(), drone.getMavlinkPort());
                        log.info("无人机[{}](ID:{})连接成功", drone.getUavName(), droneId);
                    } else {
                        log.debug("无人机[{}](ID:{})已连接，无需操作", drone.getUavName(), droneId);
                    }
                } catch (Exception e) {
                    log.error("检查/连接无人机[{}](ID:{})时发生异常: {}", drone.getUavName(), drone.getUavCode(),
                        e.getMessage(), e);
                }
            }

            log.info("定时任务完成 - 无人机连接状态维护");
        } catch (Exception e) {
            log.error("定时任务异常 - 无人机连接状态维护失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}