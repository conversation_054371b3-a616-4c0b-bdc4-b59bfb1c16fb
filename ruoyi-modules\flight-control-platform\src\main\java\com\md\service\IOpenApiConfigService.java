package com.md.service;

import com.md.domain.bo.OpenApiConfigBo;
import com.md.domain.vo.OpenApiConfigVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * 开放端接口管理服务接口
 */
public interface IOpenApiConfigService {

    /**
     * 查询开放端接口管理列表
     *
     * @param bo 开放端接口管理查询对象
     * @return 开放端接口管理集合
     */
    List<OpenApiConfigVo> queryList(OpenApiConfigBo bo);

    /**
     * 查询开放端接口管理分页列表
     *
     * @param bo 开放端接口管理查询对象
     * @param pageQuery 分页查询参数
     * @return 开放端接口管理分页列表
     */
    TableDataInfo<OpenApiConfigVo> queryPageList(OpenApiConfigBo bo, PageQuery pageQuery);

    /**
     * 根据ID查询开放端接口管理详情
     *
     * @param id 开放端接口管理ID
     * @return 开放端接口管理详情
     */
    OpenApiConfigVo queryById(Long id);

    /**
     * 新增开放端接口管理
     *
     * @param bo 开放端接口管理业务对象
     * @return 结果
     */
    Boolean insertByBo(OpenApiConfigBo bo);

    /**
     * 修改开放端接口管理
     *
     * @param bo 开放端接口管理业务对象
     * @return 结果
     */
    Boolean updateByBo(OpenApiConfigBo bo);

    /**
     * 批量删除开放端接口管理
     *
     */
    Boolean deleteWithValidByIds(Long id);
}
