package com.md.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 围栏信息VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FenceVo extends BaseEntity {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 围栏类型(1:包含 2:排除 3:高度 4:复合)
     */
    private Integer fenceType;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态(0:禁用 1:启用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 区域列表
     */
    private List<FenceAreaVO> areas;

    /**
     * 关联任务ID列表
     */
    private List<String> taskIds;

    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
