package com.md.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.md.domain.po.FlightTaskPoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年01月14日 11:00
 */
@Mapper
public interface FlightTaskPointMapper extends BaseMapper<FlightTaskPoint> {
    /**
     * 批量插入航点
     *
     * @param points
     * @return
     */
    int batchInsertPoints(@Param("list") List<FlightTaskPoint> points);

    /**
     * 根据任务ID查询所有备降点
     *
     * @param taskId 任务ID
     * @return 备降点列表
     */
    List<FlightTaskPoint> selectEmergencyPointsByTaskId(@Param("taskId") String taskId);
}