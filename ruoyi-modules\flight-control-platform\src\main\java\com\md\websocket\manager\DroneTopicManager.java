package com.md.websocket.manager;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 无人机主题管理器
 * 统一管理所有厂商的WebSocket推送主题，支持动态主题匹配
 */
@Slf4j
@Component
public class DroneTopicManager {

    /**
     * 获取所有需要WebSocket推送的主题模式
     */
    public List<String> getWebSocketTopics() {
        return Arrays.asList(
            // 大疆主题
            "dji/status", "dji/response", "dji/virtual_stick/response",

            // MAVLink主题
            "mavlink/status",

            // PX4主题（动态格式）
            "px4/+/status",

            // 联合飞机主题（动态格式）
            "thing/device/+/osd");
    }

    /**
     * 检查主题是否需要WebSocket推送
     *
     * @param topic 要检查的主题
     * @return 是否需要推送到WebSocket
     */
    public boolean shouldPushToWebSocket(String topic) {
        return getWebSocketTopics().stream().anyMatch(pattern -> matchesTopic(pattern, topic));
    }

    /**
     * 匹配主题，支持通配符 +
     * 例如：px4/+/status 可以匹配 px4/drone001/status
     *
     * @param pattern 主题模式（可包含通配符+）
     * @param topic   实际主题
     * @return 是否匹配
     */
    public boolean matchesTopic(String pattern, String topic) {
        // 如果模式中没有通配符，直接比较
        if (!pattern.contains("+")) {
            return pattern.equals(topic);
        }

        // 将MQTT通配符转换为正则表达式
        // + 匹配单层级（不包含/的任意字符）
        String regex = pattern.replace("+", "[^/]+");

        try {
            return Pattern.matches(regex, topic);
        } catch (Exception e) {
            log.warn("主题匹配失败: pattern={}, topic={}, error={}", pattern, topic, e.getMessage());
            return false;
        }
    }

    /**
     * 根据主题获取厂商类型
     *
     * @param topic 主题
     * @return 厂商类型
     */
    public String getManufacturerByTopic(String topic) {
        if (topic.startsWith("dji/")) {
            return "DJI";
        } else if (topic.startsWith("mavlink/")) {
            return "MAVLINK";
        } else if (topic.startsWith("px4/")) {
            return "PX4";
        }
        return "UNKNOWN";
    }

}
