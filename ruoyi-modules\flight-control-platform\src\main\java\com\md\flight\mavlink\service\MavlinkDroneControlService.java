package com.md.flight.mavlink.service;

import io.dronefleet.mavlink.common.GlobalPositionInt;

import java.math.BigDecimal;
import java.util.Map;

/**
 * MAVLink无人机控制服务
 * 负责无人机的基础飞行控制
 */
public interface MavlinkDroneControlService {
    /**
     * 解锁无人机
     *
     * @param droneId 无人机ID
     * @param force   是否强制解锁
     */
    void arm(String droneId, boolean force);

    /**
     * 上锁无人机
     *
     * @param droneId 无人机ID
     */
    void disarm(String droneId);

    /**
     * 起飞
     *
     * @param droneId  无人机ID
     * @param altitude 目标高度(米)
     */
    void takeoff(String droneId, float altitude);

    /**
     * 降落
     *
     * @param droneId 无人机ID
     */
    void land(String droneId);

    /**
     * 返航
     *
     * @param droneId 无人机ID
     */
    void returnToHome(String droneId);

    /**
     * 设置飞行模式
     *
     * @param droneId 无人机ID
     * @param mode    模式值
     */
    void setMode(String droneId, int mode);

    /**
     * 指点飞行
     *
     * @param droneId   无人机ID
     * @param latitude  目标纬度
     * @param longitude 目标经度
     * @param altitude  目标高度
     * @param speed     飞行速度
     */
    void flyTo(String droneId, BigDecimal latitude, BigDecimal longitude, BigDecimal altitude, float speed);

    /**
     * 获取当前位置
     *
     * @param droneId 无人机ID
     * @return 当前位置
     */
    GlobalPositionInt getCurrentPosition(String droneId);

    /**
     * 获取格式化的位置信息
     *
     * @param droneId 无人机ID
     * @return 位置信息Map，包含经纬度、高度等，如果无位置信息则返回null
     */
    Map<String, Object> getFormattedPositionData(String droneId);

    /**
     * 根据机头方向控制无人机移动
     *
     * @param droneId   无人机ID
     * @param vForward  前后速度，单位：米/秒，正值向前(机头方向)，负值向后
     * @param vRight    左右速度，单位：米/秒，正值向右，负值向左
     * @param vUp       上下速度，单位：米/秒，正值向上，负值向下
     * @param duration  持续时间，单位：秒，0表示持续发送直到新命令
     */
    void moveRelativeToHeading(String droneId, float vForward, float vRight, float vUp, float duration);

    /**
     * 设置无人机偏航角
     *
     * @param droneId      无人机ID
     * @param yawAngle     目标偏航角，单位：度，范围：-180~180
     * @param angularSpeed 旋转速度，单位：度/秒
     * @param isRelative   是否为相对角度（相对于当前角度）
     */
    void setYaw(String droneId, float yawAngle, float angularSpeed, boolean isRelative);

    /**
     * 控制云台
     *
     * @param droneId     无人机ID
     * @param pitch       俯仰角（度），负值向下，正值向上
     * @param yaw         偏航角（度），从北顺时针方向
     * @param isAbsolute  是否为绝对角度模式
     */
    void controlGimbal(String droneId, float pitch, float yaw, boolean isAbsolute);
    
    /**
     * 拍摄一张照片
     *
     * @param droneId 无人机ID
     */
    void takePhoto(String droneId);

    /**
     * 开始连续拍照
     *
     * @param droneId  无人机ID
     * @param interval 拍摄间隔（秒）
     * @param count    拍摄张数，0表示无限拍摄直到收到停止命令
     */
    void startContinuousPhotos(String droneId, float interval, int count);

    /**
     * 停止连续拍照
     *
     * @param droneId 无人机ID
     */
    void stopContinuousPhotos(String droneId);

    /**
     * 开始录像
     *
     * @param droneId 无人机ID
     */
    void startVideoRecording(String droneId);

    /**
     * 停止录像
     *
     * @param droneId 无人机ID
     */
    void stopVideoRecording(String droneId);
}