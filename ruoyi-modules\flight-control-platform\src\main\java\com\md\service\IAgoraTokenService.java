package com.md.service;

import org.dromara.common.core.domain.R;

/**
 * Agora Token服务接口
 */
public interface IAgoraTokenService {

    /**
     * 获取直播Token和房间信息
     *
     * @param droneSN 无人机SN码
     * @param uid     用户ID
     * @return Token和房间信息
     */
    R<Object> getLiveToken(String droneSN, String uid);

    /**
     * 获取房间状态
     *
     * @param droneSN 无人机SN码
     * @return 房间状态信息
     */
    R<Object> getChannelStatus(String droneSN);

    /**
     * 关闭直播房间
     *
     * @param droneSN 无人机SN码
     * @return 操作结果
     */
    R<Object> closeChannel(String droneSN);
}