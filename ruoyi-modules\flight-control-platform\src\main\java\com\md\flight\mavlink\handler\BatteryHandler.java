package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.BatteryStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * 电池状态处理器
 */
@Slf4j
public class BatteryHandler extends AbstractMavlinkHandler<BatteryStatus> {

    public BatteryHandler(MavlinkCoreService coreService) {
        super(coreService);
    }
    
    @Override
    protected void doHandle(String droneId, BatteryStatus message) {
        // 保存最新的电池状态
        coreService.setLastBatteryStatus(droneId, message);

        log.info("无人机[{}]电池状态更新: 剩余电量={}%, 电压={}V, 电流={}A", droneId, message.batteryRemaining(),
            message.voltages().get(0) / 1000.0, message.currentBattery() / 100.0);
    }

    @Override
    public Class<BatteryStatus> getMessageType() {
        return BatteryStatus.class;
    }
}