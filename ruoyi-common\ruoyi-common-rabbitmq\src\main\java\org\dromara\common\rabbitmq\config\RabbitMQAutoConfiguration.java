package org.dromara.common.rabbitmq.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * RabbitMQ自动配置类
 */
@Configuration
@Import(RabbitMQConfig.class)
@ComponentScan("org.dromara.common.rabbitmq")
@ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true", matchIfMissing = true)
public class RabbitMQAutoConfiguration {
} 