package com.md.controller;

import com.md.domain.dto.FlightReportDTO;
import com.md.service.UavReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 飞行器信息上报控制器
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/uav/platform")
public class UavInfoReportController {

    private final UavReportService uavReportService;

    /**
     * 飞行动态上报（起飞时）
     * @param uavCode 机身码
     */
    @Operation(summary = "飞行动态上报")
    @PostMapping("/flight/dynamics")
    public R<Boolean> reportFlightDynamics(@RequestParam String uavCode) {
        boolean result = uavReportService.reportFlightInfo(uavCode,null,null);
        return R.ok(result);
    }
}
