package com.md.command.annotation;

import java.lang.annotation.*;

/**
 * 指令日志注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CommandLog {
    /**
     * 描述信息
     */
    String description() default "";

    /**
     * 是否保存请求参数
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存响应数据
     */
    boolean isSaveResponseData() default true;
} 