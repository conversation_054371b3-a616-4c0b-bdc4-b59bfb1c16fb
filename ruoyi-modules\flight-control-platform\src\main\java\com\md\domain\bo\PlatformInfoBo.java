package com.md.domain.bo;

import com.md.domain.po.PlatformInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 飞控平台业务对象
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PlatformInfo.class, reverseConvertGenerate = false)
public class PlatformInfoBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 飞控厂商名称
     */
    @NotBlank(message = "飞控厂商名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String manufacturerName;

    /**
     * 飞控编号
     */
    @NotBlank(message = "飞控编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String flightControlNo;

    /**
     * 飞控网络地址
     */
    @NotBlank(message = "飞控网络地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String networkAddress;

    /**
     * 账号
     */
//    @NotBlank(message = "账号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String account;

    /**
     * 密码
     */
//    @NotBlank(message = "密码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String password;

    /**
     * 备注
     */
    private String remark;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 秘钥
     */
    private String aesKey;
}