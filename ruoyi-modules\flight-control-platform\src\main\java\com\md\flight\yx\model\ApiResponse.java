package com.md.flight.yx.model;

import lombok.Data;

/**
 * API响应包装类 统一的API响应格式
 *
 * @param <T>
 *     响应数据类型
 */
@Data
public class ApiResponse<T> {
    /** 响应状态码 */
    private String status;
    /** 响应消息 */
    private String message;
    /** 响应数据 */
    private T result;

    /**
     * 创建成功响应
     */
    public static <T> ApiResponse<T> success() {
        return success(null);
    }

    /**
     * 创建成功响应（带数据）
     */
    public static <T> ApiResponse<T> success(T result) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setStatus("000000");
        response.setMessage("success");
        response.setResult(result);
        return response;
    }

    /**
     * 创建错误响应
     */
    public static <T> ApiResponse<T> error(String status, String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setStatus(status);
        response.setMessage(message);
        return response;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return "000000".equals(status);
    }
}