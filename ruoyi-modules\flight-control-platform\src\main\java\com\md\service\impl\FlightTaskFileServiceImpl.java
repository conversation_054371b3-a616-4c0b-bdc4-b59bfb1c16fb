package com.md.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.md.domain.bo.FlightTaskBo;
import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.domain.po.FlightTaskPointAction;
import com.md.domain.vo.FlightTaskVo;
import com.md.flight.djauv.domain.ActionTriggerReq;
import com.md.flight.djauv.domain.PointActionReq;
import com.md.flight.djauv.domain.RoutePointReq;
import com.md.flight.djauv.domain.UavRouteReq;
import com.md.flight.djauv.domain.WaypointHeadingReq;
import com.md.flight.djauv.domain.WaypointTurnReq;
import com.md.flight.djauv.kml.ActionTriggerTypeEnums;
import com.md.flight.djauv.service.UavRouteService;
import com.md.mapper.FlightLineTaskMapper;
import com.md.mapper.FlightTaskPointActionMapper;
import com.md.mapper.FlightTaskPointMapper;
import com.md.service.IFlightTaskFileService;
import com.md.utils.BeanConvertUtils;
import com.md.utils.MinioUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 飞行任务文件服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FlightTaskFileServiceImpl implements IFlightTaskFileService {

    private final MinioUtils minioUtils;
    private final UavRouteService uavRouteService;
    private final FlightLineTaskMapper flightLineTaskMapper;
    private final FlightTaskPointMapper flightTaskPointMapper;
    private final FlightTaskPointActionMapper flightTaskPointActionMapper;

    @Override
    @Transactional
    public boolean buildKmzByFlightLine(String id) {
        try {
            // 1. 获取航线和航点信息
            FlightTaskVo flightLine = selectFlightTaskById(id);
            if (flightLine == null) {
                throw new ServiceException("航线不存在");
            }
            List<FlightTaskPoint> points = flightLine.getPoints();

            // 2. 更新任务状态为生成中
            FlightTask updateTask = new FlightTask();
            updateTask.setId(id);
            updateTask.setFileGenerateStatus(1);
            updateTask.setFileGenerateTime(DateUtils.getNowDate());
            updateTask.setUpdateTime(DateUtils.getNowDate());
            updateTask.setUpdateBy(LoginHelper.getUserId());
            updateTask.setIsUploaded(0); // 重置上传状态为未上传
            flightLineTaskMapper.updateById(updateTask);

            try {
                // 3. 构建UavRouteReq对象
                UavRouteReq uavRouteReq = new UavRouteReq();

                // 设置航线基本属性
                uavRouteReq.setFinishAction(flightLine.getFinishedAction());
                uavRouteReq.setExitOnRcLostAction(flightLine.getMissingAction());

                // 设置飞行速度，为空则使用默认值
                BigDecimal flightSpeed = flightLine.getFlightSpeed();
                uavRouteReq.setAutoFlightSpeed(flightSpeed != null ? flightSpeed.doubleValue() : 2.0);

                // 设置全局高度，为空则使用默认值100
                BigDecimal flightHeight = flightLine.getFlightHeight();
                uavRouteReq.setGlobalHeight(flightHeight != null ? flightHeight.doubleValue() : 20.0);

                // 4. 构建航点列表
                List<RoutePointReq> routePoints = buildRoutePoints(points);
                uavRouteReq.setRoutePointList(routePoints);

                // 5. 设置默认值
                setDefaultValues(uavRouteReq);

                // 6. 生成kmz文件，使用任务名称命名
                String kmzFilePath = uavRouteService.buildKmz(uavRouteReq, flightLine.getTaskName());

                // 7. 上传文件到MinIO
                File kmzFile = new File(kmzFilePath);
                if (!kmzFile.exists()) {
                    throw new ServiceException("KMZ文件不存在");
                }

                try (FileInputStream inputStream = new FileInputStream(kmzFile)) {
                    // 直接使用原始文件名，不做任何修改,后续可能需要禁用非法字符
                    String fileName = kmzFile.getName();
                    String objectName = "wayline/" + fileName;

                    // 创建更新对象并设置共同属性
                    updateTask = new FlightTask();
                    updateTask.setId(id);
                    updateTask.setFileGenerateStatus(2);
                    updateTask.setFileGenerateTime(DateUtils.getNowDate());
                    updateTask.setUpdateTime(DateUtils.getNowDate());
                    updateTask.setUpdateBy(LoginHelper.getUserId());
                    updateTask.setKmzFileName(fileName);
                    updateTask.setFileSize(kmzFile.length());

                    // 检查是否为DJI厂家
                    if ("DJI".equals(flightLine.getFlightControlNo())) {
                        // 上传到MinIO
                        String fileUrl =
                            minioUtils.uploadFile(minioUtils.getDefaultBucketName(), objectName, inputStream,
                                "application/vnd.google-earth.kmz");

                        // 设置文件路径和上传状态
                        updateTask.setKmzFilePath(fileUrl);
                        updateTask.setIsUploaded(1); // 设置为已上传
                        log.info("航线文件生成并上传成功，文件路径：{}", fileUrl);
                    } else {
                        // 非DJI厂家，不进行文件上传
                        log.info("非DJI厂家，不进行文件上传，厂家编号：{}", flightLine.getFlightControlNo());
                        updateTask.setIsUploaded(0); // 设置为未上传
                    }

                    // 执行更新
                    flightLineTaskMapper.updateById(updateTask);
                    return true;

                } finally {
                    // 清理临时文件
                    try {
                        Files.deleteIfExists(Paths.get(kmzFilePath));
                    } catch (IOException e) {
                        log.warn("清理临时文件失败：{}", kmzFilePath, e);
                    }
                }

            } catch (Exception e) {
                // 9. 更新任务状态为生成失败
                updateTask = new FlightTask();
                updateTask.setId(id);
                updateTask.setFileGenerateStatus(3);
                updateTask.setFileGenerateError(e.getMessage());
                updateTask.setIsUploaded(0);  // 设置为未上传状态
                updateTask.setUpdateTime(DateUtils.getNowDate());
                updateTask.setUpdateBy(LoginHelper.getUserId());
                flightLineTaskMapper.updateById(updateTask);

                throw e;
            }

        } catch (Exception e) {
            log.error("生成或上传航线文件失败，航线ID：{}", id, e);
            throw new ServiceException("生成航线文件失败：" + e.getMessage());
        }
    }

    @Override
    public String exportKmz(String taskName, HttpServletResponse response) {
        try {
            // 1. 查询航线任务
            FlightTaskBo query = new FlightTaskBo();
            query.setTaskName(taskName);
            List<FlightTaskVo> taskList = selectFlightTaskList(query);
            if (CollectionUtils.isEmpty(taskList)) {
                throw new ServiceException("航线任务不存在");
            }
            FlightTaskVo task = taskList.get(0);

            // 2. 检查文件是否已生成
            if (task.getFileGenerateStatus() != 2) {
                throw new ServiceException("航线KMZ文件尚未生成");
            }

            // 3. 获取文件访问URL
            String storedUrl = task.getKmzFilePath();
            if (StringUtils.isNotEmpty(storedUrl) && !minioUtils.isUrlExpired(storedUrl)) {
                log.info("使用已存在的文件访问URL：{}", storedUrl);
                return storedUrl;
            }

            // 4. 如果URL不存在或已过期，则重新获取
            try {
                String objectName = "wayline/" + task.getKmzFileName();
                String newUrl = minioUtils.getFileUrl(minioUtils.getDefaultBucketName(), objectName);
                // 更新数据库中的URL和上传状态
                FlightTask updateTask = new FlightTask();
                updateTask.setId(task.getId());
                updateTask.setKmzFilePath(newUrl);
                updateTask.setUpdateTime(DateUtils.getNowDate());
                updateTask.setUpdateBy(LoginHelper.getUserId());
                updateTask.setIsUploaded(1); // 设置为已上传
                flightLineTaskMapper.updateById(updateTask);
                log.info("成功获取新的文件访问URL：{}", newUrl);
                return newUrl;
            } catch (Exception e) {
                log.error("获取文件访问URL失败，使用已存储的URL：{}", storedUrl, e);
                return storedUrl;
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("导出航线KMZ文件失败，任务名称：{}，异常：", taskName, e);
            throw new ServiceException("导出航线KMZ文件失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public String copyFlightTask(String taskId) {
        log.info("开始复制航线任务，原任务ID：{}", taskId);
        try {
            // 1. 查询原任务信息
            FlightTaskVo sourceTask = selectFlightTaskById(taskId);
            if (sourceTask == null) {
                throw new ServiceException("原任务不存在");
            }

            // 2. 设置新任务名称
            String newTaskName = generateCopyTaskName(sourceTask.getTaskName());
            sourceTask.setTaskName(newTaskName);

            // 3. 重置任务状态相关字段
            FlightTask newTask = new FlightTask();
            BeanUtils.copyProperties(sourceTask, newTask);
            newTask.setId(IdWorker.getIdStr());  // 生成新ID
            newTask.setTaskStatus(0);  // 设置为未执行状态
            newTask.setFileGenerateStatus(0);  // 设置为未生成状态
            newTask.setOperatorId(null);
            newTask.setFileGenerateTime(null);
            newTask.setFileGenerateError(null);
            newTask.setKmzFileName(null);
            newTask.setKmzFilePath(null);
            newTask.setFileSize(null);
            newTask.setIsUploaded(null);
            newTask.setCreateTime(DateUtils.getNowDate());
            newTask.setUpdateTime(null);  // 确保更新时间为空
            newTask.setCreateBy(LoginHelper.getUserId());
            newTask.setUpdateBy(LoginHelper.getUserId());

            // 4. 保存新任务基本信息
            flightLineTaskMapper.insert(newTask);
            String newTaskId = newTask.getId();

            // 5. 复制航点和动作信息
            List<FlightTaskPoint> points = sourceTask.getPoints();
            if (CollectionUtils.isNotEmpty(points)) {
                List<FlightTaskPoint> newPoints = new ArrayList<>();
                List<FlightTaskPointAction> allActions = new ArrayList<>();
                String currentTenantId = TenantHelper.getTenantId();
                if (StringUtils.isBlank(currentTenantId)) {
                    currentTenantId = LoginHelper.getTenantId();
                }
                log.info("复制航点租户ID: {}", currentTenantId);

                for (FlightTaskPoint sourcePoint : points) {
                    // 创建新航点对象并复制属性
                    FlightTaskPoint newPoint = new FlightTaskPoint();
                    BeanUtils.copyProperties(sourcePoint, newPoint);

                    // 生成新的航点ID
                    String newPointId = IdWorker.getIdStr();
                    newPoint.setId(newPointId);
                    newPoint.setLineId(newTask.getId());
                    newPoint.setCreateTime(DateUtils.getNowDate());
                    newPoint.setTenantId(currentTenantId);
                    newPoints.add(newPoint);

                    // 处理航点动作
                    if (CollectionUtils.isNotEmpty(sourcePoint.getActions())) {
                        for (FlightTaskPointAction sourceAction : sourcePoint.getActions()) {
                            // 创建新动作对象并复制属性
                            FlightTaskPointAction newAction = new FlightTaskPointAction();
                            BeanUtils.copyProperties(sourceAction, newAction);

                            // 设置新的关联关系
                            newAction.setId(null);  // ID会由数据库自动生成
                            newAction.setPointId(newPointId);
                            newAction.setCreateTime(DateUtils.getNowDate());
                            // 设置动作的租户ID
                            newAction.setTenantId(currentTenantId);

                            allActions.add(newAction);
                        }
                    }
                }

                // 批量插入航点
                if (!newPoints.isEmpty()) {
                    flightTaskPointMapper.batchInsertPoints(newPoints);
                }

                // 批量插入动作
                if (!allActions.isEmpty()) {
                    flightTaskPointActionMapper.batchInsertActions(allActions);
                }
            }

            // 6. 生成KMZ文件并上传到MinIO
            try {
                log.info("开始生成并上传KMZ文件，任务ID：{}", newTaskId);
                boolean success = buildKmzByFlightLine(newTaskId);
                if (!success) {
                    log.warn("KMZ文件生成失败，任务ID：{}", newTaskId);
                }
            } catch (Exception e) {
                log.error("生成KMZ文件失败，任务ID：{}，错误：{}", newTaskId, e.getMessage(), e);
                // 更新文件生成状态为失败
                FlightTask updateTask = new FlightTask();
                updateTask.setId(newTaskId);
                updateTask.setFileGenerateStatus(3); // 生成失败
                updateTask.setFileGenerateError(e.getMessage());
                updateTask.setUpdateTime(DateUtils.getNowDate());
                flightLineTaskMapper.updateById(updateTask);
            }

            log.info("航线任务复制成功，新任务ID：{}", newTaskId);
            return newTaskId;

        } catch (Exception e) {
            log.error("复制航线任务失败，原任务ID：{}，异常：", taskId, e);
            throw new ServiceException("复制任务失败：" + e.getMessage());
        }
    }

    /**
     * 生成复制任务的名称 规则：原名称_copy[n]，n从1开始，如果已存在则n+1
     */
    private String generateCopyTaskName(String originalName) {
        String baseName = originalName + "_copy";
        String newName = baseName;
        int suffix = 1;

        while (true) {
            // 检查当前名称是否已存在
            FlightTaskBo query = new FlightTaskBo();
            query.setTaskName(newName);
            List<FlightTaskVo> existingTasks = selectFlightTaskList(query);

            if (existingTasks.isEmpty()) {
                return newName;
            }

            // 如果存在，则增加后缀数字
            newName = baseName + suffix;
            suffix++;
        }
    }

    private List<RoutePointReq> buildRoutePoints(List<FlightTaskPoint> points) {
        List<RoutePointReq> routePoints = new ArrayList<>();
        if (!points.isEmpty()) {
            for (int i = 0, routeIndex = 0; i < points.size(); i++) {
                FlightTaskPoint point = points.get(i);
                // 跳过备降点
                if (point.getIsEmergencyLandingPoint() != null && point.getIsEmergencyLandingPoint() == 1) {
                    continue;
                }
                RoutePointReq routePoint = new RoutePointReq();

                routePoint.setRoutePointIndex(routeIndex++);

                // 直接使用原始坐标
                routePoint.setLongitude(point.getLongitude().doubleValue());
                routePoint.setLatitude(point.getLatitude().doubleValue());
                routePoint.setHeight(point.getAltitude().doubleValue());

                // 设置航点速度
                Integer speed = point.getSpeed();
                if (speed != null) {
                    routePoint.setSpeed(speed.doubleValue());
                }

                // 设置航点动作
                if (CollectionUtils.isNotEmpty(point.getActions())) {
                    List<PointActionReq> actions = point.getActions().stream().map(action -> {
                        PointActionReq actionReq = new PointActionReq();
                        actionReq.setActionIndex(action.getActionIndex());
                        actionReq.setHoverTime(action.getHoverTime());
                        actionReq.setAircraftHeading(action.getAircraftHeading());
                        actionReq.setUseGlobalImageFormat(action.getUseGlobalImageFormat());
                        actionReq.setImageFormat(action.getImageFormat());
                        actionReq.setGimbalYawRotateAngle(action.getGimbalYawRotateAngle());
                        actionReq.setGimbalPitchRotateAngle(action.getGimbalPitchRotateAngle());
                        actionReq.setZoom(action.getZoom());

                        // 添加日志查看录像状态
                        log.info("航点动作录像状态，原始值：{}，动作索引：{}", action.getRecordStatus(),
                            action.getActionIndex());

                        // 保持与前端一致的录像状态
                        if (action.getRecordStatus() != null) {
                            // 1表示开始录像，0表示结束录像
                            actionReq.setRecordStatus(action.getRecordStatus());
                            log.info("设置录像状态：{}", actionReq.getRecordStatus());
                        }

                        return actionReq;
                    }).collect(Collectors.toList());
                    routePoint.setActions(actions);

                    // 创建默认的触发器（到达航点触发）
                    ActionTriggerReq triggerReq = new ActionTriggerReq();
                    triggerReq.setActionTriggerType(ActionTriggerTypeEnums.REACH_POINT.getValue());

                    // 检查航点的动作是否包含 multipleTiming 或 multipleDistance
                    List<FlightTaskPointAction> pointActions = point.getActions();
                    if (CollectionUtils.isNotEmpty(pointActions)) {
                        Optional<FlightTaskPointAction> actionWithTrigger = pointActions.stream().filter(
                            action -> ObjectUtil.isNotNull(action.getMultipleTiming()) ||
                                ObjectUtil.isNotNull(action.getMultipleDistance())).findFirst();

                        // 如果存在特殊触发条件，则覆盖默认触发器
                        actionWithTrigger.ifPresent(action -> {
                            if (ObjectUtil.isNotNull(action.getMultipleTiming()) && action.getMultipleTiming() != 0) {
                                triggerReq.setActionTriggerType(ActionTriggerTypeEnums.MULTIPLE_TIMING.getValue());
                                triggerReq.setActionTriggerParam(String.valueOf(action.getMultipleTiming()));
                            } else if (ObjectUtil.isNotNull(action.getMultipleDistance()) &&
                                action.getMultipleDistance() != 0) {
                                triggerReq.setActionTriggerType(ActionTriggerTypeEnums.MULTIPLE_DISTANCE.getValue());
                                triggerReq.setActionTriggerParam(String.valueOf(action.getMultipleDistance()));
                            }
                        });
                    }

                    // 设置触发器
                    routePoint.setActionTrigger(triggerReq);
                }
                routePoints.add(routePoint);
            }
        }
        return routePoints;
    }

    private void setDefaultValues(UavRouteReq uavRouteReq) {
        // todo 后续可以通过设备编码到上云api表manage_device_payload、manage_device获取数据
        uavRouteReq.setDroneType(77);
        uavRouteReq.setSubDroneType(0);
        uavRouteReq.setPayloadType(66);
        uavRouteReq.setPayloadPosition(0);
        uavRouteReq.setImageFormat("ir,zoom");
        uavRouteReq.setExitOnRcLostAction("goBack");
        uavRouteReq.setWaypointHeadingReq(new WaypointHeadingReq().setWaypointHeadingMode("followWayline"));
        uavRouteReq.setWaypointTurnReq(
            new WaypointTurnReq().setWaypointTurnMode("toPointAndStopWithDiscontinuityCurvature"));
        uavRouteReq.setGimbalPitchMode("usePointSetting");
    }

    /**
     * 根据ID查询航线任务详情
     */
    private FlightTaskVo selectFlightTaskById(String id) {
        // 1. 查询基础数据
        FlightTask task = flightLineTaskMapper.selectById(id);
        if (task == null) {
            return null;
        }

        // 2. 转换并补充关联数据
        FlightTaskVo vo = new FlightTaskVo();
        BeanUtils.copyProperties(task, vo);
        // 手动处理taskStatus的类型转换
        if (task.getTaskStatus() != null) {
            vo.setTaskStatus(String.valueOf(task.getTaskStatus()));
        }

        // 3. 查询并设置航点数据
        List<FlightTaskPoint> points = flightTaskPointMapper.selectList(
            new LambdaQueryWrapper<FlightTaskPoint>().eq(FlightTaskPoint::getLineId, task.getId()));

        // 4. 查询并设置航点动作数据
        points.forEach(point -> point.setActions(flightTaskPointActionMapper.selectList(
            new LambdaQueryWrapper<FlightTaskPointAction>().eq(FlightTaskPointAction::getPointId, point.getId()))));

        vo.setPoints(points);
        return vo;
    }

    /**
     * 根据条件查询航线任务列表
     */
    private List<FlightTaskVo> selectFlightTaskList(FlightTaskBo bo) {
        // 构建查询条件
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();

        // 添加任务名称条件
        if (StringUtils.isNotEmpty(bo.getTaskName())) {
            queryWrapper.eq(FlightTask::getTaskName, bo.getTaskName());
        }

        // 执行查询
        List<FlightTask> taskList = flightLineTaskMapper.selectList(queryWrapper);

        // 转换为VO
        return BeanConvertUtils.convertList(taskList, FlightTaskVo.class, (task, vo) -> {
            // 手动处理taskStatus的类型转换
            if (task.getTaskStatus() != null) {
                vo.setTaskStatus(String.valueOf(task.getTaskStatus()));
            }
        });
    }
}