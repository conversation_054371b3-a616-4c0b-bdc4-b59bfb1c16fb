package com.md.flight.yz.factory;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.md.domain.dto.YzBaseInfoDto;
import com.md.domain.po.PlatformInfo;
import com.md.enums.PlatformInfoEnum;
import com.md.flight.yz.domain.YzRequestResult;
import com.md.flight.yz.domain.dto.YzBaseReqDto;
import com.md.flight.yz.openfeign.YzQueryBaseInfoClient;
import com.md.flight.yz.properties.YzRemoteCallProperties;
import com.md.flight.yz.utils.YzTokenUtils;
import com.md.service.IPlatformInfoService;
import com.md.utils.EncryptUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 亿航基本信息工厂
 */
@Component
public class YzBaseInfoFactory {
    @Autowired
    YzTokenUtils yzTokenUtils;
    @Autowired
    YzRemoteCallProperties yzRemoteCallProperties;
    @Autowired
    YzQueryBaseInfoClient queryBaseInfoClient;
    @Autowired
    IPlatformInfoService platformInfoService;

    /**
     * 获取基本信息列表
     */
    public <T> List<T> getBaseInfoList(YzBaseInfoDto yzBaseInfoDto, Class<T> clazz) {
        String token = yzTokenUtils.getToken();
        PlatformInfo platform = platformInfoService.selectPlatformByCode(PlatformInfoEnum.YZ.getCode());
        String encrypted = EncryptUtils.encrypt(JSON.toJSONString(yzBaseInfoDto), platform.getAesKey());
        YzBaseReqDto baseReqYhDto = new YzBaseReqDto(encrypted);
        baseReqYhDto.setParams(encrypted);
        String baseInfo = queryBaseInfoClient.queryBaseInfo(baseReqYhDto, token);
        baseInfo = EncryptUtils.decrypt(baseInfo, platform.getAesKey());
        YzRequestResult yzRequestResult = JSONObject.parseObject(baseInfo, YzRequestResult.class);
        return JSONArray.parseArray(yzRequestResult.getData(), clazz);
    }
}
