package com.md.enums;


import com.md.flight.djauv.service.impl.DjaBaseInfoServiceImpl;
import com.md.flight.lh.service.impl.LhBaseInfoServiceImpl;
import com.md.flight.mavlink.service.impl.MavlinkBaseInfoServiceImpl;
import com.md.flight.md.service.impl.MdBaseInfoServiceImpl;
import com.md.flight.px4.service.impl.Px4BaseInfoServiceImpl;
import com.md.flight.yx.service.impl.YxBaseInfoServiceImpl;
import com.md.flight.yz.service.impl.YzBaseInfoServiceImpl;
import com.md.service.IBaseInfoService;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.utils.SpringUtils;

/**
 * 基础信息同步枚举
 */
@Getter
public enum BaseInfoSyncEnum {
    //邮政
    YZ_SYNC("YZ", SpringUtils.getBean(YzBaseInfoServiceImpl.class),"亿航"),
    //亿讯
    YX_SYNC("YX", SpringUtils.getBean(YxBaseInfoServiceImpl.class),"亿讯"),
    //联合
    LH_SYNC("LH", SpringUtils.getBean(LhBaseInfoServiceImpl.class),"联合"),
    //大疆
    DJI_SYNC("DJI", SpringUtils.getBean(DjaBaseInfoServiceImpl.class),"大疆"),
    //迈得
    MD_SYNC("MD", SpringUtils.getBean(MdBaseInfoServiceImpl.class),"迈得"),
    //PX4
    PX4_SYNC("PX4", SpringUtils.getBean(Px4BaseInfoServiceImpl.class),"PX4"),
    //MAVLink
    MAVLINK_SYNC("MAVLINK", SpringUtils.getBean(MavlinkBaseInfoServiceImpl.class),"MAVLink"),
    ;

    /**
     * 飞控厂商
     */
    private String syncType;

    private String plateformName;

    /**
     * 飞控同步实现类
     */
    private IBaseInfoService baseInfoService;

    BaseInfoSyncEnum(String syncType, IBaseInfoService iBaseInfoService,String plateformName) {
        this.syncType = syncType;
        this.baseInfoService = iBaseInfoService;
        this.plateformName = plateformName;
    }

    /**
     * 通过飞控厂商，获取同步实现类
     *
     * @param syncType 飞控厂商编号
     * @return 同步实现类
     */
    public static IBaseInfoService getSyncType(String syncType) {
        if (StringUtils.isBlank(syncType)) {
            return null;
        }
        for (BaseInfoSyncEnum baseInfoSyncEnum : BaseInfoSyncEnum.values()) {
            if (baseInfoSyncEnum.syncType.equals(syncType)) {
                return baseInfoSyncEnum.getBaseInfoService();
            }
        }
        return null;
    }
}
