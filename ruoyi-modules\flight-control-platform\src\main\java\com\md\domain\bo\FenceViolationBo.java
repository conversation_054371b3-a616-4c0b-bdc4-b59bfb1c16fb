package com.md.domain.bo;

import com.md.domain.po.FenceViolationLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 围栏违规记录业务对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FenceViolationLog.class, reverseConvertGenerate = false)
public class FenceViolationBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotBlank(message = "主键ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 围栏ID
     */
    private String fenceId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 无人机编码
     */
    private String uavCode;

    /**
     * 违规位置经度
     */
    private BigDecimal longitude;

    /**
     * 违规位置纬度
     */
    private BigDecimal latitude;

    /**
     * 违规位置高度
     */
    private BigDecimal height;

    /**
     * 执行的动作
     */
    private Integer actionTaken;

    /**
     * 违规时间
     */
    private Date violationTime;

    /**
     * 开始时间（用于时间范围查询）
     */
    private Date beginTime;

    /**
     * 结束时间（用于时间范围查询）
     */
    private Date endTime;

    /**
     * 备注
     */
    private String remark;
}