package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.md.domain.po.UavInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 无人机视图对象
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = UavInfo.class)
public class UavInfoVo extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 无人机ID
     */
    @ExcelProperty(value = "无人机ID")
    private Integer id;

    /**
     * 机身码
     */
    @ExcelProperty(value = "机身码")
    private String uavCode;

    /**
     * 无人机类型编码
     */
    @ExcelProperty(value = "无人机类型编码")
    private String categoryCode;

    /**
     * 飞控平台名称
     */
    @ExcelProperty(value = "飞控平台名称")
    private String manufacturerName;

    /**
     * 飞控平台编码
     */
    @ExcelProperty(value = "飞控平台编码")
    private String flightControlNo;

    /**
     * 最大承重
     */
    @ExcelProperty(value = "最大承重")
    private Integer maxLoad;

    /**
     * 数据来源
     */
    @ExcelProperty(value = "数据来源")
    private String dataSource;

    /**
     * 飞行器名称
     */
    @ExcelProperty(value = "飞行器名称")
    private String uavName;

    /**
     * 设备型号
     */
    @ExcelProperty(value = "设备型号")
    private String deviceModel;

    /**
     * 电池SN
     */
    @ExcelProperty(value = "电池SN")
    private String batterySn;

    /**
     * 是否启用（0正常 1停用）
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 批次ID
     */
    @ExcelProperty(value = "批次ID")
    private String batchId;

    /**
     * 同步时间
     */
    @ExcelProperty(value = "同步时间")
    private String syncTime;

    /**
     * HTTP端点
     */
    @ExcelProperty(value = "HTTP端点")
    private String httpEndpoint;

    /**
     * MQTT端点
     */
    @ExcelProperty(value = "MQTT端点")
    private String mqttEndpoint;

    /**
     * Mavlink IP
     */
    @ExcelProperty(value = "Mavlink IP")
    private String mavlinkIp;

    /**
     * Mavlink端口
     */
    @ExcelProperty(value = "Mavlink端口")
    private Integer mavlinkPort;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建者名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    @ExcelProperty(value = "创建者名称")
    private String createByName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;
}