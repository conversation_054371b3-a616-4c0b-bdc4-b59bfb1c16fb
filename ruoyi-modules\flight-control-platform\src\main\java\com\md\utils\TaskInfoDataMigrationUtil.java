package com.md.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * 任务信息数据迁移工具
 * 用于清理Redis中旧格式的TaskExecutionInfo数据
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TaskInfoDataMigrationUtil {

    /**
     * 清理所有旧格式的任务信息数据
     * 这些数据会在任务重新执行时自动重新创建
     */
    public void cleanupOldTaskInfoData() {
        try {
            log.info("开始清理旧格式的任务信息数据...");
            
            // 获取所有task:info:*的键
            Collection<String> keys = TenantHelper.ignore(() -> RedisUtils.keys("task:info:*"));
            
            if (keys.isEmpty()) {
                log.info("没有找到需要清理的任务信息数据");
                return;
            }
            
            log.info("找到 {} 个任务信息键需要清理", keys.size());
            
            // 批量删除
            int deletedCount = 0;
            for (String key : keys) {
                try {
                    TenantHelper.ignore(() -> RedisUtils.deleteObject(key));
                    deletedCount++;
                    log.debug("已删除键: {}", key);
                } catch (Exception e) {
                    log.warn("删除键失败: {}, 错误: {}", key, e.getMessage());
                }
            }
            
            log.info("任务信息数据清理完成，共删除 {} 个键", deletedCount);
            log.info("系统将在任务重新执行时自动创建新格式的数据");
            
        } catch (Exception e) {
            log.error("清理任务信息数据失败", e);
            throw new RuntimeException("数据清理失败", e);
        }
    }
    
    /**
     * 清理指定无人机类型的任务信息数据
     */
    public void cleanupTaskInfoByDroneType(String droneType) {
        try {
            log.info("开始清理{}类型的任务信息数据...", droneType);
            
            String pattern = "task:info:" + droneType.toLowerCase() + ":*";
            Collection<String> keys = TenantHelper.ignore(() -> RedisUtils.keys(pattern));
            
            if (keys.isEmpty()) {
                log.info("没有找到{}类型的任务信息数据", droneType);
                return;
            }
            
            log.info("找到 {} 个{}类型的任务信息键需要清理", keys.size(), droneType);
            
            int deletedCount = 0;
            for (String key : keys) {
                try {
                    TenantHelper.ignore(() -> RedisUtils.deleteObject(key));
                    deletedCount++;
                    log.debug("已删除{}类型键: {}", droneType, key);
                } catch (Exception e) {
                    log.warn("删除{}类型键失败: {}, 错误: {}", droneType, key, e.getMessage());
                }
            }
            
            log.info("{}类型任务信息数据清理完成，共删除 {} 个键", droneType, deletedCount);
            
        } catch (Exception e) {
            log.error("清理{}类型任务信息数据失败", droneType, e);
            throw new RuntimeException("数据清理失败", e);
        }
    }
}
