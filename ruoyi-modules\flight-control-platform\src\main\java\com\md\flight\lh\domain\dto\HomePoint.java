package com.md.flight.lh.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 返航点坐标信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HomePoint {

    /**
     * 纬度值，7位小数
     */
    private float latitude;

    /**
     * 经度值，7位小数
     */
    private float longitude;

    /**
     * 绝对高度值 EGM96
     */
    private float altitude;

    /**
     * 朝向角
     */
    private float heading;
}