package com.md.websocket.handler;

import com.alibaba.fastjson2.JSON;
import com.md.websocket.message.DroneMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.Collections;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 无人机WebSocket处理器
 */
@Slf4j
@Component
public class DroneWebSocketHandler implements WebSocketHandler {

    /**
     * 存储无人机ID与对应的WebSocket会话集合 key: 无人机ID value: 该无人机的所有WebSocket会话集合
     */
    private static final ConcurrentHashMap<String, Set<WebSocketSession>> DRONE_SESSIONS = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String droneId = getDroneId(session);
        if (droneId != null) {
            DRONE_SESSIONS.computeIfAbsent(droneId, k -> ConcurrentHashMap.newKeySet()).add(session);
            log.info("WebSocket连接建立: droneId={}, sessionId={}", droneId, session.getId());
        }
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) {
        // 目前不处理客户端发来的消息
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        String droneId = getDroneId(session);
        log.error("WebSocket传输错误: droneId={}, sessionId={}, error={}", droneId, session.getId(),
            exception.getMessage(), exception);
        closeSession(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        String droneId = getDroneId(session);
        if (droneId != null) {
            DRONE_SESSIONS.getOrDefault(droneId, Collections.emptySet()).remove(session);
            log.info("WebSocket连接关闭: droneId={}, sessionId={}, status={}", droneId, session.getId(), closeStatus);
        }
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 广播消息给指定无人机的所有订阅者
     */
    public void broadcastMessage(DroneMessage message) {
        String droneId = message.getDroneId();
        if (droneId != null) {
            // 发送给特定无人机的订阅者
            sendToDrone(droneId, message);
        } else {
            // 广播给所有订阅者
            broadcastToAll(message);
        }
    }

    /**
     * 发送消息给指定无人机的所有订阅者
     */
    private void sendToDrone(String droneId, DroneMessage message) {
        Set<WebSocketSession> sessions = DRONE_SESSIONS.getOrDefault(droneId, Collections.emptySet());
        for (WebSocketSession session : sessions) {
            sendMessage(session, message);
        }
    }

    /**
     * 广播消息给所有订阅者
     */
    private void broadcastToAll(DroneMessage message) {
        DRONE_SESSIONS.values().forEach(sessions -> sessions.forEach(session -> sendMessage(session, message)));
    }

    /**
     * 发送消息给指定会话
     */
    private void sendMessage(WebSocketSession session, DroneMessage message) {
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(JSON.toJSONString(message)));
            }
        } catch (IOException e) {
            log.error("发送消息失败: sessionId={}, error={}", session.getId(), e.getMessage(), e);
            closeSession(session);
        }
    }

    /**
     * 关闭WebSocket会话
     */
    private void closeSession(WebSocketSession session) {
        try {
            session.close();
        } catch (IOException e) {
            log.error("关闭WebSocket会话失败: sessionId={}, error={}", session.getId(), e.getMessage(), e);
        }
    }

    /**
     * 从会话中获取无人机ID
     */
    private String getDroneId(WebSocketSession session) {
        return (String)session.getAttributes().get("droneId");
    }

    /**
     * 获取指定无人机的在线会话数
     */
    public int getOnlineCount(String droneId) {
        return DRONE_SESSIONS.getOrDefault(droneId, Collections.emptySet()).size();
    }

    /**
     * 获取总在线会话数
     */
    public int getTotalOnlineCount() {
        return DRONE_SESSIONS.values().stream().mapToInt(Set::size).sum();
    }
}