package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.domain.bo.PlatformInfoBo;
import com.md.domain.vo.PlatformInfoVo;
import com.md.service.IPlatformInfoService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 调度系统
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/md/flightControl")
public class PlatformInfoController extends BaseController {

    private final IPlatformInfoService platformInfoService;

    /**
     * 查询飞控平台列表
     */
    @SaCheckPermission("md:flightControl:query")
    @GetMapping("/list")
    public TableDataInfo<PlatformInfoVo> list(PlatformInfoBo bo, PageQuery pageQuery) {
        return platformInfoService.selectPlatformInfoPage(bo, pageQuery);
    }

    /**
     * 导出飞控平台列表
     */
    @SaCheckPermission("md:flightControl:export")
    @Log(title = "飞控平台", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PlatformInfoBo bo, HttpServletResponse response) {
        List<PlatformInfoVo> list = platformInfoService.selectFkPlatformInfoList(bo);
        ExcelUtil.exportExcel(list, "飞控平台数据", PlatformInfoVo.class, response);
    }

    /**
     * 获取飞控平台详细信息
     */
    @SaCheckPermission("md:flightControl:query")
    @GetMapping("/{id}")
    public R<PlatformInfoVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(platformInfoService.selectFkPlatformInfoById(id));
    }

    /**
     * 新增飞控平台
     */
    @SaCheckPermission("md:flightControl:add")
    @Log(title = "飞控平台", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PlatformInfoBo bo) {
        return toAjax(platformInfoService.insertFkPlatformInfo(bo));
    }

    /**
     * 修改飞控平台
     */
    @SaCheckPermission("md:flightControl:edit")
    @Log(title = "飞控平台", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PlatformInfoBo bo) {
        return toAjax(platformInfoService.updateFkPlatformInfo(bo));
    }

    /**
     * 删除飞控平台
     */
    @SaCheckPermission("md:flightControl:remove")
    @Log(title = "飞控平台", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(platformInfoService.deleteFkPlatformInfoByIds(ids));
    }
}
