package com.md.flight.mavlink.model;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 航线任务实体类
 * 包含航线任务的基本信息和所有航点数据
 */
@Data
public class Mission {
    /**
     * 航线名称
     * 用于标识不同的航线任务
     */
    private String name;

    /**
     * 航线描述
     * 对航线任务的详细说明
     */
    private String description;

    /**
     * 航点列表
     * 包含所有航点信息的有序列表，航点按顺序执行
     */
    private List<MissionWaypoint> items = new ArrayList<>();

    /**
     * 当前执行航点索引
     * 表示当前正在执行的航点在列表中的位置
     * -1表示尚未开始执行任何航点
     */
    private int currentItemIndex = -1;

    /**
     * 航线状态
     * 表示当前航线的执行状态
     *
     * @see MissionStatus
     */
    private MissionStatus status = MissionStatus.UNKNOWN;

    /**
     * 航线飞行速度
     * 单位：米/秒
     * 0或负值表示使用飞控默认速度
     */
    private float speed = 0;

    /**
     * 航线任务状态枚举
     * 描述航线任务在不同阶段的状态
     */
    public enum MissionStatus {
        /** 未知状态 - 通常是初始状态或状态无法确定时 */
        UNKNOWN,

        /** 空闲状态 - 已创建但尚未上传或执行 */
        IDLE,

        /** 上传中 - 正在向飞控上传航线 */
        UPLOADING,

        /** 已上传 - 航线已成功上传到飞控 */
        UPLOADED,

        /** 下载中 - 正在从飞控下载航线 */
        DOWNLOADING,

        /** 执行中 - 航线正在执行 */
        EXECUTING,

        /** 已暂停 - 航线执行被暂停 */
        PAUSED,

        /** 已完成 - 航线已成功执行完毕 */
        COMPLETED,

        /** 失败 - 航线执行失败 */
        FAILED
    }
}