package com.md.domain.bo;

import com.md.mongodb.document.FlightTrackPoint;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 飞行轨迹点业务对象
 */
@Data
@AutoMapper(target = FlightTrackPoint.class, reverseConvertGenerate = false)
public class FlightTrackPointBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;
    
    /**
     * 无人机ID
     */
    @NotBlank(message = "无人机ID不能为空")
    private String droneId;
    
    /**
     * 时间戳，记录数据采集时间
     */
    @NotNull(message = "时间戳不能为空")
    private Long timestamp;
    
    /**
     * 纬度，单位：度
     */
    private Double latitude;
    
    /**
     * 经度，单位：度
     */
    private Double longitude;
    
    /**
     * 海拔高度，单位：米
     */
    private Double altitude;
    
    /**
     * 相对高度（相对起飞点），单位：米
     */
    private Double relativeHeight;
    
    /**
     * 速度，单位：米/秒
     */
    private Double speed;
    
    /**
     * 空速，单位：米/秒
     */
    private Double airSpeed;
    
    /**
     * 地速，单位：米/秒
     */
    private Double groundSpeed;
    
    /**
     * 飞行距离，单位：米
     */
    private Double flightDistance;
    
    /**
     * 航向角，单位：度，范围0-360
     */
    private Double heading;
    
    /**
     * 俯仰角，单位：度
     */
    private Double pitch;
    
    /**
     * 横滚角，单位：度
     */
    private Double roll;
    
    /**
     * 飞行模式，如：定点模式、航线模式等
     */
    private String flightMode;
    
    /**
     * 电池信息
     */
    private BatteryInfo batteryInfo;
    
    /**
     * 任务状态，如：执行中、已完成等
     */
    private String missionStatus;
    
    /**
     * RTK是否连接
     */
    private Boolean isRTKConnected;
    
    /**
     * RTK是否启用
     */
    private Boolean isRTKEnabled;
    
    /**
     * RTK是否健康
     */
    private Boolean isRTKHealthy;
    
    /**
     * RTK高度
     */
    private Double rtkAltitude;
    
    /**
     * RTK卫星数量
     */
    private Integer rtkSatelliteCount;
    
    /**
     * 卫星信息
     */
    private SatelliteInfo satelliteInfo;

    /**
     * 电池信息内部类 记录无人机电池的详细状态
     */
    @Data
    public static class BatteryInfo {
        private Integer chargeRemaining;           // 剩余电量，单位：毫安时
        private Integer chargeRemainingInPercent;  // 剩余电量百分比，范围：0-100
        private Integer totalCapacity;             // 电池总容量，单位：毫安时
    }
    
    /**
     * 卫星信息内部类 记录无人机卫星接收状态
     */
    @Data
    public static class SatelliteInfo {
        private Integer baseStationCount;     // 基站卫星数量
        private Integer gpsCount;             // GPS卫星数量 
        private Integer mobileStation1Count;  // 移动站1卫星数量
        private Integer mobileStation2Count;  // 移动站2卫星数量
    }
} 