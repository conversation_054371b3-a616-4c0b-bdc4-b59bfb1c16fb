package org.dromara.job.snailjob;

import com.aizuda.snailjob.client.job.core.annotation.JobExecutor;
import com.aizuda.snailjob.client.job.core.dto.JobArgs;
import com.aizuda.snailjob.client.model.ExecuteResult;
import com.aizuda.snailjob.common.core.util.JsonUtil;
import com.md.service.IFenceDetectionService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.springframework.stereotype.Component;

/**
 * 电子围栏缓存管理任务
 * 用于自动加载和刷新所有活跃围栏到缓存
 */
@Slf4j
@Component
@JobExecutor(name = "fenceCacheTask")
public class FenceCacheTask {

    /**
     * SnailJob执行方法
     *
     * @param jobArgs 任务参数
     * @return 执行结果
     */
    public ExecuteResult jobExecute(JobArgs jobArgs) {
        try {
            // 记录参数信息
            log.info("电子围栏缓存任务接收到参数: {}", JsonUtil.toJsonString(jobArgs));
            
            // 默认执行加载任务
            loadAllActiveFences();
            return ExecuteResult.success("电子围栏缓存加载任务执行成功");
        } catch (Exception e) {
            log.error("电子围栏缓存任务执行失败: {}", e.getMessage(), e);
            return ExecuteResult.failure("电子围栏缓存任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 加载全部活跃电子围栏到缓存
     */
    public void loadAllActiveFences() {
        log.info("定时任务触发 - 加载全部活跃电子围栏到缓存");
        try {
            IFenceDetectionService fenceDetectionService = SpringUtils.getBean(IFenceDetectionService.class);
            int count = fenceDetectionService.loadAllActiveFencesToCache();
            log.info("定时任务完成 - 成功加载{}个活跃电子围栏到缓存", count);
        } catch (Exception e) {
            log.error("定时任务异常 - 加载电子围栏缓存失败", e);
            throw e; // 重新抛出异常，让上层方法处理
        }
    }

    /**
     * 清理长时间未使用的围栏缓存
     */
    public void cleanUnusedFenceCache() {
        log.info("定时任务触发 - 清理电子围栏缓存");
        try {
            IFenceDetectionService fenceDetectionService = SpringUtils.getBean(IFenceDetectionService.class);
            fenceDetectionService.cleanUnusedFenceCache();
            log.info("定时任务完成 - 围栏缓存清理");
        } catch (Exception e) {
            log.error("定时任务异常 - 清理围栏缓存失败", e);
            throw e; // 重新抛出异常，让上层方法处理
        }
    }
}