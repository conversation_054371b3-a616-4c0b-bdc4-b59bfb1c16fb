package com.md.domain.dto;

import com.md.domain.po.FlightLine;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年12月31日 11:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlightLineDto extends FlightLine {
    /**
     * 飞控厂家名称
     */
    private String manufacturerName;

    /**
     * 创建开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 创建结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 修改开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startUpdateTime;

    /**
     * 修改结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endUpdateTime;
}