package com.md.flight.lh.domain.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 航线任务请求
 */
@Data
public class LhRouteExecuteDto {
    //航线模式 (mode=1时表示执行最新上传航线mode=2时执行route_id值id的航线)
    private int mode;

    //航线id
    private String route_id;

    //是否设定返航机库
    private int specific_dock;

    //机库信息
    private List dock_info=new ArrayList();
}
