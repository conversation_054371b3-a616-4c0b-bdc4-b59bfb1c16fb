package com.md.enums;

import lombok.Getter;

/**
 * 围栏类型枚举
 */
@Getter
public enum FenceTypeEnum {
    /**
     * 包含型围栏
     */
    INCLUSIVE(1, "包含型围栏"),

    /**
     * 排除型围栏
     */
    EXCLUSIVE(2, "排除型围栏"),

    /**
     * 高度限制围栏
     */
    HEIGHT(3, "高度限制围栏"),

    /**
     * 复合型围栏
     */
    COMPOUND(4, "复合型围栏");

    private final int code;
    private final String info;

    FenceTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static FenceTypeEnum getByCode(int code) {
        for (FenceTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}