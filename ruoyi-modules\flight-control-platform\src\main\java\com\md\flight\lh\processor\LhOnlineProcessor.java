package com.md.flight.lh.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.constant.MqttConstants;
import com.md.domain.po.UavInfo;
import com.md.domain.vo.UavInfoVo;
import com.md.flight.lh.constant.LhTopicConstantReq;
import com.md.flight.lh.domain.dto.LhBaseReq;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseOnlineData;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import com.md.mapper.UavInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 联合设备上线消息处理
 */
@Component
@Slf4j
public class LhOnlineProcessor implements LhBaseProcessor{

    @Value("${mqtt.redis.expire-seconds:20}")  // 默认20秒，与其他无人机配置一致
    private int expireSeconds;

    @Autowired
    private MqttMessageHandler messageHandler;

    @Override
    public void processMessage(String payload) {
        try {
            log.info("收到联合设备上线消息: {}", payload);
            // 消息转换
            LhBaseReq lhBaseReq = JSONObject.parseObject(payload, LhBaseReq.class);

            String uavCode = lhBaseReq.getGateway();
            DroneStatus droneStatus;

            // 使用与大疆相同的存储结构
            String redisKey = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + uavCode;

            // 获取Redis中的数据
            String statusData = TenantHelper.ignore(() -> RedisUtils.getCacheObject(redisKey));

            if (statusData == null) {
                droneStatus = new DroneStatus();
            } else {
                droneStatus = JSON.parseObject(statusData, DroneStatus.class);
            }

            // 设置上线
            droneStatus.setOnline(true);

            // 设置无人机序列号（如果尚未设置）
            if (droneStatus.getDroneSN() == null || droneStatus.getDroneSN().isEmpty()) {
                droneStatus.setDroneSN(uavCode);
            }

            // 转换为JSON字符串
            String jsonStatus = JSON.toJSONString(droneStatus);

            // 存储到Redis，设置过期时间与其他无人机保持一致
            TenantHelper.ignore(() -> RedisUtils.setCacheObject(redisKey, jsonStatus, Duration.ofSeconds(expireSeconds)));

            // 消息响应
            String topic = String.format(LhTopicConstantReq.ONLINE, lhBaseReq.getGateway());
            // 消息转换
            LhResponse<LhResponseOnlineData> lhResponse = JSON.parseObject(
                payload,
                new TypeReference<>() {});
            lhResponse.setData(new LhResponseOnlineData());
            // 响应消息
            messageHandler.sendToMqtt(topic, JSON.toJSONString(lhResponse));
        } catch (Exception e) {
            log.error("联合设备上线消息处理失败: {}", e.getMessage(), e);
        }
    }
}
