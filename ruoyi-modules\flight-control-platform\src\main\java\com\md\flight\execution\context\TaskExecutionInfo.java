package com.md.flight.execution.context;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.md.enums.TaskStatusEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 任务执行上下文信息
 * 用于跟踪和管理任务执行过程中的状态信息
 * 支持所有无人机类型（PX4、DJI、MAVLink、LH等）的任务状态管理
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TaskExecutionInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 无人机ID
     */
    private String droneId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态
     */
    private TaskStatusEnum status;

    /**
     * 当前航点
     */
    private Integer currentWaypoint;

    /**
     * 总航点数
     */
    private Integer totalWaypoints;

    /**
     * 任务进度（0.0-1.0）
     */
    private Double progress;

    /**
     * 构造函数
     */
    public TaskExecutionInfo() {
    }

    /**
     * 构造函数
     *
     * @param taskId   任务ID
     * @param droneId  无人机ID
     * @param taskName 任务名称
     */
    public TaskExecutionInfo(String taskId, String droneId, String taskName) {
        this.taskId = taskId;
        this.droneId = droneId;
        this.taskName = taskName;
        this.status = TaskStatusEnum.PENDING;
    }
}
