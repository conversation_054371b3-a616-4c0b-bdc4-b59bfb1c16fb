package com.md.flight.lh.processor;

import com.alibaba.fastjson2.JSON;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.service.impl.LhDroneControlServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 联合航线继续消息处理
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LhRouteResumeProcessor implements LhBaseProcessor{

    private final LhDroneControlServiceImpl lhDroneControlService;

    @Override
    public void processMessage(String payload) {
        log.info("收到联合飞行器航线继续消息: {}", payload);
        // 解析响应
        LhResponse response = JSON.parseObject(payload, LhResponse.class);
        // 获取bid
        String bid = response.getBid();
        // 通知等待的线程继续执行
        lhDroneControlService.notifyResponse(bid);
    }
}
