package com.md.mqtt.processor;

import com.md.flight.lh.constant.LhTopicConstantRsp;
import com.md.flight.lh.mqtt.LhCustomMqttMessageReceiver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * LH消息处理器
 */
@Component
public class LhMqttMessageProcessor extends AbstractMqttMessageProcessor{
    @Autowired
    private LhCustomMqttMessageReceiver customMqttMessageReceiver;

    /**
     * 获取所有LH推送的主题模式
     */
    public List<String> getLHTopics() {
        return Arrays.asList(
            //无人机操作主题响应
            LhTopicConstantRsp.EVENTS,
            //无人机数据主题响应
            LhTopicConstantRsp.OSD,
            //服务回复主题
            LhTopicConstantRsp.SERVICES_REPLY,
            //设备状态主题响应
            LhTopicConstantRsp.ONLINE
        );
    }

    @Override
    protected String getTopicPattern() {
        // 将LH处理消息的主题转换为正则表达式
        List<String> topics = getLHTopics();
        String regexPattern = topics.stream().map(topic -> {
            return topic.replace("+", "[^/]+");
        }).collect(Collectors.joining("|"));
        return regexPattern;
    }

    @Override
    protected void doProcessMessage(String topic, String payload) {
        customMqttMessageReceiver.handleCustomMessage(topic, payload);
    }
}
