package com.md.flight.mavlink.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import com.md.flight.mavlink.model.Mission;
import com.md.flight.mavlink.model.MissionWaypoint;
import com.md.flight.mavlink.service.MavlinkMissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.HashMap;

/**
 * 航线任务控制器
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/drone/mission")
@Tag(name = "航线任务管理")
public class MissionController extends BaseController {

    private final MavlinkMissionService missionService;

    /**
     * 上传航线任务
     */
    @PostMapping("/upload")
    @SaCheckPermission("drone:mission:upload")
    @Log(title = "上传航线任务", businessType = BusinessType.INSERT)
    @Operation(summary = "上传航线任务")
    @RepeatSubmit
    public R<Void> uploadMission(
            @RequestBody @Valid Mission mission,
            @Parameter(description = "无人机ID", required = true) 
            @RequestParam @NotBlank(message = "无人机ID不能为空") String droneId) {
        try {
            CompletableFuture<Boolean> future = missionService.uploadMission(droneId, mission);
            Boolean result = future.get(); // 等待上传完成
            return toAjax(result);
        } catch (Exception e) {
            log.error("上传航线任务失败", e);
            return R.fail("上传航线任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建矩形航线任务
     */
    @PostMapping("/create/rectangle")
    @SaCheckPermission("drone:mission:create")
    @Log(title = "创建矩形航线任务", businessType = BusinessType.INSERT)
    @Operation(summary = "创建矩形航线任务")
    @RepeatSubmit
    public R<Void> createRectangleMission(
            @RequestBody Map<String, Object> params,
            @Parameter(description = "无人机ID", required = true) 
            @RequestParam @NotBlank(message = "无人机ID不能为空") String droneId) {
        try {
            // 参数校验
            if (!params.containsKey("centerLat") || !params.containsKey("centerLon") || !params.containsKey("width") ||
                !params.containsKey("height") || !params.containsKey("altitude")) {
                return R.fail("缺少必要参数：中心点坐标、宽度、高度或高度参数");
            }

            // 调用服务创建矩形航线
            CompletableFuture<Boolean> future = missionService.createRectangleMission(droneId, params);
            Boolean result = future.get(); // 等待创建完成

            return toAjax(result);
        } catch (IllegalArgumentException e) {
            log.error("创建矩形航线参数错误", e);
            return R.fail("参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("创建矩形航线失败", e);
            return R.fail("创建矩形航线失败: " + e.getMessage());
        }
    }

    /**
     * 创建多航点航线任务
     */
    @PostMapping("/create/waypoints")
    @SaCheckPermission("drone:mission:create")
    @Log(title = "创建多航点航线任务", businessType = BusinessType.INSERT)
    @Operation(summary = "创建多航点航线任务")
    @RepeatSubmit
    public R<Void> createWaypointsMission(
            @RequestBody Map<String, Object> params,
            @Parameter(description = "无人机ID", required = true) 
            @RequestParam @NotBlank(message = "无人机ID不能为空") String droneId) {
        try {
            // 参数校验
            if (!params.containsKey("waypoints") || !params.containsKey("altitude")) {
                return R.fail("缺少必要参数：航点列表或高度参数");
            }

            // 调用服务创建多航点航线
            CompletableFuture<Boolean> future = missionService.createWaypointsMission(droneId, params);
            Boolean result = future.get(); // 等待创建完成

            return toAjax(result);
        } catch (IllegalArgumentException e) {
            log.error("创建多航点航线参数错误", e);
            return R.fail("参数错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("创建多航点航线失败", e);
            return R.fail("创建多航点航线失败: " + e.getMessage());
        }
    }

    /**
     * 开始执行任务
     */
    @PostMapping("/start")
    @SaCheckPermission("drone:mission:start")
    @Log(title = "开始执行任务", businessType = BusinessType.UPDATE)
    @Operation(summary = "开始执行任务")
    @RepeatSubmit
    public R<Void> startMission(
            @Parameter(description = "飞行速度", required = true) 
            @RequestParam(defaultValue = "8") BigDecimal speed,
            @Parameter(description = "任务标识", required = false) 
            @RequestParam(required = false) String missionId,
            @Parameter(description = "无人机ID", required = true) 
            @RequestParam @NotBlank(message = "无人机ID不能为空") String droneId) {
        try {
            missionService.startMission(droneId, speed, missionId);
            return R.ok("开始执行任务成功");
        } catch (Exception e) {
            log.error("开始执行任务失败", e);
            return R.fail("开始执行任务失败: " + e.getMessage());
        }
    }

    /**
     * 暂停任务
     */
    @PostMapping("/pause")
    @SaCheckPermission("drone:mission:pause")
    @Log(title = "暂停任务", businessType = BusinessType.UPDATE)
    @Operation(summary = "暂停任务")
    public R<Void> pauseMission(
            @Parameter(description = "无人机ID", required = true) 
            @RequestParam @NotBlank(message = "无人机ID不能为空") String droneId) {
        try {
            missionService.pauseMission(droneId);
            return R.ok("任务暂停成功");
        } catch (Exception e) {
            log.error("暂停任务失败", e);
            return R.fail("暂停任务失败: " + e.getMessage());
        }
    }

    /**
     * 继续任务
     */
    @PostMapping("/resume")
    @SaCheckPermission("drone:mission:resume")
    @Log(title = "继续任务", businessType = BusinessType.UPDATE)
    @Operation(summary = "继续任务")
    public R<Void> resumeMission(
            @Parameter(description = "无人机ID", required = true) 
            @RequestParam @NotBlank(message = "无人机ID不能为空") String droneId) {
        try {
            missionService.resumeMission(droneId);
            return R.ok("任务继续执行成功");
        } catch (Exception e) {
            log.error("继续任务失败", e);
            return R.fail("继续任务失败: " + e.getMessage());
        }
    }

    /**
     * 停止任务
     */
    @PostMapping("/stop")
    @SaCheckPermission("drone:mission:stop")
    @Log(title = "停止任务", businessType = BusinessType.UPDATE)
    @Operation(summary = "停止任务")
    public R<Void> stopMission(
            @Parameter(description = "无人机ID", required = true) 
            @RequestParam @NotBlank(message = "无人机ID不能为空") String droneId) {
        try {
            missionService.stopMission(droneId);
            return R.ok("任务停止成功");
        } catch (Exception e) {
            log.error("停止任务失败", e);
            return R.fail("停止任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前航线任务
     */
    @GetMapping("/current")
    @SaCheckPermission("drone:mission:query")
    @Operation(summary = "获取当前航线任务")
    public R<Map<String, Object>> getCurrentMission(
            @Parameter(description = "无人机ID", required = true) 
            @RequestParam @NotBlank(message = "无人机ID不能为空") String droneId) {
        try {
            CompletableFuture<List<MissionWaypoint>> future = missionService.getCurrentMission(droneId);
            List<MissionWaypoint> waypoints = future.get(); // 等待获取完成

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("waypoints", waypoints);
            data.put("count", waypoints.size());

            return R.ok(data);
        } catch (Exception e) {
            log.error("获取当前航线任务失败", e);
            return R.fail("获取当前航线任务失败: " + e.getMessage());
        }
    }

    /**
     * 设置飞行速度
     */
    @PostMapping("/set/speed")
    @SaCheckPermission("drone:mission:set")
    @Log(title = "设置飞行速度", businessType = BusinessType.UPDATE)
    @Operation(summary = "设置飞行速度")
    public R<Void> setSpeed(
            @Parameter(description = "飞行速度", required = true) 
            @RequestParam @NotNull(message = "飞行速度不能为空") float speed,
            @Parameter(description = "无人机ID", required = true) 
            @RequestParam @NotBlank(message = "无人机ID不能为空") String droneId) {
        try {
            missionService.setSpeed(droneId, speed);
            return R.ok("飞行速度设置成功");
        } catch (Exception e) {
            log.error("设置飞行速度失败", e);
            return R.fail("设置飞行速度失败: " + e.getMessage());
        }
    }
}