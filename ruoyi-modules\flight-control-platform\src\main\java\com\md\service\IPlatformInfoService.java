package com.md.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.md.domain.bo.PlatformInfoBo;
import com.md.domain.po.PlatformInfo;
import com.md.domain.vo.PlatformInfoVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * 飞控平台Service接口
 */
public interface IPlatformInfoService extends IService<PlatformInfo> {
    /**
     * 查询飞控平台
     *
     * @param id 飞控平台主键
     * @return 飞控平台
     */
    public PlatformInfoVo selectFkPlatformInfoById(Long id);

    /**
     * 查询飞控平台列表
     *
     * @param bo 飞控平台
     * @return 飞控平台集合
     */
    public List<PlatformInfoVo> selectFkPlatformInfoList(PlatformInfoBo bo);

    /**
     * 分页查询飞控平台列表
     *
     * @param bo        飞控平台
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<PlatformInfoVo> selectPlatformInfoPage(PlatformInfoBo bo, PageQuery pageQuery);

    /**
     * 新增飞控平台
     *
     * @param bo 飞控平台
     * @return 结果
     */
    public int insertFkPlatformInfo(PlatformInfoBo bo);

    /**
     * 修改飞控平台
     *
     * @param bo 飞控平台
     * @return 结果
     */
    public int updateFkPlatformInfo(PlatformInfoBo bo);

    /**
     * 批量删除飞控平台
     *
     * @param ids 需要删除的飞控平台主键集合
     * @return 结果
     */
    public int deleteFkPlatformInfoByIds(Long[] ids);

    /**
     * 删除飞控平台信息
     *
     * @param id 飞控平台主键
     * @return 结果
     */
    public int deleteFkPlatformInfoById(Long id);

    /**
     * 通过飞控编号获取飞控实体
     *
     * @param code 飞控编号
     * @return 飞控平台
     */
    public PlatformInfo selectPlatformByCode(String code);
}
