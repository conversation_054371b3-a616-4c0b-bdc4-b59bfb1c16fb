package com.md.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.md.command.model.dto.CommandResult;
import com.md.domain.bo.FlightTaskBo;
import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.domain.vo.FlightTaskVo;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 航线任务服务接口
 *
 * <AUTHOR>
 */
public interface IFlightTaskService extends IService<FlightTask> {
    /**
     * 查询航线任务列表（不分页）
     *
     * @param bo 查询条件
     * @return 航线任务列表
     */
    List<FlightTaskVo> selectFlightTaskList(FlightTaskBo bo);

    /**
     * 查询航线任务列表（分页）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页航线任务列表
     */
    TableDataInfo<FlightTaskVo> selectFlightTaskList(FlightTaskBo bo, PageQuery pageQuery);

    /**
     * 查询航线任务详细信息
     *
     * @param id 航线任务ID
     * @return 航线任务详细信息
     */
    FlightTaskVo selectFlightTaskById(String id);

    /**
     * 新增航线任务
     *
     * @param bo 航线任务信息
     * @return 任务ID
     */
    String insertFlightTask(FlightTaskBo bo);

    /**
     * 修改航线任务
     *
     * @param bo 航线任务信息
     * @return 任务ID
     */
    String updateFlightTask(FlightTaskBo bo);

    /**
     * 批量删除航线任务
     *
     * @param ids 需要删除的航线任务ID数组
     * @return 结果
     */
    int deleteFlightTaskByIds(Long[] ids);

    /**
     * 生成航线任务对应的kmz文件
     *
     * @param id 任务ID
     * @return 结果
     */
    boolean buildKmzByFlightLine(String id);

    /**
     * 导出航线任务对应的kmz文件
     *
     * @param taskName 任务名称
     * @param response HTTP响应对象
     * @return 文件URL
     */
    String exportKmz(String taskName, HttpServletResponse response);

    /**
     * 查询无人机当前任务状态
     *
     * @param uavCode 无人机机身码
     * @return 当前执行的任务信息，如果没有执行中的任务则返回null
     */
    Map<String, Object> getCurrentTask(String uavCode);

    /**
     * 更新任务的执飞人ID
     *
     * @param taskName   任务名称
     * @param operatorId 执飞人ID
     * @return 是否更新成功
     */
    boolean updateTaskOperator(String taskName, Long operatorId);

    /**
     * 复制航线任务
     *
     * @param taskId 原任务ID
     * @return 新任务ID
     */
    String copyFlightTask(String taskId);

    /**
     * 更新任务状态
     *
     * @param taskName 任务名称
     * @param status   任务状态
     * @param username 操作用户名
     * @return 更新结果
     */
    boolean updateTaskStatus(String taskName, Integer status, String username);

    /**
     * 查找最近的备降点
     *
     * @param taskId    任务ID
     * @param latitude  当前纬度
     * @param longitude 当前经度
     * @return 最近的备降点
     */
    FlightTaskPoint findNearestEmergencyLandingPoint(String taskId, BigDecimal latitude, BigDecimal longitude);

    /**
     * 获取任务中的所有备降点
     *
     * @param taskId 任务ID
     * @return 备降点列表
     */
    List<FlightTaskPoint> getAllEmergencyLandingPoints(String taskId);

    /**
     * 执行备降操作
     *
     * @param taskId           任务ID
     * @param droneId          无人机ID
     * @param currentLatitude  当前纬度
     * @param currentLongitude 当前经度
     * @param currentAltitude  当前高度
     * @return 命令执行结果
     */
    CommandResult executeEmergencyLanding(String taskId, String droneId, BigDecimal currentLatitude,
        BigDecimal currentLongitude, BigDecimal currentAltitude);

    /**
     * 更新任务关联的围栏
     *
     * @param taskId   任务ID
     * @param fenceIds 围栏ID列表，以逗号分隔的字符串
     * @return 是否更新成功
     */
    boolean updateTaskFences(String taskId, String fenceIds);

    /**
     * 获取任务关联的围栏ID
     *
     * @param taskId 任务ID
     * @return 以逗号分隔的围栏ID字符串
     */
    String getTaskFences(String taskId);

    /**
     * 执行航线任务
     *
     * @param taskId 任务ID
     * @return 执行结果
     */
    CompletableFuture<Boolean> executeFlightTask(String taskId);

    /**
     * 创建航线任务
     *
     * @param lineId             航线ID
     * @param uavCode            无人机编码
     * @param executeImmediately 是否立即执行任务
     * @return 任务ID
     */
    String createTaskFromLine(String lineId, String uavCode, Boolean executeImmediately);

    /**
     * 创建航线任务（指定租户ID）
     *
     * @param lineId             航线ID
     * @param uavCode            无人机编码
     * @param executeImmediately 是否立即执行任务
     * @param tenantId           租户ID
     * @return 任务ID
     */
    String createTaskFromLine(String lineId, String uavCode, Boolean executeImmediately, String tenantId);

    /**
     * 获取厂商支持的协议类型列表
     *
     * @param manufacturerType 厂商类型
     * @return 支持的协议类型集合
     * @throws IllegalArgumentException 如果厂商类型不受支持
     */
    Set<String> getSupportedProtocols(String manufacturerType);
}
