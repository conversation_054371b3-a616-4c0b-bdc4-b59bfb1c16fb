package com.md.flight.mavlink.service.impl;

import com.md.flight.mavlink.model.DroneStatus;
import com.md.flight.mavlink.service.MavlinkConnectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * MAVLink连接管理服务实现
 */
@Slf4j
@Service
public class MavlinkConnectionServiceImpl implements MavlinkConnectionService {

    @Autowired
    private MavlinkCoreService coreService;

    @Override
    public void connect(String droneId, String host, int port) {
        coreService.connect(droneId, host, port);
    }

    @Override
    public void disconnect(String droneId) {
        coreService.disconnect(droneId);
    }

    @Override
    public boolean isConnected(String droneId) {
        return coreService.isConnected(droneId);
    }

    @Override
    public boolean reconnect(String droneId, String host, int port) {
        return coreService.reconnect(droneId, host, port);
    }

    @Override
    public DroneStatus getDroneStatus(String droneId) {
        return coreService.getDroneStatus(droneId);
    }

    @Override
    public List<String> getConnectedDrones() {
        return coreService.getConnectedDrones();
    }
}