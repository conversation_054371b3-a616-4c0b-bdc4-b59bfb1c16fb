package com.md.enums;

import lombok.Getter;

/**
 * 围栏区域类型枚举
 */
@Getter
public enum FenceAreaTypeEnum {
    /**
     * 圆形
     */
    CIRCLE(1, "圆形"),

    /**
     * 多边形
     */
    POLYGON(2, "多边形"),

    /**
     * 矩形
     */
    RECTANGLE(3, "矩形");

    private final int code;
    private final String info;

    FenceAreaTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static FenceAreaTypeEnum getByCode(int code) {
        for (FenceAreaTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}