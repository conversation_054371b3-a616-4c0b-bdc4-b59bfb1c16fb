package com.md.command.listener;

import com.md.command.constant.CommandType;
import com.md.command.model.dto.CommandRequest;
import com.md.command.model.dto.CommandResult;
import com.md.command.service.DroneCommandService;
import com.md.domain.po.FlightTaskPoint;
import com.md.mqtt.event.EmergencyLandingEvent;
import com.md.service.IFlightTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 紧急降落事件监听器
 * 用于接收并处理无人机紧急降落指令
 */
@Component
@Slf4j
public class EmergencyLandingEventListener {

    @Autowired
    private DroneCommandService droneCommandService;

    @Autowired
    private IFlightTaskService flightTaskService;

    /**
     * 处理紧急降落事件
     *
     * @param event 紧急降落事件
     */
    @EventListener
    public void handleEmergencyLandingEvent(EmergencyLandingEvent event) {
        log.info("接收到紧急降落事件: 无人机ID={}, 任务ID={}", event.getDroneId(), event.getTaskId());

        try {
            // 查找最近的备降点
            FlightTaskPoint emergencyPoint =
                flightTaskService.findNearestEmergencyLandingPoint(event.getTaskId(), event.getCurrentLatitude(),
                    event.getCurrentLongitude());

            if (emergencyPoint == null) {
                log.error("未找到可用的备降点，任务ID: {}", event.getTaskId());
                return;
            }

            log.info("找到最近的备降点: 位置=[{}, {}, {}]", emergencyPoint.getLatitude(), emergencyPoint.getLongitude(),
                emergencyPoint.getAltitude());

            // 构造导航到备降点的命令
            CommandRequest request = new CommandRequest();
            request.setDroneId(event.getDroneId());
            request.setCommandType(CommandType.VIRTUAL_STICK_NAVIGATE_TO_POINT);

            // 设置导航参数
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("latitude", emergencyPoint.getLatitude().doubleValue());
            parameters.put("longitude", emergencyPoint.getLongitude().doubleValue());
            parameters.put("altitude", emergencyPoint.getAltitude().doubleValue());
            parameters.put("speed", 5.5);  // 备降速度默认5.5m/s
            parameters.put("timeout", 50000);  // 默认超时50秒
            parameters.put("isEmergencyLanding", true);

            // 设置降落选项
            Map<String, Object> landingOptions = new HashMap<>();
            landingOptions.put("precisionLanding", true);
            landingOptions.put("landingSpeed", 2.0);
            landingOptions.put("hoveringHeight", 0.0);
            landingOptions.put("obstacleAvoidance", true);
            parameters.put("landingOptions", landingOptions);

            request.setParameters(parameters);

            // 执行命令
            log.info("发送备降命令到无人机: {}", event.getDroneId());
            CommandResult result = droneCommandService.executeCommand(request.toCommand());

            log.info("紧急降落命令执行结果: {}", result);
        } catch (Exception e) {
            log.error("处理紧急降落事件失败", e);
        }
    }
}