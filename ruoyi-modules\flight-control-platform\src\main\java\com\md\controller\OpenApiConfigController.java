package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.domain.bo.OpenApiConfigBo;
import com.md.domain.bo.OpenApiTenantBindingBo;
import com.md.domain.dto.ConfigInfo;
import com.md.domain.dto.EncryInfo;
import com.md.domain.po.OpenApiConfig;
import com.md.domain.vo.OpenApiConfigVo;
import com.md.domain.vo.OpenApiTenantBindingVo;
import com.md.mapper.OpenApiConfigMapper;
import com.md.service.IOpenApiConfigService;
import com.md.service.IOpenApiTenantBindingService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开放端接口管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/open/api/config")
public class OpenApiConfigController extends BaseController {

    private final IOpenApiConfigService openApiConfigService;
    private final IOpenApiTenantBindingService openApiTenantBindingService;
    private final OpenApiConfigMapper baseMapper;

    /**
     * 查询开放端接口管理列表
     */
    @GetMapping("/list")
    @SaCheckPermission("open:api:config:list")
    public TableDataInfo<OpenApiConfigVo> list(OpenApiConfigBo bo, PageQuery pageQuery) {
        return openApiConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取开放端接口管理详细信息
     */
    @GetMapping("/{id}")
    @SaCheckPermission("open:api:config:query")
    public R<OpenApiConfigVo> getInfo(@PathVariable("id") Long id) {
        return R.ok(openApiConfigService.queryById(id));
    }

    /**
     * 新增开放端接口管理
     */
    @Log(title = "开放端接口管理", businessType = BusinessType.INSERT)
    @PostMapping
    @SaCheckPermission("open:api:config:add")
    public R<Void> add(@Validated @RequestBody OpenApiConfigBo bo) {
        // 处理密钥配置信息转换
        R<Void> result = handleEncryptInfo(bo);
        if (result != null) {
            return result;
        }
        // 检查目标平台是否重复（排除当前ID）
        OpenApiConfig existConfig = checkOpenAddressExists(bo.getOpenAddress(), bo.getId());
        if (existConfig != null) {
            return R.fail("该路由地址 [" + bo.getOpenAddress() + "] 已存在，请使用其他名称");
        }
        return toAjax(openApiConfigService.insertByBo(bo));
    }

    /**
     * 修改开放端接口管理
     */
    @Log(title = "开放端接口管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @SaCheckPermission("open:api:config:edit")
    public R<Void> edit(@Validated @RequestBody OpenApiConfigBo bo) {
        // 处理密钥配置信息转换
        R<Void> result = handleEncryptInfo(bo);
        if (result != null) {
            return result;
        }
        // 检查目标平台是否重复（排除当前ID）
        OpenApiConfig existConfig = checkOpenAddressExists(bo.getOpenAddress(), bo.getId());
        if (existConfig != null) {
            return R.fail("该路由地址 [" + bo.getOpenAddress() + "] 已存在，请使用其他名称");
        }
        return toAjax(openApiConfigService.updateByBo(bo));
    }

    /**
     * 删除开放端接口管理
     */
    @Log(title = "开放端接口管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @SaCheckPermission("open:api:config:remove")
    public R<Void> remove(@PathVariable Long id) {
        return toAjax(openApiConfigService.deleteWithValidByIds(id));
    }

    public OpenApiConfig checkOpenAddressExists(String openAddress, Long excludeId) {
        LambdaQueryWrapper<OpenApiConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OpenApiConfig::getOpenAddress, openAddress);
        queryWrapper.ne(OpenApiConfig::getDelFlag, "1"); // 排除已删除的记录

        // 如果是更新操作，需要排除当前记录
        if (excludeId != null) {
            queryWrapper.ne(OpenApiConfig::getId, excludeId);
        }

        return baseMapper.selectOne(queryWrapper);
    }

    /**
     * 根据接口Id获取绑定租户信息列表
     */
    @GetMapping("/binding/list/{apiId}")
    @SaCheckPermission("open:api:config:binding:list")
    public TableDataInfo<OpenApiTenantBindingVo> bindingList(@PathVariable("apiId") @NotNull Long apiId,
        PageQuery pageQuery) {
        OpenApiTenantBindingBo bo = new OpenApiTenantBindingBo();
        bo.setApiId(apiId);
        return openApiTenantBindingService.queryPageList(bo, pageQuery);
    }

    /**
     * 修改租户绑定
     */
    @Log(title = "租户绑定", businessType = BusinessType.UPDATE)
    @PutMapping("/binding")
    @SaCheckPermission("open:api:config:binding:edit")
    public R<Void> editBinding(@Validated @RequestBody OpenApiTenantBindingBo bo) {
        handleConfigInfo(bo);
        return toAjax(openApiTenantBindingService.updateByBo(bo));
    }

    /**
     * 处理配置信息转换
     */
    private R<Void> handleConfigInfo(OpenApiTenantBindingBo bo) {
        String encryptInfoStr = bo.getConfigInfo();
        if (StringUtils.isNotBlank(encryptInfoStr)) {
            try {
                // 将String转换为ConfigInfo对象
                ConfigInfo configInfo = JsonUtils.parseObject(encryptInfoStr, ConfigInfo.class);
                if (configInfo != null) {
                    // 将ConfigInfo对象转换回标准格式的JSON字符串
                    String formattedEncryptInfo = JsonUtils.toJsonString(configInfo);
                    bo.setConfigInfo(formattedEncryptInfo);
                }
            } catch (Exception e) {
                return R.fail("密钥配置信息格式不正确");
            }
        }
        return null;
    }

    /**
     * 处理密钥配置信息转换
     */
    private R<Void> handleEncryptInfo(OpenApiConfigBo bo) {
        String encryptInfoStr = bo.getEncryptInfo();
        if (StringUtils.isNotBlank(encryptInfoStr)) {
            try {
                EncryInfo encryInfo = JsonUtils.parseObject(encryptInfoStr, EncryInfo.class);
                if (encryInfo != null) {
                    String formattedEncryptInfo = JsonUtils.toJsonString(encryInfo);
                    bo.setEncryptInfo(formattedEncryptInfo);
                }
            } catch (Exception e) {
                return R.fail("密钥配置信息格式不正确");
            }
        }
        return null;
    }
}
