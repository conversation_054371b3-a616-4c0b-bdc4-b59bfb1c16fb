package com.md.service.impl;

import com.md.constant.FenceConstants;
import com.md.domain.po.FlightTask;
import com.md.mapper.FlightLineTaskMapper;
import com.md.service.IFlightTaskFenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 航线任务围栏服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FlightTaskFenceServiceImpl implements IFlightTaskFenceService {

    private final FlightLineTaskMapper flightLineTaskMapper;

    @Override
    public boolean updateTaskFences(String taskId, String fenceIds) {
        log.info("更新任务关联的围栏: taskId={}, fenceIds={}", taskId, fenceIds);
        try {
            if (StringUtils.isEmpty(taskId)) {
                log.warn("任务ID为空，无法更新围栏关联");
                return false;
            }

            // 检查任务是否存在
            FlightTask task = flightLineTaskMapper.selectById(taskId);
            if (task == null) {
                log.warn("任务不存在: taskId={}", taskId);
                return false;
            }

            // 更新任务表中的fence_id字段
            FlightTask updateTask = new FlightTask();
            updateTask.setId(taskId);
            updateTask.setFenceId(fenceIds);
            boolean updateResult = flightLineTaskMapper.updateById(updateTask) > 0;

            if (updateResult) {
                // 更新缓存
                String taskFenceCacheKey = FenceConstants.REDIS_FENCE_PREFIX + "task:" + taskId;
                if (StringUtils.isNotEmpty(fenceIds)) {
                    RedisUtils.setCacheObject(taskFenceCacheKey, fenceIds,
                        Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));
                    // 加载围栏数据
                    // TODO: 这里可以添加预加载围栏数据的逻辑
                    log.info("已更新任务围栏关联并缓存: taskId={}, fenceIds={}", taskId, fenceIds);
                } else {
                    // 如果围栏ID为空，则删除缓存
                    RedisUtils.deleteObject(taskFenceCacheKey);
                    log.info("已清空任务围栏关联: taskId={}", taskId);
                }
                return true;
            } else {
                log.warn("更新任务围栏关联失败: taskId={}, fenceIds={}", taskId, fenceIds);
                return false;
            }
        } catch (Exception e) {
            log.error("更新任务围栏关联时发生异常: taskId={}, fenceIds={}", taskId, fenceIds, e);
            return false;
        }
    }

    @Override
    public String getTaskFences(String taskId) {
        FlightTask task = flightLineTaskMapper.selectById(taskId);
        if (task == null) {
            log.warn("任务不存在: taskId={}", taskId);
            return "";
        }
        return task.getFenceId() != null ? task.getFenceId() : "";
    }
}