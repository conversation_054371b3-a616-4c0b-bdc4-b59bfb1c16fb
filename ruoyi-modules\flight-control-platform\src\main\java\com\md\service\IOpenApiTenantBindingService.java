package com.md.service;

import com.md.domain.bo.OpenApiTenantBindingBo;
import com.md.domain.vo.OpenApiTenantBindingVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * 开放端接口租户绑定关系服务接口
 */
public interface IOpenApiTenantBindingService {

    /**
     * 查询租户绑定列表
     *
     * @param bo 租户绑定查询条件
     * @return 租户绑定集合
     */
    TableDataInfo<OpenApiTenantBindingVo> queryPageList(OpenApiTenantBindingBo bo, PageQuery pageQuery);

    /**
     * 查询租户绑定列表
     *
     * @param bo 租户绑定查询条件
     * @return 租户绑定集合
     */
    List<OpenApiTenantBindingVo> queryList(OpenApiTenantBindingBo bo);

    /**
     * 新增租户绑定
     *
     * @param bo 租户绑定对象
     * @return 结果
     */
    Boolean insertByBo(OpenApiTenantBindingBo bo);

    /**
     * 修改租户绑定
     *
     * @param bo 租户绑定对象
     * @return 结果
     */
    Boolean updateByBo(OpenApiTenantBindingBo bo);

}
