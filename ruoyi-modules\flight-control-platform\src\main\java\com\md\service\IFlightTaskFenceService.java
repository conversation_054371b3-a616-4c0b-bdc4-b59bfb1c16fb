package com.md.service;

/**
 * 航线任务围栏服务接口
 * 
 * <AUTHOR>
 */
public interface IFlightTaskFenceService {
    
    /**
     * 更新任务关联的围栏
     *
     * @param taskId   任务ID
     * @param fenceIds 围栏ID列表，以逗号分隔的字符串
     * @return 是否更新成功
     */
    boolean updateTaskFences(String taskId, String fenceIds);

    /**
     * 获取任务关联的围栏ID
     *
     * @param taskId 任务ID
     * @return 以逗号分隔的围栏ID字符串
     */
    String getTaskFences(String taskId);
}