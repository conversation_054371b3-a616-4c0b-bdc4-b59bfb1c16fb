package com.md.utils;

import io.minio.*;
import io.minio.http.Method;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MinioUtils {

    @Getter
    @Value("${minio.bucket}")
    private String defaultBucketName;

    private final MinioClient minioClient;

    public MinioUtils(MinioClient minioClient) {
        this.minioClient = minioClient;
    }

    /**
     * 上传文件
     *
     * @param bucketName  存储桶名称
     * @param objectName  对象名称
     * @param inputStream 输入流
     * @param contentType 文件类型
     * @return 文件访问路径
     */
    public String uploadFile(String bucketName, String objectName, InputStream inputStream, String contentType) {
        try {
            // 检查存储桶是否存在
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!exists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("创建存储桶: {}", bucketName);
            }

            // 设置文件元数据
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", contentType);

            // 上传文件
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(inputStream, -1, 10485760) // 10MB 分片
                            .headers(headers)
                            .build()
            );

            log.info("文件上传成功: bucket={}, object={}", bucketName, objectName);
            return getFileUrl(bucketName, objectName);
        } catch (Exception e) {
            log.error("文件上传失败: bucket={}, object={}", bucketName, objectName, e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件访问URL
     */
    public String getFileUrl(String bucketName, String objectName) {
        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .method(Method.GET)
                            .expiry(7, TimeUnit.DAYS)  // URL有效期7天
                            .build()
            );
        } catch (Exception e) {
            log.error("获取文件URL失败: bucket={}, object={}", bucketName, objectName, e);
            throw new RuntimeException("获取文件URL失败: " + e.getMessage());
        }
    }

    /**
     * 检查MinIO预签名URL是否过期
     *
     * @param url MinIO预签名URL
     * @return true-已过期，false-未过期
     */
    public boolean isUrlExpired(String url) {
        if (StringUtils.isEmpty(url)) {
            return true;
        }

        try {
            // 检查是否是MinIO预签名URL
            if (url.contains("X-Amz-Expires=") && url.contains("X-Amz-Date=")) {
                // 提取X-Amz-Date参数值 (格式: YYYYMMDDTHHMMSSZ)
                String dateStr = extractParameter(url, "X-Amz-Date=");
                // 提取X-Amz-Expires参数值 (单位: 秒)
                String expiresStr = extractParameter(url, "X-Amz-Expires=");

                if (dateStr != null && expiresStr != null) {
                    // 解析日期时间
                    Date signedDate = parseAwsDate(dateStr);
                    int expiresInSeconds = Integer.parseInt(expiresStr);

                    // 计算过期时间
                    Date expirationDate = new Date(signedDate.getTime() + (expiresInSeconds * 1000L));

                    // 判断是否过期
                    return !expirationDate.after(new Date());
                }
            }
            return true; // 不是预签名URL，视为已过期
        } catch (Exception e) {
            log.warn("解析MinIO URL失败: {}", e.getMessage());
            return true; // 解析失败，视为已过期
        }
    }

    /**
     * 从URL中提取指定参数的值
     */
    private String extractParameter(String url, String paramPrefix) {
        int startIndex = url.indexOf(paramPrefix);
        if (startIndex == -1) return null;

        startIndex += paramPrefix.length();
        int endIndex = url.indexOf("&", startIndex);
        if (endIndex == -1) {
            endIndex = url.length();
        }

        return url.substring(startIndex, endIndex);
    }

    /**
     * 解析AWS格式的日期时间字符串 (格式: YYYYMMDDTHHMMSSZ)
     */
    private Date parseAwsDate(String dateStr) throws Exception {
        // 格式：YYYYMMDDTHHMMSSZ
        String year = dateStr.substring(0, 4);
        String month = dateStr.substring(4, 6);
        String day = dateStr.substring(6, 8);
        String hour = dateStr.substring(9, 11);
        String minute = dateStr.substring(11, 13);
        String second = dateStr.substring(13, 15);

        Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        cal.set(Integer.parseInt(year),
                Integer.parseInt(month) - 1,  // 月份从0开始
                Integer.parseInt(day),
                Integer.parseInt(hour),
                Integer.parseInt(minute),
                Integer.parseInt(second));

        return cal.getTime();
    }
}