package com.md.websocket.converter;

import com.md.websocket.message.DroneMessage;
import com.md.websocket.message.MessageType;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统一的无人机消息转换器
 * 使用策略模式管理不同厂商的转换逻辑
 */
@Slf4j
@Component
public class UnifiedMessageConverter {

    @Autowired
    private List<DroneMessageConverter> converters;

    private final Map<String, DroneMessageConverter> converterMap = new HashMap<>();

    @PostConstruct
    public void initConverters() {
        for (DroneMessageConverter converter : converters) {
            converterMap.put(converter.getManufacturer(), converter);
            log.debug("注册无人机消息转换器: {}", converter.getManufacturer());
        }

        log.info("统一消息转换器初始化完成，支持 {} 个厂商: [{}]", converterMap.size(),
            String.join(", ", converterMap.keySet()));
    }

    /**
     * 根据主题转换消息
     *
     * @param topic   MQTT主题
     * @param payload 消息载荷
     * @return 转换后的无人机消息
     */
    public DroneMessage convertByTopic(String topic, String payload) {
        try {
            // 根据主题获取消息类型
            MessageType messageType = MessageType.fromTopic(topic);
            if (messageType == null) {
                log.debug("未找到匹配的消息类型: topic={}", topic);
                return null;
            }

            // 根据厂商获取对应的转换器
            String manufacturer = messageType.getManufacturer();
            DroneMessageConverter converter = converterMap.get(manufacturer);

            if (converter == null) {
                log.warn("未找到厂商 [{}] 的消息转换器: topic={}", manufacturer, topic);
                return null;
            }

            // 执行转换
            DroneMessage message = converter.convert(topic, payload, messageType);
            if (message != null) {
                // 设置厂商信息
                message.setManufacturer(manufacturer);
                log.debug("消息转换成功: topic={}, manufacturer={}, droneId={}", topic, manufacturer,
                    message.getDroneId());
            }

            return message;

        } catch (Exception e) {
            log.error("消息转换失败: topic={}, error={}", topic, e.getMessage(), e);
            return null;
        }
    }

}
