package com.md.service.impl;

import com.md.command.constant.CommandType;
import com.md.command.model.dto.CommandRequest;
import com.md.command.service.DroneCommandService;
import com.md.config.AgoraConfig;
import com.md.constant.MqttConstants;
import com.md.service.IAgoraTokenService;
import com.md.service.IDroneStatusService;
import org.dromara.common.core.domain.R;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import io.agora.media.RtcTokenBuilder;
import io.agora.media.RtcTokenBuilder.Role;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service
public class AgoraTokenServiceImpl implements IAgoraTokenService {

    private final AgoraConfig agoraConfig;
    private final DroneCommandService droneCommandService;
    private final IDroneStatusService droneStatusService;

    private static final String AGORA_TOKEN_KEY = "agora:token:";  // 用于存储token和channel信息
    private static final int TOKEN_EXPIRE_TIME = 60; // Token有效期（分钟）
    private static final int TOKEN_BUFFER_TIME = 5; // Token过期缓冲时间（分钟）

    @Override
    public R<Object> getLiveToken(String droneSN, String uid) {
        try {
            // 1. 获取或创建房间ID
            String channelId = getOrCreateChannel(droneSN, uid);

            // 2. 尝试从Redis获取token信息
            String tokenCacheKey = AGORA_TOKEN_KEY + droneSN;
            Map<String, Object> tokenInfo = TenantHelper.ignore(() -> RedisUtils.getCacheMap(tokenCacheKey));

            // 3. 检查token是否存在且有效
            if (tokenInfo != null && !tokenInfo.isEmpty() && !isTokenExpiringSoon(tokenInfo)) {
                log.info("使用缓存的Token: channelId={}, uid={}", channelId, uid);
            } else {
                // 4. 如果token不存在或已过期，重新生成
                final Map<String, Object> newTokenInfo = generateToken(channelId, uid);

                // 5. 缓存token信息
                TenantHelper.ignore(() -> {
                    RedisUtils.setCacheMap(tokenCacheKey, newTokenInfo);
                    RedisUtils.expire(tokenCacheKey, Duration.ofMinutes(TOKEN_EXPIRE_TIME));
                    return null;
                });

                log.info("生成新的Token并缓存: channelId={}, uid={}", channelId, uid);

                // 更新tokenInfo引用
                tokenInfo = newTokenInfo;
            }

            // 6. 配置并开启直播
            startLiveStream(droneSN, channelId, uid, (String)tokenInfo.get("token"));

            // 7. 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("token", tokenInfo.get("token"));
            data.put("channelId", tokenInfo.get("channel"));
            data.put("droneSN", droneSN);
            data.put("appId", agoraConfig.getAppId());

            return R.ok("获取Token成功", data);

        } catch (Exception e) {
            log.error("获取直播Token失败: droneSN={}, error={}", droneSN, e.getMessage(), e);
            return R.fail("获取Token失败：" + e.getMessage());
        }
    }

    @Override
    public R<Object> getChannelStatus(String droneSN) {
        try {
            // 从token信息中获取channel状态
            String tokenCacheKey = AGORA_TOKEN_KEY + droneSN;
            Map<String, Object> tokenInfo = TenantHelper.ignore(() -> RedisUtils.getCacheMap(tokenCacheKey));
            String channelId = tokenInfo != null ? (String)tokenInfo.get("channel") : null;

            Map<String, Object> status = new HashMap<>();
            status.put("isLive", channelId != null);
            status.put("channelId", channelId);
            status.put("droneSN", droneSN);

            log.info("查询直播房间状态: droneSN={}, isLive={}", droneSN, channelId != null);
            return R.ok(status);
        } catch (Exception e) {
            log.error("查询房间状态失败: droneSN={}, error={}", droneSN, e.getMessage(), e);
            return R.fail("查询房间状态失败：" + e.getMessage());
        }
    }

    @Override
    public R<Object> closeChannel(String droneSN) {
        try {
            // 删除token信息即可关闭房间
            String tokenCacheKey = AGORA_TOKEN_KEY + droneSN;
            TenantHelper.ignore(() -> RedisUtils.deleteObject(tokenCacheKey));

            log.info("关闭直播房间: droneSN={}", droneSN);
            return R.ok("房间已关闭");
        } catch (Exception e) {
            log.error("关闭房间失败: droneSN={}, error={}", droneSN, e.getMessage(), e);
            return R.fail("关闭房间失败：" + e.getMessage());
        }
    }

    /**
     * 获取或创建直播房间ID，并在需要时配置和开启直播
     */
    private String getOrCreateChannel(String droneSN, String uid) {
        // 1. 尝试从Redis获取现有的token信息
        String tokenCacheKey = AGORA_TOKEN_KEY + droneSN;
        Map<String, Object> tokenInfo = TenantHelper.ignore(() -> RedisUtils.getCacheMap(tokenCacheKey));

        String channelId = null;
        if (tokenInfo != null && tokenInfo.get("channel") != null) {
            channelId = (String)tokenInfo.get("channel");
        }

        if (channelId == null) {
            // 创建新的房间ID：drone_sn_timestamp
            channelId = String.format("drone_%s_%d", droneSN, System.currentTimeMillis());
            log.info("创建新的直播房间: droneSN={}, channelId={}", droneSN, channelId);
        } else {
            log.debug("使用现有直播房间: droneSN={}, channelId={}", droneSN, channelId);
        }

        return channelId;
    }

    /**
     * 生成Agora Token
     */
    private Map<String, Object> generateToken(String channelName, String uid) {
        try {
            // 当前时间戳（秒）
            int timestamp = (int)(System.currentTimeMillis() / 1000);
            // 过期时间戳
            int expireTimestamp = timestamp + agoraConfig.getTokenExpireSeconds();

            // 生成Token
            RtcTokenBuilder tokenBuilder = new RtcTokenBuilder();
            String token =
                tokenBuilder.buildTokenWithUid(agoraConfig.getAppId(), agoraConfig.getAppCertificate(), channelName,
                    Integer.parseInt(uid), Role.Role_Publisher, expireTimestamp);

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("appId", agoraConfig.getAppId());
            result.put("channel", channelName);
            result.put("uid", uid);
            result.put("expireTime", expireTimestamp);

            return result;
        } catch (Exception e) {
            log.error("生成Token失败: channel={}, uid={}", channelName, uid, e);
            throw new RuntimeException("生成Token失败: " + e.getMessage());
        }
    }

    /**
     * 检查Token是否即将过期（预留5分钟缓冲时间）
     */
    private boolean isTokenExpiringSoon(Map<String, Object> tokenInfo) {
        try {
            Object expireTimeObj = tokenInfo.get("expireTime");
            if (expireTimeObj == null) {
                return true;
            }

            long expireTime = Long.parseLong(expireTimeObj.toString());
            long currentTime = System.currentTimeMillis() / 1000;
            // 如果token将在5分钟内过期，则认为即将过期
            return (expireTime - currentTime) < TOKEN_BUFFER_TIME * 60;
        } catch (Exception e) {
            log.error("检查Token过期时间出错", e);
            return true;
        }
    }

    /**
     * 配置并开启直播流
     *
     * @param droneSN   无人机编号
     * @param channelId 频道ID
     * @param uid       用户ID
     * @param token     声网Token
     */
    private void startLiveStream(String droneSN, String channelId, String uid, String token) {
        try {
            // 检查无人机是否在线
            if (!droneStatusService.isDroneOnline(droneSN)) {
                log.error("无人机不在线，无法开启直播: droneSN={}", droneSN);
                return;
            }

            // 检查直播状态
            String livestreamStatusKey = MqttConstants.REDIS_LIVESTREAM_STATUS_KEY_PREFIX + droneSN;
            String livestreamStatusStr = TenantHelper.ignore(() -> RedisUtils.getCacheObject(livestreamStatusKey));

            if (livestreamStatusStr != null) {
                JSONObject livestreamStatus = JSON.parseObject(livestreamStatusStr);
                boolean isStreaming = livestreamStatus.getBooleanValue("isStreaming");

                if (isStreaming) {
                    log.info("无人机已在直播中，跳过开启直播: droneSN={}", droneSN);
                    return;
                }
            }

            // 1. 发送直播配置命令
            CommandRequest configRequest = buildConfigCommand(droneSN, channelId, uid, token);
            droneCommandService.executeCommand(configRequest.toCommand());
            log.info("发送直播配置命令成功: droneSN={}, channelId={}", droneSN, channelId);

            // 2. 发送开启直播命令
            CommandRequest startRequest = buildStartCommand(droneSN);
            droneCommandService.executeCommand(startRequest.toCommand());
            log.info("发送开启直播命令成功: droneSN={}", droneSN);
        } catch (Exception e) {
            log.error("配置或开启直播失败: droneSN={}, channelId={}, error={}", droneSN, channelId, e.getMessage(), e);
            // 不抛出异常，继续返回Token信息
        }
    }

    /**
     * 构建直播配置命令
     */
    private CommandRequest buildConfigCommand(String droneSN, String channelId, String uid, String token) {
        Map<String, Object> configParams = new HashMap<>();
        configParams.put("channelId", channelId);
        configParams.put("token", token);
        configParams.put("uid", uid);
        configParams.put("quality", "SD");
        configParams.put("camera", "LEFT_MAIN");
        configParams.put("bitRateMode", "AUTO");

        CommandRequest configRequest = new CommandRequest();
        configRequest.setCommandType(CommandType.AGORA_CONFIG);
        configRequest.setDroneId(droneSN);
        configRequest.setParameters(configParams);

        return configRequest;
    }

    /**
     * 构建开启直播命令
     */
    private CommandRequest buildStartCommand(String droneSN) {
        CommandRequest startRequest = new CommandRequest();
        startRequest.setCommandType(CommandType.AGORA_START);
        startRequest.setDroneId(droneSN);
        startRequest.setParameters(new HashMap<>());

        return startRequest;
    }
}