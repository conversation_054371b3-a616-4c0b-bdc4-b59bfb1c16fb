package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.domain.bo.FenceBo;
import com.md.domain.vo.FenceVo;
import com.md.service.IFenceService;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.log.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;

/**
 * 电子围栏管理 控制器
 */
@RestController
@RequestMapping("/fence")
@Validated
public class FenceController extends BaseController {
    @Autowired
    private IFenceService fenceService;

    /**
     * 查询围栏列表
     */
    @SaCheckPermission("fence:fence:list")
    @GetMapping("/list")
    public TableDataInfo<FenceVo> list(@Validated(QueryGroup.class) FenceBo bo, PageQuery pageQuery) {
        return fenceService.selectFenceList(bo, pageQuery);
    }

    /**
     * 导出围栏列表
     */
    @SaCheckPermission("fence:fence:export")
    @Log(title = "电子围栏管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @Validated(QueryGroup.class) FenceBo bo) {
        //        List<FenceVO> list = fenceService.selectFenceList(fenceVO);
        //        ExcelUtil.exportExcel(list, "围栏数据", FenceVO.class, response);
    }

    /**
     * 获取围栏详细信息
     */
    @SaCheckPermission("fence:fence:query")
    @GetMapping("/{id}")
    public R<FenceVo> getInfo(@PathVariable("id") String id) {
        return R.ok(fenceService.selectFenceById(id));
    }

    /**
     * 新增围栏
     */
    @SaCheckPermission("fence:fence:add")
    @Log(title = "电子围栏管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<String> add(@Validated(AddGroup.class) @RequestBody FenceBo bo) {
        bo.setCreateBy(LoginHelper.getUserId());
        String fenceId = fenceService.insertFence(bo);
        return R.ok(fenceId);
    }

    /**
     * 修改围栏
     */
    @SaCheckPermission("fence:fence:edit")
    @Log(title = "电子围栏管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<String> edit(@Validated(EditGroup.class) @RequestBody FenceBo bo) {
        bo.setUpdateBy(LoginHelper.getUserId());
        String fenceId = fenceService.updateFence(bo);
        return R.ok(fenceId);
    }

    /**
     * 删除围栏
     */
    @SaCheckPermission("fence:fence:remove")
    @Log(title = "电子围栏管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable @NotEmpty(message = "围栏ID不能为空") String[] ids) {
        return toAjax(fenceService.deleteFenceByIds(ids));
    }

    /**
     * 启用围栏
     */
    @SaCheckPermission("fence:fence:edit")
    @Log(title = "电子围栏管理", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{id}")
    public R<Void> enable(@PathVariable("id") String id) {
        return toAjax(fenceService.enableFence(id));
    }

    /**
     * 禁用围栏
     */
    @SaCheckPermission("fence:fence:edit")
    @Log(title = "电子围栏管理", businessType = BusinessType.UPDATE)
    @PutMapping("/disable/{id}")
    public R<Void> disable(@PathVariable("id") String id) {
        return toAjax(fenceService.disableFence(id));
    }
}
