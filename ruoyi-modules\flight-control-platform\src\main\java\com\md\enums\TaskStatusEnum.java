package com.md.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.dromara.common.core.exception.ServiceException;

/**
 * 任务状态枚举
 *
 * <AUTHOR>
 * @date 2025年01月15日 10:05
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {

    PENDING(0, "待执行"),
    EXECUTING(1, "执行中"),
    PAUSED(2, "已暂停"),
    RESUMED(3, "已恢复"),
    COMPLETED(4, "已完成"),
    FAILED(5, "执行失败"),
    CANCELLED(6, "已取消");

    private final Integer code;
    private final String info;

    /**
     * 根据code获取枚举
     */
    public static TaskStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaskStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据code获取info
     */
    public static String getInfo(Integer code) {
        TaskStatusEnum status = getByCode(code);
        return status != null ? status.getInfo() : "";
    }

    /**
     * 校验状态是否合法
     */
    public static void validateStatus(Integer code) {
        if (getByCode(code) == null) {
            throw new ServiceException("非法的任务状态：" + code);
        }
    }
}
