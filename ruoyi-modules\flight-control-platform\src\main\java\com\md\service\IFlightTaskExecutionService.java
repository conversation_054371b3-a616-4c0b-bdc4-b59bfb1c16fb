package com.md.service;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 航线任务执行服务接口
 * 
 * <AUTHOR>
 */
public interface IFlightTaskExecutionService {
    
    /**
     * 查询无人机当前任务状态
     *
     * @param uavCode 无人机机身码
     * @return 当前执行的任务信息，如果没有执行中的任务则返回null
     */
    Map<String, Object> getCurrentTask(String uavCode);

    /**
     * 执行航线任务
     *
     * @param taskId 任务ID
     * @return 执行结果
     */
    CompletableFuture<Boolean> executeFlightTask(String taskId);

    /**
     * 创建航线任务
     *
     * @param lineId             航线ID
     * @param uavCode            无人机编码
     * @param executeImmediately 是否立即执行任务
     * @return 任务ID
     */
    String createTaskFromLine(String lineId, String uavCode, Boolean executeImmediately);

    /**
     * 创建航线任务（指定租户ID）
     *
     * @param lineId             航线ID
     * @param uavCode            无人机编码
     * @param executeImmediately 是否立即执行任务
     * @param tenantId           租户ID
     * @return 任务ID
     */
    String createTaskFromLine(String lineId, String uavCode, Boolean executeImmediately, String tenantId);

    /**
     * 获取厂商支持的协议类型列表
     *
     * @param manufacturerType 厂商类型
     * @return 支持的协议类型集合
     * @throws IllegalArgumentException 如果厂商类型不受支持
     */
    Set<String> getSupportedProtocols(String manufacturerType);
}