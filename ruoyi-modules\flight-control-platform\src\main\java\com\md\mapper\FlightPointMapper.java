package com.md.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.md.domain.po.FlightPoint;
import org.apache.ibatis.annotations.Param;

/**
 * 航点Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public interface FlightPointMapper extends BaseMapper<FlightPoint> {
    /**
     * 查询航点
     *
     * @param id 航点主键
     * @return 航点
     */
    public FlightPoint selectFlightPointById(Long id);

    /**
     * 查询航点列表
     *
     * @param flightPoint 航点
     * @return 航点集合
     */
    public List<FlightPoint> selectFlightPointList(FlightPoint flightPoint);

    /**
     * 新增航点
     *
     * @param flightPoint 航点
     * @return 结果
     */
    public int insertFlightPoint(FlightPoint flightPoint);

    /**
     * 修改航点
     *
     * @param flightPoint 航点
     * @return 结果
     */
    public int updateFlightPoint(FlightPoint flightPoint);

    /**
     * 删除航点
     *
     * @param id 航点主键
     * @return 结果
     */
    public int deleteFlightPointById(Long id);

    /**
     * 批量删除航点
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightPointByIds(Long[] ids);

    /**
     * 批量插入航点
     *
     * @param points
     */
    int batchInsertPoints(List<FlightPoint> points);

    /**
     * 根据航线ID删除航点
     */
    int deletePointsByLineId(String lineId);

    /**
     * 根据航线获取航点信息
     *
     * @param lineId
     * @return
     */
    List<FlightPoint> selectPointsByLineId(String lineId);

    /**
     * 根据航线ID列表批量删除航点
     */
    int deletePointsByLineIds(@Param("lineIds") List<String> lineIds);

    /**
     * 根据航线ID列表查询航点ID
     */
    List<String> selectPointIdsByLineIds(@Param("singletonList") List<String> singletonList);
}