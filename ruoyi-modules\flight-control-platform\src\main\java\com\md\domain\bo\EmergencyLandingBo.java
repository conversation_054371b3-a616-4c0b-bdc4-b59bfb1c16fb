package com.md.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 紧急备降请求业务对象
 *
 * <AUTHOR>
 */
@Data
public class EmergencyLandingBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 无人机ID
     */
    @NotBlank(message = "无人机ID不能为空")
    private String droneId;

    /**
     * 当前纬度
     */
    @NotNull(message = "当前纬度不能为空")
    private BigDecimal currentLatitude;

    /**
     * 当前经度
     */
    @NotNull(message = "当前经度不能为空")
    private BigDecimal currentLongitude;

    /**
     * 当前高度
     */
    @NotNull(message = "当前高度不能为空")
    private BigDecimal currentAltitude;
}