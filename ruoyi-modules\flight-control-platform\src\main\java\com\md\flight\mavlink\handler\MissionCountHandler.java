package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.MissionCount;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务计数处理器
 * 处理MAVLink任务计数消息
 */
@Slf4j
public class MissionCountHandler extends AbstractMavlinkHandler<MissionCount> {
    
    public MissionCountHandler(MavlinkCoreService coreService) {
        super(coreService);
    }
    
    @Override
    protected void doHandle(String droneId, MissionCount message) {
        log.info("收到无人机[{}]任务计数: count={}", droneId, message.count());
        coreService.handleMissionCount(droneId, message);
    }

    @Override
    public Class<MissionCount> getMessageType() {
        return MissionCount.class;
    }
}