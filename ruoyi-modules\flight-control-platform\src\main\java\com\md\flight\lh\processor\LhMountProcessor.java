package com.md.flight.lh.processor;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseArrivalData;
import com.md.service.ITaskCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 联合飞行器负载数据
 */
@Component
@Slf4j
public class LhMountProcessor implements LhBaseProcessor{

    @Override
    public void processMessage(String payload) {
        try {
        } catch (Exception e) {
            log.error("收到联合飞行器负载数据处理失败: {}", e.getMessage(), e);
        }
    }
}
