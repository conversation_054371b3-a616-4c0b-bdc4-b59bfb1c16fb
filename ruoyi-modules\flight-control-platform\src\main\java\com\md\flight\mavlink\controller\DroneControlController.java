package com.md.flight.mavlink.controller;

import com.md.flight.mavlink.model.DroneStatus;
import com.md.flight.mavlink.service.MavlinkConnectionService;
import com.md.flight.mavlink.service.MavlinkDroneControlService;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import cn.dev33.satoken.annotation.SaCheckPermission;
import io.dronefleet.mavlink.common.GlobalPositionInt;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 无人机控制接口
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/drone/control")
@Tag(name = "无人机控制接口")
@Slf4j
public class DroneControlController extends BaseController {

    private final MavlinkConnectionService connectionService;
    private final MavlinkDroneControlService controlService;

    /**
     * 连接到无人机
     */
    @Log(title = "无人机连接", businessType = BusinessType.OTHER)
    @PostMapping("/connect")
    @SaCheckPermission("drone:control:connect")
    @Operation(summary = "连接到无人机")
    @RepeatSubmit
    public R<Void> connect(@Parameter(description = "主机地址", required = true) @RequestParam String host,
        @Parameter(description = "端口", required = true) @RequestParam int port,
        @Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            connectionService.connect(droneId, host, port);
            return R.ok("连接成功");
        } catch (Exception e) {
            log.error("连接无人机失败", e);
            return R.fail("连接失败: " + e.getMessage());
        }
    }

    /**
     * 断开连接
     */
    @Log(title = "无人机断开连接", businessType = BusinessType.OTHER)
    @PostMapping("/disconnect")
    @SaCheckPermission("drone:control:connect")
    @Operation(summary = "断开与无人机的连接")
    @RepeatSubmit
    public R<Void> disconnect(@Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            connectionService.disconnect(droneId);
            return R.ok("已断开连接");
        } catch (Exception e) {
            log.error("断开连接失败", e);
            return R.fail("断开连接失败: " + e.getMessage());
        }
    }

    /**
     * 检查是否已连接
     */
    @GetMapping("/connected")
    @SaCheckPermission("drone:status:query")
    @Operation(summary = "检查是否已连接到无人机")
    public R<Boolean> isConnected(@Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        return R.ok(connectionService.isConnected(droneId));
    }

    /**
     * 解锁无人机
     */
    @Log(title = "无人机解锁", businessType = BusinessType.OTHER)
    @PostMapping("/arm")
    @SaCheckPermission("drone:control:arm")
    @Operation(summary = "解锁无人机")
    @RepeatSubmit
    public R<Void> arm(
        @Parameter(description = "是否强制解锁", required = false) @RequestParam(defaultValue = "false") boolean force,
        @Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            controlService.arm(droneId, force);
            return R.ok("解锁命令已发送");
        } catch (Exception e) {
            log.error("无人机解锁失败", e);
            return R.fail("解锁失败: " + e.getMessage());
        }
    }

    /**
     * 上锁无人机
     */
    @Log(title = "无人机上锁", businessType = BusinessType.OTHER)
    @PostMapping("/disarm")
    @SaCheckPermission("drone:control:arm")
    @Operation(summary = "上锁无人机")
    @RepeatSubmit
    public R<Void> disarm(@Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            controlService.disarm(droneId);
            return R.ok("上锁命令已发送");
        } catch (Exception e) {
            log.error("无人机上锁失败", e);
            return R.fail("上锁失败: " + e.getMessage());
        }
    }

    /**
     * 起飞
     */
    @Log(title = "无人机起飞", businessType = BusinessType.OTHER)
    @PostMapping("/takeoff")
    @SaCheckPermission("drone:control:takeoff")
    @Operation(summary = "使无人机起飞")
    @RepeatSubmit
    public R<Void> takeoff(@Parameter(description = "目标高度(米)", required = true) @RequestParam float altitude,
        @Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            controlService.takeoff(droneId, altitude);
            return R.ok("起飞命令已发送");
        } catch (Exception e) {
            log.error("无人机起飞失败", e);
            return R.fail("起飞失败: " + e.getMessage());
        }
    }

    /**
     * 降落
     */
    @Log(title = "无人机降落", businessType = BusinessType.OTHER)
    @PostMapping("/land")
    @SaCheckPermission("drone:control:land")
    @Operation(summary = "使无人机降落")
    @RepeatSubmit
    public R<Void> land(@Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            controlService.land(droneId);
            return R.ok("降落命令已发送");
        } catch (Exception e) {
            log.error("无人机降落失败", e);
            return R.fail("降落失败: " + e.getMessage());
        }
    }

    /**
     * 返航
     */
    @Log(title = "无人机返航", businessType = BusinessType.OTHER)
    @PostMapping("/return-to-home")
    @SaCheckPermission("drone:control:rth")
    @Operation(summary = "使无人机返航")
    @RepeatSubmit
    public R<Void> returnToHome(@Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            controlService.returnToHome(droneId);
            return R.ok("返航命令已发送");
        } catch (Exception e) {
            log.error("无人机返航失败", e);
            return R.fail("返航失败: " + e.getMessage());
        }
    }

    /**
     * 设置飞行模式
     */
    @Log(title = "设置飞行模式", businessType = BusinessType.OTHER)
    @PostMapping("/set-mode")
    @SaCheckPermission("drone:control:mode")
    @Operation(summary = "设置无人机飞行模式")
    @RepeatSubmit
    public R<Void> setMode(@Parameter(description = "模式值", required = true) @RequestParam int mode,
        @Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            controlService.setMode(droneId, mode);
            return R.ok("设置飞行模式命令已发送");
        } catch (Exception e) {
            log.error("设置飞行模式失败", e);
            return R.fail("设置飞行模式失败: " + e.getMessage());
        }
    }

    /**
     * 指点飞行
     */
    @Log(title = "指点飞行", businessType = BusinessType.OTHER)
    @PostMapping("/fly-to")
    @SaCheckPermission("drone:control:flyto")
    @Operation(summary = "控制无人机飞行到指定位置")
    @RepeatSubmit
    public R<Void> flyTo(@Parameter(description = "目标纬度", required = true) @RequestParam BigDecimal latitude,
        @Parameter(description = "目标经度", required = true) @RequestParam BigDecimal longitude,
        @Parameter(description = "目标高度", required = true) @RequestParam BigDecimal altitude,
        @Parameter(description = "飞行速度", required = true) @RequestParam float speed,
        @Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            controlService.flyTo(droneId, latitude, longitude, altitude, speed);
            return R.ok("指点飞行命令已发送");
        } catch (Exception e) {
            log.error("指点飞行失败", e);
            return R.fail("指点飞行失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前位置
     */
    @GetMapping("/position")
    @SaCheckPermission("drone:status:query")
    @Operation(summary = "获取无人机当前位置")
    public R<Map<String, Object>> getCurrentPosition(
        @Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            GlobalPositionInt position = controlService.getCurrentPosition(droneId);
            if (position != null) {
                Map<String, Object> formattedData = controlService.getFormattedPositionData(droneId);
                return R.ok(formattedData);
            } else {
                return R.fail("无法获取位置信息");
            }
        } catch (Exception e) {
            log.error("获取位置信息失败", e);
            return R.fail("获取位置信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取无人机状态
     */
    @GetMapping("/status")
    @SaCheckPermission("drone:status:query")
    @Operation(summary = "获取无人机状态数据")
    public R<DroneStatus> getDroneStatus(
        @Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            DroneStatus status = connectionService.getDroneStatus(droneId);
            return R.ok(status);
        } catch (Exception e) {
            log.error("获取无人机状态失败", e);
            return R.fail("获取无人机状态失败: " + e.getMessage());
        }
    }

    /**
     * 重新连接
     */
    @Log(title = "重新连接无人机", businessType = BusinessType.OTHER)
    @PostMapping("/reconnect")
    @SaCheckPermission("drone:control:connect")
    @Operation(summary = "重新连接到无人机")
    @RepeatSubmit
    public R<Void> reconnect(@Parameter(description = "主机地址", required = true) @RequestParam String host,
        @Parameter(description = "端口", required = true) @RequestParam int port,
        @Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            // 先断开连接
            if (connectionService.isConnected(droneId)) {
                connectionService.disconnect(droneId);
            }
            // 重新连接
            connectionService.connect(droneId, host, port);
            return R.ok("重新连接成功");
        } catch (Exception e) {
            log.error("重新连接失败", e);
            return R.fail("重新连接失败: " + e.getMessage());
        }
    }

    /**
     * 基于机头方向的速度控制
     */
    @Log(title = "无人机相对机头速度控制", businessType = BusinessType.OTHER)
    @PostMapping("/relative-velocity")
    @SaCheckPermission("drone:control:velocity")
    @Operation(summary = "通过相对于机头方向的速度矢量控制无人机移动")
    public R<Void> moveRelativeToHeading(@Parameter(description = "前后速度，单位：米/秒，正值向前(机头方向)，负值向后",
            required = true) @RequestParam float vForward,
        @Parameter(description = "左右速度，单位：米/秒，正值向右，负值向左", required = true) @RequestParam float vRight,
        @Parameter(description = "上下速度，单位：米/秒，正值向上，负值向下", required = true) @RequestParam float vUp,
        @Parameter(description = "持续时间，单位：秒", required = true) @RequestParam float duration,
        @Parameter(description = "无人机ID", required = true) @RequestParam String droneId) {
        try {
            controlService.moveRelativeToHeading(droneId, vForward, vRight, vUp, duration);
            return R.ok("机头相对速度控制命令已发送");
        } catch (Exception e) {
            log.error("机头相对速度控制失败", e);
            return R.fail("机头相对速度控制失败: " + e.getMessage());
        }
    }

    /**
     * 设置偏航角
     */
    @Log(title = "设置无人机偏航角", businessType = BusinessType.OTHER)
    @PostMapping("/set-yaw")
    @SaCheckPermission("drone:control:yaw")
    @Operation(summary = "设置无人机偏航角，适合键盘控制")
    public R<Void> setYaw(@RequestParam("droneId") String droneId, @RequestParam("yawAngle") float yawAngle,
        @RequestParam("angularSpeed") float angularSpeed, @RequestParam("isRelative") boolean isRelative) {
        try {
            controlService.setYaw(droneId, yawAngle, angularSpeed, isRelative);
            return R.ok();
        } catch (Exception e) {
            log.error("无人机[{}]设置偏航角失败: {}", droneId, e.getMessage());
            return R.fail("无人机设置偏航角失败: " + e.getMessage());
        }
    }
}