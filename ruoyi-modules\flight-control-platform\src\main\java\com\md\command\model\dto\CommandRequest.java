package com.md.command.model.dto;

import com.md.command.constant.CommandType;
import com.md.command.core.DroneCommand;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

@Data
public class CommandRequest {
    @NotBlank(message = "无人机ID不能为空")
    private String droneId;

    @NotNull(message = "指令类型不能为空")
    private CommandType commandType;

    private Map<String, Object> parameters;

    private String commandId;

    private String taskId;

    public DroneCommand toCommand() {
        return new DroneCommand() {
            @Override
            public String getDroneId() {
                return droneId;
            }

            @Override
            public CommandType getCommandType() {
                return commandType;
            }

            @Override
            public Map<String, Object> getParameters() {
                return parameters;
            }

            @Override
            public String getCommandId() {
                return commandId;
            }

            @Override
            public String getTaskId() {
                return taskId;
            }
        };
    }
}
