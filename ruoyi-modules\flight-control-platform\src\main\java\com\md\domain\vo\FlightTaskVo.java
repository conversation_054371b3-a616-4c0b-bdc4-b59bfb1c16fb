package com.md.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.po.FlightTaskPoint;
import lombok.Data;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年01月13日 10:52
 */
@Data
public class FlightTaskVo {
    /**
     * 任务ID
     */
    private String id;

    /**
     * 飞控平台Id
     */
    private String flightControlNo;

    /**
     * 飞控平台名称
     */
    private String manufacturerName;

    /**
     * 航线ID
     */
    private String lineId;

    /**
     * 航线名称
     */
    private String lineName;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 飞行器SN
     */
    private String uavCode;

    /**
     * 飞手ID
     */
    private Long flyerId;

    /**
     * 飞手名称
     */
    private String flyerName;

    /**
     * 计划执飞时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightTime;

    /**
     * 开始时间-执飞开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginFlightTime;

    /**
     * 结束时间-执飞结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endFlightTime;

    /**
     * 开始时间-创建时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间-创建时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 开始时间-更新时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginUpdateTime;

    /**
     * 结束时间-更新时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endUpdateTime;

    /**
     * 执飞人ID
     */
    private Long operatorId;

    /**
     * 执飞人名称
     */
    private String operatorName;

    /**
     * 任务状态(0:待执行 1:执行中 2:已暂停 3:已恢复 4:已完成 5:执行失败 6:已取消)
     * 支持单个状态或逗号分隔的多个状态，如: "1" 或 "1,4"
     */
    private String taskStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建者名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 修改者名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "updateBy")
    private String updateByName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 飞行速度(m/s)
     */
    private BigDecimal flightSpeed;

    /**
     * 飞行高度(m)
     */
    private BigDecimal flightHeight;

    /**
     * 返航高度(m)
     */
    private BigDecimal returnHeight;

    /**
     * 任务结束后的动作(原地降落/返回起点等)
     */
    private String finishedAction;

    /**
     * 网络失联后动作(返航/继续执行)
     */
    private String missingAction;

    /**
     * 航点列表
     */
    private List<FlightTaskPoint> points;

    /**
     * 文件生成状态(0:未生成 1:生成中 2:已生成 3:生成失败)
     */
    private Integer fileGenerateStatus;

    /**
     * 航线文件名称
     */
    private String kmzFileName;

    /**
     * 航线文件存储路径
     */
    private String kmzFilePath;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileGenerateTime;

    /**
     * 文件生成错误信息
     */
    private String fileGenerateError;

    /**
     * 是否已上传(0:未上传 1:已上传)
     */
    private Integer isUploaded;

    /**
     * 协议类型（例如：MQTT、MAVLINK、SDK等）
     */
    private String protocolType;
}