package com.md.command.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.command.constant.ExecutorType;
import com.md.command.core.DroneCommand;
import com.md.command.model.dto.CommandResult;
import com.md.command.service.DroneCommandLogService;
import com.md.domain.po.DroneCommandLog;
import com.md.mapper.DroneCommandLogMapper;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 无人机指令日志Service实现
 */
@Slf4j
@Service
public class DroneCommandLogServiceImpl extends ServiceImpl<DroneCommandLogMapper, DroneCommandLog> implements DroneCommandLogService {

    @Override
    public DroneCommandLog createCommandLog(DroneCommand command, ExecutorType executorType) {
        DroneCommandLog commandLog = new DroneCommandLog()
                .setCommandId(command.getCommandId())  // 使用传入的commandId
                .setDroneId(command.getDroneId())
                .setCommandType(command.getCommandType())
                .setExecutorType(executorType)
                .setParameters(JSON.toJSONString(command.getParameters()))
                .setExecutionStatus(0)  // 执行中
                .setCreateTime(DateUtils.getNowDate())
                .setCreateBy(LoginHelper.getUsername());

        save(commandLog);
        return commandLog;
    }

    @Override
    public void updateCommandResult(String commandId, CommandResult result, long executionTime) {
        // 先查询现有记录
        DroneCommandLog existingLog = getOne(
            new LambdaQueryWrapper<DroneCommandLog>()
                .eq(DroneCommandLog::getCommandId, commandId));

        if (existingLog == null) {
            log.warn("未找到对应的命令日志记录: commandId={}", commandId);
            return;
        }

        DroneCommandLog update = new DroneCommandLog();
        
        // 检查是否是超时消息
        boolean isTimeout = !result.isSuccess() && result.getMessage() != null && 
                           (result.getMessage().contains("超时") || 
                            result.getMessage().toLowerCase().contains("timeout"));
        
        if (isTimeout) {
            // 超时状态 - 使用特殊状态码3表示
            update.setExecutionStatus(3);  // 3:超时
        } else {
            // 正常成功或失败状态
            update.setExecutionStatus(result.isSuccess() ? 1 : 2);  // 1:成功 2:失败
        }

        // 设置错误消息（无论是失败还是超时）
        if (!result.isSuccess()) {
            String newErrorMessage = result.getMessage();
            if (StringUtils.hasText(existingLog.getErrorMessage())) {
                update.setErrorMessage(existingLog.getErrorMessage() + "\n" + newErrorMessage);
            } else {
                update.setErrorMessage(newErrorMessage);
            }
        }

        // 如果有新的响应数据，追加到现有数据后面
        if (result.getData() != null) {
            String newResponseData = JSON.toJSONString(result.getData());
            if (StringUtils.hasText(existingLog.getResponseData())) {
                update.setResponseData(existingLog.getResponseData() + "\n" + newResponseData);
            } else {
                update.setResponseData(newResponseData);
            }
        }

        update.setExecutionTime(executionTime)
              .setUpdateTime(DateUtils.getNowDate())
              .setUpdateBy(LoginHelper.getUsername());

        update(update, new LambdaQueryWrapper<DroneCommandLog>()
                .eq(DroneCommandLog::getCommandId, commandId));
    }

    @Override
    public List<DroneCommandLog> queryCommandLogs(DroneCommandLog query) {
        LambdaQueryWrapper<DroneCommandLog> wrapper = new LambdaQueryWrapper<DroneCommandLog>()
                .eq(query.getDroneId() != null, DroneCommandLog::getDroneId, query.getDroneId())
                .eq(query.getCommandType() != null, DroneCommandLog::getCommandType, query.getCommandType())
                .eq(query.getExecutorType() != null, DroneCommandLog::getExecutorType, query.getExecutorType())
                .eq(query.getExecutionStatus() != null, DroneCommandLog::getExecutionStatus, query.getExecutionStatus())
                .orderByDesc(DroneCommandLog::getCreateTime);

        return list(wrapper);
    }
}
