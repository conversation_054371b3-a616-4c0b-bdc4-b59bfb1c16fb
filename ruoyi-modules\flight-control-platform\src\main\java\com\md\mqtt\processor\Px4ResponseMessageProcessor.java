package com.md.mqtt.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.command.model.dto.CommandResult;
import com.md.command.service.DroneCommandLogService;
import com.md.flight.execution.context.TaskExecutionInfo;
import com.md.flight.px4.service.Px4TaskStatusService;
import com.md.mqtt.exception.MqttProcessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * PX4无人机响应消息处理器
 * 处理来自PX4无人机的命令响应消息
 */
@Component
@Slf4j
public class Px4ResponseMessageProcessor extends AbstractMqttMessageProcessor {

    @Autowired
    private DroneCommandLogService commandLogService;

    @Autowired
    private Px4TaskStatusService taskStatusService;

    @Override
    protected String getTopicPattern() {
        return "px4/[^/]+/response|px4/[^/]+/error";  // 匹配响应和错误主题
    }

    @Override
    protected void doProcessMessage(String topic, String payload) {
        try {
            // 解析响应数据
            JSONObject responseData = JSON.parseObject(payload);
            String commandId = responseData.getString("commandId");
            String droneId = responseData.getString("droneId");
            boolean success = responseData.getBooleanValue("success");
            String message = responseData.getString("message");

            if (commandId == null || droneId == null) {
                log.error("PX4响应消息格式错误，缺少必要字段: {}", payload);
                return;
            }

            log.info("收到PX4命令响应: topic={}, commandId={}, droneId={}, success={}, message={}", topic, commandId,
                droneId, success, message);

            // 更新命令执行结果
            CommandResult result = new CommandResult().setSuccess(success).setMessage(message).setCommandId(commandId);

            // 如果响应中包含额外数据，也添加到结果中
            JSONObject data = responseData.getJSONObject("data");
            if (data != null) {
                Map<String, Object> dataMap = new HashMap<>();
                for (String key : data.keySet()) {
                    dataMap.put(key, data.get(key));
                }
                result.setData(dataMap);
            }

            // 更新命令日志
            try {
                commandLogService.updateCommandResult(commandId, result, 0);
                log.debug("已更新PX4命令执行结果: commandId={}, success={}", commandId, success);
            } catch (Exception e) {
                log.error("更新PX4命令执行结果失败: commandId={}, error={}", commandId, e.getMessage(), e);
            }

            // 处理任务相关的响应
            handleTaskRelatedResponse(droneId, commandId, success, message, topic);
        } catch (Exception e) {
            log.error("处理PX4响应消息失败: topic={}, payload={}, error={}", topic, payload, e.getMessage(), e);
            throw new MqttProcessException("处理PX4响应消息失败", e);
        }
    }

    /**
     * 处理任务相关的响应
     */
    private void handleTaskRelatedResponse(String droneId, String commandId, boolean success, String message,
        String topic) {
        try {
            // 检查是否是错误主题
            boolean isError = topic.contains("/error");

            // 获取当前任务信息
            String taskId = taskStatusService.getCurrentTaskId(droneId);
            if (taskId == null) {
                log.debug("无人机没有正在执行的任务，跳过任务相关处理: droneId={}", droneId);
                return;
            }

            TaskExecutionInfo taskInfo = taskStatusService.getTaskInfo(taskId);
            if (taskInfo == null) {
                log.warn("未找到任务执行信息: taskId={}", taskId);
                return;
            }

            if (isError || !success) {
                log.warn("PX4任务命令执行失败: droneId={}, taskId={}, commandId={}, message={}", droneId, taskId,
                    commandId, message);

                // 如果是任务执行命令失败，强制停止任务
                taskStatusService.forceStopTask(droneId);
            } else {
                log.info("PX4任务命令执行成功: droneId={}, taskId={}, commandId={}", droneId, taskId, commandId);
            }

        } catch (Exception e) {
            log.error("处理任务相关响应失败: droneId={}, commandId={}, error={}", droneId, commandId, e.getMessage(),
                e);
        }
    }

    /**
     * 从主题中提取无人机ID
     * 例如：从 "px4/drone_001/response" 中提取 "drone_001"
     */
    private String extractDroneIdFromTopic(String topic) {
        try {
            String[] parts = topic.split("/");
            if (parts.length >= 3) {
                return parts[1];
            }
        } catch (Exception e) {
            log.error("提取无人机ID失败: {}", topic, e);
        }
        return null;
    }
}
