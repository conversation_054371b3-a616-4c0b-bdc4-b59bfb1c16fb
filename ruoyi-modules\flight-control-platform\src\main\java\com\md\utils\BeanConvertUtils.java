package com.md.utils;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 对象转换工具类，支持分页和非分页的对象转换
 */
public class BeanConvertUtils {

    /**
     * 转换对象列表，自动处理分页，支持自定义转换逻辑
     *
     * @param sourceList  源对象列表
     * @param targetClass 目标对象类型
     * @param customizer  自定义转换逻辑
     * @param <S>         源对象类型
     * @param <T>         目标对象类型
     * @return 转换后的对象列表（保留分页信息）
     */
    public static <S, T> List<T> convertList(List<S> sourceList, Class<T> targetClass, BiConsumer<S, T> customizer) {
        if (sourceList == null || sourceList.isEmpty()) {
            return new ArrayList<>();
        }

        // 处理分页情况
        if (sourceList instanceof Page) {
            Page<S> sourcePage = (Page<S>)sourceList;
            Page<T> targetPage = new Page<>();

            // 复制分页信息
            targetPage.setTotal(sourcePage.getTotal());
            targetPage.setCurrent(sourcePage.getCurrent());
            targetPage.setSize(sourcePage.getSize());

            // 转换数据
            List<T> records = sourceList.stream().map(source -> convert(source, targetClass, customizer))
                .collect(Collectors.toList());

            targetPage.setRecords(records);
            return (List<T>)targetPage;
        }

        // 非分页情况
        return sourceList.stream().map(source -> convert(source, targetClass, customizer)).collect(Collectors.toList());
    }

    /**
     * 转换单个对象
     *
     * @param source      源对象
     * @param targetClass 目标对象类型
     * @param <S>         源对象类型
     * @param <T>         目标对象类型
     * @return 转换后的对象
     */
    public static <S, T> T convert(S source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert object", e);
        }
    }

    /**
     * 转换单个对象，支持自定义转换逻辑
     *
     * @param source      源对象
     * @param targetClass 目标对象类型
     * @param customizer  自定义转换逻辑
     * @param <S>         源对象类型
     * @param <T>         目标对象类型
     * @return 转换后的对象
     */
    public static <S, T> T convert(S source, Class<T> targetClass, BiConsumer<S, T> customizer) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            if (customizer != null) {
                customizer.accept(source, target);
            }
            return target;
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert object", e);
        }
    }
}
