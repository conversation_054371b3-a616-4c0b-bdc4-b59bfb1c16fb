package com.md.flight.px4.service;

import com.md.flight.execution.context.TaskExecutionInfo;
import com.md.enums.TaskStatusEnum;

/**
 * PX4任务状态服务接口
 * 提供PX4任务状态查询和管理功能
 */
public interface Px4TaskStatusService {

    /**
     * 获取无人机当前执行的任务ID
     *
     * @param droneId 无人机ID
     * @return 任务ID，如果没有执行任务则返回null
     */
    String getCurrentTaskId(String droneId);

    /**
     * 获取任务执行信息
     *
     * @param taskId 任务ID
     * @return 任务执行信息
     */
    TaskExecutionInfo getTaskInfo(String taskId);

    /**
     * 检查无人机是否正在执行任务
     *
     * @param droneId 无人机ID
     * @return 是否正在执行任务
     */
    boolean isTaskExecuting(String droneId);

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    TaskStatusEnum getTaskStatus(String taskId);

    /**
     * 获取所有活动任务数量
     *
     * @return 活动任务数量
     */
    int getActiveTaskCount();

    /**
     * 强制停止任务（用于异常情况）
     *
     * @param droneId 无人机ID
     */
    void forceStopTask(String droneId);
}
