package com.md.mqtt.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.md.constant.TaskCallbackConstants;
import com.md.flight.execution.context.TaskExecutionInfo;
import com.md.enums.TaskStatusEnum;
import com.md.flight.px4.constant.Px4Constants;
import com.md.flight.px4.service.Px4TaskStatusService;
import com.md.mqtt.event.DroneType;
import com.md.mqtt.exception.MqttProcessException;
import com.md.service.IFlightTaskService;
import com.md.service.ITaskCallbackService;
import com.md.utils.BusinessDistributedLockUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * PX4任务状态消息处理器
 * 处理来自PX4无人机的任务状态响应消息
 */
@Component
@Slf4j
public class Px4MissionStatusProcessor extends AbstractMqttMessageProcessor {

    @Autowired
    private Px4TaskStatusService taskStatusService;

    @Autowired
    private IFlightTaskService flightTaskService;

    @Autowired
    private ITaskCallbackService taskCallbackService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private BusinessDistributedLockUtils lockUtils;

    @Override
    protected String getTopicPattern() {
        // 使用Px4Constants构建主题模式
        return Px4Constants.TOPIC_PREFIX.replace("/", "") + "/[^/]+" + Px4Constants.TopicSuffix.MISSION_STATUS;
    }

    @Override
    protected void doProcessMessage(String topic, String payload) {
        try {
            log.info("收到PX4任务状态消息: topic={}, payload={}", topic, payload);

            // 解析MAVSDK格式的消息
            Map<String, Object> message = JSON.parseObject(payload, new TypeReference<Map<String, Object>>() {
            });

            // MAVSDK消息格式字段
            String droneId = (String)message.get("droneId");
            String timestamp = (String)message.get("timestamp");
            String status = (String)message.get("mission_status");
            Boolean missionInProgress = (Boolean)message.get("mission_in_progress");
            Boolean missionPaused = (Boolean)message.get("mission_paused");
            Integer currentWaypoint = (Integer)message.get("current_waypoint");
            Integer totalWaypoints = (Integer)message.get("total_waypoints");
            String error = (String)message.get("error");

            // 计算进度百分比
            Double progress = null;
            if (currentWaypoint != null && totalWaypoints != null && totalWaypoints > 0) {
                progress = (double)currentWaypoint / totalWaypoints;
            }

            log.info(
                "PX4任务状态更新: droneId={}, mission_status={}, mission_in_progress={}, waypoint={}/{}, progress={}%",
                droneId, status, missionInProgress, currentWaypoint, totalWaypoints, progress);

            // 处理任务状态更新
            if (droneId != null && status != null) {
                processTaskStatusUpdate(droneId, status, currentWaypoint, totalWaypoints, progress, missionInProgress,
                    error);
            }

        } catch (Exception e) {
            log.error("处理PX4任务状态消息失败: topic={}, payload={}, error={}", topic, payload, e.getMessage(), e);
            throw new MqttProcessException("处理PX4任务状态消息失败", e);
        }
    }

    /**
     * 处理任务状态更新
     */
    private void processTaskStatusUpdate(String droneId, String status, Integer currentWaypoint, Integer totalWaypoints,
        Double progress, Boolean missionInProgress, String error) {
        try {
            // 获取当前任务ID
            String taskId = taskStatusService.getCurrentTaskId(droneId);
            if (taskId == null) {
                log.warn("无人机没有正在执行的任务: droneId={}", droneId);
                return;
            }

            // 获取任务信息
            TaskExecutionInfo taskInfo = taskStatusService.getTaskInfo(taskId);
            if (taskInfo == null) {
                log.warn("未找到任务执行信息: taskId={}", taskId);
                return;
            }

            // 解析任务状态
            TaskStatusEnum taskStatus = parseTaskStatus(status, missionInProgress);
            if (taskStatus == null) {
                log.warn("无法解析任务状态: status={}, missionInProgress={}", status, missionInProgress);
                return;
            }

            // 更新任务状态管理器 - 检查返回值确保状态更新成功
            boolean updateSuccess = taskStatusService.updateTaskStatus(taskId, taskStatus, currentWaypoint, totalWaypoints, progress);
            if (!updateSuccess) {
                log.warn("任务状态更新失败，跳过后续操作避免数据不一致: taskId={}, droneId={}, status={}", taskId, droneId, taskStatus);
                return;
            }

            // 只有状态更新成功才执行后续操作
            updateDatabaseTaskStatus(taskInfo.getTaskName(), taskStatus);

            // 处理特殊状态
            handleSpecialStatus(droneId, taskId, taskInfo, taskStatus);

        } catch (Exception e) {
            log.error("处理任务状态更新失败: droneId={}, status={}, error={}", droneId, status, e.getMessage(), e);
        }
    }

    /**
     * 解析任务状态
     * 支持MAVSDK机载程序的完整状态格式
     */
    private TaskStatusEnum parseTaskStatus(String status, Boolean missionInProgress) {
        if (!StringUtils.hasText(status)) {
            return null;
        }

        return switch (status.toUpperCase()) {
            case Px4Constants.MissionStatus.PENDING, Px4Constants.MissionStatus.UNKNOWN -> TaskStatusEnum.PENDING;
            case Px4Constants.MissionStatus.EXECUTING, "RUNNING", "IN_PROGRESS" -> TaskStatusEnum.EXECUTING;
            case Px4Constants.MissionStatus.PAUSED, "SUSPENDED" -> TaskStatusEnum.PAUSED;
            case Px4Constants.MissionStatus.RESUMED -> TaskStatusEnum.RESUMED;
            case Px4Constants.MissionStatus.COMPLETED, "FINISHED", "SUCCESS" -> TaskStatusEnum.COMPLETED;
            case Px4Constants.MissionStatus.FAILED, "ERROR" -> TaskStatusEnum.FAILED;
            case Px4Constants.MissionStatus.CANCELLED, "CANCELED", "ABORTED" -> TaskStatusEnum.CANCELLED;
            default -> {
                // 如果状态字符串无法识别，根据missionInProgress字段判断
                if (missionInProgress != null) {
                    yield missionInProgress ? TaskStatusEnum.EXECUTING : TaskStatusEnum.PENDING;
                }
                yield null;
            }
        };
    }

    /**
     * 更新数据库中的任务状态
     */
    private void updateDatabaseTaskStatus(String taskName, TaskStatusEnum status) {
        try {
            boolean result = flightTaskService.updateTaskStatus(taskName, status.getCode(), "PX4_SYSTEM");
            if (result) {
                log.info("数据库任务状态更新成功: taskName={}, status={}", taskName, status.getInfo());
            } else {
                log.warn("数据库任务状态更新失败: taskName={}, status={}", taskName, status.getInfo());
            }
        } catch (Exception e) {
            log.error("更新数据库任务状态异常: taskName={}, status={}, error={}", taskName, status.getInfo(),
                e.getMessage(), e);
        }
    }

    /**
     * 处理特殊状态
     */
    private void handleSpecialStatus(String droneId, String taskId, TaskExecutionInfo taskInfo, TaskStatusEnum status) {
        try {
            switch (status) {
                case EXECUTING -> {
                    // 任务开始执行，安全发布事件（防重复）
                    boolean eventPublished = lockUtils.publishTaskExecutionEventSafely(eventPublisher, droneId, taskId, true, DroneType.PX4,
                        "EXECUTING");
                    if (!eventPublished) {
                        log.warn("PX4任务开始执行事件发布失败: droneId={}, taskId={}", droneId, taskId);
                    }
                }
                case PAUSED -> {
                    // 任务暂停
                    handleTaskPause(droneId, taskId, taskInfo);
                }
                case RESUMED -> {
                    // 任务恢复
                    handleTaskResume(droneId, taskId, taskInfo);
                }
                case COMPLETED -> {
                    // 任务完成
                    handleTaskCompletion(droneId, taskId, taskInfo);
                }
                case FAILED, CANCELLED -> {
                    // 任务失败或取消
                    handleTaskFailure(droneId, taskId, taskInfo, status);
                }
            }
        } catch (Exception e) {
            log.error("处理特殊状态失败: droneId={}, taskId={}, status={}, error={}", droneId, taskId, status.getInfo(),
                e.getMessage(), e);
        }
    }

    /**
     * 处理任务暂停
     */
    private void handleTaskPause(String droneId, String taskId, TaskExecutionInfo taskInfo) {
        try {
            log.info("PX4任务已暂停: droneId={}, taskId={}, taskName={}", droneId, taskId, taskInfo.getTaskName());

            // 更新任务状态管理器 - 检查返回值
            boolean updateSuccess = taskStatusService.updateTaskStatus(taskId, TaskStatusEnum.PAUSED, null, null, null);
            if (!updateSuccess) {
                log.warn("任务暂停状态更新失败，跳过后续操作: taskId={}, droneId={}", taskId, droneId);
                return;
            }

            // 只有状态更新成功才执行后续操作
            // 安全发布任务暂停事件（防重复）
            boolean eventPublished = lockUtils.publishTaskExecutionEventSafely(eventPublisher, droneId, taskId, false, DroneType.PX4, "PAUSED");
            if (!eventPublished) {
                log.warn("PX4任务暂停事件发布失败: droneId={}, taskId={}", droneId, taskId);
            }

            // 发送任务暂停回调
            try {
                taskCallbackService.sendTaskCallback(taskId, TaskCallbackConstants.CODE_SUCCESS, "PX4任务已暂停");
                log.info("PX4任务暂停回调已发送: taskId={}", taskId);
            } catch (Exception e) {
                log.error("发送PX4任务暂停回调失败: taskId={}, error={}", taskId, e.getMessage(), e);
            }

        } catch (Exception e) {
            log.error("处理PX4任务暂停失败: droneId={}, taskId={}, error={}", droneId, taskId, e.getMessage(), e);
        }
    }

    /**
     * 处理任务恢复
     */
    private void handleTaskResume(String droneId, String taskId, TaskExecutionInfo taskInfo) {
        try {
            log.info("PX4任务已恢复: droneId={}, taskId={}, taskName={}", droneId, taskId, taskInfo.getTaskName());

            // 更新任务状态管理器 - 检查返回值
            boolean updateSuccess = taskStatusService.updateTaskStatus(taskId, TaskStatusEnum.RESUMED, null, null, null);
            if (!updateSuccess) {
                log.warn("任务恢复状态更新失败，跳过后续操作: taskId={}, droneId={}", taskId, droneId);
                return;
            }

            // 只有状态更新成功才执行后续操作
            // 安全发布任务恢复事件（防重复）
            boolean eventPublished = lockUtils.publishTaskExecutionEventSafely(eventPublisher, droneId, taskId, true, DroneType.PX4, "RESUMED");
            if (!eventPublished) {
                log.warn("PX4任务恢复事件发布失败: droneId={}, taskId={}", droneId, taskId);
            }

            // 发送任务恢复回调
            try {
                taskCallbackService.sendTaskCallback(taskId, TaskCallbackConstants.CODE_SUCCESS, "PX4任务已恢复");
                log.info("PX4任务恢复回调已发送: taskId={}", taskId);
            } catch (Exception e) {
                log.error("发送PX4任务恢复回调失败: taskId={}, error={}", taskId, e.getMessage(), e);
            }

        } catch (Exception e) {
            log.error("处理PX4任务恢复失败: droneId={}, taskId={}, error={}", droneId, taskId, e.getMessage(), e);
        }
    }

    /**
     * 处理任务完成
     */
    private void handleTaskCompletion(String droneId, String taskId, TaskExecutionInfo taskInfo) {
        try {
            log.info("PX4任务执行完成: droneId={}, taskId={}, taskName={}", droneId, taskId, taskInfo.getTaskName());

            // 完成任务状态管理
            taskStatusService.completeTask(droneId, TaskStatusEnum.COMPLETED);

            // 安全发布任务完成事件（防重复）
            boolean eventPublished = lockUtils.publishTaskExecutionEventSafely(eventPublisher, droneId, taskId, false, DroneType.PX4,
                "COMPLETED");
            if (!eventPublished) {
                log.warn("PX4任务完成事件发布失败: droneId={}, taskId={}", droneId, taskId);
            }

            // 发送任务完成回调
            try {
                taskCallbackService.sendTaskCallback(taskId, TaskCallbackConstants.CODE_SUCCESS, "PX4任务执行成功");
                log.info("PX4任务完成回调已发送: taskId={}", taskId);
            } catch (Exception e) {
                log.error("发送PX4任务完成回调失败: taskId={}, error={}", taskId, e.getMessage(), e);
            }

            // 清理任务信息（延迟清理，给其他组件处理时间）
            new Thread(() -> {
                try {
                    Thread.sleep(5000); // 延迟5秒
                    taskStatusService.cleanupCompletedTask(taskId);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();

        } catch (Exception e) {
            log.error("处理PX4任务完成失败: droneId={}, taskId={}, error={}", droneId, taskId, e.getMessage(), e);
        }
    }

    /**
     * 处理任务失败
     */
    private void handleTaskFailure(String droneId, String taskId, TaskExecutionInfo taskInfo, TaskStatusEnum status) {
        try {
            log.warn("PX4任务执行失败: droneId={}, taskId={}, taskName={}, status={}", droneId, taskId,
                taskInfo.getTaskName(), status.getInfo());

            // 完成任务状态管理
            taskStatusService.completeTask(droneId, status);

            // 安全发布任务失败事件（防重复）
            String eventType = status == TaskStatusEnum.CANCELLED ? "CANCELLED" : "FAILED";
            boolean eventPublished = lockUtils.publishTaskExecutionEventSafely(eventPublisher, droneId, taskId, false, DroneType.PX4, eventType);
            if (!eventPublished) {
                log.warn("PX4任务{}事件发布失败: droneId={}, taskId={}", eventType, droneId, taskId);
            }

            // 发送任务失败回调
            try {
                String message = status == TaskStatusEnum.CANCELLED ? "PX4任务已取消" : "PX4任务执行失败";
                taskCallbackService.sendTaskCallback(taskId, TaskCallbackConstants.CODE_FAILURE, message);
                log.info("PX4任务失败回调已发送: taskId={}, message={}", taskId, message);
            } catch (Exception e) {
                log.error("发送PX4任务失败回调失败: taskId={}, error={}", taskId, e.getMessage(), e);
            }

            // 清理任务信息
            new Thread(() -> {
                try {
                    Thread.sleep(3000); // 延迟3秒
                    taskStatusService.cleanupCompletedTask(taskId);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();

        } catch (Exception e) {
            log.error("处理PX4任务失败失败: droneId={}, taskId={}, error={}", droneId, taskId, e.getMessage(), e);
        }
    }
}
