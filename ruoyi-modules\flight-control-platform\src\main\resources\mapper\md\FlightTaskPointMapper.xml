<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.FlightTaskPointMapper">
    <resultMap id="BaseResultMap" type="com.md.domain.po.FlightTaskPoint">
        <!--@mbg.generated-->
        <!--@Table flight_task_point-->
        <id column="id" property="id"/>
        <result column="line_id" property="lineId"/>
        <result column="point_index" property="pointIndex"/>
        <result column="latitude" property="latitude"/>
        <result column="longitude" property="longitude"/>
        <result column="altitude" property="altitude"/>
        <result column="create_time" property="createTime"/>
        <result column="speed" property="speed"/>
        <result column="is_emergency_landing_point" property="isEmergencyLandingPoint"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        line_id,
        point_index,
        latitude,
        longitude,
        altitude,
        create_time,
        speed,
        is_emergency_landing_point,
        tenant_id
    </sql>

    <insert id="batchInsertPoints">
        insert into flight_task_point (id, line_id, point_index, latitude, longitude, altitude, create_time, speed, is_emergency_landing_point, tenant_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.lineId}, #{item.pointIndex}, #{item.latitude}, #{item.longitude}, #{item.altitude},
            #{item.createTime}, #{item.speed}, #{item.isEmergencyLandingPoint}, #{item.tenantId})
        </foreach>
    </insert>

    <select id="selectEmergencyPointsByTaskId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from flight_task_point
        where line_id = #{taskId}
        and is_emergency_landing_point = 1
        order by point_index asc
    </select>
</mapper>