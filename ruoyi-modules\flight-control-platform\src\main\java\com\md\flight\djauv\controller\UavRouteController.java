package com.md.flight.djauv.controller;

import com.md.flight.djauv.domain.UavRouteReq;
import com.md.flight.djauv.service.UavRouteService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/djAuv/flight")
public class UavRouteController {

    @Resource
    private UavRouteService routeService;

    /**
     * 编辑kmz文件
     */
    @PostMapping("/updateKmz")
    public void updateKmz(@RequestBody UavRouteReq uavRouteReq) {
        routeService.updateKmz(uavRouteReq);
    }

    /**
     * 生成kmz文件
     */
    @PostMapping("/buildKmz")
    public void buildKmz(@RequestBody UavRouteReq uavRouteReq) {
        routeService.buildKmz(uavRouteReq, "航线kmz文件");
    }

}
