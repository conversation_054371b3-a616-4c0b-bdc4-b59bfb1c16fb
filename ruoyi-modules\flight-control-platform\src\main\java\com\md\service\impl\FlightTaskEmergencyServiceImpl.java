package com.md.service.impl;

import com.md.command.model.dto.CommandResult;
import com.md.domain.po.FlightTaskPoint;
import com.md.mapper.FlightTaskPointMapper;
import com.md.mqtt.event.EmergencyLandingEvent;
import com.md.service.IFlightTaskEmergencyService;
import com.md.utils.GeoUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 航线任务紧急处理服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FlightTaskEmergencyServiceImpl implements IFlightTaskEmergencyService {

    private final FlightTaskPointMapper flightTaskPointMapper;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public FlightTaskPoint findNearestEmergencyLandingPoint(String taskId, BigDecimal latitude, BigDecimal longitude) {
        // 获取任务的所有备降点
        List<FlightTaskPoint> emergencyPoints = flightTaskPointMapper.selectEmergencyPointsByTaskId(taskId);
        if (CollectionUtils.isEmpty(emergencyPoints)) {
            throw new ServiceException("当前任务没有设置备降点");
        }

        // 使用Haversine公式计算最近的备降点
        FlightTaskPoint nearestPoint = null;
        double minDistance = Double.MAX_VALUE;

        for (FlightTaskPoint point : emergencyPoints) {
            double distance = GeoUtils.calculateDistance(
                latitude.doubleValue(), 
                longitude.doubleValue(),
                point.getLatitude().doubleValue(), 
                point.getLongitude().doubleValue());
            
            if (distance < minDistance) {
                minDistance = distance;
                nearestPoint = point;
            }
        }

        return nearestPoint;
    }

    @Override
    public List<FlightTaskPoint> getAllEmergencyLandingPoints(String taskId) {
        // 使用专门的Mapper方法查询备降点
        List<FlightTaskPoint> emergencyPoints = flightTaskPointMapper.selectEmergencyPointsByTaskId(taskId);
        if (CollectionUtils.isEmpty(emergencyPoints)) {
            log.info("任务 {} 未设置备降点", taskId);
            return new ArrayList<>();
        }

        log.info("任务 {} 设置了 {} 个备降点", taskId, emergencyPoints.size());
        return emergencyPoints;
    }

    @Override
    public CommandResult executeEmergencyLanding(String taskId, String droneId, BigDecimal currentLatitude,
        BigDecimal currentLongitude, BigDecimal currentAltitude) {
        log.info("执行无人机紧急备降操作: 任务ID={}, 无人机ID={}, 当前位置=[{}, {}, {}]", 
            taskId, droneId, currentLatitude, currentLongitude, currentAltitude);
        
        try {
            // 查找最近的备降点
            FlightTaskPoint emergencyPoint = findNearestEmergencyLandingPoint(taskId, currentLatitude, currentLongitude);
            if (emergencyPoint == null) {
                log.error("未找到可用的备降点，任务ID: {}", taskId);
                throw new ServiceException("未找到任务中的备降点，请先在航线任务中设置备降点");
            }

            log.info("找到最近的备降点: 位置=[{}, {}, {}]", 
                emergencyPoint.getLatitude(), emergencyPoint.getLongitude(), emergencyPoint.getAltitude());

            // 发布紧急降落事件
            eventPublisher.publishEvent(
                new EmergencyLandingEvent(taskId, droneId, currentLatitude, currentLongitude, currentAltitude));

            // 返回初步成功结果，实际命令执行结果将由事件监听器处理
            return CommandResult.success("紧急降落指令已发送，正在执行");

        } catch (ServiceException e) {
            log.error("执行紧急备降失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("执行紧急备降时发生异常", e);
            throw new ServiceException("执行紧急备降失败: " + e.getMessage());
        }
    }
}