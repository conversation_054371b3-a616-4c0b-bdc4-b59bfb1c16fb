package com.md.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.constant.MqttConstants;
import com.md.domain.dto.FlightReportDTO;
import com.md.domain.dto.FlightReportDTO.FlightReportDTOBuilder;
import com.md.domain.dto.UavInfoDTO;
import com.md.domain.po.FlightPoint;
import com.md.enums.FlightStatusEnum;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.mapper.FlightPointMapper;
import com.md.service.IDroneStatusService;
import com.md.service.UavReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.rabbitmq.service.RabbitMQService;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.time.Duration;

/**
 * 飞行器报告服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UavReportServiceImpl implements UavReportService {

    private final RabbitMQService rabbitMQService;
    private final FlightPointMapper flightPointMapper;
    private final IDroneStatusService droneStatusService;

    @Value("${rabbitmq.flight-dynamics.exchange}")
    private String flightDynamicsExchange;

    @Value("${rabbitmq.flight-dynamics.routing-key}")
    private String flightDynamicsRoutingKey;

    // 用于存储各无人机的定时任务
    private final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    // 用于执行定时任务的线程池
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(5);


    /**
     * 起飞时上报飞行信息
     *
     * @param uavCode 机身码
     * @param lineId  航线id
     * @param type    飞行状态类型
     * @return 是否成功
     */
    @Override
    public boolean reportFlightInfo(String uavCode, String lineId, Integer type) {
        try {
            // 飞行记录编号Redis键
            final String orderIdKey = "uav:flight:orderId:" + uavCode;

            // 处理起飞事件
            if (type == FlightStatusEnum.TAKEOFF.getCode()) {
                return handleTakeoff(uavCode, lineId, orderIdKey);
            }
            // 处理降落事件
            else if (type == FlightStatusEnum.LAND.getCode()) {
                return handleLanding(uavCode, lineId, orderIdKey);
            }

            return true;
        } catch (Exception e) {
            log.error("上报飞行信息失败：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理起飞事件
     *
     * @param uavCode   无人机编码
     * @param lineId    航线ID
     * @param orderIdKey Redis中存储飞行记录编号的键
     * @return 是否成功
     */
    private boolean handleTakeoff(String uavCode, String lineId, String orderIdKey) {
        // 起飞时生成新的飞行记录编号
        String currentDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        String randomCode = generateRandomCode(8);
        final String orderID = uavCode + "-" + currentDate + "-" + randomCode;

        // 存储到Redis中，有效期24小时
        TenantHelper.ignore(() -> {
            RedisUtils.setCacheObject(orderIdKey, orderID, Duration.ofHours(24));
            return null;
        });

        log.info("无人机[{}]起飞，生成新的飞行记录编号: {}", uavCode, orderID);

        FlightPoint point = flightPointMapper.selectOne(Wrappers.<FlightPoint>lambdaQuery()
            .eq(FlightPoint::getLineId, lineId)
            .eq(FlightPoint::getPointIndex, 0));

        Double longitude = point.getLongitude() != null ? point.getLongitude().doubleValue() : 0.0;
        Double latitude = point.getLatitude() != null ? point.getLatitude().doubleValue() : 0.0;
        Double altitude = point.getAltitude() != null ? point.getAltitude().doubleValue() : 0.0;
        String flightStatus = FlightStatusEnum.TAKEOFF.getName();

        // 发送起飞状态消息
        FlightReportDTO takeoffReport = buildFlightReportDTO(orderID, uavCode, flightStatus,
            longitude, latitude, altitude,null,null);
        rabbitMQService.sendMessage(flightDynamicsExchange, flightDynamicsRoutingKey,
            JSONUtil.toJsonStr(takeoffReport));

        // 启动定时任务，每分钟获取无人机状态并发送
        startStatusReportTimer(uavCode, orderID, lineId);

        log.info("已启动无人机[{}]状态上报定时任务", uavCode);
        return true;
    }

    /**
     * 处理降落事件
     *
     * @param uavCode   无人机编码
     * @param lineId    航线ID
     * @param orderIdKey Redis中存储飞行记录编号的键
     * @return 是否成功
     */
    private boolean handleLanding(String uavCode, String lineId, String orderIdKey) {
        // 从Redis获取飞行记录编号
        String orderID = TenantHelper.ignore(() -> RedisUtils.getCacheObject(orderIdKey));

        if (orderID == null) {
            log.warn("无人机[{}]降落时未找到对应的飞行记录编号，将重新生成", uavCode);
            String currentDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
            String randomCode = generateRandomCode(8);
            orderID = uavCode + "-" + currentDate + "-" + randomCode;
        } else {
            log.info("无人机[{}]降落，使用已有飞行记录编号: {}", uavCode, orderID);
        }

        // 查询该航线的所有航点
        List<FlightPoint> points = flightPointMapper.selectPointsByLineId(lineId);
        // 获取最后一个航点（按pointIndex排序）
        FlightPoint lastPoint = points.stream()
            .filter(point -> point.getPointIndex() != null)
            .sorted((p1, p2) -> p2.getPointIndex().compareTo(p1.getPointIndex())) // 降序排列
            .findFirst()
            .orElse(null);

        Double longitude = 0.0;
        Double latitude = 0.0;
        Double altitude = 0.0;

        if (lastPoint != null) {
            // 获取航点的经纬度和高度
            longitude = lastPoint.getLongitude() != null ? lastPoint.getLongitude().doubleValue() : 0.0;
            latitude = lastPoint.getLatitude() != null ? lastPoint.getLatitude().doubleValue() : 0.0;
            altitude = lastPoint.getAltitude() != null ? lastPoint.getAltitude().doubleValue() : 0.0;
        }
        String flightStatus = FlightStatusEnum.LAND.getName();

        // 构建数据
        final String finalOrderID = orderID;
        FlightReportDTO landReport = buildFlightReportDTO(finalOrderID, uavCode, flightStatus,
            longitude, latitude, altitude, null,null);

        // 发送降落状态消息
        rabbitMQService.sendMessage(flightDynamicsExchange, flightDynamicsRoutingKey,
            JSONUtil.toJsonStr(landReport));

        // 停止定时任务
        stopStatusReportTimer(uavCode);

        // 删除Redis中的飞行记录编号
        TenantHelper.ignore(() -> {
            RedisUtils.deleteObject(orderIdKey);
            return null;
        });

        log.info("已停止无人机[{}]状态上报定时任务，并清除飞行记录编号", uavCode);
        return true;
    }

    /**
     * 启动状态上报定时任务
     *
     * @param uavCode 无人机编码
     * @param orderId 飞行记录编号
     * @param lineId  航线ID
     */
    private void startStatusReportTimer(String uavCode, String orderId, String lineId) {
        // 先停止之前的任务（如果有）
        stopStatusReportTimer(uavCode);

        // 获取飞行记录编号（使用传入的orderId，避免lambda中引用外部变量）
        final String flightOrderId = orderId;

        // 创建定时任务，每60秒执行一次
        ScheduledFuture<?> task = scheduler.scheduleAtFixedRate(() -> {
            try {
                // 从Redis获取无人机状态
                String redisKey = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + uavCode;
                String statusData = TenantHelper.ignore(() -> RedisUtils.getCacheObject(redisKey));

                if (statusData == null) {
                    log.warn("无人机[{}]状态数据不存在，跳过上报", uavCode);
                    return;
                }

                // 解析状态数据
                DroneStatus droneStatus = com.alibaba.fastjson2.JSON.parseObject(statusData, DroneStatus.class);
                if (droneStatus == null) {
                    log.warn("无人机[{}]状态数据解析失败，跳过上报", uavCode);
                    return;
                }

                // 构建飞行状态消息
                double longitude = droneStatus.getLongitude() != null ? droneStatus.getLongitude().doubleValue() : 0.0;
                double latitude = droneStatus.getLatitude() != null ? droneStatus.getLatitude().doubleValue() : 0.0;
                double altitude = droneStatus.getAbsoluteAltitude();
                double course = droneStatus.getHeading();
                double VS = droneStatus.getVerticalSpeed();

                // 构建并发送飞行中状态消息
                FlightReportDTO reportDTO = buildFlightReportDTO(
                    flightOrderId,
                    uavCode,
                    FlightStatusEnum.INFLIGHT.getName(),
                    longitude,
                    latitude,
                    altitude,
                    VS,
                    course
                );

                // 添加额外的飞行状态信息
                reportDTO.setVS((double) droneStatus.getVerticalSpeed());
                reportDTO.setGS((double) droneStatus.getGroundSpeed());
                reportDTO.setCourse((double) droneStatus.getHeading());
                reportDTO.setHeight((double) droneStatus.getRelativeAltitude());

                // 发送消息
                rabbitMQService.sendMessage(flightDynamicsExchange, flightDynamicsRoutingKey,
                    JSONUtil.toJsonStr(reportDTO));

                log.info("已上报无人机[{}]飞行中状态，位置=[{}, {}, {}]", uavCode, latitude, longitude, altitude);

            } catch (Exception e) {
                log.error("定时上报无人机[{}]状态失败：{}", uavCode, e.getMessage(), e);
            }
        }, 60, 60, TimeUnit.SECONDS);

        // 保存任务引用
        scheduledTasks.put(uavCode, task);
    }

    /**
     * 停止状态上报定时任务
     *
     * @param uavCode 无人机编码
     */
    private void stopStatusReportTimer(String uavCode) {
        ScheduledFuture<?> task = scheduledTasks.remove(uavCode);
        if (task != null && !task.isCancelled()) {
            task.cancel(false);
            log.info("已取消无人机[{}]状态上报定时任务", uavCode);
        }
    }

    /**
     * 构建飞行报告DTO对象
     */
    private FlightReportDTO buildFlightReportDTO(String orderID, String uavCode, String flightStatus,
                                                 Double longitude, Double latitude, Double altitude,Double VS,Double course) {
        return FlightReportDTO.builder()
            .orderID(orderID)
            .reqNo("")
            .flightStatus(flightStatus)
            .manufacturerID(uavCode)
            .manufacturer(null)
            .operatorID(null)
            .operator(null)
            .logo(null)
            .uasID("UAS-DEFAULT")
            .timeStamp(DateUtil.now())
            .SN(uavCode)
            .uasModel(null)
            .coordinate("1")
            .longitude(longitude)
            .latitude(latitude)
            .heightAltitype(null)
            .height(null)
            .altitude(altitude)
            .VS(VS)
            .GS(null)
            .course(course)
            .build();
    }

    /**
     * 生成指定长度的随机码（数字和字母组合）
     */
    private String generateRandomCode(int length) {
        String params = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder randomCode = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = (int) (Math.random() * params.length());
            randomCode.append(params.charAt(index));
        }
        return randomCode.toString();
    }
}
