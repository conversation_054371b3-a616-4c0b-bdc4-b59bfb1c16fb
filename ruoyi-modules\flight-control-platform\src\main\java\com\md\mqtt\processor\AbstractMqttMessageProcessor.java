package com.md.mqtt.processor;

import lombok.extern.slf4j.Slf4j;

/**
 * MQTT消息处理器抽象基类 提供基础的消息处理功能和Redis缓存支持
 */
@Slf4j
public abstract class AbstractMqttMessageProcessor implements MqttMessageProcessor {

    /**
     * 获取当前处理器支持的主题模式 子类需要实现此方法来指定其可以处理的主题格式
     *
     * @return 主题的正则表达式模式
     */
    protected abstract String getTopicPattern();

    /**
     * 判断是否可以处理指定的主题 通过将主题与主题模式进行匹配来确定
     *
     * @param topic MQTT消息主题
     * @return true-可以处理该主题，false-不能处理该主题
     */
    @Override
    public boolean canProcess(String topic) {
        return topic.matches(getTopicPattern());
    }

    /**
     * 处理消息的模板方法 子类应该重写此方法以实现具体的业务逻辑
     */
    @Override
    public void processMessage(String topic, String payload) {
        try {
            log.debug("收到消息: topic={}, payload={}", topic, payload);
            doProcessMessage(topic, payload);
        } catch (Exception e) {
            log.error("处理消息失败: topic={}, payload={}, error={}", topic, payload, e.getMessage(), e);
        }
    }

    /**
     * 具体的消息处理逻辑 子类必须实现此方法
     */
    protected abstract void doProcessMessage(String topic, String payload);
}
