package com.md.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.po.FlightLine;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 航线管理业务对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FlightLine.class, reverseConvertGenerate = false)
public class FlightLineBo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 飞控平台ID
     */
    @NotBlank(message = "飞控平台ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String flightControlNo;

    /**
     * 航线名称
     */
    @NotBlank(message = "航线名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String lineName;

    /**
     * 飞行速度(m/s)
     */
    @NotNull(message = "飞行速度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal flightSpeed;

    /**
     * 飞行高度(m)
     */
    @NotNull(message = "飞行高度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal flightHeight;

    /**
     * 围栏ID
     */
    private String fenceId;

    /**
     * 返航高度(m)
     */
    private BigDecimal returnHeight;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 网络失联后动作(返航/继续执行)
     */
    private String missingAction;

    /**
     * 任务结束后的动作(原地降落/返回起点等)
     */
    private String finishedAction;

    /**
     * 文件生成状态(0:未生成 1:生成中 2:已生成 3:生成失败)
     */
    private Integer fileGenerateStatus;

    /**
     * 航线文件名称
     */
    private String kmzFileName;

    /**
     * 航线文件存储路径
     */
    private String kmzFilePath;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 文件生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileGenerateTime;

    /**
     * 文件生成错误信息
     */
    private String fileGenerateError;

    /**
     * 是否已上传(0:未上传 1:已上传)
     */
    private Integer isUploaded;

    /**
     * 航点列表
     */
    private List<FlightPointBo> points;

    /**
     * 飞控厂家名称
     */
    private String manufacturerName;

    /**
     * 创建者名称
     */
    private String createByName;

    /**
     * 修改者名称
     */
    private String updateByName;

    /**
     * 查询开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 查询结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 修改开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startUpdateTime;

    /**
     * 修改结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endUpdateTime;

    /**
     * 租户编号
     */
    private String tenantId;
}
