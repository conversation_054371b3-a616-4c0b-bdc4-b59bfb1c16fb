package com.md.service;

/**
 * 任务回调服务接口
 * 负责向外部系统发送任务状态变更通知
 */
public interface ITaskCallbackService {
    
    /**
     * 发送任务状态回调
     * 
     * @param taskId 任务ID
     * @param code 状态码
     * @param msg 消息内容
     * @return 是否发送成功
     */
    boolean sendTaskCallback(String taskId, Integer code, String msg);
    
    /**
     * 发送任务状态回调（带额外数据）
     * 
     * @param taskId 任务ID
     * @param code 状态码
     * @param msg 消息内容
     * @param extraData 额外数据
     * @return 是否发送成功
     */
    boolean sendTaskCallback(String taskId, Integer code, String msg, Object extraData);
} 