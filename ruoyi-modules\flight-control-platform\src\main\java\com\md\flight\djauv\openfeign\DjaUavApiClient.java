package com.md.flight.djauv.openfeign;

import com.md.flight.djauv.common.HttpResultResponse;
import com.md.flight.djauv.openfeign.fallback.DjaUavApiFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * 大疆上云api 接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@FeignClient(name = "DjiUavApiClient", url = "${dji-uav.api.base-url}", fallbackFactory = DjaUavApiFallback.class)
public interface DjaUavApiClient {

    /**
     * 导入KMZ航线文件
     *
     * @param file        航线文件
     * @param workspaceId 工作空间ID
     * @param creator     创建者
     * @return 响应结果
     */
    @PostMapping(value = "/waylines/file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    HttpResultResponse importKmzFile(
            @RequestPart("file") MultipartFile file,
            @RequestParam(value = "workspaceId", defaultValue = "e3dea0f5-37f2-4d79-ae58-490af3228069") String workspaceId,
            @RequestParam(value = "creator", defaultValue = "adminPC") String creator
    );

    /**
     * 导出航线KMZ文件
     *
     * @param lineName 航线名称
     * @return 响应对象
     */
    @GetMapping("/waylines/file/export")
    String exportKmzFile(@RequestParam("lineName") String lineName);
}