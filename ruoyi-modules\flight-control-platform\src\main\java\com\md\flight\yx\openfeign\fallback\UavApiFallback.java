package com.md.flight.yx.openfeign.fallback;

import com.md.flight.yx.openfeign.UavApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UavA<PERSON>Fallback implements FallbackFactory<UavApiClient> {

    @Override
    public UavApiClient create(Throwable throwable) {
        return new UavApiClient() {
            @Override
            public String getDroneList(String request, String timestamp, String sign, String appId) {
                log.error("获取无人机列表失败", throwable);
                return null;
            }

            /**
             * 查询飞手列表
             *
             * @param request
             * @param timestamp
             * @param sign
             * @param appId
             * @return
             */
            @Override
            public String getFlyerList(String request, String timestamp, String sign, String appId) {
                log.error("获取飞手列表失败", throwable);
                return null;
            }

            @Override
            public String getLineList(String request, String timestamp, String sign, String appId) {
                log.error("获取航线列表失败", throwable);
                return null;
            }

            @Override
            public String getLineDatail(String timestamp, String sign, String request, String appId) {
                log.error("获取航线详情失败", throwable);
                return null;
            }
        };
    }
}