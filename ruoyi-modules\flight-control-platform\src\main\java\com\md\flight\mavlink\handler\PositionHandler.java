package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.GlobalPositionInt;
import lombok.extern.slf4j.Slf4j;

/**
 * 位置信息处理器
 */
@Slf4j
public class PositionHandler extends AbstractMavlinkHandler<GlobalPositionInt> {

    public PositionHandler(MavlinkCoreService coreService) {
        super(coreService);
    }

    @Override
    protected void doHandle(String droneId, GlobalPositionInt message) {
        // 保存最新的位置信息
        coreService.setLastPosition(droneId, message);

        // 只在非RTK模式下设置RTK海拔高度的近似值
        // 检查GPS信息中的fixType，如果不是RTK模式，才使用GlobalPositionInt中的高度
        if (coreService.getLastGpsInfo(droneId) == null || coreService.getLastGpsInfo(droneId).fixType().value() < 4) {
            // 使用绝对高度作为RTK海拔高度的近似值（仅在非RTK模式下）
            double absoluteAltitude = message.alt() / 1000.0; // 转换为米
            coreService.setRtkAltitude(droneId, absoluteAltitude);
        }

        log.info("无人机[{}]位置信息: lat={}, lon={}, alt={}, relative_alt={}",
            droneId,
            message.lat() / 1e7,
            message.lon() / 1e7,
            message.alt() / 1000f,
            message.relativeAlt() / 1000f);
    }

    @Override
    public Class<GlobalPositionInt> getMessageType() {
        return GlobalPositionInt.class;
    }
}