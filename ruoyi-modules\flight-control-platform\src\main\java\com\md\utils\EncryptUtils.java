package com.md.utils;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.domain.dto.ConfigInfo;
import com.md.domain.po.OpenApiConfig;
import com.md.domain.po.OpenApiTenantBinding;
import com.md.domain.po.SysApiConfig;
import com.md.domain.po.SysApiTenantBinding;
import com.md.mapper.OpenApiConfigMapper;
import com.md.mapper.OpenApiTenantBindingMapper;
import com.md.mapper.SysApiConfigMapper;
import com.md.mapper.SysApiTenantBindingMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * 加密工具类
 */
@RequiredArgsConstructor
@Component
public class EncryptUtils {

    private final OpenApiConfigMapper openApiConfigMapper;

    private final OpenApiTenantBindingMapper openApiTenantBindingMapper;

    private final SysApiConfigMapper sysApiConfigMapper;

    private final SysApiTenantBindingMapper sysApiTenantBindingMapper;

    private final AESUtil aesUtil;

    /**
     * 调度系统作为开放端时解密
     *
     * @param openUrl     开放路由地址
     * @param encryptData 加密数据
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @return 解密后的数据
     */
    public String parseOpenDecrypt(String openUrl, String encryptData, String openTenantId , String apiTenantId) {
        if (openUrl == null || encryptData == null || openTenantId == null || apiTenantId == null) {
            return null;
        }
        try {
            //根据开放路由地址查找对应的开放端信息
            OpenApiConfig openApiConfig = openApiConfigMapper.selectOne(
                Wrappers.<OpenApiConfig>lambdaQuery().eq(OpenApiConfig::getOpenAddress, openUrl));
            if (openApiConfig == null) {
                throw new RuntimeException("未找到对应的开放API配置");
            }

            //根据目标平台租户id和本平台租户id进行租户绑定关系查询
            OpenApiTenantBinding openApiTenantBinding = openApiTenantBindingMapper.selectOne(
                Wrappers.<OpenApiTenantBinding>lambdaQuery().eq(OpenApiTenantBinding::getApiId, openApiConfig.getId())
                    .eq(OpenApiTenantBinding::getBindTenantId, openTenantId)
                    .eq(OpenApiTenantBinding::getTargetTenantId, apiTenantId));
            if (openApiTenantBinding == null) {
                throw new RuntimeException("未找到绑定关系");
            }
            //获取到配置信息
            String configInfostr = openApiTenantBinding.getConfigInfo();
            ConfigInfo configInfo = JSON.parseObject(configInfostr, ConfigInfo.class);
            //根据密钥类型进行解密
            switch(configInfo.getEncryptType()){
                case "AES":
                    return aesUtil.decrypt(encryptData, configInfo.getEncryptKey());
                default:
                    return aesUtil.decrypt(encryptData, configInfo.getEncryptKey());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 调度系统作为开放端时加密
     *
     * @param openUrl     开放路由地址
     * @param encryptData 待加密数据
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @return 加密后的数据
     */
    public String parseAESEncrypt(String openUrl, String encryptData, String openTenantId ,String apiTenantId) {
        if (openUrl == null || encryptData == null || openTenantId == null || apiTenantId == null) {
            return null;
        }
        try {
            //根据开放路由地址在开放端管理中找到对应密钥
            OpenApiConfig openApiConfig = openApiConfigMapper.selectOne(
                Wrappers.<OpenApiConfig>lambdaQuery().eq(OpenApiConfig::getOpenAddress, openUrl));
            if (openApiConfig == null) {
                throw new RuntimeException("未找到对应的开放API配置");
            }

            //根据目标平台租户id和本平台租户id进行租户绑定关系查询
            OpenApiTenantBinding openApiTenantBinding = openApiTenantBindingMapper.selectOne(
                Wrappers.<OpenApiTenantBinding>lambdaQuery().eq(OpenApiTenantBinding::getApiId, openApiConfig.getId())
                    .eq(OpenApiTenantBinding::getBindTenantId, openTenantId)
                    .eq(OpenApiTenantBinding::getTargetTenantId, apiTenantId));
            if (openApiTenantBinding == null) {
                throw new RuntimeException("未找到绑定关系");
            }
            ConfigInfo configInfo = JSON.parseObject(openApiTenantBinding.getConfigInfo(), ConfigInfo.class);
            //根据密钥类型进行加密
            switch(configInfo.getEncryptType()){
                case "AES":
                    return aesUtil.encrypt(encryptData, configInfo.getEncryptKey());
                default:
                    return aesUtil.encrypt(encryptData, configInfo.getEncryptKey());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 调度系统作为调用端时AES加密
     *
     * @param targetPlatform 目标平台
     * @param encryptData    待加密数据
     * @param tenantId       本平台Id
     * @return 加密后的数据
     */
    public String parseApiAESEncrypt(String targetPlatform, String encryptData, String tenantId) {
        if (targetPlatform == null || encryptData == null || tenantId == null) {
            return null;
        }
        try {
            //根据目标平台查询平台配置信息
            SysApiConfig sysApiConfig = sysApiConfigMapper.selectOne(
                Wrappers.<SysApiConfig>lambdaQuery().eq(SysApiConfig::getPlateformCode, targetPlatform));
            if (sysApiConfig == null) {
                throw new RuntimeException("未找到对应平台");
            }
            //根据平台id和本平台租户id进行租户绑定关系查询
            SysApiTenantBinding sysApiTenantBinding = sysApiTenantBindingMapper.selectOne(
                Wrappers.<SysApiTenantBinding>lambdaQuery().eq(SysApiTenantBinding::getApiId, sysApiConfig.getId())
                    .eq(SysApiTenantBinding::getBindTenantId, tenantId));
            if (sysApiTenantBinding == null) {
                throw new RuntimeException("未找到绑定关系");
            }
            ConfigInfo configInfo = JSON.parseObject(sysApiTenantBinding.getConfigInfo(), ConfigInfo.class);

            return aesUtil.encrypt(encryptData, configInfo.getEncryptKey());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }



    /**
     * 亿航AES加密
     *
     * @param content  需要加密的内容
     * @param password 加密密码
     * @return 加密后的字符串
     */
    public static String encryptYiHang(String content, String password) {
        if (content == null || password == null) {
            return null;
        }
        try {
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(password.getBytes());
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            kgen.init(128, secureRandom);
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
            Cipher cipher = Cipher.getInstance("AES");// 创建密码器
            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8);
            cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化
            return parseByte2HexStr(cipher.doFinal(byteContent));//加密
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException |
            BadPaddingException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 亿航AES解密
     *
     * @param content  待解密内容
     * @param password 解密密钥
     * @return 解密后的字符串
     */
    public static String decryptYiHang(String content, String password) {
        if (content == null || password == null) {
            return null;
        }
        try {
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(password.getBytes());
            byte[] contentTemp = parseHexStr2Byte(content);
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            kgen.init(128, secureRandom);
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
            Cipher cipher = Cipher.getInstance("AES");// 创建密码器
            cipher.init(Cipher.DECRYPT_MODE, key);// 初始化
            return new String(cipher.doFinal(contentTemp), StandardCharsets.UTF_8);//解密
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将16进制转换为二进制
     *
     * @param hexStr 16进制字符串
     * @return 二进制数组
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr == null || hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte)(high * 16 + low);
        }
        return result;
    }

    /**
     * 将二进制转换成16进制
     *
     * @param buf 二进制数组
     * @return 16进制字符串
     */
    public static String parseByte2HexStr(byte[] buf) {
        if (buf == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (byte b : buf) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 兼容旧版方法，保持向后兼容性
     *
     * @deprecated 请使用 {@link #encryptYiHang(String, String)}
     */
    @Deprecated
    public static String encrypt(String content, String password) {
        return encryptYiHang(content, password);
    }

    /**
     * 兼容旧版方法，保持向后兼容性
     *
     * @deprecated 请使用 {@link #decryptYiHang(String, String)}
     */
    @Deprecated
    public static String decrypt(String content, String password) {
        return decryptYiHang(content, password);
    }
}
