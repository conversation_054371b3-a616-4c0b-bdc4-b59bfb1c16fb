package com.md.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.md.domain.bo.SysApiConfigBo;
import com.md.domain.po.SysApiConfig;
import com.md.domain.vo.SysApiConfigVo;
import com.md.mapper.SysApiConfigMapper;
import com.md.service.ISysApiConfigService;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 接口管理Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class SysApiConfigServiceImpl implements ISysApiConfigService {

    private final SysApiConfigMapper baseMapper;

    /**
     * 查询接口管理
     */
    @Override
    public SysApiConfigVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询接口管理列表
     */
    @Override
    public TableDataInfo<SysApiConfigVo> queryPageList(SysApiConfigBo bo, PageQuery pageQuery) {

        LambdaQueryWrapper<SysApiConfig> lqw = buildQueryWrapper(bo);
        Page<SysApiConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询接口管理列表
     */
    @Override
    public List<SysApiConfigVo> queryList(SysApiConfigBo bo) {
        LambdaQueryWrapper<SysApiConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysApiConfig> buildQueryWrapper(SysApiConfigBo bo) {
        LambdaQueryWrapper<SysApiConfig> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTargetPlatform()), SysApiConfig::getTargetPlatform, bo.getTargetPlatform());
        lqw.like(StringUtils.isNotBlank(bo.getUrl()), SysApiConfig::getUrl, bo.getUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysApiConfig::getStatus, bo.getStatus());
        lqw.eq(SysApiConfig::getDelFlag, "0");
        lqw.orderByAsc(SysApiConfig::getSort);
        return lqw;
    }

    /**
     * 新增接口管理
     */
    @Override
    public Boolean insertByBo(SysApiConfigBo bo) {
        SysApiConfig add = MapstructUtils.convert(bo, SysApiConfig.class);
        // 设置默认值
        if (StringUtils.isBlank(add.getStatus())) {
            add.setStatus("0"); // 默认为正常状态
            add.setDelFlag("0");
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改接口管理
     */
    @Override
    public Boolean updateByBo(SysApiConfigBo bo) {
        SysApiConfig update = MapstructUtils.convert(bo, SysApiConfig.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 批量删除接口管理
     */
    @Override
    public Boolean deleteWithValidByIds(Long id) {
        try {
            SysApiConfig config = new SysApiConfig();
            config.setId(id);
            config.setDelFlag("1"); // 设置为删除状态
            baseMapper.updateById(config);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 检查目标平台是否已存在
     *
     * @param targetPlatform 目标平台
     * @param excludeId 排除的ID（修改时用）
     * @return 如果存在则返回对应的SysApiConfig对象，不存在返回null
     */
    @Override
    public SysApiConfig checkTargetPlatformExists(String targetPlatform, Long excludeId) {
        LambdaQueryWrapper<SysApiConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SysApiConfig::getTargetPlatform, targetPlatform);
        queryWrapper.ne(SysApiConfig::getDelFlag, "1"); // 排除已删除的记录

        // 如果是更新操作，需要排除当前记录
        if (excludeId != null) {
            queryWrapper.ne(SysApiConfig::getId, excludeId);
        }

        return baseMapper.selectOne(queryWrapper);
    }

}
