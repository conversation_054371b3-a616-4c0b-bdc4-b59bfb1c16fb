package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 租户配置对象 tenant_configuration
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "platform_configuration")
public class PlatformConfiguration
{
    private static final long serialVersionUID = 1L;

    /** 租户配置ID */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** aes密钥 */
    private String aesEncrypted;

    /** 租户编号 */
    private String tenantId;

    /** 飞控平台 */
    private String platformInfo;

    /** 飞控平台名称 */
    private String platformName;
}
