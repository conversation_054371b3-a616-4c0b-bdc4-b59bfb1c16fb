package com.md.mapper;

import java.util.List;

import com.md.domain.po.PlatformInfo;
import com.md.domain.vo.PlatformInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 飞控平台Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Mapper
public interface PlatformInfoMapper extends BaseMapperPlus<PlatformInfo, PlatformInfoVo> {
    /**
     * 查询飞控平台
     *
     * @param id 飞控平台主键
     * @return 飞控平台
     */
    public PlatformInfo selectFkPlatformInfoById(Long id);

    /**
     * 查询飞控平台列表
     *
     * @param platformInfo 飞控平台
     * @return 飞控平台集合
     */
    public List<PlatformInfo> selectFkPlatformInfoList(PlatformInfo platformInfo);

    /**
     * 新增飞控平台
     *
     * @param platformInfo 飞控平台
     * @return 结果
     */
    public int insertFkPlatformInfo(PlatformInfo platformInfo);

    /**
     * 修改飞控平台
     *
     * @param platformInfo 飞控平台
     * @return 结果
     */
    public int updateFkPlatformInfo(PlatformInfo platformInfo);

    /**
     * 删除飞控平台
     *
     * @param id 飞控平台主键
     * @return 结果
     */
    public int deleteFkPlatformInfoById(Long id);

    /**
     * 批量删除飞控平台
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFkPlatformInfoByIds(Long[] ids);

    /**
     * 根据平台编号查询平台信息
     *
     * @param code
     * @return
     */
    PlatformInfo selectPlatformtByCode(@Param("code") String code);

    /**
     * 校验飞控编号是否唯一
     *
     * @param flightControlNo 飞控编号
     * @return 平台信息
     */
    PlatformInfo checkFlightControlNoUnique(@Param("flightControlNo") String flightControlNo);
}
