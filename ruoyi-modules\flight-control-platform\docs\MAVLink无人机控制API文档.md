# MavLink虚拟摇杆控制API文档

## 接口说明

该接口用于实现无人机的虚拟摇杆控制，支持相对于机头方向的速度控制、偏航角控制、云台控制和相机控制功能。

## 请求格式

### 请求URL

```
POST /command/execute
```

### 请求头

```
Content-Type: application/json
```

### 请求体

#### 1. 速度控制

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "VIRTUAL_STICK_CONTROL",
  "parameters": {
    "controlType": "relativeVelocity",
    "vForward": 0.0,
    "vRight": 0.0,
    "vUp": 0.0,
    "duration": 0.0
  }
}
```

#### 2. 偏航角控制

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "VIRTUAL_STICK_CONTROL",
  "parameters": {
    "controlType": "yaw",
    "yawAngle": 60.0,
    "isRelative": true,
    "angularSpeed": 30.0
  }
}
```

#### 3. 云台控制

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "GIMBAL_CONTROL",
  "parameters": {
    "pitch": -30.0,
    "yaw": 0.0,
    "isAbsolute": true
  }
}
```

#### 4. 相机控制 - 拍照

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "TAKE_PHOTO"
  }
}
```

#### 5. 相机控制 - 连续拍照

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "START_CONTINUOUS_PHOTOS",
    "interval": 2.0,
    "count": 10
  }
}
```

#### 6. 相机控制 - 停止连续拍照

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "STOP_CONTINUOUS_PHOTOS"
  }
}
```

#### 7. 相机控制 - 开始录像

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "START_VIDEO_RECORDING"
  }
}
```

#### 8. 相机控制 - 停止录像

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "STOP_VIDEO_RECORDING"
  }
}
```

### 参数说明

#### 1. 通用参数

| 参数名                    | 类型     | 必填                       | 说明                                                       |
|------------------------|--------|--------------------------|----------------------------------------------------------|
| droneId                | String | 是                        | 无人机ID                                                    |
| executorType           | String | 是                        | 执行器类型，固定为"MAVLINK"                                       |
| commandType            | String | 是                        | 命令类型，VIRTUAL_STICK_CONTROL、GIMBAL_CONTROL或CAMERA_CONTROL |
| parameters             | Object | 是                        | 控制参数对象                                                   |
| parameters.controlType | String | VIRTUAL_STICK_CONTROL时必填 | 控制类型，"relativeVelocity"或"yaw"                            |

#### 2. 速度控制参数（controlType = "relativeVelocity"）

| 参数名                 | 类型    | 必填 | 说明                      |
|---------------------|-------|----|-------------------------|
| parameters.vForward | Float | 是  | 前后速度（m/s），正值向前，负值向后     |
| parameters.vRight   | Float | 是  | 左右速度（m/s），正值向右，负值向左     |
| parameters.vUp      | Float | 是  | 上下速度（m/s），正值向上，负值向下     |
| parameters.duration | Float | 是  | 持续时间（秒），0表示持续控制直到发送停止命令 |

#### 3. 偏航角控制参数（controlType = "yaw"）

| 参数名                     | 类型      | 必填 | 说明                             |
|-------------------------|---------|----|--------------------------------|
| parameters.yawAngle     | Float   | 是  | 目标偏航角（度），范围：-180~180           |
| parameters.isRelative   | Boolean | 是  | 是否为相对角度，true=相对当前角度，false=绝对角度 |
| parameters.angularSpeed | Float   | 否  | 旋转速度（度/秒），默认30.0               |

#### 4. 云台控制参数（commandType = "GIMBAL_CONTROL"）

| 参数名                   | 类型      | 必填 | 说明                             |
|-----------------------|---------|----|--------------------------------|
| parameters.pitch      | Float   | 是  | 俯仰角（度），范围：-90~90，负值表示向下，正值表示向上 |
| parameters.yaw        | Float   | 是  | 偏航角（度），范围：-180~180，相对于机身的偏航角   |
| parameters.isAbsolute | Boolean | 是  | 是否为绝对角度，true=绝对角度，false=相对角度   |

#### 5. 相机控制参数（commandType = "CAMERA_CONTROL"）

| 参数名                 | 类型      | 必填                         | 说明                                                                                                           |
|---------------------|---------|----------------------------|--------------------------------------------------------------------------------------------------------------|
| parameters.action   | String  | 是                          | 相机动作，包括：TAKE_PHOTO、START_CONTINUOUS_PHOTOS、STOP_CONTINUOUS_PHOTOS、START_VIDEO_RECORDING、STOP_VIDEO_RECORDING |
| parameters.interval | Float   | START_CONTINUOUS_PHOTOS时必填 | 连续拍照间隔（秒）                                                                                                    |
| parameters.count    | Integer | START_CONTINUOUS_PHOTOS时必填 | 连续拍照张数，0表示无限拍摄直到收到停止命令                                                                                       |

## 使用说明

1. 速度控制是相对于无人机机头方向的：
    - vForward：控制前后移动，正值表示向前（机头方向），负值表示向后
    - vRight：控制左右移动，正值表示向右，负值表示向左
    - vUp：控制上下移动，正值表示向上，负值表示向下

2. 偏航角控制说明：
    - 相对角度模式（isRelative = true）：
        - 正值：顺时针旋转指定角度
        - 负值：逆时针旋转指定角度
    - 绝对角度模式（isRelative = false）：
        - 使用最短路径旋转到指定的绝对角度
        - 0度表示正北方向，90度表示正东方向

3. 云台控制说明：
    - pitch（俯仰角）：
        - 负值：向下倾斜（-90度表示垂直向下）
        - 正值：向上倾斜（90度表示垂直向上）
        - 0度：水平
    - yaw（偏航角）：
        - 相对于机身的旋转角度
        - 0度通常表示与机身方向一致
    - isAbsolute（绝对角度模式）：
        - true：设置为绝对角度
        - false：设置为相对当前角度的变化量

4. 相机控制说明：
    - 拍照：立即拍摄一张照片
    - 连续拍照：按指定间隔拍摄指定数量的照片
        - interval：拍照间隔时间（秒）
        - count：拍照数量，0表示无限拍摄
    - 停止连续拍照：终止当前的连续拍照任务
    - 开始录像：开始视频录制
    - 停止录像：结束视频录制并保存

5. 停止移动：
    - 发送所有速度分量为0的命令即可停止移动
   ```json
   {
       "droneId": "1581F5FHD241H00D7EEW",
       "executorType": "MAVLINK",
       "commandType": "VIRTUAL_STICK_CONTROL",
       "parameters": {
           "controlType": "relativeVelocity",
           "vForward": 0.0,
           "vRight": 0.0,
           "vUp": 0.0,
           "duration": 0.0
       }
   }
   ```

6. 持续时间说明：
    - duration = 0：持续控制直到发送停止命令
    - duration > 0：控制指定时间后自动停止

## 示例

### 1. 向前飞行1米/秒，持续5秒

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "VIRTUAL_STICK_CONTROL",
  "parameters": {
    "controlType": "relativeVelocity",
    "vForward": 1.0,
    "vRight": 0.0,
    "vUp": 0.0,
    "duration": 5.0
  }
}
```

### 2. 向右飞行0.5米/秒，持续控制

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "VIRTUAL_STICK_CONTROL",
  "parameters": {
    "controlType": "relativeVelocity",
    "vForward": 0.0,
    "vRight": 0.5,
    "vUp": 0.0,
    "duration": 0.0
  }
}
```

### 3. 顺时针旋转60度，速度30度/秒

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "VIRTUAL_STICK_CONTROL",
  "parameters": {
    "controlType": "yaw",
    "yawAngle": 60.0,
    "isRelative": true,
    "angularSpeed": 30.0
  }
}
```

### 4. 旋转到指定绝对角度90度（正东方向）

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "VIRTUAL_STICK_CONTROL",
  "parameters": {
    "controlType": "yaw",
    "yawAngle": 90.0,
    "isRelative": false,
    "angularSpeed": 30.0
  }
}
```

### 5. 控制云台向下倾斜30度（绝对角度）

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "GIMBAL_CONTROL",
  "parameters": {
    "pitch": -30.0,
    "yaw": 0.0,
    "isAbsolute": true
  }
}
```

### 6. 控制云台相对当前位置向右旋转15度

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "GIMBAL_CONTROL",
  "parameters": {
    "pitch": 0.0,
    "yaw": 15.0,
    "isAbsolute": false
  }
}
```

### 7. 拍摄一张照片

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "TAKE_PHOTO"
  }
}
```

### 8. 每2秒拍摄一张照片，共拍10张

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "START_CONTINUOUS_PHOTOS",
    "interval": 2.0,
    "count": 10
  }
}
```

### 9. 停止连续拍照

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "STOP_CONTINUOUS_PHOTOS"
  }
}
```

### 10. 开始录制视频

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "START_VIDEO_RECORDING"
  }
}
```

### 11. 停止录制视频

```json
{
  "droneId": "1581F5FHD241H00D7EEW",
  "executorType": "MAVLINK",
  "commandType": "CAMERA_CONTROL",
  "parameters": {
    "action": "STOP_VIDEO_RECORDING"
  }
}
```

## 注意事项

1. 速度控制是相对于机头方向的，不受无人机航向角变化的影响
2. 所有速度值的单位都是米/秒（m/s）
3. 云台控制和相机控制需要无人机具备相应的硬件设备
4. 连续拍照功能会占用系统资源，请合理设置拍照间隔和数量
5. 视频录制时请确保无人机有足够的存储空间
6. 偏航角控制时，建议使用合适的旋转速度（默认30度/秒），过快的旋转可能影响飞行稳定性
7. 云台控制时，请注意不同飞机的云台可能有不同的活动范围限制，超出范围的命令可能会被忽略
8. 对于没有云台的无人机，云台控制命令将不起作用 