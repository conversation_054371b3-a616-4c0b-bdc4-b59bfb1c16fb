package com.md.config;

import com.md.command.executor.decorator.DroneControlLockDecorator;
import com.md.command.core.DroneCommandExecutor;
import com.md.command.executor.mavlink.MavlinkCommandExecutor;
import com.md.command.executor.mqtt.DjiMqttCommandExecutor;
import com.md.utils.DroneLockUtil;
import org.dromara.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 无人机命令执行器配置
 */
@Configuration
public class DroneCommandConfig {

    @Bean
    @Primary
    @Qualifier("mqttCommandExecutorWithLock")
    public DroneCommandExecutor primaryDroneCommandExecutor(@Autowired DjiMqttCommandExecutor djiMqttCommandExecutor,
        @Autowired DroneLockUtil droneLockUtil, @Autowired ISysUserService sysUserService) {
        // 为MQTT执行器添加锁装饰器
        return new DroneControlLockDecorator(djiMqttCommandExecutor, droneLockUtil, sysUserService);
    }

    @Bean
    @Qualifier("mavlinkCommandExecutorWithLock")
    public DroneCommandExecutor mavlinkCommandExecutorWithLock(@Autowired MavlinkCommandExecutor mavlinkCommandExecutor,
        @Autowired DroneLockUtil droneLockUtil, @Autowired ISysUserService sysUserService) {
        // 为MAVLink执行器添加锁装饰器
        return new DroneControlLockDecorator(mavlinkCommandExecutor, droneLockUtil, sysUserService);
    }
}