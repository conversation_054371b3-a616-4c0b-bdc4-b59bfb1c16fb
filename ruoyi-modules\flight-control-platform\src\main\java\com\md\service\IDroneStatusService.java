package com.md.service;

import com.md.domain.bo.DroneStatusBo;
import com.md.domain.vo.DroneStatusVo;
import org.dromara.common.core.domain.R;

import java.util.List;
import java.util.Map;

/**
 * 无人机状态服务接口
 */
public interface IDroneStatusService {

    /**
     * 判断无人机是否在线
     *
     * @param droneId 无人机ID
     * @return true-在线，false-离线
     */
    boolean isDroneOnline(String droneId);

    /**
     * 获取所有在线无人机信息列表
     *
     * @return 在线无人机信息列表
     */
    List<DroneStatusVo> getOnlineDrones();

    /**
     * 批量查询无人机在线状态
     *
     * @param bo 包含无人机ID列表的业务对象
     * @return Map<无人机ID, 在线状态>
     */
    Map<String, Boolean> getDronesOnlineStatus(DroneStatusBo bo);

    /**
     * 获取无人机最新状态数据
     *
     * @param droneId 无人机ID
     * @return 状态数据，无数据时返回null
     */
    DroneStatusVo getDroneStatus(String droneId);

    /**
     * 批量获取无人机状态数据
     *
     * @param bo 包含无人机ID列表的业务对象
     * @return Map<无人机ID, 状态数据JSON字符串>
     */
    Map<String, String> getDronesStatus(DroneStatusBo bo);

    /**
     * 查询无人机虚拟摇杆状态
     *
     * @param droneId 无人机ID
     * @return 状态信息
     */
    R<DroneStatusVo> getVirtualStickStatus(String droneId);
}