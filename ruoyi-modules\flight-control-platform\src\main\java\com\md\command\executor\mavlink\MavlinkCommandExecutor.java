package com.md.command.executor.mavlink;

import com.md.command.constant.CommandType;
import com.md.command.core.DroneCommand;
import com.md.command.core.DroneCommandExecutor;
import com.md.command.model.dto.CommandResult;
import com.md.flight.mavlink.service.MavlinkDroneControlService;
import com.md.service.IFlightTaskExecutionService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.EnumMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.HashMap;

/**
 * MAVLink命令执行器
 * 负责将通用命令转发给MAVLink服务执行
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MavlinkCommandExecutor implements DroneCommandExecutor {

    private final MavlinkDroneControlService mavlinkDroneControlService;

    @Lazy
    private final IFlightTaskExecutionService flightTaskExecutionService;

    // 跟踪已开启虚拟摇杆的无人机
    private final Set<String> virtualStickEnabledDrones = ConcurrentHashMap.newKeySet();

    // 命令处理器映射
    private final Map<CommandType, CommandHandler> commandHandlers = new EnumMap<>(CommandType.class);

    /**
     * 命令处理器函数式接口
     */
    @FunctionalInterface
    private interface CommandHandler {
        CommandResult handle(DroneCommand command) throws Exception;
    }

    /**
     * 初始化命令处理器映射
     */
    @PostConstruct
    private void initCommandHandlers() {
        // 航线任务相关
        commandHandlers.put(CommandType.EXECUTE_MINIO_MISSION, this::executeFlightTask);

        // 虚拟摇杆控制
        commandHandlers.put(CommandType.VIRTUAL_STICK_CONTROL, this::handleVirtualStickControlWrapper);
        commandHandlers.put(CommandType.VIRTUAL_STICK_START, this::executeVirtualStickStartWrapper);
        commandHandlers.put(CommandType.VIRTUAL_STICK_STOP, this::executeVirtualStickStopWrapper);
        commandHandlers.put(CommandType.VIRTUAL_STICK_TAKEOFF, this::executeVirtualStickTakeoffWrapper);
        commandHandlers.put(CommandType.VIRTUAL_STICK_LAND, this::executeVirtualStickLandWrapper);
        commandHandlers.put(CommandType.VIRTUAL_STICK_EMERGENCY_STOP, this::executeVirtualStickEmergencyStopWrapper);
        commandHandlers.put(CommandType.VIRTUAL_STICK_NAVIGATE_TO_POINT, this::executeVirtualStickNavigateToPointWrapper);
        commandHandlers.put(CommandType.GIMBAL_CONTROL, this::executeGimbalControlWrapper);

        // 相机控制命令
        commandHandlers.put(CommandType.CAMERA_CONTROL, this::executeCameraControlWrapper);
        commandHandlers.put(CommandType.CAMERA_TAKE_PHOTO, this::executeTakePhotoWrapper);
        commandHandlers.put(CommandType.CAMERA_START_SHOOTING, this::executeStartContinuousPhotosWrapper);
        commandHandlers.put(CommandType.CAMERA_STOP_SHOOTING, this::executeStopContinuousPhotosWrapper);
        commandHandlers.put(CommandType.CAMERA_START_RECORDING, this::executeStartVideoRecordingWrapper);
        commandHandlers.put(CommandType.CAMERA_STOP_RECORDING, this::executeStopVideoRecordingWrapper);
    }

    @Override
    public CommandResult executeCommand(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            CommandType commandType = command.getCommandType();
            Map<String, Object> params = command.getParameters();

            log.info("收到MAVLink命令: droneId={}, commandType={}, params={}", droneId, commandType, params);

            // 查找对应的命令处理器
            CommandHandler handler = commandHandlers.get(commandType);
            if (handler != null) {
                log.debug("开始执行MAVLink命令: droneId={}, commandType={}", droneId, commandType);
                CommandResult result = handler.handle(command);
                log.info("MAVLink命令执行完成: droneId={}, commandType={}, 结果={}", droneId, commandType,
                    result.isSuccess() ? "成功" : "失败");
                return result;
            }

            log.warn("不支持的MAVLink命令类型: droneId={}, commandType={}", droneId, commandType);
            return CommandResult.failed("不支持的MAVLink命令类型: " + commandType);
        } catch (Exception e) {
            log.error("MAVLink命令执行失败: command={}, error={}", command, e.getMessage(), e);
            return CommandResult.failed("MAVLink命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 处理虚拟摇杆控制命令，根据控制类型分发到不同的处理方法
     */
    private CommandResult handleVirtualStickControl(String droneId, Map<String, Object> params) {
        log.info("收到MAVLink虚拟摇杆控制命令: droneId={}, params={}", droneId, params);

        // 检查是否为大疆格式的摇杆控制指令
        if (params != null && params.containsKey("leftStick") && params.containsKey("rightStick")) {
            try {
                // 解析左右摇杆参数
                Map<String, Object> leftStick = (Map<String, Object>)params.get("leftStick");
                Map<String, Object> rightStick = (Map<String, Object>)params.get("rightStick");

                if (leftStick != null && rightStick != null) {
                    // 提取摇杆值
                    float leftHorizontal = getStickValue(leftStick, "horizontal", 0);
                    float leftVertical = getStickValue(leftStick, "vertical", 0);
                    float rightHorizontal = getStickValue(rightStick, "horizontal", 0);
                    float rightVertical = getStickValue(rightStick, "vertical", 0);

                    log.info("解析摇杆值: leftH={}, leftV={}, rightH={}, rightV={}", leftHorizontal, leftVertical,
                        rightHorizontal, rightVertical);

                    // 检查是否为偏航角控制命令
                    // 当左摇杆水平值不为0，且其他值都为0或接近0时，视为偏航角控制
                    if (Math.abs(leftHorizontal) > 5 && Math.abs(leftVertical) < 5 && Math.abs(rightHorizontal) < 5 &&
                        Math.abs(rightVertical) < 5) {

                        log.info("检测到偏航角控制命令: leftHorizontal={}", leftHorizontal);

                        // 构造偏航角控制参数
                        Map<String, Object> yawParams = new HashMap<>();

                        // 将左摇杆水平值转换为偏航角
                        // 根据摇杆方向决定旋转方向，但固定旋转360度
                        float yawAngle = leftHorizontal > 0 ? 360f : -360f; // 固定旋转360度，根据摇杆方向决定顺时针或逆时针
                        float angularSpeed = Math.abs(leftHorizontal) * 0.3f + 10; // 角速度，根据摇杆值调整

                        yawParams.put("yawAngle", yawAngle);
                        yawParams.put("angularSpeed", angularSpeed);
                        yawParams.put("isRelative", true);

                        log.info("转换为偏航角控制: yawAngle={}, angularSpeed={}, isRelative=true", yawAngle,
                            angularSpeed);

                        // 调用偏航角控制方法
                        return executeSetYaw(droneId, yawParams);
                    }

                    // 原有的速度控制逻辑
                    // 右摇杆垂直值控制前后速度
                    float vForward = rightVertical / 100.0f * 5.0f; // 将摇杆值(-100到100)转换为速度(-5到5 m/s)

                    // 右摇杆水平值控制左右速度
                    float vRight = -rightHorizontal / 100.0f * 5.0f; // 添加负号修正方向，使其符合预期控制方向

                    // 左摇杆垂直值控制上下速度
                    float vUp = leftVertical / 100.0f * 5.0f;

                    // 持续时间设为0，表示持续发送直到新命令
                    float duration = 0.0f;

                    // 创建新的参数Map用于调用moveRelativeToHeading
                    Map<String, Object> velocityParams = new HashMap<>();
                    velocityParams.put("vForward", vForward);
                    velocityParams.put("vRight", vRight);
                    velocityParams.put("vUp", vUp);
                    velocityParams.put("duration", duration);

                    log.info("转换为速度控制: vForward={}, vRight={}, vUp={}", vForward, vRight, vUp);

                    // 调用速度控制方法
                    return executeMoveRelativeToHeading(droneId, velocityParams);
                }
            } catch (Exception e) {
                log.error("解析摇杆控制参数失败: {}", e.getMessage(), e);
                return CommandResult.failed("解析摇杆控制参数失败: " + e.getMessage());
            }
        }

        // 如果不是大疆格式，则使用原有的处理逻辑
        String controlType = getStringParam(params, "controlType", "");
        if ("relativeVelocity".equalsIgnoreCase(controlType)) {
            return executeMoveRelativeToHeading(droneId, params);
        } else if ("yaw".equalsIgnoreCase(controlType)) {
            return executeSetYaw(droneId, params);
        } else {
            return CommandResult.failed("未知的MAVLink控制类型: " + controlType);
        }
    }

    /**
     * 从摇杆参数中获取摇杆值，并转换为-100到100的范围
     */
    private float getStickValue(Map<String, Object> stickParams, String key, float defaultValue) {
        if (stickParams == null) {
            return defaultValue;
        }

        Object value = stickParams.get(key);
        if (value == null) {
            return defaultValue;
        }

        float stickValue;
        if (value instanceof Number) {
            stickValue = ((Number)value).floatValue();
        } else {
            try {
                stickValue = Float.parseFloat(value.toString());
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }

        // 确保值在-100到100的范围内
        return Math.max(-100, Math.min(100, stickValue));
    }

    /**
     * 执行相对于机头方向的速度控制命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeMoveRelativeToHeading(String droneId, Map<String, Object> params) {
        try {
            // 检查虚拟摇杆是否已开启
            if (requiresVirtualStickMode(droneId)) {
                log.warn("无人机未开启虚拟摇杆控制模式: droneId={}", droneId);
                return CommandResult.failed("请先开启虚拟摇杆控制模式");
            }

            // 提取参数
            float vForward = getFloatParam(params, "vForward", 0.0f);
            float vRight = getFloatParam(params, "vRight", 0.0f);
            float vUp = getFloatParam(params, "vUp", 0.0f);
            float duration = getFloatParam(params, "duration", 0.0f);

            log.info("执行MAVLink速度控制命令: droneId={}, vForward={}, vRight={}, vUp={}, duration={}", droneId,
                vForward, vRight, vUp, duration);

            // 调用MAVLink服务执行命令
            mavlinkDroneControlService.moveRelativeToHeading(droneId, vForward, vRight, vUp, duration);

            return CommandResult.success("相对机头方向的速度控制命令已发送");
        } catch (Exception e) {
            log.error("相对机头方向的速度控制命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("相对机头方向的速度控制命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行偏航角控制命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeSetYaw(String droneId, Map<String, Object> params) {
        try {
            // 检查虚拟摇杆是否已开启
            if (requiresVirtualStickMode(droneId)) {
                return CommandResult.failed("请先开启虚拟摇杆控制模式");
            }

            // 提取参数
            float yawAngle = getFloatParam(params, "yawAngle");
            float angularSpeed = getFloatParam(params, "angularSpeed", 30.0f);
            boolean isRelative = getBooleanParam(params, "isRelative", true);

            // 调用MAVLink服务执行命令
            mavlinkDroneControlService.setYaw(droneId, yawAngle, angularSpeed, isRelative);

            return CommandResult.success("偏航角控制命令已发送");
        } catch (Exception e) {
            log.error("偏航角控制命令执行失败", e);
            return CommandResult.failed("偏航角控制命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行开启虚拟摇杆控制命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeVirtualStickStart(String droneId, Map<String, Object> params) {
        try {
            log.info("收到MAVLink开启虚拟摇杆控制命令: droneId={}, params={}", droneId, params);

            // 检查是否已连接
            if (mavlinkDroneControlService.getCurrentPosition(droneId) == null) {
                log.warn("无人机未连接: droneId={}", droneId);
                return CommandResult.failed("无人机未连接");
            }

            // 切换到GUIDED模式，这是虚拟摇杆控制的前提
            log.info("切换无人机到GUIDED模式: droneId={}", droneId);
            mavlinkDroneControlService.setMode(droneId, 4); // 4 = GUIDED模式

            // 标记无人机已开启虚拟摇杆
            virtualStickEnabledDrones.add(droneId);
            log.info("已启用虚拟摇杆控制模式: droneId={}", droneId);

            return CommandResult.success("虚拟摇杆控制模式已启用");
        } catch (Exception e) {
            log.error("启用虚拟摇杆控制模式失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("启用虚拟摇杆控制模式失败: " + e.getMessage());
        }
    }

    /**
     * 执行停止虚拟摇杆控制命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeVirtualStickStop(String droneId, Map<String, Object> params) {
        try {
            // 停止所有速度控制
            stopMovement(droneId);

            // 移除无人机的虚拟摇杆状态
            virtualStickEnabledDrones.remove(droneId);

            return CommandResult.success("虚拟摇杆控制已停止");
        } catch (Exception e) {
            log.error("停止虚拟摇杆控制失败", e);
            return CommandResult.failed("停止虚拟摇杆控制失败: " + e.getMessage());
        } finally {
            // 确保在发生异常时也移除状态
            virtualStickEnabledDrones.remove(droneId);
        }
    }

    /**
     * 执行虚拟摇杆起飞命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeVirtualStickTakeoff(String droneId, Map<String, Object> params) {
        try {
            log.info("收到MAVLink虚拟摇杆起飞命令: droneId={}, params={}", droneId, params);

            // 检查虚拟摇杆是否已开启
            if (requiresVirtualStickMode(droneId)) {
                return CommandResult.failed("请先开启虚拟摇杆控制模式");
            }

            // 提取起飞高度参数，如果没有则使用默认值10.0米
            float altitude = 10.0f;
            if (params != null && params.containsKey("altitude")) {
                try {
                    Object altObj = params.get("altitude");
                    if (altObj instanceof Number) {
                        altitude = ((Number)altObj).floatValue();
                    } else if (altObj instanceof String) {
                        altitude = Float.parseFloat((String)altObj);
                    }
                } catch (Exception e) {
                    log.warn("解析高度参数失败，使用默认高度10.0米: {}", e.getMessage());
                }
            }

            log.info("执行MAVLink起飞命令: droneId={}, altitude={}米", droneId, altitude);

            // 执行起飞
            mavlinkDroneControlService.takeoff(droneId, altitude);

            return CommandResult.success("虚拟摇杆起飞命令已发送，目标高度: " + altitude + "米");
        } catch (Exception e) {
            log.error("虚拟摇杆起飞失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("虚拟摇杆起飞失败: " + e.getMessage());
        }
    }

    /**
     * 执行虚拟摇杆降落命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeVirtualStickLand(String droneId, Map<String, Object> params) {
        try {
            log.info("收到MAVLink虚拟摇杆降落命令: droneId={}, params={}", droneId, params);

            // 检查虚拟摇杆是否已开启
            if (requiresVirtualStickMode(droneId)) {
                log.warn("无人机未开启虚拟摇杆控制模式: droneId={}", droneId);
                return CommandResult.failed("请先开启虚拟摇杆控制模式");
            }

            log.info("执行MAVLink降落命令: droneId={}", droneId);

            // 执行降落
            mavlinkDroneControlService.land(droneId);

            return CommandResult.success("虚拟摇杆降落命令已发送");
        } catch (Exception e) {
            log.error("虚拟摇杆降落失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("虚拟摇杆降落失败: " + e.getMessage());
        }
    }

    /**
     * 执行虚拟摇杆紧急停止命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeVirtualStickEmergencyStop(String droneId, Map<String, Object> params) {
        try {
            // 检查虚拟摇杆是否已开启
            if (requiresVirtualStickMode(droneId)) {
                return CommandResult.failed("请先开启虚拟摇杆控制模式");
            }

            // 立即停止所有速度控制
            stopMovement(droneId);

            // 强制解锁电机（紧急停止）
            mavlinkDroneControlService.disarm(droneId);

            // 移除无人机的虚拟摇杆状态
            virtualStickEnabledDrones.remove(droneId);

            return CommandResult.success("虚拟摇杆紧急停止命令已发送");
        } catch (Exception e) {
            log.error("虚拟摇杆紧急停止失败", e);
            return CommandResult.failed("虚拟摇杆紧急停止失败: " + e.getMessage());
        } finally {
            // 确保在发生异常时也移除状态
            virtualStickEnabledDrones.remove(droneId);
        }
    }

    /**
     * 执行虚拟摇杆导航至点命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeVirtualStickNavigateToPoint(String droneId, Map<String, Object> params) {
        try {
            // 检查虚拟摇杆是否已开启
            if (requiresVirtualStickMode(droneId)) {
                return CommandResult.failed("请先开启虚拟摇杆控制模式");
            }

            // 提取目标位置参数
            BigDecimal latitude = new BigDecimal(getStringParam(params, "latitude", "0"));
            BigDecimal longitude = new BigDecimal(getStringParam(params, "longitude", "0"));
            BigDecimal altitude = new BigDecimal(getStringParam(params, "altitude", "0"));
            float speed = getFloatParam(params, "speed", 2.0f); // 默认2米/秒

            // 检查参数有效性
            if (latitude.compareTo(BigDecimal.ZERO) == 0 || longitude.compareTo(BigDecimal.ZERO) == 0) {
                return CommandResult.failed("无效的目标位置参数");
            }

            // 执行导航
            mavlinkDroneControlService.flyTo(droneId, latitude, longitude, altitude, speed);

            return CommandResult.success("虚拟摇杆导航命令已发送");
        } catch (Exception e) {
            log.error("虚拟摇杆导航失败", e);
            return CommandResult.failed("虚拟摇杆导航失败: " + e.getMessage());
        }
    }

    /**
     * 执行云台控制命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeGimbalControl(String droneId, Map<String, Object> params) {
        try {
            // 提取参数
            float pitch = getFloatParam(params, "pitch", 0.0f);
            float yaw = getFloatParam(params, "yaw", 0.0f);
            boolean isAbsolute = getBooleanParam(params, "isAbsolute", true);

            // 调用MAVLink服务执行命令
            mavlinkDroneControlService.controlGimbal(droneId, pitch, yaw, isAbsolute);

            return CommandResult.success("云台控制命令已发送");
        } catch (Exception e) {
            log.error("云台控制命令执行失败", e);
            return CommandResult.failed("云台控制命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行相机控制命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeCameraControl(String droneId, Map<String, Object> params) {
        try {
            String action = getStringParam(params, "action", "");

            switch (action.toUpperCase()) {
                case "TAKE_PHOTO":
                    return executeTakePhoto(droneId, params);
                case "START_CONTINUOUS_PHOTOS":
                    return executeStartContinuousPhotos(droneId, params);
                case "STOP_CONTINUOUS_PHOTOS":
                    return executeStopContinuousPhotos(droneId, params);
                case "START_VIDEO_RECORDING":
                    return executeStartVideoRecording(droneId, params);
                case "STOP_VIDEO_RECORDING":
                    return executeStopVideoRecording(droneId, params);
                default:
                    return CommandResult.failed("未知的相机动作: " + action);
            }
        } catch (Exception e) {
            log.error("相机控制命令执行失败", e);
            return CommandResult.failed("相机控制命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行拍照命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeTakePhoto(String droneId, Map<String, Object> params) {
        try {
            // 调用服务执行拍照
            mavlinkDroneControlService.takePhoto(droneId);
            return CommandResult.success("拍照命令已发送");
        } catch (Exception e) {
            log.error("拍照命令执行失败", e);
            return CommandResult.failed("拍照命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行开始连续拍照命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeStartContinuousPhotos(String droneId, Map<String, Object> params) {
        try {
            // 提取参数
            float interval = getFloatParam(params, "interval", 1.0f); // 默认1秒间隔
            int count = getIntParam(params, "count", 0); // 默认无限拍摄

            // 调用服务执行开始连续拍照
            mavlinkDroneControlService.startContinuousPhotos(droneId, interval, count);

            String countStr = count == 0 ? "无限" : String.valueOf(count);
            return CommandResult.success("已开始连续拍照，间隔: " + interval + "秒，数量: " + countStr);
        } catch (Exception e) {
            log.error("开始连续拍照命令执行失败", e);
            return CommandResult.failed("开始连续拍照命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行停止连续拍照命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeStopContinuousPhotos(String droneId, Map<String, Object> params) {
        try {
            // 调用服务执行停止连续拍照
            mavlinkDroneControlService.stopContinuousPhotos(droneId);
            return CommandResult.success("已停止连续拍照");
        } catch (Exception e) {
            log.error("停止连续拍照命令执行失败", e);
            return CommandResult.failed("停止连续拍照命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行开始录像命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeStartVideoRecording(String droneId, Map<String, Object> params) {
        try {
            // 调用服务执行开始录像
            mavlinkDroneControlService.startVideoRecording(droneId);
            return CommandResult.success("已开始录像");
        } catch (Exception e) {
            log.error("开始录像命令执行失败", e);
            return CommandResult.failed("开始录像命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行停止录像命令
     *
     * @param droneId 无人机ID
     * @param params  命令参数
     * @return 执行结果
     */
    private CommandResult executeStopVideoRecording(String droneId, Map<String, Object> params) {
        try {
            // 调用服务执行停止录像
            mavlinkDroneControlService.stopVideoRecording(droneId);
            return CommandResult.success("已停止录像");
        } catch (Exception e) {
            log.error("停止录像命令执行失败", e);
            return CommandResult.failed("停止录像命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取整数参数
     *
     * @param params       参数Map
     * @param name         参数名
     * @param defaultValue 默认值
     * @return 参数值
     */
    private int getIntParam(Map<String, Object> params, String name, int defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Number) {
            return ((Number)value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            log.warn("参数{}解析为整数失败，使用默认值{}", name, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 检查无人机是否需要先开启虚拟摇杆模式
     *
     * @param droneId 无人机ID
     * @return true-需要先开启虚拟摇杆模式，false-已开启虚拟摇杆模式
     */
    private boolean requiresVirtualStickMode(String droneId) {
        return !virtualStickEnabledDrones.contains(droneId);
    }

    /**
     * 获取浮点参数
     *
     * @param params       参数Map
     * @param name         参数名
     * @param defaultValue 默认值
     * @return 参数值
     */
    private float getFloatParam(Map<String, Object> params, String name, float defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Number) {
            return ((Number)value).floatValue();
        }
        try {
            return Float.parseFloat(value.toString());
        } catch (NumberFormatException e) {
            log.warn("参数{}解析为浮点数失败，使用默认值{}", name, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 获取必须的浮点参数，如果不存在则抛出异常
     *
     * @param params 参数Map
     * @param name   参数名
     * @return 参数值
     */
    private float getFloatParam(Map<String, Object> params, String name) {
        Object value = params.get(name);
        if (value == null) {
            throw new IllegalArgumentException("缺少必需参数: " + name);
        }
        if (value instanceof Number) {
            return ((Number)value).floatValue();
        }
        try {
            return Float.parseFloat(value.toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("参数 " + name + " 必须是数字");
        }
    }

    /**
     * 获取布尔参数
     *
     * @param params       参数Map
     * @param name         参数名
     * @param defaultValue 默认值
     * @return 参数值
     */
    private boolean getBooleanParam(Map<String, Object> params, String name, boolean defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Boolean) {
            return (Boolean)value;
        }
        return Boolean.parseBoolean(value.toString());
    }

    /**
     * 获取字符串参数
     *
     * @param params       参数Map
     * @param name         参数名
     * @param defaultValue 默认值
     * @return 参数值
     */
    private String getStringParam(Map<String, Object> params, String name, String defaultValue) {
        Object value = params.get(name);
        if (value == null) {
            return defaultValue;
        }
        return value.toString();
    }

    private void stopMovement(String droneId) {
        // 发送停止命令（所有速度分量为0）
        mavlinkDroneControlService.moveRelativeToHeading(droneId, 0, 0, 0, 0);
    }

    @Override
    public Set<String> getSupportedManufacturers() {
        return Set.of("MD");
    }

    @Override
    public String getSupportedProtocol() {
        return "MAVLINK";
    }

    @Override
    public int getPriority() {
        return 10; // 默认优先级
    }

    /**
     * 执行航线任务命令
     * MAVLink直接通过executeFlightTask执行任务
     *
     * @param command 无人机命令
     * @return 命令执行结果
     */
    private CommandResult executeFlightTask(DroneCommand command) {
        try {
            String droneId = command.getDroneId();

            // 从命令中获取任务ID
            String taskId = getTaskIdFromCommand(command);
            if (taskId == null) {
                log.error("未找到任务ID: droneId={}", droneId);
                return CommandResult.failed("未找到任务ID");
            }

            return executeFlightTaskInternal(droneId, taskId);
        } catch (Exception e) {
            log.error("执行MAVLink航线任务命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行MAVLink航线任务命令失败: " + e.getMessage());
        }
    }

    /**
     * 内部执行航线任务的方法
     *
     * @param droneId 无人机ID
     * @param taskId  任务ID
     * @return 命令执行结果
     */
    private CommandResult executeFlightTaskInternal(String droneId, String taskId) {
        try {
            log.info("执行MAVLink航线任务命令: droneId={}, taskId={}", droneId, taskId);

            // 使用MAVLink任务执行引擎执行任务
            log.info("开始通过MAVLink任务执行引擎执行航线任务: taskId={}", taskId);
            CompletableFuture<Boolean> future = flightTaskExecutionService.executeFlightTask(taskId);

            // 异步处理执行结果
            future.whenComplete((success, throwable) -> {
                if (throwable != null) {
                    log.error("MAVLink航线任务执行失败: taskId={}, error={}", taskId, throwable.getMessage(),
                        throwable);
                } else if (success) {
                    log.info("MAVLink航线任务执行成功: taskId={}", taskId);
                } else {
                    log.warn("MAVLink航线任务执行失败: taskId={}", taskId);
                }
            });

            return CommandResult.success("MAVLink航线任务已开始执行");
        } catch (Exception e) {
            log.error("执行MAVLink航线任务命令失败: droneId={}, taskId={}, error={}", droneId, taskId, e.getMessage(),
                e);
            return CommandResult.failed("执行MAVLink航线任务命令失败: " + e.getMessage());
        }
    }

    /**
     * 从命令中获取任务ID
     * 优先从命令的taskId字段获取，如果没有再从参数中获取
     *
     * @param command 无人机命令
     * @return 任务ID，如果未找到则返回null
     */
    private String getTaskIdFromCommand(DroneCommand command) {
        // 优先从命令的taskId字段获取
        String taskId = command.getTaskId();
        if (taskId != null && !taskId.trim().isEmpty()) {
            return taskId;
        }

        // 如果命令级别没有taskId，再从参数中获取
        if (command.getParameters() != null) {
            Map<String, Object> params = command.getParameters();
            Object taskIdObj = params.get("taskId");
            if (taskIdObj != null) {
                return taskIdObj.toString();
            }
        }

        return null;
    }

    // 包装器方法，将DroneCommand转换为原有的方法调用
    private CommandResult handleVirtualStickControlWrapper(DroneCommand command) {
        return handleVirtualStickControl(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeVirtualStickStartWrapper(DroneCommand command) {
        return executeVirtualStickStart(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeVirtualStickStopWrapper(DroneCommand command) {
        return executeVirtualStickStop(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeVirtualStickTakeoffWrapper(DroneCommand command) {
        return executeVirtualStickTakeoff(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeVirtualStickLandWrapper(DroneCommand command) {
        return executeVirtualStickLand(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeVirtualStickEmergencyStopWrapper(DroneCommand command) {
        return executeVirtualStickEmergencyStop(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeVirtualStickNavigateToPointWrapper(DroneCommand command) {
        return executeVirtualStickNavigateToPoint(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeGimbalControlWrapper(DroneCommand command) {
        return executeGimbalControl(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeCameraControlWrapper(DroneCommand command) {
        return executeCameraControl(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeTakePhotoWrapper(DroneCommand command) {
        return executeTakePhoto(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeStartContinuousPhotosWrapper(DroneCommand command) {
        return executeStartContinuousPhotos(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeStopContinuousPhotosWrapper(DroneCommand command) {
        return executeStopContinuousPhotos(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeStartVideoRecordingWrapper(DroneCommand command) {
        return executeStartVideoRecording(command.getDroneId(), command.getParameters());
    }

    private CommandResult executeStopVideoRecordingWrapper(DroneCommand command) {
        return executeStopVideoRecording(command.getDroneId(), command.getParameters());
    }
}