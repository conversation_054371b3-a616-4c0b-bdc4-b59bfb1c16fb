package com.md.service;

import com.md.domain.bo.FlightTrackPointBo;
import com.md.domain.vo.FlightTrackPointVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * 飞行轨迹服务接口 提供无人机飞行轨迹的记录、查询和管理功能
 */
public interface IFlightTrackService {

    /**
     * 保存飞行轨迹点 将无人机的实时位置、姿态等信息保存到数据库
     *
     * @param trackPointBo 轨迹点业务对象
     */
    void saveTrackPoint(FlightTrackPointBo trackPointBo);

    /**
     * 查询指定任务的所有轨迹点 按时间顺序返回轨迹点列表
     *
     * @param taskId 任务ID
     * @return 轨迹点视图对象列表
     */
    List<FlightTrackPointVo> getTaskTrackPoints(String taskId);

    /**
     * 查询指定任务的所有轨迹点（分页版本）
     *
     * @param taskId    任务ID
     * @param pageQuery 分页参数
     * @return 分页轨迹点视图对象列表
     */
    TableDataInfo<FlightTrackPointVo> getTaskTrackPointsPage(String taskId, PageQuery pageQuery);

    /**
     * 查询指定任务在给定时间范围内的轨迹点
     *
     * @param taskId    任务ID
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @return 符合条件的轨迹点视图对象列表
     */
    List<FlightTrackPointVo> getTaskTrackPointsByTimeRange(String taskId, Long startTime, Long endTime);

    /**
     * 查询指定任务在给定时间范围内的轨迹点（分页版本）
     *
     * @param taskId    任务ID
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @param pageQuery 分页参数
     * @return 分页轨迹点视图对象列表
     */
    TableDataInfo<FlightTrackPointVo> getTaskTrackPointsByTimeRangePage(String taskId, Long startTime, Long endTime,
        PageQuery pageQuery);

    /**
     * 查询指定无人机的所有轨迹点
     *
     * @param droneId 无人机ID
     * @return 轨迹点视图对象列表
     */
    List<FlightTrackPointVo> getDroneTrackPoints(String droneId);

    /**
     * 查询指定无人机的所有轨迹点（分页版本）
     *
     * @param droneId   无人机ID
     * @param pageQuery 分页参数
     * @return 分页轨迹点视图对象列表
     */
    TableDataInfo<FlightTrackPointVo> getDroneTrackPointsPage(String droneId, PageQuery pageQuery);

    /**
     * 查询指定任务和无人机的所有轨迹点
     *
     * @param taskId  任务ID
     * @param droneId 无人机ID
     * @return 轨迹点视图对象列表
     */
    List<FlightTrackPointVo> getTaskDroneTrackPoints(String taskId, String droneId);

    /**
     * 查询指定任务的最新轨迹点
     *
     * @param taskId 任务ID
     * @return 最新轨迹点视图对象，如果没有则返回null
     */
    FlightTrackPointVo getTaskLatestTrackPoint(String taskId);

    /**
     * 查询指定无人机的最新轨迹点
     *
     * @param droneId 无人机ID
     * @return 最新轨迹点视图对象，如果没有则返回null
     */
    FlightTrackPointVo getDroneLatestTrackPoint(String droneId);

    /**
     * 删除指定任务的所有轨迹点 用于清理历史数据
     *
     * @param taskId 任务ID
     */
    void deleteTrackPoints(String taskId);

    /**
     * 删除指定任务和无人机的所有轨迹点 用于清理特定无人机在特定任务中的历史轨迹数据
     *
     * @param taskId  任务ID
     * @param droneId 无人机ID
     */
    void deleteTrackPointsByTaskIdAndDroneId(String taskId, String droneId);
}