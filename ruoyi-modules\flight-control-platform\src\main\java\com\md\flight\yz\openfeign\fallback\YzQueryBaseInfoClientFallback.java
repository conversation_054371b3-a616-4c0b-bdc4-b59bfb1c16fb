package com.md.flight.yz.openfeign.fallback;

import com.md.flight.yz.domain.dto.YzBaseReqDto;
import com.md.flight.yz.openfeign.YzQueryBaseInfoClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 基本信息接口容错降级
 */
@Slf4j
@Component
public class YzQueryBaseInfoClientFallback implements FallbackFactory<YzQueryBaseInfoClient> {

    @Override
    public YzQueryBaseInfoClient create(Throwable throwable) {
        log.error("QueryBaseInfoClient异常原因:{}", throwable.getMessage(), throwable);
        return new YzQueryBaseInfoClient() {
            @Override
            public String queryBaseInfo(YzBaseReqDto baseReqDto, String token) {
                return "exception";
            }
        };
    }
}