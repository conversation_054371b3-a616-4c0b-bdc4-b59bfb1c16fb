package com.md.service;

import com.md.domain.po.PlatformConfiguration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月17日 10:36
 */
public interface IBaseInfoService {
    /**
     * 同步无人机
     */
    void syncUAV();

    /**
     * 同步飞手信息
     */
    void syncFlyer();

    /**
     * 同步航线信息
     */
    void syncFlightLine();

    /**
     * 航线上传
     */
    boolean buildKmzByFlightLine(String routeId);
}
