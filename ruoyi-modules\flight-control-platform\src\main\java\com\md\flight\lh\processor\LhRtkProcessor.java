package com.md.flight.lh.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.md.constant.MqttConstants;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseRtkData;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.flight.mavlink.model.DroneStatus.SatelliteInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;

/**
 * 联合飞行器 RTK 数据处理器
 * 处理联合飞机的RTK定位数据，包括：
 * - GPS定位类型和精度信息
 * - 经纬度坐标和高度信息
 * - 卫星数量和信号质量
 * - 速度和航向信息
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LhRtkProcessor implements LhBaseProcessor {

    @Value("${mqtt.redis.expire-seconds:20}")  // 默认20秒，与其他无人机配置一致
    private int expireSeconds;

    @Override
    public void processMessage(String payload) {
        try {
            log.debug("收到联合飞机 RTK 数据: {}", payload);

            // 消息转换
            LhResponse<LhResponseRtkData> lhResponse = JSON.parseObject(payload, new TypeReference<>() {
            });

            String uavCode = lhResponse.getGateway();
            DroneStatus droneStatus;

            // 使用与其他无人机相同的存储结构
            String redisKey = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + uavCode;

            // 获取Redis中的现有数据
            String statusData = TenantHelper.ignore(() -> RedisUtils.getCacheObject(redisKey));

            if (statusData == null) {
                droneStatus = new DroneStatus();
            } else {
                droneStatus = JSON.parseObject(statusData, DroneStatus.class);
            }

            // 执行 RTK 数据转换
            droneStatusSet(droneStatus, lhResponse.getData(), uavCode);

            // 转换为JSON字符串
            String jsonStatus = JSON.toJSONString(droneStatus);

            // 存储到Redis，设置过期时间与其他无人机保持一致
            TenantHelper.ignore(
                () -> RedisUtils.setCacheObject(redisKey, jsonStatus, Duration.ofSeconds(expireSeconds)));

            log.info("联合飞机 {} RTK 数据处理成功", uavCode);
        } catch (Exception e) {
            log.error("收到联合飞行器rtk数据处理失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 将 LhResponseRtkData 数据转换并设置到 DroneStatus 中
     *
     * @param droneStatus 无人机状态对象
     * @param rtkData     RTK 数据
     * @param uavCode     无人机编码
     */
    public static void droneStatusSet(DroneStatus droneStatus, LhResponseRtkData rtkData, String uavCode) {
        if (rtkData == null || droneStatus == null) {
            return;
        }

        // 设置在线状态 - 收到 RTK 数据说明设备在线
        droneStatus.setOnline(true);

        // 设置无人机序列号（如果尚未设置）
        if (droneStatus.getDroneSN() == null || droneStatus.getDroneSN().isEmpty()) {
            droneStatus.setDroneSN(uavCode);
        }

        // 设置位置信息
        if (rtkData.getLatitude() != null) {
            droneStatus.setLatitude(BigDecimal.valueOf(rtkData.getLatitude()));
        }
        if (rtkData.getLongitude() != null) {
            droneStatus.setLongitude(BigDecimal.valueOf(rtkData.getLongitude()));
        }
        if (rtkData.getAltitude() != null) {
            droneStatus.setAbsoluteAltitude(rtkData.getAltitude().floatValue());
            droneStatus.setRtkAltitude(rtkData.getAltitude());
        }

        // 设置 RTK 卫星数量（使用可见卫星数量）
        if (rtkData.getSatellite_visible() != null) {
            droneStatus.setRtkSatelliteCount(rtkData.getSatellite_visible());

            SatelliteInfo satelliteInfo = droneStatus.getSatelliteInfo();
            if (satelliteInfo == null) {
                satelliteInfo = new SatelliteInfo();
                droneStatus.setSatelliteInfo(satelliteInfo);
            }
            satelliteInfo.setMobileStation1Count(rtkData.getSatellite_visible());
        }

        // 设置 GPS 定位类型
        if (rtkData.getFix_type() != null) {
            droneStatus.setGpsFixType(rtkData.getFix_type());

            // 根据fix_type和精度信息判断RTK健康状态
            // fix_type: 6表示RTK固定解，5表示RTK浮点解，3表示3D定位，2表示2D定位，1表示无定位
            boolean isRtkHealthy = false;
            if (rtkData.getFix_type() >= 5) {
                // RTK固定解或浮点解，且精度较好时认为健康
                if (rtkData.getEph() != null && rtkData.getEph() < 1.0) {
                    isRtkHealthy = true;
                }
            }
            droneStatus.setRTKHealthy(isRtkHealthy);
        }

        // 设置速度信息
        if (rtkData.getVelocity() != null) {
            droneStatus.setGroundSpeed(rtkData.getVelocity().floatValue());
        }

        // 设置时间戳
        droneStatus.setLastUpdateTime(System.currentTimeMillis());

        log.debug("RTK 数据转换完成: 定位类型={}, 可见卫星数量={}, 水平精度={}, 位置=({}, {})", rtkData.getFix_type(),
            rtkData.getSatellite_visible(), rtkData.getEph(), rtkData.getLatitude(), rtkData.getLongitude());
    }
}
