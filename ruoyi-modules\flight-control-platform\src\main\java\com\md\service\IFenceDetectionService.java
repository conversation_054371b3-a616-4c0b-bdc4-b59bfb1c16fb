package com.md.service;

import com.md.domain.bo.FenceDetectionBo;
import com.md.domain.dto.FenceDetectionResult;
import com.md.domain.vo.FenceVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 围栏检测服务接口
 */
public interface IFenceDetectionService {
    /**
     * 检测无人机位置是否违反围栏规则
     *
     * @param bo 包含任务ID、无人机编码、位置信息的业务对象
     * @return 检测结果
     */
    FenceDetectionResult detectUavPosition(FenceDetectionBo bo);

    /**
     * 检测无人机位置是否违反围栏规则（兼容旧方法）
     *
     * @param taskId    任务ID
     * @param uavCode   无人机编码
     * @param longitude 经度
     * @param latitude  纬度
     * @param height    高度
     * @return 检测结果
     */
    FenceDetectionResult detectUavPosition(String taskId, String uavCode, BigDecimal longitude, BigDecimal latitude,
        BigDecimal height);

    /**
     * 加载任务相关的围栏到缓存
     *
     * @param taskId 任务ID
     */
    void loadFencesForTask(String taskId);

    /**
     * 从缓存中移除任务相关的围栏
     *
     * @param taskId 任务ID
     */
    void removeFencesForTask(String taskId);

    /**
     * 更新围栏缓存
     *
     * @param fenceId 围栏ID
     */
    void updateFenceCache(String fenceId);

    /**
     * 从缓存中移除围栏
     *
     * @param fenceId 围栏ID
     */
    void removeFenceFromCache(String fenceId);

    /**
     * 加载所有活跃围栏到缓存
     *
     * @return 加载的围栏数量
     */
    int loadAllActiveFencesToCache();

    /**
     * 清理未使用的围栏缓存
     */
    void cleanUnusedFenceCache();

    /**
     * 获取指定点位附近的围栏
     *
     * @param bo 包含位置和半径的业务对象
     * @return 围栏列表，包含围栏信息、区域信息和到指定点的距离
     */
    List<FenceVo> getNearbyFences(FenceDetectionBo bo);

    /**
     * 获取指定点位附近的围栏（兼容旧方法）
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param radius    半径（米），默认10000米
     * @return 围栏列表，包含围栏信息、区域信息和到指定点的距离
     */
    List<FenceVo> getNearbyFences(BigDecimal longitude, BigDecimal latitude, Long radius);

    /**
     * 获取指定点位附近的围栏（分页版本）
     *
     * @param bo        包含位置和半径的业务对象
     * @param pageQuery 分页参数
     * @return 分页围栏列表
     */
    TableDataInfo<FenceVo> getNearbyFencesPage(FenceDetectionBo bo, PageQuery pageQuery);

    /**
     * 获取指定点位附近的围栏（分页版本）（兼容旧方法）
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @param radius    半径（米），默认10000米
     * @param pageQuery 分页参数
     * @return 分页围栏列表
     */
    TableDataInfo<FenceVo> getNearbyFences(BigDecimal longitude, BigDecimal latitude, Long radius, PageQuery pageQuery);
}