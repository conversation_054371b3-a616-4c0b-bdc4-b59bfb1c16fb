package com.md.mapper;

import com.md.domain.dto.FlightLineDto;
import com.md.domain.po.FlightLine;
import com.md.domain.vo.FlightLineVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 航线管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public interface FlightLineMapper extends BaseMapperPlus<FlightLine, FlightLineVo> {
    /**
     * 查询航线管理
     *
     * @param id 航线管理主键
     * @return 航线管理
     */
    public FlightLine selectFlightLineById(String id);

    /**
     * 查询航线管理列表
     *
     * @param flightLine 航线管理
     * @return 航线管理集合
     */
    public List<FlightLineDto> selectFlightLineList(FlightLineDto flightLine);

    /**
     * 新增航线管理
     *
     * @param flightLine 航线管理
     * @return 结果
     */
    public int insertFlightLine(FlightLine flightLine);

    /**
     * 修改航线管理
     *
     * @param flightLine 航线管理
     * @return 结果
     */
    public int updateFlightLine(FlightLine flightLine);

    /**
     * 删除航线管理
     *
     * @param id 航线管理主键
     * @return 结果
     */
    public int deleteFlightLineById(String id);

    /**
     * 批量删除航线管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightLineByIds(String[] ids);
}