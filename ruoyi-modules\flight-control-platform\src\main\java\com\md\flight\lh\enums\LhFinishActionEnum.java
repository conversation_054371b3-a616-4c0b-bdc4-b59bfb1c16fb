package com.md.flight.lh.enums;

import lombok.Getter;

/**
 * LH航线完成动作枚举
 */
@Getter
public enum LhFinishActionEnum {
    GO_HOME("goHome", "gotoFirstWaypoint"),// 返航
    AUTO_LAND("autoLand", "autoLand"),// 降落
    ;

    // 成员变量
    private String name;
    private String code;

    // 构造方法
    private LhFinishActionEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    // 普通方法
    public static String getName(String code) {
        for (LhFinishActionEnum c : LhFinishActionEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    //获取枚举
    public static LhFinishActionEnum parseEnum(String code) {
        for (LhFinishActionEnum platformInfoEnum : LhFinishActionEnum.values()) {
            if (platformInfoEnum.getCode().equals(code)) {
                return platformInfoEnum;
            }
        }
        return null;
    }
}
