package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.md.command.model.dto.CommandResult;
import com.md.domain.bo.EmergencyLandingBo;
import com.md.domain.bo.FlightTaskBo;
import com.md.domain.po.FlightTaskPoint;
import com.md.domain.vo.FlightTaskVo;
import com.md.service.IFlightTaskService;
import com.md.service.IFlightTaskFileService;
import com.md.service.IFlightTaskExecutionService;
import com.md.service.IFlightTaskEmergencyService;
import com.md.service.IFlightTaskFenceService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 航线任务管理 - 兼容性控制器
 * 保持原有API路径可用，同时转发到新的专门控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/flight/task")
@Validated
public class FlightTaskCompatibilityController extends BaseController {

    private final IFlightTaskService flightTaskService;
    private final IFlightTaskFileService flightTaskFileService;
    private final IFlightTaskExecutionService flightTaskExecutionService;
    private final IFlightTaskEmergencyService flightTaskEmergencyService;
    private final IFlightTaskFenceService flightTaskFenceService;

    // ===== 核心CRUD操作 (保持原有路径) =====

    /**
     * 查询航线任务列表
     */
    @SaCheckPermission("flight:task:list")
    @GetMapping("/list")
    public TableDataInfo<FlightTaskVo> list(FlightTaskBo bo, PageQuery pageQuery) {
        return flightTaskService.selectFlightTaskList(bo, pageQuery);
    }

    /**
     * 导出航线任务列表
     */
    @SaCheckPermission("flight:task:export")
    @Log(title = "航线任务管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FlightTaskBo bo) {
        List<FlightTaskVo> list = flightTaskService.selectFlightTaskList(bo);
        ExcelUtil.exportExcel(list, "航线任务数据", FlightTaskVo.class, response);
    }

    /**
     * 获取航线任务详细信息
     */
    @SaCheckPermission("flight:task:query")
    @GetMapping(value = "/{id}")
    public R<FlightTaskVo> getInfo(@PathVariable("id") @NotBlank(message = "任务ID不能为空") String id) {
        return R.ok(flightTaskService.selectFlightTaskById(id));
    }

    /**
     * 新增航线任务
     */
    @SaCheckPermission("flight:task:add")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<String> add(@RequestBody @Validated(AddGroup.class) FlightTaskBo bo) {
        String taskId = flightTaskService.insertFlightTask(bo);
        return R.ok(taskId);
    }

    /**
     * 修改航线任务
     */
    @SaCheckPermission("flight:task:edit")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<String> edit(@RequestBody @Validated(EditGroup.class) FlightTaskBo bo) {
        String taskId = flightTaskService.updateFlightTask(bo);
        return R.ok(taskId);
    }

    /**
     * 删除航线任务
     */
    @SaCheckPermission("flight:task:remove")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable @NotEmpty(message = "任务ID不能为空") Long[] ids) {
        return toAjax(flightTaskService.deleteFlightTaskByIds(ids));
    }

    // ===== 文件操作 (保持原有路径，转发到新控制器) =====

    /**
     * 根据航线ID生成kmz文件
     * @deprecated 建议使用 /flight/task/file/buildKmzByFlightLine
     */
    @SaCheckPermission("flight:task:generateKmz")
    @RepeatSubmit
    @GetMapping(value = "/buildKmzByFlightLine")
    @Deprecated
    public R<Void> buildKmzByFlightLine(@RequestParam @NotBlank(message = "任务ID不能为空") String id) {
        log.info("兼容性API调用: buildKmzByFlightLine - 转发到文件服务");
        return toAjax(flightTaskFileService.buildKmzByFlightLine(id));
    }

    /**
     * 导出航线KMZ文件
     * @deprecated 建议使用 /flight/task/file/export/kmz
     */
    @GetMapping("/export/kmz")
    @SaCheckPermission("flight:task:export")
    @Deprecated
    public R<String> exportKmz(@RequestParam @NotBlank(message = "任务名称不能为空") String taskName,
        HttpServletResponse response) {
        log.info("兼容性API调用: exportKmz - 转发到文件服务");
        String fileUrl = flightTaskFileService.exportKmz(taskName, response);
        return R.ok(fileUrl);
    }

    /**
     * 复制航线任务
     * @deprecated 建议使用 /flight/task/file/copy/{taskId}
     */
    @SaCheckPermission("flight:task:add")
    @Log(title = "航线任务管理", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{taskId}")
    @Deprecated
    public R<String> copyTask(@PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId) {
        log.info("兼容性API调用: copyTask - 转发到文件服务");
        String newTaskId = flightTaskFileService.copyFlightTask(taskId);
        return R.ok(newTaskId, "复制成功");
    }

    // ===== 执行操作 (保持原有路径，转发到新控制器) =====

    /**
     * 查询无人机当前任务状态
     * @deprecated 建议使用 /flight/task/execution/current/{uavCode}
     */
    @GetMapping("/current/{uavCode}")
    @SaCheckPermission(value = {"flight:task:query", "flight:task:execute"}, mode = SaMode.OR)
    @Deprecated
    public R<Object> getCurrentTask(@PathVariable("uavCode") @NotBlank(message = "无人机编码不能为空") String uavCode) {
        log.info("兼容性API调用: getCurrentTask - 转发到执行服务");
        return R.ok(flightTaskExecutionService.getCurrentTask(uavCode));
    }

    /**
     * 执行航线任务
     * @deprecated 建议使用 /flight/task/execution/execute/{taskId}
     */
    @SaCheckPermission("flight:task:execute")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.UPDATE)
    @PostMapping("/execute/{taskId}")
    @Deprecated
    public R<String> executeTask(@PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId) {
        log.info("兼容性API调用: executeTask - 转发到执行服务");
        try {
            CompletableFuture<Boolean> future = flightTaskExecutionService.executeFlightTask(taskId);
            Boolean result = future.get();
            if (result) {
                return R.ok("任务执行成功");
            } else {
                return R.fail("任务执行失败");
            }
        } catch (Exception e) {
            return R.fail("执行航线任务失败：" + e.getMessage());
        }
    }

    /**
     * 根据航线ID和无人机编码创建航线任务
     * @deprecated 建议使用 /flight/task/execution/createFromLine
     */
    @SaCheckPermission("flight:task:add")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.INSERT)
    @PostMapping("/createFromLine")
    @Deprecated
    public R<String> createTaskFromLine(@RequestParam @NotBlank(message = "航线ID不能为空") String lineId,
        @RequestParam @NotBlank(message = "无人机编码不能为空") String uavCode,
        @RequestParam(required = false, defaultValue = "false") Boolean executeImmediately) {
        log.info("兼容性API调用: createTaskFromLine - 转发到执行服务");
        try {
            String taskId = flightTaskExecutionService.createTaskFromLine(lineId, uavCode, executeImmediately);
            if (StringUtils.isEmpty(taskId)) {
                return R.fail("创建航线任务失败：未返回有效的任务ID");
            }
            return R.ok(taskId, "创建航线任务成功" + (executeImmediately ? "并已开始执行" : ""));
        } catch (ServiceException e) {
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("创建航线任务异常", e);
            return R.fail("创建航线任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取厂商支持的协议类型列表
     * @deprecated 建议使用 /flight/task/execution/protocols/{manufacturerType}
     */
    @SaCheckPermission("flight:task:query")
    @GetMapping("/protocols/{manufacturerType}")
    @Deprecated
    public R<Set<String>> getSupportedProtocols(
        @PathVariable("manufacturerType") @NotBlank(message = "厂商类型不能为空") String manufacturerType) {
        log.info("兼容性API调用: getSupportedProtocols - 转发到执行服务");
        try {
            Set<String> protocols = flightTaskExecutionService.getSupportedProtocols(manufacturerType);
            return R.ok(protocols);
        } catch (IllegalArgumentException e) {
            log.error("获取协议类型列表失败", e);
            return R.fail("不支持的厂商类型: " + e.getMessage());
        }
    }

    // ===== 紧急处理 (保持原有路径，转发到新控制器) =====

    /**
     * 紧急备降操作
     * @deprecated 建议使用 /flight/task/emergency/landing
     */
    @SaCheckPermission("flight:task:emergency")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.UPDATE)
    @PostMapping("/emergency-landing")
    @Deprecated
    public R<String> executeEmergencyLanding(@Valid @RequestBody EmergencyLandingBo bo) {
        log.info("兼容性API调用: executeEmergencyLanding - 转发到紧急服务");
        CommandResult result = flightTaskEmergencyService.executeEmergencyLanding(
            bo.getTaskId(),
            bo.getDroneId(),
            bo.getCurrentLatitude(),
            bo.getCurrentLongitude(),
            bo.getCurrentAltitude());

        if (result.isSuccess()) {
            return R.ok("紧急备降指令已发送");
        } else {
            return R.fail("紧急备降指令发送失败: " + result.getMessage());
        }
    }

    /**
     * 获取任务的所有备降点
     * @deprecated 建议使用 /flight/task/emergency/points/{taskId}
     */
    @SaCheckPermission("flight:task:query")
    @GetMapping("/emergency-points/{taskId}")
    @Deprecated
    public R<List<FlightTaskPoint>> getEmergencyPoints(
        @PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId) {
        log.info("兼容性API调用: getEmergencyPoints - 转发到紧急服务");
        List<FlightTaskPoint> points = flightTaskEmergencyService.getAllEmergencyLandingPoints(taskId);
        return R.ok(points);
    }

    // ===== 围栏管理 (保持原有路径，转发到新控制器) =====

    /**
     * 更新任务关联的围栏
     * @deprecated 建议使用 /flight/task/fence/{taskId}
     */
    @SaCheckPermission("flight:task:edit")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.UPDATE)
    @PutMapping("/fences/{taskId}")
    @Deprecated
    public R<Void> updateTaskFences(@PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId,
        @RequestParam @NotBlank(message = "围栏ID不能为空") String fenceIds) {
        log.info("兼容性API调用: updateTaskFences - 转发到围栏服务");
        boolean result = flightTaskFenceService.updateTaskFences(taskId, fenceIds);
        return result ? R.ok() : R.fail("更新任务围栏关联失败");
    }

    /**
     * 获取任务关联的围栏ID
     * @deprecated 建议使用 /flight/task/fence/{taskId}
     */
    @SaCheckPermission("flight:task:query")
    @GetMapping("/fences/{taskId}")
    @Deprecated
    public R<String> getTaskFences(@PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId) {
        log.info("兼容性API调用: getTaskFences - 转发到围栏服务");
        String fenceIds = flightTaskFenceService.getTaskFences(taskId);
        return R.ok(fenceIds);
    }
}
