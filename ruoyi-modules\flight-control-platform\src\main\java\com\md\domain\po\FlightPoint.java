package com.md.domain.po;

import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 航点对象 flight_point
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "flight_point")
public class FlightPoint extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 所属航线ID
     */
//    @Excel(name = "所属航线ID")
    @TableField(value = "line_id")
    private String lineId;

    /**
     * 航点序号
     */
//    @Excel(name = "航点序号")
    @TableField(value = "point_index")
    private Long pointIndex;

    /**
     * 纬度
     */
//    @Excel(name = "纬度")
    @TableField(value = "latitude")
    private BigDecimal latitude;

    /**
     * 经度
     */
//    @Excel(name = "经度")
    @TableField(value = "longitude")
    private BigDecimal longitude;

    /**
     * 高度(m)
     */
//    @Excel(name = "高度(m)")
    @TableField(value = "altitude")
    private BigDecimal altitude;

    /**
     * 飞行速度(m/s)
     */
//    @Excel(name = "飞行速度(m/s)")
    @TableField(value = "speed")
    private int speed;

    /**
     * 航点动作列表
     */
    @TableField(exist = false)
    private List<FlightPointAction> actions;

    /**
     * 是否为备降点(0:否 1:是)
     */
//    @Excel(name = "是否为备降点")
    @TableField(value = "is_emergency_landing_point")
    private Integer isEmergencyLandingPoint;

}
