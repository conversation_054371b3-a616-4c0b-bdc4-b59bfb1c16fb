package com.md.command.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.command.annotation.CommandLog;
import com.md.command.core.DroneCommand;
import com.md.command.core.DroneCommandExecutor;
import com.md.command.core.DroneCommandFactory;
import com.md.command.model.dto.CommandResult;
import com.md.command.service.DroneCommandService;
import com.md.domain.po.UavInfo;
import com.md.mapper.UavInfoMapper;
import com.md.service.IDroneStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DroneCommandServiceImpl implements DroneCommandService {

    @Autowired
    private DroneCommandFactory commandFactory;

    @Autowired
    private IDroneStatusService droneStatusService;

    @Autowired
    private UavInfoMapper uavInfoMapper;

    @Override
    @CommandLog
    public CommandResult executeCommand(DroneCommand command) {
        try {
            log.info("开始执行指令: 指令类型={}, 无人机ID={}", command.getCommandType(), command.getDroneId());

            // 检查无人机是否在线
            if (!droneStatusService.isDroneOnline(command.getDroneId())) {
                // 查询无人机信息
                UavInfo uavInfo = uavInfoMapper.selectOne(
                    Wrappers.<UavInfo>lambdaQuery().eq(UavInfo::getUavCode, command.getDroneId()));
                String droneName =
                    (ObjectUtil.isNull(uavInfo) || StrUtil.isEmpty(uavInfo.getUavName())) ? command.getDroneId()
                        : uavInfo.getUavName();

                String errorMsg = String.format("无人机[%s]不在线，无法执行指令", droneName);
                log.warn(errorMsg);
                return CommandResult.failed(errorMsg);
            }

            // 使用工厂根据无人机ID选择合适地执行器
            DroneCommandExecutor executor = commandFactory.getExecutor(command.getDroneId());
            CommandResult result = executor.executeCommand(command);

            log.info("指令执行完成: 执行结果={}, 消息={}", result.isSuccess() ? "成功" : "失败", result.getMessage());

            return result;
        } catch (Exception e) {
            log.error("指令执行失败", e);
            return CommandResult.failed(e.getMessage());
        }
    }
}
