package com.md.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.md.domain.dto.OpenApiDto;
import com.md.service.IOpenApiService;
import com.md.utils.EncryptUtils;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.web.core.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开放接口
 */
@Slf4j
@SaIgnore
@RestController
@RequestMapping("md/openapi")
public class OpenApiController extends BaseController {
    @Autowired
    private IOpenApiService openApiService;
    @Autowired
    private EncryptUtils encryptUtils;

    /**
     * 查询航线列表
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     */
    @PostMapping("/flight/line/list")
    public String flightLineList(@RequestBody @NotBlank(message = "参数不能为空") String param,
                                 @RequestHeader("openTenantId") String openTenantId,
                                 @RequestHeader("apiTenantId") String apiTenantId) {
        String openUrl = "/md/openapi/flight/line/list";
        String decrypt = encryptUtils.parseOpenDecrypt(openUrl, param, openTenantId , apiTenantId);
        return openApiService.flightLineList(openUrl, decrypt, openTenantId,apiTenantId);
    }

    /**
     * 查询无人机列表
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     */
    @PostMapping("/uav/list")
    public String uavList(@RequestBody @NotBlank(message = "参数不能为空") String param,
                          @RequestHeader("openTenantId") String openTenantId,
                          @RequestHeader("apiTenantId") String apiTenantId) {
        String openUrl = "/md/openapi/uav/list";
        String decrypt = encryptUtils.parseOpenDecrypt(openUrl, param, openTenantId,apiTenantId);
        OpenApiDto openApiDto = JSONObject.parseObject(decrypt, OpenApiDto.class);
        return openApiService.uavList(openUrl, openApiDto, openTenantId,apiTenantId);
    }

    /**
     * 根据航线ID和无人机编码创建航线任务
     *
     * @param param        加密的请求参数，包含lineId和uavCode
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @param platformCode 平台编码
     * @return 加密的任务创建结果
     */
    @PostMapping("/flight/task/execute")
    public String createTaskFromLine(@RequestBody @NotBlank(message = "参数不能为空") String param,
                                     @RequestHeader("openTenantId") String openTenantId,
                                     @RequestHeader("apiTenantId") String apiTenantId,
                                     @RequestHeader("platformCode") String platformCode) {
        if (ObjectUtil.isNull(param)) {
            log.error("创建航线任务失败: 参数为空");
            return null;
        }
        String openUrl = "/md/openapi/flight/task/execute";
        try {
            // 直接调用Service层处理业务逻辑，包括参数解析、业务处理、结果加密和Redis存储
            return openApiService.createTaskFromLine(openUrl, param, openTenantId,apiTenantId, platformCode);
        } catch (Exception e) {
            log.error("创建航线任务异常", e);
            // 构造错误返回
            JSONObject errorResult = new JSONObject();
            errorResult.put("code", "500");
            errorResult.put("message", "创建航线任务执行失败: " + e.getMessage());
            return encryptUtils.parseAESEncrypt(openUrl, errorResult.toString(), openTenantId,apiTenantId);
        }
    }

    /**
     * 获取无人机状态数据
     *
     * @param param    加密的无人机ID参数
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @return 无人机状态数据
     */
    @PostMapping("/drone/status")
    public String getDroneStatus(@RequestBody @NotBlank(message = "参数不能为空") String param,
                                 @RequestHeader("openTenantId") String openTenantId,
                                 @RequestHeader("apiTenantId") String apiTenantId) {
        if (ObjectUtil.isNull(param)) {
            log.error("获取无人机状态失败: 参数为空");
            return null;
        }
        String openUrl = "/md/openapi/drone/status";
        try {
            // 直接调用Service层处理业务逻辑，包括参数解析、业务处理和结果加密
            return openApiService.getDroneStatus(openUrl, param, openTenantId,apiTenantId);
        } catch (Exception e) {
            log.error("获取无人机状态异常", e);
            return null;
        }
    }

    /**
     * 查询飞手列表
     */
    @PostMapping("/flyer/list")
    public String pilotList(@RequestBody @NotBlank(message = "参数不能为空") String param,
                            @RequestHeader("openTenantId") String openTenantId,
                            @RequestHeader("apiTenantId") String apiTenantId) {
        String openUrl = "/md/openapi/flyer/list";
        String decrypt = encryptUtils.parseOpenDecrypt(openUrl, param, openTenantId,apiTenantId);
        return openApiService.queryFlyerList(openUrl, decrypt, openTenantId,apiTenantId);
    }
}
