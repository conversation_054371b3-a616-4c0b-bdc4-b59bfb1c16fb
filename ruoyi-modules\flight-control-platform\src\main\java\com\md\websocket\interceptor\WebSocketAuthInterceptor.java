package com.md.websocket.interceptor;

import com.md.service.IWebSocketTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

/**
 * WebSocket认证拦截器
 */
@Slf4j
@Component
public class WebSocketAuthInterceptor implements HandshakeInterceptor {

    @Autowired
    private IWebSocketTokenService tokenService;

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler,
        Map<String, Object> attributes) {
        try {
            // 1. 解析请求参数
            Map<String, String> params =
                UriComponentsBuilder.fromUri(request.getURI()).build().getQueryParams().toSingleValueMap();

            // 2. 获取Token和无人机ID
            String token = params.get("token");
            String droneId = extractDroneId(request.getURI().getPath());

            // 3. 验证Token
            boolean isValid = tokenService.validateToken(token, droneId);
            if (!isValid) {
                log.warn("WebSocket连接认证失败: droneId={}, token={}", droneId, token);
                return false;
            }

            // 4. 将用户信息存入属性
            attributes.put("droneId", droneId);
            attributes.put("token", token);

            log.info("WebSocket连接认证成功: droneId={}", droneId);
            return true;

        } catch (Exception e) {
            log.error("WebSocket连接认证异常: error={}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler,
        Exception exception) {
        // 握手后的处理，暂不需要实现
    }

    /**
     * 从URI路径中提取无人机ID 路径格式：/ws/drone/{droneId}
     */
    private String extractDroneId(String path) {
        String[] parts = path.split("/");
        return parts[parts.length - 1];
    }
}