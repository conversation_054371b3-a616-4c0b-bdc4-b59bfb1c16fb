package com.md.flight.px4.manager;

import com.md.flight.execution.context.TaskExecutionInfo;
import com.md.enums.TaskStatusEnum;
import com.md.utils.TaskStatusRedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * PX4任务状态管理器
 * 负责管理PX4无人机的任务执行状态和相关信息
 * 支持集群部署：使用双写模式（内存+Redis），优先从Redis读取，内存作为fallback
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class Px4TaskStatusManager {

    // 注入通用任务状态Redis工具类
    private final TaskStatusRedisUtils taskStatusRedisUtils;

    // 保留现有的内存Map（向后兼容，作为fallback）
    /**
     * 无人机ID -> 任务ID的映射
     */
    private final ConcurrentMap<String, String> droneTaskMap = new ConcurrentHashMap<>();

    /**
     * 任务ID -> 任务状态的映射
     */
    private final ConcurrentMap<String, TaskExecutionInfo> taskStatusMap = new ConcurrentHashMap<>();

    /**
     * 无人机ID -> 任务执行状态的映射
     */
    private final ConcurrentMap<String, Boolean> droneExecutionMap = new ConcurrentHashMap<>();

    /**
     * 开始任务执行（双写模式：同时写入Redis和内存）
     */
    public void startTask(String droneId, String taskId, String taskName, String commandId) {
        log.info("开始PX4任务执行: droneId={}, taskId={}, taskName={}, commandId={}", droneId, taskId, taskName,
            commandId);

        // 创建任务执行信息
        TaskExecutionInfo taskInfo = new TaskExecutionInfo(taskId, droneId, taskName);
        taskInfo.setStatus(TaskStatusEnum.EXECUTING);

        // 双写模式：优先写入Redis，同时写入内存作为fallback
        try {
            // 1. 写入Redis（主存储）
            taskStatusRedisUtils.setTaskInfo("PX4", taskId, taskInfo);
            taskStatusRedisUtils.setDroneTask("PX4", droneId, taskId);
            taskStatusRedisUtils.setDroneExecution("PX4", droneId, true);
            log.debug("PX4任务状态已写入Redis: droneId={}, taskId={}", droneId, taskId);
        } catch (Exception e) {
            log.error("写入PX4任务状态到Redis失败，将依赖内存存储: droneId={}, taskId={}, error={}", droneId, taskId,
                e.getMessage(), e);
        }

        // 2. 写入内存（兼容现有逻辑，作为fallback）
        droneTaskMap.put(droneId, taskId);
        taskStatusMap.put(taskId, taskInfo);
        droneExecutionMap.put(droneId, true);

        log.info("PX4任务执行状态已记录: droneId={}, taskId={}", droneId, taskId);
    }

    /**
     * 更新任务状态（使用Redisson分布式锁确保线程安全）
     *
     * @return true表示更新成功，false表示获取锁失败
     */
    public boolean updateTaskStatus(String taskId, TaskStatusEnum status, Integer currentWaypoint, Integer totalWaypoints,
        Double progress) {
        // 使用分布式锁确保任务状态更新的原子性
        boolean updated = taskStatusRedisUtils.tryUpdateTaskStatus("PX4", taskId, () -> {
            try {
                // 优先更新Redis中的任务信息
                TaskExecutionInfo redisTaskInfo = taskStatusRedisUtils.getTaskInfo("PX4", taskId);
                if (redisTaskInfo != null) {
                    redisTaskInfo.setStatus(status);
                    if (currentWaypoint != null) {
                        redisTaskInfo.setCurrentWaypoint(currentWaypoint);
                    }
                    if (totalWaypoints != null) {
                        redisTaskInfo.setTotalWaypoints(totalWaypoints);
                    }
                    if (progress != null) {
                        redisTaskInfo.setProgress(progress);
                    }
                    taskStatusRedisUtils.setTaskInfo("PX4", taskId, redisTaskInfo);
                }
            } catch (Exception e) {
                log.error("更新Redis中的PX4任务状态失败: taskId={}, error={}", taskId, e.getMessage(), e);
            }

            // 同时更新内存中的任务信息（fallback）
            TaskExecutionInfo taskInfo = taskStatusMap.get(taskId);
            if (taskInfo != null) {
                taskInfo.setStatus(status);
                if (currentWaypoint != null) {
                    taskInfo.setCurrentWaypoint(currentWaypoint);
                }
                if (totalWaypoints != null) {
                    taskInfo.setTotalWaypoints(totalWaypoints);
                }
                if (progress != null) {
                    taskInfo.setProgress(progress);
                }
            }
        });

        if (updated) {
            log.info("PX4任务状态已更新: taskId={}, status={}, waypoint={}/{}, progress={}%", taskId, status.getInfo(),
                currentWaypoint, totalWaypoints, progress);
        } else {
            log.warn("更新PX4任务状态失败，可能由于锁竞争: taskId={}", taskId);
        }

        return updated;
    }

    /**
     * 完成任务（双清理：Redis和内存）
     */
    public void completeTask(String droneId, TaskStatusEnum finalStatus) {
        String taskId = getCurrentTaskId(droneId); // 使用改进后的方法
        if (taskId != null) {
            TaskExecutionInfo taskInfo = getTaskInfo(taskId); // 使用改进后的方法
            if (taskInfo != null) {
                taskInfo.setStatus(finalStatus);
                log.info("PX4任务已完成: droneId={}, taskId={}, finalStatus={}", droneId, taskId,
                    finalStatus.getInfo());
            }

            // 双清理：Redis和内存
            try {
                taskStatusRedisUtils.cleanupTaskData("PX4", droneId);
            } catch (Exception e) {
                log.error("清理PX4任务Redis数据失败: droneId={}, error={}", droneId, e.getMessage(), e);
            }

            // 清理内存映射关系
            droneTaskMap.remove(droneId);
            droneExecutionMap.remove(droneId);

        }
    }

    /**
     * 获取无人机当前执行的任务ID（优先从Redis读取，内存作为fallback）
     */
    public String getCurrentTaskId(String droneId) {
        try {
            // 优先从Redis读取
            String taskId = taskStatusRedisUtils.getDroneTask("PX4", droneId);
            if (taskId != null) {
                return taskId;
            }
        } catch (Exception e) {
            log.warn("从Redis获取PX4任务ID失败，使用内存fallback: droneId={}, error={}", droneId, e.getMessage());
        }

        // Fallback到内存
        return droneTaskMap.get(droneId);
    }

    /**
     * 获取任务执行信息（优先从Redis读取，内存作为fallback）
     */
    public TaskExecutionInfo getTaskInfo(String taskId) {
        try {
            // 优先从Redis读取
            TaskExecutionInfo redisTaskInfo = taskStatusRedisUtils.getTaskInfo("PX4", taskId);
            if (redisTaskInfo != null) {
                return redisTaskInfo;
            }
        } catch (Exception e) {
            log.warn("从Redis获取PX4任务信息失败，使用内存fallback: taskId={}, error={}", taskId, e.getMessage());
        }

        // Fallback到内存
        return taskStatusMap.get(taskId);
    }

    /**
     * 检查无人机是否正在执行任务（优先从Redis读取，内存作为fallback）
     */
    public boolean isTaskExecuting(String droneId) {
        try {
            // 优先从Redis读取
            Boolean executing = taskStatusRedisUtils.isDroneExecuting("PX4", droneId);
            if (executing != null) {
                return executing;
            }
        } catch (Exception e) {
            log.warn("从Redis获取PX4执行状态失败，使用内存fallback: droneId={}, error={}", droneId, e.getMessage());
        }

        // Fallback到内存
        return droneExecutionMap.getOrDefault(droneId, false);
    }

    /**
     * 获取任务状态（优先从Redis读取，内存作为fallback）
     */
    public TaskStatusEnum getTaskStatus(String taskId) {
        TaskExecutionInfo taskInfo = getTaskInfo(taskId); // 使用改进后的方法
        return taskInfo != null ? taskInfo.getStatus() : null;
    }

    /**
     * 清理已完成的任务信息（双清理：Redis和内存）
     */
    public void cleanupCompletedTask(String taskId) {
        try {
            // 先从Redis获取任务信息
            TaskExecutionInfo redisTaskInfo = taskStatusRedisUtils.getTaskInfo("PX4", taskId);
            if (redisTaskInfo != null) {
                taskStatusRedisUtils.removeTaskInfo("PX4", taskId);
                log.info("已清理PX4任务Redis信息: taskId={}, droneId={}", taskId, redisTaskInfo.getDroneId());
            }
        } catch (Exception e) {
            log.error("清理PX4任务Redis信息失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }

        // 清理内存信息
        TaskExecutionInfo taskInfo = taskStatusMap.remove(taskId);
        if (taskInfo != null) {
            log.info("已清理PX4任务内存信息: taskId={}, droneId={}", taskId, taskInfo.getDroneId());
        }
    }

    /**
     * 获取所有活动任务数量（优先从Redis读取）
     */
    public int getActiveTaskCount() {
        try {
            // 优先从Redis获取活动任务数量
            Map<String, String> activeTasks = taskStatusRedisUtils.getAllActiveTasks("PX4");
            return activeTasks.size();
        } catch (Exception e) {
            log.warn("从Redis获取PX4活动任务数量失败，使用内存fallback: error={}", e.getMessage());
            // Fallback到内存
            return droneTaskMap.size();
        }
    }

    /**
     * 强制停止任务（用于异常情况）
     */
    public void forceStopTask(String droneId) {
        String taskId = getCurrentTaskId(droneId); // 使用改进后的方法
        if (taskId != null) {
            TaskExecutionInfo taskInfo = getTaskInfo(taskId); // 使用改进后的方法
            if (taskInfo != null) {
                taskInfo.setStatus(TaskStatusEnum.FAILED);
                log.warn("强制停止PX4任务: droneId={}, taskId={}", droneId, taskId);
            }

            // 双清理：Redis和内存
            try {
                taskStatusRedisUtils.cleanupTaskData("PX4", droneId);
            } catch (Exception e) {
                log.error("清理PX4任务Redis数据失败: droneId={}, error={}", droneId, e.getMessage(), e);
            }

            // 清理内存映射关系
            droneTaskMap.remove(droneId);
            droneExecutionMap.remove(droneId);
        }
    }

}
