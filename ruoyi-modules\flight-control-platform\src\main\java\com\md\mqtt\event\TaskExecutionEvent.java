package com.md.mqtt.event;

import lombok.Getter;

/**
 * 任务执行状态变更事件 用于在任务开始执行和结束时通知相关组件
 */
@Getter
public class TaskExecutionEvent {
    private final String droneId;      // 无人机ID
    private final String taskId;       // 任务ID
    private final boolean isExecuting; // 是否正在执行
    private final DroneType droneType; // 无人机类型

    /**
     * 构造函数（向后兼容）
     *
     * @param droneId 无人机ID
     * @param taskId 任务ID
     * @param isExecuting 是否正在执行
     */
    public TaskExecutionEvent(String droneId, String taskId, boolean isExecuting) {
        this.droneId = droneId;
        this.taskId = taskId;
        this.isExecuting = isExecuting;
        this.droneType = DroneType.fromIdentifier(droneId); // 根据无人机ID自动判断类型
    }

    /**
     * 构造函数（推荐使用）
     *
     * @param droneId 无人机ID
     * @param taskId 任务ID
     * @param isExecuting 是否正在执行
     * @param droneType 无人机类型
     */
    public TaskExecutionEvent(String droneId, String taskId, boolean isExecuting, DroneType droneType) {
        this.droneId = droneId;
        this.taskId = taskId;
        this.isExecuting = isExecuting;
        this.droneType = droneType;
    }
}