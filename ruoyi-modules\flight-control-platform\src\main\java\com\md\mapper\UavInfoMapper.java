package com.md.mapper;

import com.md.domain.po.UavInfo;
import com.md.domain.vo.UavInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 无人机Mapper接口
 */
@Mapper
public interface UavInfoMapper extends BaseMapperPlus<UavInfo, UavInfoVo> {
    /**
     * 查询无人机
     *
     * @param id 无人机主键
     * @return 无人机
     */
    public UavInfo selectFkUavInfoById(Long id);

    /**
     * 查询无人机列表
     *
     * @param uavInfo 无人机
     * @return 无人机集合
     */
    public List<UavInfo> selectFkUavInfoList(UavInfo uavInfo);

    /**
     * 新增无人机
     *
     * @param uavInfo 无人机
     * @return 结果
     */
    public int insertFkUavInfo(UavInfo uavInfo);

    /**
     * 修改无人机
     *
     * @param uavInfo 无人机
     * @return 结果
     */
    public int updateFkUavInfo(UavInfo uavInfo);

    /**
     * 删除无人机
     *
     * @param id 无人机主键
     * @return 结果
     */
    public int deleteFkUavInfoById(Long id);

    /**
     * 批量删除无人机
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFkUavInfoByIds(Long[] ids);

    /**
     * 根据无人机编码列表批量查询无人机信息
     *
     * @param uavCodes 无人机编码列表
     * @return 无人机信息列表
     */
    List<UavInfo> selectByUavCodes(@Param("uavCodes") List<String> uavCodes);
}
