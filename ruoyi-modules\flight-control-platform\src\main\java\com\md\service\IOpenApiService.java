package com.md.service;

import com.md.domain.dto.OpenApiDto;

/**
 * 开放接口
 */
public interface IOpenApiService {

    /**
     * 查询无人机列表
     *
     * @param openApiDto
     * @return
     */
    String uavList(String openUrl, OpenApiDto openApiDto, String openTenantId, String apiTenantId);

    String flightLineList(String openUrl, String decrypt, String openTenantId, String apiTenantId);

    /**
     * 创建航线任务（包含平台编码）
     *
     * @param openUrl      开放接口URL
     * @param param        加密的请求参数
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @param platformCode 平台编码
     * @return 加密后的创建结果
     */
    String createTaskFromLine(String openUrl, String param, String openTenantId, String apiTenantId, String platformCode);

    /**
     * 获取无人机最新状态数据
     *
     * @param param    加密的无人机ID参数
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @return 加密后的状态数据JSON字符串，无数据时返回null
     */
    String getDroneStatus(String openUrl, String param, String openTenantId, String apiTenantId);

    /**
     * 查询飞手列表
     * @param openUrl
     * @param decrypt
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID
     * @return
     */
    String queryFlyerList(String openUrl, String decrypt,String openTenantId, String apiTenantId);
}
