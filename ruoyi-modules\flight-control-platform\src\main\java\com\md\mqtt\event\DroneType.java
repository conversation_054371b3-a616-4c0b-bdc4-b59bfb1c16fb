package com.md.mqtt.event;

/**
 * 无人机类型枚举
 * 用于区分不同类型的无人机，确保事件能够正确路由到对应的处理器
 */
public enum DroneType {
    /**
     * MAVLink协议无人机
     */
    MAVLINK("mavlink", "MAVLink协议无人机"),
    
    /**
     * PX4飞控无人机
     */
    PX4("px4", "PX4飞控无人机"),
    
    /**
     * 大疆无人机
     */
    DJI("dji", "大疆无人机"),
    
    /**
     * 联合飞机
     */
    LH("lh", "联合飞机"),
    
    /**
     * 未知类型
     */
    UNKNOWN("unknown", "未知类型");
    
    private final String code;
    private final String description;
    
    DroneType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据无人机ID或主题前缀判断无人机类型
     * 
     * @param identifier 无人机ID或主题前缀
     * @return 无人机类型
     */
    public static DroneType fromIdentifier(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        String lowerIdentifier = identifier.toLowerCase();
        
        // 根据主题前缀判断
        if (lowerIdentifier.startsWith("px4/") || lowerIdentifier.contains("px4")) {
            return PX4;
        } else if (lowerIdentifier.startsWith("dji/") || lowerIdentifier.contains("dji")) {
            return DJI;
        } else if (lowerIdentifier.startsWith("lh/") || lowerIdentifier.contains("_lh_")) {
            return LH;
        } else if (lowerIdentifier.startsWith("mavlink/") || lowerIdentifier.contains("mavlink")) {
            return MAVLINK;
        }
        
        return UNKNOWN;
    }
    
    /**
     * 根据MQTT主题判断无人机类型
     * 
     * @param topic MQTT主题
     * @return 无人机类型
     */
    public static DroneType fromTopic(String topic) {
        return fromIdentifier(topic);
    }
}
