package com.md.openfeign;

import com.md.openfeign.fallback.TaskCallbackClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.net.URI;

/**
 * 任务回调接口客户端
 */
@FeignClient(name = "taskCallbackClient", url = "${task-callback.url:}",
    fallbackFactory = TaskCallbackClientFallback.class)
public interface TaskCallbackClient {

    /**
     * 任务执行回调
     *
     * @param uri 回调URI
     * @param requestData 请求数据
     * @param openTenantId 开放端租户ID
     * @param apiTenantId 调用段租户ID(本平台)
     * @return 回调结果
     */
    @PostMapping("/md/flight/openApiService/callback")
    String taskCallback(URI uri, @RequestBody String requestData,
                        @RequestHeader("openTenantId") String openTenantId,
                        @RequestHeader("apiTenantId") String apiTenantId);
}
