package com.md.mqtt.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.command.model.dto.CommandResult;
import com.md.constant.MqttConstants;
import com.md.domain.bo.FlightTrackPointBo;
import com.md.domain.dto.FenceDetectionResult;
import com.md.mqtt.event.TaskExecutionEvent;
import com.md.mqtt.exception.MqttProcessException;
import com.md.mqtt.listener.DroneStatusListener;
import com.md.service.IDroneStatusService;
import com.md.service.IFenceDetectionService;
import com.md.service.IFlightTaskService;
import com.md.service.IFlightTrackService;
import com.md.utils.TaskStatusRedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class DroneStatusMessageProcessor extends AbstractMqttMessageProcessor {
    @Value("${mqtt.redis.expire-seconds:20}")  // 默认20秒
    private int expireSeconds;

    @Value("${mqtt.state.min-update-interval:500}")  // 默认500毫秒
    private long minUpdateInterval;

    // 围栏违规冷却期（秒）
    private static final int FENCE_VIOLATION_COOLDOWN_SECONDS = 30;

    // 围栏违规缓存键前缀
    private static final String FENCE_VIOLATION_KEY = "drone:fence:violation:";

    @Autowired(required = false)
    private List<DroneStatusListener> statusListeners;

    @Autowired
    private IFlightTaskService flightTaskService;

    @Autowired
    private IDroneStatusService droneStatusService;

    @Autowired
    private IFlightTrackService flightTrackService;

    @Autowired
    private IFenceDetectionService fenceDetectionService;

    @Autowired
    private DroneResponseMessageProcessor droneResponseMessageProcessor;

    @Autowired
    private TaskStatusRedisUtils taskStatusRedisUtils;

    // 状态更新限流器，每架无人机每秒最多处理2次状态更新
    private final Map<String, Long> lastUpdateTimeMap = new ConcurrentHashMap<>();


    @Override
    protected String getTopicPattern() {
        return MqttConstants.TOPIC_STATE_PATTERN;
    }

    @Override
    public void doProcessMessage(String topic, String payload) {
        try {
            // 解析 payload 中的 droneSN
            JSONObject jsonPayload = JSON.parseObject(payload);
            String droneId = jsonPayload.getString("droneSN");
            if (StringUtils.isEmpty(droneId)) {
                log.error("无人机状态消息中未包含droneId字段，消息内容：{}", payload);
                return;
            }

            // 检查是否需要采样处理
            long currentTime = System.currentTimeMillis();
            Long lastTime = lastUpdateTimeMap.get(droneId);
            if (lastTime != null && currentTime - lastTime < minUpdateInterval) {
                // 距离上次更新时间太短，跳过此次处理
                log.debug("无人机状态更新过于频繁，跳过处理，无人机ID：{}", droneId);
                return;
            }
            lastUpdateTimeMap.put(droneId, currentTime);

            // 检查之前的在线状态
            boolean wasOnline = droneStatusService.isDroneOnline(droneId);

            // 存储到Redis
            TenantHelper.ignore(() -> {
                RedisUtils.setCacheObject(MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId, jsonPayload.toString(),
                    Duration.ofSeconds(expireSeconds));
                return null;
            });

            // 获取当前任务信息，检查是否需要围栏检测（优先从Redis读取，支持集群）
            String taskId = getCurrentTaskId(droneId);
            if (taskId != null && droneResponseMessageProcessor.isTaskExecuting(droneId)) {
                // 保存轨迹点
                saveTrackPoint(taskId, jsonPayload);

                // 执行围栏检测
                try {
                    // 如果正在执行任务，检查是否违反围栏规则
                    checkFenceViolation(droneId, taskId, jsonPayload);
                } catch (Exception e) {
                    log.error("围栏检测异常", e);
                }
            }

            // 触发状态变化事件
            if (statusListeners != null && !statusListeners.isEmpty()) {
                if (!wasOnline) {
                    // 之前离线，现在上线
                    for (DroneStatusListener listener : statusListeners) {
                        try {
                            listener.onDroneOnline(droneId, jsonPayload.toString());
                        } catch (Exception e) {
                            log.error("处理无人机上线事件失败", e);
                        }
                    }
                }
            }

            log.debug("处理无人机状态消息成功，无人机ID：{}", droneId);
        } catch (Exception e) {
            log.error("处理无人机状态消息失败", e);
            throw new MqttProcessException("处理UAV状态消息失败", e);
        }
    }

    /**
     * 检查是否违反围栏规则
     *
     * @param droneId    无人机ID
     * @param taskId     任务ID
     * @param statusData 状态数据
     */
    private void checkFenceViolation(String droneId, String taskId, JSONObject statusData) {
        // 检查是否在违规冷却期内
        if (isInViolationCooldown(droneId)) {
            return;
        }

        try {
            // 提取位置信息
            BigDecimal longitude = null;
            BigDecimal latitude = null;
            BigDecimal altitude = null;

            try {
                longitude = new BigDecimal(statusData.getString("longitude"));
                latitude = new BigDecimal(statusData.getString("latitude"));
                altitude = new BigDecimal(statusData.getString("altitude"));
            } catch (Exception e) {
                log.debug("无人机状态消息中位置信息格式不正确，跳过围栏检测：{}", e.getMessage());
                return;
            }

            // 执行围栏检测
            FenceDetectionResult result =
                fenceDetectionService.detectUavPosition(taskId, droneId, longitude, latitude, altitude);

            // 如果检测到违规
            if (result.isViolated()) {
                log.warn("检测到无人机围栏违规，触发紧急降落: 无人机ID={}, 任务ID={}, 违规信息={}", droneId, taskId,
                    result.getViolationReason());

                // 设置冷却期，避免重复触发
                setViolationCooldown(droneId);

                // 执行紧急降落
                CommandResult cmdResult =
                    flightTaskService.executeEmergencyLanding(taskId, droneId, latitude, longitude, altitude);

                log.info("围栏违规紧急降落指令执行结果: {}", cmdResult);
            }
        } catch (Exception e) {
            log.error("围栏检测过程中发生异常", e);
        }
    }

    /**
     * 获取无人机当前执行的任务ID
     */
    private String getCurrentTaskId(String droneId) {
        // 尝试不同的无人机类型，因为DroneStatusMessageProcessor处理所有类型
        String[] droneTypes = {"PX4", "DJI", "MAVLINK", "LH"};

        for (String droneType : droneTypes) {
            String taskId = taskStatusRedisUtils.getDroneTask(droneType, droneId);
            if (taskId != null) {
                log.debug("从Redis获取到{}无人机[{}]的任务ID: {}", droneType, droneId, taskId);
                return taskId;
            }
        }
        return null;
    }

    /**
     * 监听任务执行状态变更事件
     */
    @EventListener
    public void onTaskExecutionEvent(TaskExecutionEvent event) {
        String droneId = event.getDroneId();
        String taskId = event.getTaskId();
        String droneType = event.getDroneType().name();

        if (event.isExecuting()) {
            // 任务开始执行，记录任务ID到Redis
            taskStatusRedisUtils.setDroneTask(droneType, droneId, taskId);
            log.info("已记录{}无人机任务映射: droneId={}, taskId={}", droneType, droneId, taskId);
        } else {
            // 任务结束，移除记录
            taskStatusRedisUtils.removeDroneTask(droneType, droneId);
            log.info("已清理{}无人机任务映射: droneId={}", droneType, droneId);
        }
    }

    private void saveTrackPoint(String taskId, JSONObject stateData) {
        FlightTrackPointBo trackPoint = new FlightTrackPointBo();
        trackPoint.setTaskId(taskId);
        trackPoint.setDroneId(stateData.getString("droneSN"));
        trackPoint.setTimestamp(stateData.getLong("timestamp"));
        trackPoint.setLatitude(stateData.getDouble("latitude"));
        trackPoint.setLongitude(stateData.getDouble("longitude"));
        trackPoint.setAltitude(stateData.getDouble("altitude"));
        trackPoint.setRelativeHeight(stateData.getDouble("relativeHeight"));
        trackPoint.setSpeed(stateData.getDouble("speed"));
        trackPoint.setAirSpeed(stateData.getDouble("airSpeed"));
        trackPoint.setGroundSpeed(stateData.getDouble("groundSpeed"));
        trackPoint.setFlightDistance(stateData.getDouble("flightDistance"));
        trackPoint.setHeading(stateData.getDouble("heading"));
        trackPoint.setPitch(stateData.getDouble("pitch"));
        trackPoint.setRoll(stateData.getDouble("roll"));
        trackPoint.setFlightMode(stateData.getString("flightMode"));
        trackPoint.setMissionStatus(stateData.getString("missionStatus"));

        // 设置电池信息
        JSONObject batteryInfo = stateData.getJSONObject("batteryInfo");
        if (batteryInfo != null) {
            FlightTrackPointBo.BatteryInfo battery = new FlightTrackPointBo.BatteryInfo();
            battery.setChargeRemaining(batteryInfo.getInteger("chargeRemaining"));
            battery.setChargeRemainingInPercent(batteryInfo.getInteger("chargeRemainingInPercent"));
            battery.setTotalCapacity(batteryInfo.getInteger("totalCapacity"));
            trackPoint.setBatteryInfo(battery);
        }

        // 设置RTK相关信息
        trackPoint.setIsRTKConnected(stateData.getBoolean("isRTKConnected"));
        trackPoint.setIsRTKEnabled(stateData.getBoolean("isRTKEnabled"));
        trackPoint.setIsRTKHealthy(stateData.getBoolean("isRTKHealthy"));
        trackPoint.setRtkAltitude(stateData.getDouble("rtkAltitude"));
        trackPoint.setRtkSatelliteCount(stateData.getInteger("rtkSatelliteCount"));

        // 设置卫星信息
        JSONObject satelliteInfo = stateData.getJSONObject("satelliteInfo");
        if (satelliteInfo != null) {
            FlightTrackPointBo.SatelliteInfo satInfo = new FlightTrackPointBo.SatelliteInfo();
            satInfo.setBaseStationCount(satelliteInfo.getInteger("baseStationCount"));
            satInfo.setGpsCount(satelliteInfo.getInteger("gpsCount"));
            satInfo.setMobileStation1Count(satelliteInfo.getInteger("mobileStation1Count"));
            satInfo.setMobileStation2Count(satelliteInfo.getInteger("mobileStation2Count"));
            trackPoint.setSatelliteInfo(satInfo);
        }

        flightTrackService.saveTrackPoint(trackPoint);
    }

    /**
     * 判断无人机是否在围栏违规冷却期内
     */
    private boolean isInViolationCooldown(String droneId) {
        return TenantHelper.ignore(() -> RedisUtils.getCacheObject(FENCE_VIOLATION_KEY + droneId) != null);
    }

    /**
     * 设置围栏违规冷却期
     */
    private void setViolationCooldown(String droneId) {
        TenantHelper.ignore(() -> {
            RedisUtils.setCacheObject(FENCE_VIOLATION_KEY + droneId, true,
                Duration.ofSeconds(FENCE_VIOLATION_COOLDOWN_SECONDS));
            return null;
        });
    }
}