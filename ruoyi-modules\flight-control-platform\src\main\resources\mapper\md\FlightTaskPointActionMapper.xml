<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.FlightTaskPointActionMapper">
    <resultMap id="BaseResultMap" type="com.md.domain.po.FlightTaskPointAction">
        <!--@mbg.generated-->
        <!--@Table flight_task_point_action-->
        <id column="id" property="id"/>
        <result column="point_id" property="pointId"/>
        <result column="action_index" property="actionIndex"/>
        <result column="hover_time" property="hoverTime"/>
        <result column="aircraft_heading" property="aircraftHeading"/>
        <result column="use_global_image_format" property="useGlobalImageFormat"/>
        <result column="image_format" property="imageFormat"/>
        <result column="gimbal_yaw_rotate_angle" property="gimbalYawRotateAngle"/>
        <result column="gimbal_pitch_rotate_angle" property="gimbalPitchRotateAngle"/>
        <result column="multiple_timing" property="multipleTiming"/>
        <result column="multiple_distance" property="multipleDistance"/>
        <result column="zoom" property="zoom"/>
        <result column="record_status" property="recordStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="type" property="type"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        point_id,
        action_index,
        hover_time,
        aircraft_heading,
        use_global_image_format,
        image_format,
        gimbal_yaw_rotate_angle,
        gimbal_pitch_rotate_angle,
        multiple_timing,
        multiple_distance,
        zoom,
        record_status,
        create_time,
        update_time,
        `type`,
        tenant_id
    </sql>

    <insert id="batchInsertActions">
        insert into flight_task_point_action (point_id, action_index, hover_time,
        aircraft_heading, use_global_image_format, image_format,
        gimbal_yaw_rotate_angle, gimbal_pitch_rotate_angle, multiple_timing,
        multiple_distance, zoom, record_status, create_time, update_time, `type`, tenant_id)
        values
        <foreach collection="actions" item="item" index="index" separator=",">
            (#{item.pointId}, #{item.actionIndex}, #{item.hoverTime},
            #{item.aircraftHeading}, #{item.useGlobalImageFormat}, #{item.imageFormat},
            #{item.gimbalYawRotateAngle}, #{item.gimbalPitchRotateAngle}, #{item.multipleTiming},
            #{item.multipleDistance}, #{item.zoom}, #{item.recordStatus}, #{item.createTime},
            #{item.updateTime}, #{item.type}, #{item.tenantId})
        </foreach>
    </insert>
</mapper>