package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.po.Flyer;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 飞手视图对象
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Flyer.class)
public class FlyerVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 飞手id
     */
    @ExcelProperty(value = "飞手ID")
    private Long id;

    /**
     * 飞手姓名
     */
    @ExcelProperty(value = "飞手姓名")
    private String flyerName;

    /**
     * 飞手手机号
     */
    @ExcelProperty(value = "飞手手机号")
    private String flyerPhone;

    /**
     * 飞手性别
     */
    @ExcelProperty(value = "性别")
    private int flyerSex;

    /**
     * 数据来源
     */
    @ExcelProperty(value = "数据来源")
    private String dataSource;

    /**
     * 数据来源标识（0手动添加，1同步接口获取）
     */
    @ExcelProperty(value = "数据来源标识")
    private String dataSourceValue;

    /**
     * 无人机厂商id
     */
    @ExcelProperty(value = "无人机厂商ID")
    private String plateformId;

    /**
     * 飞控平台名称
     */
    @ExcelProperty(value = "飞控平台名称")
    private String plateformName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建者名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 修改者名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "updateBy")
    private String updateByName;
}