package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.po.FlightTaskPoint;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 航点视图对象
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FlightTaskPoint.class)
public class FlightTaskPointVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 航点ID
     */
    @ExcelProperty(value = "航点ID")
    private String id;

    /**
     * 所属航线ID
     */
    @ExcelProperty(value = "所属航线ID")
    private String lineId;

    /**
     * 航点序号
     */
    @ExcelProperty(value = "航点序号")
    private Integer pointIndex;

    /**
     * 纬度
     */
    @ExcelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @ExcelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 高度(m)
     */
    @ExcelProperty(value = "高度(m)")
    private BigDecimal altitude;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 飞行速度
     */
    @ExcelProperty(value = "飞行速度")
    private Integer speed;

    /**
     * 航点动作列表
     */
    private List<FlightTaskPointActionVo> actions;

    /**
     * 是否为备降点(0:否 1:是)
     */
    @ExcelProperty(value = "是否为备降点")
    private Integer isEmergencyLandingPoint;
} 