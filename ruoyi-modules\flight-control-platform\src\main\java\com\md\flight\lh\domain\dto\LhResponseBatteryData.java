package com.md.flight.lh.domain.dto;

import lombok.Data;

/**
 * 联合飞机电池数据实体类，用于接收和解析电池状态信息
 */
@Data
public class LhResponseBatteryData {
    /** 电池ID */
    private Integer id;

    /** 满充容量，单位：mAh */
    private Integer full_charge_capacity;

    /** 剩余电量，单位：mAh */
    private Float charge_remaining;

    /** 电池电压，单位：V */
    private Float voltage;

    /** 电流，单位：mA */
    private Float current;

    /** 低电压警告值 */
    private Integer low_volt_warn_value;

    /** 电池状态（0：电池异常 1：开机 2：充电中 3：需要保养） */
    private Integer status;

    /** 温度，单位：°C */
    private Float temperature;

    /** 循环次数 */
    private Integer cycle_index;

    /** 是否激活 */
    private Integer active;

    /** 使用容量 */
    private Integer using_capacity;

    /** 飞行时间 */
    private Integer time_flying;

    /** 电池健康度，单位：% */
    private Integer health;

    /** 剩余使用时间，单位：秒 */
    private Integer time_remaining;

    /** 充电剩余时间 */
    private Integer charge_time_remaining;

    /** 标志位 */
    private Integer flags;

    /** 电池 UID */
    private String uid;

    /** 固件版本 */
    private String version;

    /** 电池型号 */
    private String model;
}
