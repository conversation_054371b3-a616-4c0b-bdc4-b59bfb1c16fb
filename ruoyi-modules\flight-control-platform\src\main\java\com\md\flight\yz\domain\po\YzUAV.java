package com.md.flight.yz.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 邮政无人机对接实体
 */
@Data
@TableName("yz_uav_info")
public class YzUAV {
    /**
     * 无人机id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 飞机序列号
     */
    @TableField("drone_code")
    private String droneCode;

    /**
     * 平台编码
     */
    @TableField("platform_code")
    private String platformCode;

    /**
     * 无人机类型编码
     */
    @TableField("category_code")
    private String categoryCode;

    /**
     * 飞机编码
     */
    @TableField("drone_sn_code")
    private String droneSnCode;

    /**
     * 是否启用（0正常  1停用）
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 是否删除（0正常  2删除）
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 批次号
     */
    @TableField("batch_id")
    private String batchId;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("sync_time")
    private Date syncTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;
}
