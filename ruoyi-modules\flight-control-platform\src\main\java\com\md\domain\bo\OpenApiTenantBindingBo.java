package com.md.domain.bo;

import com.md.domain.po.OpenApiTenantBinding;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 开放端接口租户绑定关系业务对象 sys_open_api_tenant_binding
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OpenApiTenantBinding.class, reverseConvertGenerate = false)
public class OpenApiTenantBindingBo extends BaseEntity {

    /**
     * 绑定ID
     */
    private Long id;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 绑定关系的租户编号
     */
    private String bindTenantId;

    /**
     * 接口ID
     */
    @NotNull(message = "接口ID不能为空")
    private Long apiId;

    /**
     * 企业名称
     */
    @NotBlank(message = "企业名称不能为空")
    @Size(min = 0, max = 200, message = "企业名称长度不能超过{max}个字符")
    private String companyName;

    /**
     * 目标系统租户编号
     */
    @NotBlank(message = "目标系统租户编号不能为空")
    @Size(min = 0, max = 64, message = "目标系统租户编号长度不能超过{max}个字符")
    private String targetTenantId;

    /**
     * 配置信息
     */
    private String configInfo;

    /**
     * 是否生效（0生效 1失效）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
