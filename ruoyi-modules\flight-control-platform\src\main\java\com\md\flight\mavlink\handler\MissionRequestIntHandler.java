package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.MissionRequestInt;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务请求Int处理器
 * 处理MAVLink任务请求Int消息
 */
@Slf4j
public class MissionRequestIntHandler extends AbstractMavlinkHandler<MissionRequestInt> {

    public MissionRequestIntHandler(MavlinkCoreService coreService) {
        super(coreService);
    }
    
    @Override
    protected void doHandle(String droneId, MissionRequestInt message) {
        log.info("收到无人机[{}]任务请求Int: seq={}", droneId, message.seq());
        coreService.handleMissionRequestInt(droneId, message);
    }

    @Override
    public Class<MissionRequestInt> getMessageType() {
        return MissionRequestInt.class;
    }
}