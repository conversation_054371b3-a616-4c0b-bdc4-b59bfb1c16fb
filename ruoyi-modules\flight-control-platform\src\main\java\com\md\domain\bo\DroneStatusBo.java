package com.md.domain.bo;

import com.md.domain.dto.DroneStatusDto;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * 无人机状态业务对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = DroneStatusDto.class, reverseConvertGenerate = false)
public class DroneStatusBo extends BaseEntity {

    /**
     * 无人机ID（序列号）
     */
    @NotBlank(message = "无人机ID不能为空")
    private String droneId;

    /**
     * 无人机名称
     */
    private String droneName;

    /**
     * 是否在线
     */
    private Boolean online;

    /**
     * 无人机ID列表，用于批量查询
     */
    @NotEmpty(message = "无人机ID列表不能为空")
    private List<String> droneIds;
}