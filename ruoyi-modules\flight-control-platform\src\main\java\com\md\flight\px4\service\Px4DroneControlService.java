package com.md.flight.px4.service;

import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * PX4无人机控制服务接口
 * 提供PX4无人机的各种控制方法，包括基本飞行控制、虚拟摇杆控制和航线任务执行
 */
public interface Px4DroneControlService {
    /**
     * 发送MQTT命令
     *
     * @param droneId     无人机ID
     * @param commandType MQTT命令类型
     * @param payload     命令参数
     * @param topicSuffix 主题后缀
     * @return 命令ID
     */
    String sendCommand(String droneId, String commandType, Map<String, Object> payload, String topicSuffix);

    /**
     * 执行解锁命令
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    boolean arm(String droneId);

    /**
     * 执行上锁命令
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    boolean disarm(String droneId);

    /**
     * 执行起飞命令
     *
     * @param droneId  无人机ID
     * @param altitude 起飞高度（米）
     * @return 命令执行结果
     */
    boolean takeoff(String droneId, float altitude);

    /**
     * 执行降落命令
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    boolean land(String droneId);

    /**
     * 执行返航命令
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    boolean goHome(String droneId);

    /**
     * 开启虚拟摇杆控制
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    boolean startVirtualStick(String droneId);

    /**
     * 停止虚拟摇杆控制
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    boolean stopVirtualStick(String droneId);

    /**
     * 发送虚拟摇杆控制命令
     *
     * @param droneId    无人机ID
     * @param x          前后移动，正值前进，负值后退，范围 -1.0 到 1.0
     * @param y          左右移动，正值右移，负值左移，范围 -1.0 到 1.0
     * @param z          上下移动，正值下降，负值上升，范围 -1.0 到 1.0
     * @param yaw        偏航旋转，正值顺时针，负值逆时针，范围 -1.0 到 1.0
     * @param coordinate 坐标系统，默认为"BODY"
     * @return 命令执行结果
     */
    boolean controlVirtualStick(String droneId, float x, float y, float z, float yaw, String coordinate);

    /**
     * 检查无人机是否已启用虚拟摇杆模式
     *
     * @param droneId 无人机ID
     * @return 是否已启用
     */
    boolean isVirtualStickEnabled(String droneId);

    /**
     * 执行航线任务
     *
     * @param droneId 无人机ID
     * @param task 飞行任务
     * @param points 航点列表
     * @return 异步执行结果
     */
    CompletableFuture<Boolean> executeMission(String droneId, FlightTask task, List<FlightTaskPoint> points);

    /**
     * 暂停航线任务
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    boolean pauseMission(String droneId);

    /**
     * 恢复航线任务
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    boolean resumeMission(String droneId);

    /**
     * 停止航线任务
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    boolean stopMission(String droneId);
}