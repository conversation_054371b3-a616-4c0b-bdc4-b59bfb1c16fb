package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.MissionItemInt;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务项Int处理器
 * 处理MAVLink任务项Int消息
 */
@Slf4j
public class MissionItemIntHandler extends AbstractMavlinkHandler<MissionItemInt> {

    public MissionItemIntHandler(MavlinkCoreService coreService) {
        super(coreService);
    }
    
    @Override
    protected void doHandle(String droneId, MissionItemInt message) {
        int seq = message.seq();
        log.info("处理无人机[{}]任务项: seq={}", droneId, seq);
        coreService.handleMissionItemInt(droneId, message);
    }

    @Override
    public Class<MissionItemInt> getMessageType() {
        return MissionItemInt.class;
    }
}