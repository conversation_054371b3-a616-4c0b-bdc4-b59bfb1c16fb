<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.FlightLineTaskMapper">
    <resultMap type="FlightTask" id="FlightLineTaskResult">
        <!--@mbg.generated-->
        <!--@Table flight_task-->
        <id column="id" property="id"/>
        <result column="flight_control_no" property="flightControlNo"/>
        <result column="line_id" property="lineId"/>
        <result column="line_name" property="lineName"/>
        <result column="task_name" property="taskName"/>
        <result column="uav_code" property="uavCode"/>
        <result column="flyer_id" property="flyerId"/>
        <result column="flight_time" property="flightTime"/>
        <result column="operator_id" property="operatorId"/>
        <result column="task_status" property="taskStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="remark" property="remark"/>
        <result column="flight_speed" property="flightSpeed"/>
        <result column="flight_height" property="flightHeight"/>
        <result column="return_height" property="returnHeight"/>
        <result column="finished_action" property="finishedAction"/>
        <result column="missing_action" property="missingAction"/>
        <result column="file_generate_status" property="fileGenerateStatus"/>
        <result column="kmz_file_name" property="kmzFileName"/>
        <result column="kmz_file_path" property="kmzFilePath"/>
        <result column="file_size" property="fileSize"/>
        <result column="file_generate_time" property="fileGenerateTime"/>
        <result column="file_generate_error" property="fileGenerateError"/>
        <result column="is_uploaded" property="isUploaded"/>
    </resultMap>

    <sql id="selectFlightLineTask">
        select id,
               flight_control_no,
               line_id,
               line_name,
               task_name,
               uav_code,
               flyer_id,
               flight_time,
               operator_id,
               task_status,
               create_time,
               create_by,
               update_time,
               update_by,
               remark,
               flight_speed,
               flight_height,
               return_height,
               finished_action,
               missing_action,
               file_generate_status,
               kmz_file_name,
               kmz_file_path,
               file_size,
               file_generate_time,
               file_generate_error,
               is_uploaded
        from flight_task
    </sql>

    <select id="selectFlightLineTaskList" parameterType="FlightTaskBo" resultMap="FlightLineTaskResult">
        <include refid="selectFlightLineTask"/>
        <where>
            <if test="flightControlNo != null">
                AND flight_control_no = #{flightControlNo}
            </if>
            <if test="lineId != null">
                AND line_id = #{lineId}
            </if>
            <if test="taskName != null and taskName != ''">
                AND task_name like concat('%', #{taskName}, '%')
            </if>
            <if test="lineName != null and lineName != ''">
                AND line_name like concat('%', #{lineName}, '%')
            </if>
            <if test="uavCode != null and uavCode != ''">
                AND uav_code = #{uavCode}
            </if>
            <if test="flyerId != null">
                AND flyer_id = #{flyerId}
            </if>
            <if test="createBy != null and createBy != ''">
                AND create_by like concat('%', #{createBy}, '%')
            </if>
            <if test="updateBy != null and updateBy != ''">
                AND update_by like concat('%', #{updateBy}, '%')
            </if>
            <if test="taskStatus != null and taskStatus != ''">
                <choose>
                    <when test='taskStatus.indexOf(",") > -1'>
                        AND task_status IN
                        <foreach collection="taskStatus.split(',')" item="status" open="(" separator="," close=")">
                            CAST(#{status} as SIGNED)
                        </foreach>
                    </when>
                    <otherwise>
                        AND task_status = CAST(#{taskStatus} as SIGNED)
                    </otherwise>
                </choose>
            </if>
            <if test="beginFlightTime != null">
                AND flight_time <![CDATA[ >= ]]> #{beginFlightTime}
            </if>
            <if test="endFlightTime != null">
                AND flight_time <![CDATA[ < ]]> DATE_ADD(#{endFlightTime}, INTERVAL 1 DAY)
            </if>
            <if test="startTime != null">
                AND create_time <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time <![CDATA[ < ]]> DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
            <if test="beginUpdateTime != null">
                AND update_time <![CDATA[ >= ]]> #{beginUpdateTime}
            </if>
            <if test="endUpdateTime != null">
                AND update_time <![CDATA[ < ]]> DATE_ADD(#{endUpdateTime}, INTERVAL 1 DAY)
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectFlightLineTaskById" parameterType="Long" resultMap="FlightLineTaskResult">
        <include refid="selectFlightLineTask"/>
        where id = #{id}
    </select>

    <select id="selectTaskIdsByFenceId" parameterType="String" resultType="String">
        select id
        from flight_task
        where fence_id = #{fenceId}
    </select>

    <select id="selectFenceIdByTaskId" parameterType="String" resultType="String">
        select fence_id
        from flight_task
        where id = #{taskId}
    </select>
</mapper>