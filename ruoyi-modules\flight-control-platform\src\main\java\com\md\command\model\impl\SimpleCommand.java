package com.md.command.model.impl;

import com.md.command.constant.CommandType;
import com.md.command.core.DroneCommand;

import java.util.Map;

/**
 * DroneCommand接口的简单实现
 * 用于创建基本的无人机命令
 */
public class SimpleCommand implements DroneCommand {

    private final String droneId;
    private final CommandType commandType;
    private final Map<String, Object> parameters;
    private final String commandId;
    private final String taskId;
    
    /**
     * 创建一个简单命令
     *
     * @param droneId     无人机ID
     * @param commandType 命令类型
     * @param parameters  命令参数
     * @param commandId   命令ID
     * @param taskId      任务ID
     */
    public SimpleCommand(String droneId, CommandType commandType, Map<String, Object> parameters, String commandId, String taskId) {
        this.droneId = droneId;
        this.commandType = commandType;
        this.parameters = parameters;
        this.commandId = commandId;
        this.taskId = taskId;
    }

    /**
     * 创建一个简单命令（不包含taskId）
     *
     * @param droneId     无人机ID
     * @param commandType 命令类型
     * @param parameters  命令参数
     * @param commandId   命令ID
     */
    public SimpleCommand(String droneId, CommandType commandType, Map<String, Object> parameters, String commandId) {
        this(droneId, commandType, parameters, commandId, null);
    }
    
    /**
     * 创建一个无命令ID的简单命令
     *
     * @param droneId     无人机ID
     * @param commandType 命令类型
     * @param parameters  命令参数
     */
    public SimpleCommand(String droneId, CommandType commandType, Map<String, Object> parameters) {
        this(droneId, commandType, parameters, null, null);
    }
    
    @Override
    public String getDroneId() {
        return droneId;
    }
    
    @Override
    public CommandType getCommandType() {
        return commandType;
    }
    
    @Override
    public Map<String, Object> getParameters() {
        return parameters;
    }
    
    @Override
    public String getCommandId() {
        return commandId;
    }

    @Override
    public String getTaskId() {
        return taskId;
    }
} 