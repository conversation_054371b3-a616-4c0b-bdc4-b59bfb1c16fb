package com.md.mongodb.repository;

import com.md.mongodb.document.FlightTrackPoint;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

/**
 * 飞行轨迹点数据访问接口 提供对MongoDB中flight_track_points集合的基本CRUD操作 继承MongoRepository以获取基础的MongoDB操作功能
 */
public interface FlightTrackPointRepository extends MongoRepository<FlightTrackPoint, String> {

    /**
     * 根据任务ID查询轨迹点列表 按时间戳升序排序，确保轨迹点按时间顺序返回
     *
     * @param taskId 任务ID
     * @return 该任务的所有轨迹点列表
     */
    List<FlightTrackPoint> findByTaskIdOrderByTimestampAsc(String taskId);
    
    /**
     * 根据任务ID查询轨迹点列表（分页版本）
     *
     * @param taskId   任务ID
     * @param pageable 分页参数
     * @return 分页的轨迹点列表
     */
    Page<FlightTrackPoint> findByTaskIdOrderByTimestampAsc(String taskId, Pageable pageable);

    /**
     * 根据任务ID和时间范围查询轨迹点 用于查询特定时间段内的轨迹数据
     *
     * @param taskId    任务ID
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @return 符合条件的轨迹点列表
     */
    List<FlightTrackPoint> findByTaskIdAndTimestampBetweenOrderByTimestampAsc(String taskId, Long startTime,
        Long endTime);
    
    /**
     * 根据任务ID和时间范围查询轨迹点（分页版本）
     *
     * @param taskId    任务ID
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @param pageable  分页参数
     * @return 分页的轨迹点列表
     */
    Page<FlightTrackPoint> findByTaskIdAndTimestampBetweenOrderByTimestampAsc(String taskId, Long startTime,
        Long endTime, Pageable pageable);
    
    /**
     * 根据无人机ID查询轨迹点列表
     *
     * @param droneId 无人机ID
     * @return 该无人机的所有轨迹点列表
     */
    List<FlightTrackPoint> findByDroneIdOrderByTimestampAsc(String droneId);
    
    /**
     * 根据无人机ID查询轨迹点列表（分页版本）
     *
     * @param droneId  无人机ID
     * @param pageable 分页参数
     * @return 分页的轨迹点列表
     */
    Page<FlightTrackPoint> findByDroneIdOrderByTimestampAsc(String droneId, Pageable pageable);
    
    /**
     * 根据任务ID和无人机ID查询轨迹点列表
     *
     * @param taskId  任务ID
     * @param droneId 无人机ID
     * @return 特定任务中特定无人机的所有轨迹点列表
     */
    List<FlightTrackPoint> findByTaskIdAndDroneIdOrderByTimestampAsc(String taskId, String droneId);
    
    /**
     * 根据任务ID查询最新的轨迹点
     *
     * @param taskId 任务ID
     * @return 该任务的最新轨迹点
     */
    FlightTrackPoint findFirstByTaskIdOrderByTimestampDesc(String taskId);
    
    /**
     * 根据无人机ID查询最新的轨迹点
     *
     * @param droneId 无人机ID
     * @return 该无人机的最新轨迹点
     */
    FlightTrackPoint findFirstByDroneIdOrderByTimestampDesc(String droneId);

    /**
     * 根据任务ID删除轨迹点 用于清理历史轨迹数据
     *
     * @param taskId 任务ID
     */
    void deleteByTaskId(String taskId);

    /**
     * 根据任务ID和无人机ID删除轨迹点 用于清理特定无人机在特定任务中的历史轨迹数据
     *
     * @param taskId  任务ID
     * @param droneId 无人机ID
     */
    void deleteByTaskIdAndDroneId(String taskId, String droneId);
}