package com.md.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.constant.MqttConstants;
import com.md.domain.bo.UavInfoBo;
import com.md.domain.po.PlatformInfo;
import com.md.domain.po.UavInfo;
import com.md.domain.vo.UavInfoVo;
import com.md.enums.BaseInfoSyncEnum;
import com.md.mapper.UavInfoMapper;
import com.md.service.IBaseInfoService;
import com.md.service.IDroneStatusService;
import com.md.service.IPlatformInfoService;
import com.md.service.IUavInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.utils.QueryUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 无人机Service业务层处理
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UavInfoServiceImpl extends ServiceImpl<UavInfoMapper, UavInfo> implements IUavInfoService {

    private final UavInfoMapper uavInfoMapper;

    private final IPlatformInfoService platformInfoService;

    private final IDroneStatusService droneStatusService;

    /**
     * 查询无人机
     *
     * @param id 无人机主键
     * @return 无人机
     */
    @Override
    public UavInfoVo selectFkUavInfoById(Long id) {
        // 获取基础信息
        UavInfo uavInfo = uavInfoMapper.selectFkUavInfoById(id);

        if (uavInfo != null && uavInfo.getParams() != null) {
            try {
                // 1. 获取在线状态
                boolean isOnline = droneStatusService.isDroneOnline(uavInfo.getUavCode());
                uavInfo.getParams().put("online", isOnline);

                // 2. 获取Redis中的实时状态数据
                String redisKey = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + uavInfo.getUavCode();
                String jsonStr = TenantHelper.ignore(() -> RedisUtils.getCacheObject(redisKey));

                if (jsonStr != null) {
                    // 将实时状态数据放入params Map中
                    JSONObject statusData = JSON.parseObject(jsonStr);
                    uavInfo.getParams().put("realTimeStatus", statusData);
                    log.info("获取无人机 {} 的实时状态数据成功", uavInfo.getUavCode());
                } else {
                    log.info("无人机 {} 暂无实时状态数据", uavInfo.getUavCode());
                }
            } catch (Exception e) {
                log.error("获取无人机 {} 的状态数据失败", uavInfo.getUavCode(), e);
            }
        }

        return MapstructUtils.convert(uavInfo, UavInfoVo.class);
    }

    /**
     * 查询无人机列表
     *
     * @param bo 无人机
     * @return 无人机
     */
    @Override
    public List<UavInfoVo> selectFkUavInfoList(UavInfoBo bo) {
        UavInfo uavInfo = MapstructUtils.convert(bo, UavInfo.class);
        List<UavInfo> list = uavInfoMapper.selectFkUavInfoList(uavInfo);
        return MapstructUtils.convert(list, UavInfoVo.class);
    }

    /**
     * 新增无人机
     *
     * @param bo 无人机
     * @return 结果
     */
    @Override
    public int insertFkUavInfo(UavInfoBo bo) {
        UavInfo uavInfo = MapstructUtils.convert(bo, UavInfo.class);

        assert uavInfo != null;
        uavInfo.setDataSource("HANDLE");  // 手动添加
        uavInfo.setCreateTime(DateUtils.getNowDate());
        uavInfo.setCreateBy(LoginHelper.getUserId());

        if (StringUtils.isNotEmpty(uavInfo.getFlightControlNo())) {
            PlatformInfo platformInfo = platformInfoService.getOne(
                Wrappers.<PlatformInfo>lambdaQuery().eq(PlatformInfo::getFlightControlNo, uavInfo.getFlightControlNo())
                    .last("limit 1"));
            if (platformInfo != null) {
                uavInfo.setManufacturerName(platformInfo.getManufacturerName());
            }
        }

        return uavInfoMapper.insertFkUavInfo(uavInfo);
    }

    /**
     * 修改无人机
     *
     * @param bo 无人机
     * @return 结果
     */
    @Override
    public int updateFkUavInfo(UavInfoBo bo) {
        UavInfo uavInfo = MapstructUtils.convert(bo, UavInfo.class);

        uavInfo.setUpdateTime(DateUtils.getNowDate());
        uavInfo.setUpdateBy(LoginHelper.getUserId());

        if (StringUtils.isNotEmpty(uavInfo.getFlightControlNo())) {
            PlatformInfo platformInfo = platformInfoService.getOne(
                Wrappers.<PlatformInfo>lambdaQuery().eq(PlatformInfo::getFlightControlNo, uavInfo.getFlightControlNo())
                    .last("limit 1"));
            if (platformInfo != null) {
                uavInfo.setManufacturerName(platformInfo.getManufacturerName());
            }
        }
        return uavInfoMapper.updateFkUavInfo(uavInfo);
    }

    /**
     * 批量删除无人机
     *
     * @param ids 需要删除的无人机主键
     * @return 结果
     */
    @Override
    public int deleteFkUavInfoByIds(Long[] ids) {
        return uavInfoMapper.deleteFkUavInfoByIds(ids);
    }

    /**
     * 删除无人机信息
     *
     * @param id 无人机主键
     * @return 结果
     */
    @Override
    public int deleteFkUavInfoById(Long id) {
        return uavInfoMapper.deleteFkUavInfoById(id);
    }

    /**
     * 同步无人机信息
     *
     * @param code
     * @return
     */
    @Override
    public boolean syncUAV(String code) {
        IBaseInfoService syncType = BaseInfoSyncEnum.getSyncType(code);
        if (syncType != null) {
            syncType.syncUAV();
        }
        return true;
    }

    /**
     * 分页查询无人机列表
     *
     * @param bo        无人机
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<UavInfoVo> selectUavInfoPage(UavInfoBo bo, PageQuery pageQuery) {
        UavInfo uavInfo = MapstructUtils.convert(bo, UavInfo.class);
        String createByName = bo.getCreateByName();
        assert uavInfo != null;
        LambdaQueryWrapper<UavInfo> lqw = buildQueryWrapper(uavInfo, createByName);
        Page<UavInfo> page = baseMapper.selectPage(pageQuery.build(), lqw);

        TableDataInfo<UavInfoVo> dataInfo = new TableDataInfo<>();
        dataInfo.setRows(MapstructUtils.convert(page.getRecords(), UavInfoVo.class));
        dataInfo.setTotal(page.getTotal());
        return dataInfo;
    }

    /**
     * 构建查询条件
     *
     * @param uavInfo      无人机信息
     * @param createByName 创建者名称
     * @return 查询条件
     */
    private LambdaQueryWrapper<UavInfo> buildQueryWrapper(UavInfo uavInfo, String createByName) {
        LambdaQueryWrapper<UavInfo> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(uavInfo.getUavCode()), UavInfo::getUavCode, uavInfo.getUavCode());
        lqw.eq(StringUtils.isNotBlank(uavInfo.getCategoryCode()), UavInfo::getCategoryCode, uavInfo.getCategoryCode());
        lqw.like(StringUtils.isNotBlank(uavInfo.getManufacturerName()), UavInfo::getManufacturerName,
            uavInfo.getManufacturerName());
        lqw.eq(StringUtils.isNotBlank(uavInfo.getFlightControlNo()), UavInfo::getFlightControlNo,
            uavInfo.getFlightControlNo());
        lqw.like(StringUtils.isNotBlank(uavInfo.getUavName()), UavInfo::getUavName, uavInfo.getUavName());
        lqw.eq(uavInfo.getStatus() != null, UavInfo::getStatus, uavInfo.getStatus());
        lqw.eq(StringUtils.isNotBlank(uavInfo.getDataSource()), UavInfo::getDataSource, uavInfo.getDataSource());
        lqw.eq(uavInfo.getCreateBy() != null, UavInfo::getCreateBy, uavInfo.getCreateBy());
        lqw.ge(uavInfo.getStartTime() != null, UavInfo::getCreateTime, uavInfo.getStartTime());
        lqw.le(uavInfo.getEndTime() != null, UavInfo::getCreateTime, uavInfo.getEndTime());
        lqw.orderByDesc(UavInfo::getId);

        // 根据创建者名称查询
        QueryUtils.buildCreateByQuery(lqw, UavInfo::getCreateBy, createByName);

        if (uavInfo.getStartTime() != null && uavInfo.getEndTime() != null) {
            lqw.between(UavInfo::getCreateTime, uavInfo.getStartTime(), uavInfo.getEndTime());
        }
        // 数据范围过滤
        // dataScope(lqw, SecurityUtils.getDeptId());
        return lqw;
    }
}
