package com.md.flight.yx.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.md.flight.yx.domain.po.YxFlightRoute;
import com.md.flight.yx.model.task.RoutePoineTaskRequest;

import java.util.List;

/**
 * 亿讯航线Service接口
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
public interface IYxFlightRouteService extends IService<YxFlightRoute> {
    /**
     * 获取航线列表
     *
     * @return
     */
    List<YxFlightRoute> getLineList();

    /**
     * 航线详情
     *
     * @param routePoineTaskRequest
     * @return
     */
    YxFlightRoute getLineDetail(RoutePoineTaskRequest routePoineTaskRequest);
}
