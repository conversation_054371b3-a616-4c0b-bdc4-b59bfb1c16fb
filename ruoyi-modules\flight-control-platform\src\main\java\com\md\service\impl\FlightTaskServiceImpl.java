package com.md.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.command.model.dto.CommandResult;
import com.md.domain.bo.FlightTaskBo;
import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.domain.po.FlightTaskPointAction;
import com.md.domain.po.Flyer;
import com.md.domain.po.PlatformInfo;
import com.md.domain.vo.FlightTaskVo;
import com.md.mapper.FlightLineTaskMapper;
import com.md.mapper.FlightTaskPointActionMapper;
import com.md.mapper.FlightTaskPointMapper;
import com.md.mapper.FlyerMapper;
import com.md.mapper.PlatformInfoMapper;
import com.md.mapper.UavInfoMapper;
import com.md.service.IFenceDetectionService;
import com.md.service.IFlightLineService;
import com.md.service.IFlightTaskEmergencyService;
import com.md.service.IFlightTaskExecutionService;
import com.md.service.IFlightTaskFenceService;
import com.md.service.IFlightTaskFileService;
import com.md.service.IFlightTaskService;
import com.md.service.IFlightTaskStatusService;
import com.md.utils.BeanConvertUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.utils.QueryUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.service.ISysUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 飞行任务服务实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FlightTaskServiceImpl extends ServiceImpl<FlightLineTaskMapper, FlightTask> implements IFlightTaskService {

    private final FlyerMapper flyerMapper;
    private final ISysUserService sysUserService;
    private final PlatformInfoMapper platformInfoMapper;
    private final FlightTaskPointMapper flightTaskPointMapper;
    private final FlightTaskPointActionMapper flightTaskPointActionMapper;
    private final ApplicationEventPublisher eventPublisher;
    private final UavInfoMapper uavInfoMapper;

    private final IFenceDetectionService fenceDetectionService;
    private final IFlightLineService flightLineService;

    // 新增的专门服务 - 用于委托模式
    private final IFlightTaskFileService flightTaskFileService;
    private final IFlightTaskExecutionService flightTaskExecutionService;
    private final IFlightTaskEmergencyService flightTaskEmergencyService;
    private final IFlightTaskFenceService flightTaskFenceService;
    private final IFlightTaskStatusService flightTaskStatusService;

    @Override
    public List<FlightTaskVo> selectFlightTaskList(FlightTaskBo bo) {
        // 1. 查询基础数据
        List<FlightTask> taskList = baseMapper.selectFlightLineTaskList(bo);
        List<PlatformInfo> platformInfoList = platformInfoMapper.selectList(null);

        // 使用 BeanConvertUtils 进行转换，自动处理分页，并提供自定义转换逻辑
        List<FlightTaskVo> resultList = BeanConvertUtils.convertList(taskList, FlightTaskVo.class, (task, vo) -> {
            // 手动处理taskStatus的类型转换
            if (task.getTaskStatus() != null) {
                vo.setTaskStatus(String.valueOf(task.getTaskStatus()));
            }

            // 补充飞手信息
            Optional.ofNullable(task.getFlyerId()).map(flyerId -> flyerMapper.selectOne(
                    new LambdaQueryWrapper<Flyer>().select(Flyer::getFlyerName).eq(Flyer::getId, flyerId).last("limit 1")))
                .ifPresent(flyer -> vo.setFlyerName(flyer.getFlyerName()));

            // 补充执飞人信息
            Optional.ofNullable(task.getOperatorId()).map(operatorId -> sysUserService.selectUserById(operatorId))
                .ifPresent(operator -> vo.setOperatorName(operator.getUserName()));

            // 补充厂家信息
            Optional.ofNullable(task.getFlightControlNo()).ifPresent(controlNo -> platformInfoList.stream()
                .filter(platform -> controlNo.equals(platform.getFlightControlNo())).findFirst()
                .ifPresent(platform -> vo.setManufacturerName(platform.getManufacturerName())));
        });

        // 根据飞手名称过滤
        if (bo.getFlyerName() != null && !bo.getFlyerName().isEmpty()) {
            String flyerNameFilter = bo.getFlyerName().toLowerCase();
            resultList = resultList.stream()
                .filter(vo -> vo.getFlyerName() != null && vo.getFlyerName().toLowerCase().contains(flyerNameFilter))
                .collect(Collectors.toList());
        }

        // 根据操作员名称过滤
        if (bo.getOperatorName() != null && !bo.getOperatorName().isEmpty()) {
            String operatorNameFilter = bo.getOperatorName().toLowerCase();
            resultList = resultList.stream().filter(
                    vo -> vo.getOperatorName() != null && vo.getOperatorName().toLowerCase().contains(operatorNameFilter))
                .collect(Collectors.toList());
        }

        return resultList;
    }

    @Override
    public FlightTaskVo selectFlightTaskById(String id) {
        // 1. 查询基础数据
        FlightTask task = baseMapper.selectById(id);
        if (task == null) {
            return null;
        }

        // 2. 转换并补充关联数据
        FlightTaskVo vo = new FlightTaskVo();
        BeanUtils.copyProperties(task, vo);
        // 手动处理taskStatus的类型转换
        if (task.getTaskStatus() != null) {
            vo.setTaskStatus(String.valueOf(task.getTaskStatus()));
        }

        // 3. 补充飞手信息
        Optional.ofNullable(task.getFlyerId()).map(flyerId -> flyerMapper.selectOne(
                new LambdaQueryWrapper<Flyer>().select(Flyer::getFlyerName).eq(Flyer::getId, flyerId).last("limit 1")))
            .ifPresent(flyer -> vo.setFlyerName(flyer.getFlyerName()));

        // 4. 补充执飞人信息
        Optional.ofNullable(task.getOperatorId()).map(operatorId -> sysUserService.selectUserById(operatorId))
            .ifPresent(operator -> vo.setOperatorName(operator.getUserName()));

        // 5. 补充厂家信息
        Optional.ofNullable(task.getFlightControlNo()).ifPresent(
            controlNo -> platformInfoMapper.selectList(null).stream()
                .filter(platform -> controlNo.equals(platform.getFlightControlNo())).findFirst()
                .ifPresent(platform -> vo.setManufacturerName(platform.getManufacturerName())));

        // 6. 查询并设置航点数据
        List<FlightTaskPoint> points = flightTaskPointMapper.selectList(
            new LambdaQueryWrapper<FlightTaskPoint>().eq(FlightTaskPoint::getLineId, task.getId()));

        // 7. 查询并设置航点动作数据
        points.forEach(point -> point.setActions(flightTaskPointActionMapper.selectList(
            new LambdaQueryWrapper<FlightTaskPointAction>().eq(FlightTaskPointAction::getPointId, point.getId()))));

        vo.setPoints(points);
        return vo;
    }

    /**
     * 校验任务名称唯一性
     *
     * @param taskName 任务名称
     * @param taskId   任务ID（更新时使用，新增时为null）
     */
    private void validateTaskNameUnique(String taskName, String taskId) {
        if (StringUtils.isEmpty(taskName)) {
            throw new ServiceException("任务名称不能为空");
        }

        // 校验任务名称不能包含特殊字符和空格
        String regex = "^[a-zA-Z0-9\\u4e00-\\u9fa5\\-_]+$";
        if (!taskName.matches(regex)) {
            throw new ServiceException("任务名称只能包含字母、数字、中文、连字符和下划线，不允许包含空格和特殊字符");
        }

        LambdaQueryWrapper<FlightTask> queryWrapper =
            new LambdaQueryWrapper<FlightTask>().eq(FlightTask::getTaskName, taskName);

        // 如果是更新操作，排除自身
        if (StringUtils.isNotEmpty(taskId)) {
            queryWrapper.ne(FlightTask::getId, taskId);
        }

        // 判断是否存在相同名称的任务
        if (baseMapper.selectCount(queryWrapper) > 0) {
            throw new ServiceException("任务名称'" + taskName + "'已存在");
        }
    }

    /**
     * 创建航线任务
     *
     * @param bo 航线任务信息
     * @return 任务ID
     */
    @Override
    @Transactional
    public String insertFlightTask(FlightTaskBo bo) {
        log.info("开始创建航线任务，参数：{}", bo);
        try {
            // 转换Bo为Vo进行处理
            FlightTaskVo vo = BeanConvertUtils.convert(bo, FlightTaskVo.class);

            // 校验任务名称唯一性
            validateTaskNameUnique(vo.getTaskName(), null);

            FlightTask flightTask = new FlightTask();
            // 1. 设置航线信息
            BeanUtils.copyProperties(vo, flightTask);
            flightTask.setId(IdWorker.getIdStr());
            flightTask.setCreateTime(DateUtils.getNowDate());
            //            flightTask.setCreateBy(SecurityUtils.getUsername());
            flightTask.setCreateBy(LoginHelper.getUserId());
            flightTask.setTaskStatus(0);

            // 暂存围栏ID
            String fenceIds = flightTask.getFenceId();

            // 清空围栏ID，避免重复处理
            flightTask.setFenceId(null);

            // 插入任务数据
            baseMapper.insert(flightTask);

            // 2. 设置航点信息和动作信息
            List<FlightTaskPoint> points = vo.getPoints();
            if (points != null && !points.isEmpty()) {
                List<FlightTaskPointAction> allActions = new ArrayList<>();
                String currentTenantId = TenantHelper.getTenantId();
                if (StringUtils.isBlank(currentTenantId)) {
                    currentTenantId = LoginHelper.getTenantId();
                }
                log.info("设置航点租户ID: {}", currentTenantId);

                for (FlightTaskPoint point : points) {
                    String pointId = IdWorker.getIdStr();
                    point.setId(pointId);
                    point.setLineId(flightTask.getId());
                    point.setTenantId(currentTenantId);
                    log.debug("航点[{}]设置租户ID: {}", pointId, currentTenantId);

                    // 处理航点动作
                    if (point.getActions() != null && !point.getActions().isEmpty()) {
                        for (FlightTaskPointAction action : point.getActions()) {
                            action.setPointId(pointId);
                            action.setTenantId(currentTenantId);
                        }
                        allActions.addAll(point.getActions());
                    }
                }
                flightTaskPointMapper.batchInsertPoints(points);

                // 批量插入动作
                if (!allActions.isEmpty()) {
                    flightTaskPointActionMapper.batchInsertActions(allActions);
                }
            }

            // 3. 处理围栏关联 - 调用围栏更新方法
            if (StringUtils.isNotEmpty(fenceIds)) {
                updateTaskFences(flightTask.getId(), fenceIds);
            }

            log.info("航线任务创建成功，ID：{}", flightTask.getId());
            return flightTask.getId();
        } catch (BeansException e) {
            log.error("航线任务创建失败，参数：{}，异常：", bo, e);
            throw new ServiceException("创建任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public String updateFlightTask(FlightTaskBo bo) {
        try {
            // 转换Bo为Vo进行处理
            FlightTaskVo vo = BeanConvertUtils.convert(bo, FlightTaskVo.class);

            log.info("开始更新航线任务，参数：{}", bo);
            // 1. 验证任务是否存在
            FlightTask existTask = baseMapper.selectById(vo.getId());
            if (existTask == null) {
                throw new ServiceException("任务不存在");
            }

            // 校验任务名称唯一性
            validateTaskNameUnique(vo.getTaskName(), vo.getId());

            // 2. 更新任务基本信息
            FlightTask flightTask = new FlightTask();
            BeanUtils.copyProperties(vo, flightTask);
            flightTask.setUpdateTime(DateUtils.getNowDate());
            flightTask.setUpdateBy(LoginHelper.getUserId());

            // 暂存围栏ID
            String fenceIds = flightTask.getFenceId();

            // 清空围栏ID，避免重复处理
            flightTask.setFenceId(null);

            // 更新任务数据
            baseMapper.updateById(flightTask);

            // 3. 先查询所有相关的航点
            List<FlightTaskPoint> existPoints = flightTaskPointMapper.selectList(
                new LambdaQueryWrapper<FlightTaskPoint>().eq(FlightTaskPoint::getLineId, flightTask.getId()));

            // 4. 删除相关的动作和航点
            if (CollectionUtils.isNotEmpty(existPoints)) {
                List<String> pointIds = existPoints.stream().map(FlightTaskPoint::getId).collect(Collectors.toList());

                // 删除动作
                if (CollectionUtils.isNotEmpty(pointIds)) {
                    flightTaskPointActionMapper.delete(
                        new LambdaQueryWrapper<FlightTaskPointAction>().in(FlightTaskPointAction::getPointId,
                            pointIds));
                }
            }
            // 删除航点
            flightTaskPointMapper.delete(
                new LambdaQueryWrapper<FlightTaskPoint>().eq(FlightTaskPoint::getLineId, flightTask.getId()));

            // 5. 重新插入航点和动作数据
            List<FlightTaskPoint> points = vo.getPoints();
            if (points != null && !points.isEmpty()) {
                List<FlightTaskPointAction> allActions = new ArrayList<>();
                String currentTenantId = TenantHelper.getTenantId();
                if (StringUtils.isBlank(currentTenantId)) {
                    currentTenantId = LoginHelper.getTenantId();
                }
                log.info("更新航点租户ID: {}", currentTenantId);

                for (FlightTaskPoint point : points) {
                    String pointId = IdWorker.getIdStr();
                    point.setId(pointId);
                    point.setLineId(flightTask.getId());
                    point.setCreateTime(DateUtils.getNowDate());
                    point.setTenantId(currentTenantId);

                    // 处理航点动作
                    if (point.getActions() != null && !point.getActions().isEmpty()) {
                        for (FlightTaskPointAction action : point.getActions()) {
                            action.setPointId(pointId);
                            action.setCreateTime(DateUtils.getNowDate());
                            action.setTenantId(currentTenantId);
                        }
                        allActions.addAll(point.getActions());
                    }
                }

                // 批量插入航点
                flightTaskPointMapper.batchInsertPoints(points);

                // 批量插入动作
                if (!allActions.isEmpty()) {
                    flightTaskPointActionMapper.batchInsertActions(allActions);
                }
            }

            // 6. 处理围栏关联 - 调用围栏更新方法
            updateTaskFences(vo.getId(), fenceIds);

            log.info("航线任务更新成功，ID：{}", vo.getId());
            return vo.getId();
        } catch (ServiceException e) {
            log.error("航线任务更新失败，参数：{}，异常：", bo, e);
            throw new ServiceException("更新任务失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public int deleteFlightTaskByIds(Long[] ids) {
        // 1. 验证任务状态
        LambdaQueryWrapper<FlightTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FlightTask::getId, Arrays.asList(ids)).ne(FlightTask::getTaskStatus, 0);
        Long count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            throw new ServiceException("只能删除未执行的任务");
        }
        // 2. 查询所有相关的航点
        List<FlightTaskPoint> points = flightTaskPointMapper.selectList(
            new LambdaQueryWrapper<FlightTaskPoint>().in(FlightTaskPoint::getLineId, Arrays.asList(ids)));

        // 3. 删除相关的动作
        if (CollectionUtils.isNotEmpty(points)) {
            List<String> pointIds = points.stream().map(FlightTaskPoint::getId).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(pointIds)) {
                flightTaskPointActionMapper.delete(
                    new LambdaQueryWrapper<FlightTaskPointAction>().in(FlightTaskPointAction::getPointId, pointIds));
            }
        }

        // 4. 删除航点
        flightTaskPointMapper.delete(
            new LambdaQueryWrapper<FlightTaskPoint>().in(FlightTaskPoint::getLineId, Arrays.asList(ids)));

        // 5. 删除任务
        return baseMapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    @Transactional
    public boolean buildKmzByFlightLine(String id) {
        // 委托给文件服务
        return flightTaskFileService.buildKmzByFlightLine(id);
    }

    // 以下是原有方法的备份，已迁移到FlightTaskFileServiceImpl中
    /*
        try {
            // 1. 获取航线和航点信息
            FlightTaskVo flightLine = this.selectFlightTaskById(id);
            if (flightLine == null) {
                throw new ServiceException("航线不存在");
            }
            List<FlightTaskPoint> points = flightLine.getPoints();

            // 2. 更新任务状态为生成中
            FlightTask updateTask = new FlightTask();
            updateTask.setId(id);
            updateTask.setFileGenerateStatus(1);
            updateTask.setFileGenerateTime(DateUtils.getNowDate());
            updateTask.setUpdateTime(DateUtils.getNowDate());
            updateTask.setUpdateBy(LoginHelper.getUserId());
            updateTask.setIsUploaded(0); // 重置上传状态为未上传
            baseMapper.updateById(updateTask);

            try {
                // 3. 构建UavRouteReq对象
                UavRouteReq uavRouteReq = new UavRouteReq();

                // 设置航线基本属性
                uavRouteReq.setFinishAction(flightLine.getFinishedAction());
                uavRouteReq.setExitOnRcLostAction(flightLine.getMissingAction());

                // 设置飞行速度，为空则使用默认值
                BigDecimal flightSpeed = flightLine.getFlightSpeed();
                uavRouteReq.setAutoFlightSpeed(flightSpeed != null ? flightSpeed.doubleValue() : 2.0);

                // 设置全局高度，为空则使用默认值100
                BigDecimal flightHeight = flightLine.getFlightHeight();
                uavRouteReq.setGlobalHeight(flightHeight != null ? flightHeight.doubleValue() : 20.0);

                // 4. 构建航点列表
                List<RoutePointReq> routePoints = buildRoutePoints(points);
                uavRouteReq.setRoutePointList(routePoints);

                // 5. 设置默认值
                setDefaultValues(uavRouteReq);

                // 6. 生成kmz文件，使用任务名称命名
                String kmzFilePath = uavRouteService.buildKmz(uavRouteReq, flightLine.getTaskName());

                // 7. 上传文件到MinIO
                File kmzFile = new File(kmzFilePath);
                if (!kmzFile.exists()) {
                    throw new ServiceException("KMZ文件不存在");
                }

                try (FileInputStream inputStream = new FileInputStream(kmzFile)) {
                    // 直接使用原始文件名，不做任何修改,后续可能需要禁用非法字符
                    String fileName = kmzFile.getName();
                    String objectName = "wayline/" + fileName;

                    // 创建更新对象并设置共同属性
                    updateTask = new FlightTask();
                    updateTask.setId(id);
                    updateTask.setFileGenerateStatus(2);
                    updateTask.setFileGenerateTime(DateUtils.getNowDate());
                    updateTask.setUpdateTime(DateUtils.getNowDate());
                    updateTask.setUpdateBy(LoginHelper.getUserId());
                    updateTask.setKmzFileName(fileName);
                    updateTask.setFileSize(kmzFile.length());

                    // 检查是否为DJI厂家
                    if ("DJI".equals(flightLine.getFlightControlNo())) {
                        // 上传到MinIO
                        String fileUrl =
                            minioUtils.uploadFile(minioUtils.getDefaultBucketName(), objectName, inputStream,
                                "application/vnd.google-earth.kmz");

                        // 设置文件路径和上传状态
                        updateTask.setKmzFilePath(fileUrl);
                        updateTask.setIsUploaded(1); // 设置为已上传
                        log.info("航线文件生成并上传成功，文件路径：{}", fileUrl);
                    } else {
                        // 非DJI厂家，不进行文件上传
                        log.info("非DJI厂家，不进行文件上传，厂家编号：{}", flightLine.getFlightControlNo());
                        updateTask.setIsUploaded(0); // 设置为未上传
                    }

                    // 执行更新
                    baseMapper.updateById(updateTask);
                    return true;

                } finally {
                    // 清理临时文件
                    try {
                        Files.deleteIfExists(Paths.get(kmzFilePath));
                    } catch (IOException e) {
                        log.warn("清理临时文件失败：{}", kmzFilePath, e);
                    }
                }

            } catch (Exception e) {
                // 9. 更新任务状态为生成失败
                updateTask = new FlightTask();
                updateTask.setId(id);
                updateTask.setFileGenerateStatus(3);
                updateTask.setFileGenerateError(e.getMessage());
                updateTask.setIsUploaded(0);  // 设置为未上传状态
                updateTask.setUpdateTime(DateUtils.getNowDate());
                updateTask.setUpdateBy(LoginHelper.getUserId());
                baseMapper.updateById(updateTask);

                throw e;
            }

        } catch (Exception e) {
            log.error("生成或上传航线文件失败，航线ID：{}", id, e);
            throw new ServiceException("生成航线文件失败：" + e.getMessage());
        }
    }
    */

    @Override
    public String exportKmz(String taskName, HttpServletResponse response) {
        // 委托给文件服务
        return flightTaskFileService.exportKmz(taskName, response);
    }

    // 以下是原有方法的备份，已迁移到FlightTaskFileServiceImpl中
    /*
        try {
            // 1. 查询航线任务
            FlightTaskBo query = new FlightTaskBo();
            query.setTaskName(taskName);
            List<FlightTaskVo> taskList = this.selectFlightTaskList(query);
            if (CollectionUtils.isEmpty(taskList)) {
                throw new ServiceException("航线任务不存在");
            }
            FlightTaskVo task = taskList.get(0);

            // 2. 检查文件是否已生成
            if (task.getFileGenerateStatus() != 2) {
                throw new ServiceException("航线KMZ文件尚未生成");
            }

            // 3. 获取文件访问URL
            String storedUrl = task.getKmzFilePath();
            if (StringUtils.isNotEmpty(storedUrl) && !minioUtils.isUrlExpired(storedUrl)) {
                log.info("使用已存在的文件访问URL：{}", storedUrl);
                return storedUrl;
            }

            // 4. 如果URL不存在或已过期，则重新获取
            try {
                String objectName = "wayline/" + task.getKmzFileName();
                String newUrl = minioUtils.getFileUrl(minioUtils.getDefaultBucketName(), objectName);
                // 更新数据库中的URL和上传状态
                FlightTask updateTask = new FlightTask();
                updateTask.setId(task.getId());
                updateTask.setKmzFilePath(newUrl);
                updateTask.setUpdateTime(DateUtils.getNowDate());
                updateTask.setUpdateBy(LoginHelper.getUserId());
                updateTask.setIsUploaded(1); // 设置为已上传
                baseMapper.updateById(updateTask);
                log.info("成功获取新的文件访问URL：{}", newUrl);
                return newUrl;
            } catch (Exception e) {
                log.error("获取文件访问URL失败，使用已存储的URL：{}", storedUrl, e);
                return storedUrl;
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("导出航线KMZ文件失败，任务名称：{}，异常：", taskName, e);
            throw new ServiceException("导出航线KMZ文件失败：" + e.getMessage());
        }
    }

    /**
     * 根据任务名称查询任务
     *
     * @param taskName 任务名称
     * @return 航线任务
     */
    private FlightTask selectFlightTaskByTaskName(String taskName) {
        return this.lambdaQuery().eq(FlightTask::getTaskName, taskName).last("limit 1").one();
    }

    @Override
    public Map<String, Object> getCurrentTask(String uavCode) {
        // 委托给执行服务
        return flightTaskExecutionService.getCurrentTask(uavCode);
    }

    /**
     * 更新任务的执飞人ID
     *
     * @param taskName   任务名称
     * @param operatorId 执飞人ID
     * @return 是否更新成功
     */
    @Override
    public boolean updateTaskOperator(String taskName, Long operatorId) {
        // 委托给专门的状态管理服务
        return flightTaskStatusService.updateTaskOperator(taskName, operatorId);
    }

    /**
     * 复制航线任务
     *
     * @param taskId 原任务ID
     * @return 新任务ID
     */
    @Override
    @Transactional
    public String copyFlightTask(String taskId) {
        // 委托给文件服务
        return flightTaskFileService.copyFlightTask(taskId);
    }

    /**
     * 生成复制任务的名称 规则：原名称_copy[n]，n从1开始，如果已存在则n+1
     */
    private String generateCopyTaskName(String originalName) {
        String baseName = originalName + "_copy";
        String newName = baseName;
        int suffix = 1;

        while (true) {
            // 检查当前名称是否已存在
            FlightTaskBo query = new FlightTaskBo();
            query.setTaskName(newName);
            List<FlightTaskVo> existingTasks = selectFlightTaskList(query);

            if (existingTasks.isEmpty()) {
                return newName;
            }

            // 如果存在，则增加后缀数字
            newName = baseName + suffix;
            suffix++;
        }
    }

    @Override
    public boolean updateTaskStatus(String taskName, Integer status, String username) {
        // 委托给专门的状态管理服务
        return flightTaskStatusService.updateTaskStatus(taskName, status, username);
    }

    @Override
    public CommandResult executeEmergencyLanding(String taskId, String droneId, BigDecimal currentLatitude,
        BigDecimal currentLongitude, BigDecimal currentAltitude) {
        // 委托给紧急服务
        return flightTaskEmergencyService.executeEmergencyLanding(taskId, droneId, currentLatitude, currentLongitude,
            currentAltitude);
    }

    @Override
    public FlightTaskPoint findNearestEmergencyLandingPoint(String taskId, BigDecimal latitude, BigDecimal longitude) {
        // 委托给紧急服务
        return flightTaskEmergencyService.findNearestEmergencyLandingPoint(taskId, latitude, longitude);
    }

    @Override
    public List<FlightTaskPoint> getAllEmergencyLandingPoints(String taskId) {
        // 委托给紧急服务
        return flightTaskEmergencyService.getAllEmergencyLandingPoints(taskId);
    }

    @Override
    public boolean updateTaskFences(String taskId, String fenceIds) {
        // 委托给围栏服务
        return flightTaskFenceService.updateTaskFences(taskId, fenceIds);
    }

    @Override
    public String getTaskFences(String taskId) {
        // 委托给围栏服务
        return flightTaskFenceService.getTaskFences(taskId);
    }

    @Override
    public CompletableFuture<Boolean> executeFlightTask(String taskId) {
        // 委托给执行服务
        return flightTaskExecutionService.executeFlightTask(taskId);
    }

    /**
     * 检查航点是否违反地理围栏
     */
    private void checkFenceViolation(String fenceId, List<FlightTaskPoint> points) {
        for (FlightTaskPoint point : points) {
            boolean isInFence = fenceDetectionService.detectUavPosition(null, // 任务ID为空，因为这是任务执行前的检查
                null, // 无人机编码为空，因为这是任务执行前的检查
                point.getLongitude(), point.getLatitude(), point.getAltitude()).isViolated();

            if (!isInFence) {
                throw new ServiceException(String.format("航点[%d]超出地理围栏范围", point.getPointIndex()));
            }
        }
    }

    /**
     * 创建航线任务并可选择是否异步执行
     *
     * @param lineId             航线ID
     * @param uavCode            无人机编码
     * @param executeImmediately 是否立即执行任务
     * @return 任务ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTaskFromLine(String lineId, String uavCode, Boolean executeImmediately) {
        // 委托给执行服务
        return flightTaskExecutionService.createTaskFromLine(lineId, uavCode, executeImmediately);
    }

    /**
     * 创建航线任务并可选择是否异步执行（指定租户ID）
     *
     * @param lineId             航线ID
     * @param uavCode            无人机编码
     * @param executeImmediately 是否立即执行任务
     * @param tenantId           租户ID
     * @return 任务ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTaskFromLine(String lineId, String uavCode, Boolean executeImmediately, String tenantId) {
        // 委托给执行服务
        return flightTaskExecutionService.createTaskFromLine(lineId, uavCode, executeImmediately, tenantId);
    }

    @Override
    public TableDataInfo<FlightTaskVo> selectFlightTaskList(FlightTaskBo bo, PageQuery pageQuery) {
        // 构建查询条件
        LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();

        // 添加筛选条件，如果需要的话
        if (StringUtils.isNotEmpty(bo.getTaskName())) {
            queryWrapper.like(FlightTask::getTaskName, bo.getTaskName());
        }
        if (StringUtils.isNotEmpty(bo.getUavCode())) {
            queryWrapper.eq(FlightTask::getUavCode, bo.getUavCode());
        }
        if (StringUtils.isNotEmpty(bo.getLineName())) {
            queryWrapper.like(FlightTask::getLineName, bo.getLineName());
        }
        if (StringUtils.isNotEmpty(bo.getFlightControlNo())) {
            queryWrapper.eq(FlightTask::getFlightControlNo, bo.getFlightControlNo());
        }
        if (StringUtils.isNotEmpty(bo.getTaskStatus())) {
            // 处理可能包含逗号分隔的多个状态值
            String taskStatus = bo.getTaskStatus();
            if (taskStatus.contains(",")) {
                // 包含多个状态值，使用 in 条件
                String[] statusArray = taskStatus.split(",");
                List<Integer> statusList = new ArrayList<>();
                for (String status : statusArray) {
                    if (StringUtils.isNotEmpty(status)) {
                        statusList.add(Integer.parseInt(status.trim()));
                    }
                }
                if (!statusList.isEmpty()) {
                    queryWrapper.in(FlightTask::getTaskStatus, statusList);
                }
            } else {
                // 单个状态值，使用 eq 条件
                queryWrapper.eq(FlightTask::getTaskStatus, Integer.parseInt(taskStatus.trim()));
            }
        }

        // 根据创建者名称查询
        String createByName = bo.getCreateByName();
        QueryUtils.buildCreateByQuery(queryWrapper, FlightTask::getCreateBy, createByName);

        // 根据修改者名称查询
        String updateByName = bo.getUpdateByName();
        QueryUtils.buildUpdateByQuery(queryWrapper, FlightTask::getUpdateBy, updateByName);

        // 按任务创建时间范围筛选
        if (bo.getStartTime() != null && bo.getEndTime() != null) {
            if (bo.getStartTime().before(bo.getEndTime()) || bo.getStartTime().equals(bo.getEndTime())) {
                queryWrapper.between(FlightTask::getCreateTime, bo.getStartTime(), bo.getEndTime());
            } else {
                log.warn("任务查询时间范围无效：创建时间的开始时间晚于结束时间");
            }
        }

        // 按执飞时间范围筛选
        if (bo.getBeginFlightTime() != null && bo.getEndFlightTime() != null) {
            if (bo.getBeginFlightTime().before(bo.getEndFlightTime()) ||
                bo.getBeginFlightTime().equals(bo.getEndFlightTime())) {
                queryWrapper.between(FlightTask::getFlightTime, bo.getBeginFlightTime(), bo.getEndFlightTime());
            } else {
                log.warn("任务查询时间范围无效：执飞时间的开始时间晚于结束时间");
            }
        }

        // 按更新时间范围筛选
        if (bo.getBeginUpdateTime() != null && bo.getEndUpdateTime() != null) {
            if (bo.getBeginUpdateTime().before(bo.getEndUpdateTime()) ||
                bo.getBeginUpdateTime().equals(bo.getEndUpdateTime())) {
                queryWrapper.between(FlightTask::getUpdateTime, bo.getBeginUpdateTime(), bo.getEndUpdateTime());
            } else {
                log.warn("任务查询时间范围无效：更新时间的开始时间晚于结束时间");
            }
        }

        // 排序
        queryWrapper.orderByDesc(FlightTask::getId);

        // 执行分页查询
        Page<FlightTask> page = baseMapper.selectPage(pageQuery.build(), queryWrapper);

        // 转换查询结果
        List<FlightTaskVo> resultList =
            BeanConvertUtils.convertList(page.getRecords(), FlightTaskVo.class, (task, vo) -> {
                // 手动处理taskStatus的类型转换
                if (task.getTaskStatus() != null) {
                    vo.setTaskStatus(String.valueOf(task.getTaskStatus()));
                }

                // 补充飞手信息
                Optional.ofNullable(task.getFlyerId()).map(flyerId -> flyerMapper.selectOne(
                    new LambdaQueryWrapper<Flyer>().select(Flyer::getFlyerName).eq(Flyer::getId, flyerId)
                        .last("limit 1"))).ifPresent(flyer -> vo.setFlyerName(flyer.getFlyerName()));

                // 补充执飞人信息
                Optional.ofNullable(task.getOperatorId()).map(operatorId -> sysUserService.selectUserById(operatorId))
                    .ifPresent(operator -> vo.setOperatorName(operator.getUserName()));

                // 补充厂家信息
                Optional.ofNullable(task.getFlightControlNo()).ifPresent(
                    controlNo -> platformInfoMapper.selectList(null).stream()
                        .filter(platform -> controlNo.equals(platform.getFlightControlNo())).findFirst()
                        .ifPresent(platform -> vo.setManufacturerName(platform.getManufacturerName())));
            });

        // 根据飞手名称过滤
        if (bo.getFlyerName() != null && !bo.getFlyerName().isEmpty()) {
            String flyerNameFilter = bo.getFlyerName().toLowerCase();
            resultList = resultList.stream()
                .filter(vo -> vo.getFlyerName() != null && vo.getFlyerName().toLowerCase().contains(flyerNameFilter))
                .collect(Collectors.toList());
        }

        // 根据操作员名称过滤
        if (bo.getOperatorName() != null && !bo.getOperatorName().isEmpty()) {
            String operatorNameFilter = bo.getOperatorName().toLowerCase();
            resultList = resultList.stream().filter(
                    vo -> vo.getOperatorName() != null && vo.getOperatorName().toLowerCase().contains(operatorNameFilter))
                .collect(Collectors.toList());
        }

        // 封装分页结果
        TableDataInfo<FlightTaskVo> dataInfo = new TableDataInfo<>();
        dataInfo.setRows(resultList);
        dataInfo.setTotal(page.getTotal());
        return dataInfo;
    }

    /**
     * 获取厂商支持的协议类型列表
     *
     * @param manufacturerType 厂商类型
     * @return 支持的协议类型集合
     * @throws IllegalArgumentException 如果厂商类型不受支持
     */
    @Override
    public Set<String> getSupportedProtocols(String manufacturerType) {
        // 委托给执行服务
        return flightTaskExecutionService.getSupportedProtocols(manufacturerType);
    }
}

