package com.md.service;

import java.util.Map;

/**
 * WebSocket Token服务接口
 */
public interface IWebSocketTokenService {
    /**
     * 生成WebSocket连接Token
     *
     * @param droneId 无人机ID
     * @return Token信息
     */
    Map<String, Object> generateToken(String droneId);

    /**
     * 验证Token
     *
     * @param token   Token字符串
     * @param droneId 无人机ID
     * @return 验证结果
     */
    boolean validateToken(String token, String droneId);
}