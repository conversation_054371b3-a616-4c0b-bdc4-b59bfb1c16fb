package com.md.constant;

/**
 * 电子围栏常量类
 */
public class FenceConstants {
    /**
     * Redis缓存前缀 - 任务关联围栏
     */
    public static final String REDIS_FENCE_PREFIX = "fence:";

    /**
     * Redis缓存前缀 - 围栏信息
     */
    public static final String REDIS_FENCE_INFO_KEY = "fence:info:";

    /**
     * Redis缓存前缀 - 围栏区域
     */
    public static final String REDIS_FENCE_AREAS_KEY = "fence:areas:";

    /**
     * Redis缓存前缀 - 围栏顶点
     */
    public static final String REDIS_FENCE_VERTICES_KEY = "fence:vertices:";

    /**
     * Redis缓存前缀 - 围栏多边形
     */
    public static final String REDIS_FENCE_POLYGON_KEY = "fence:polygon:";

    /**
     * 缓存超时时间（小时）
     */
    public static final int CACHE_TIMEOUT_HOURS = 24;

}