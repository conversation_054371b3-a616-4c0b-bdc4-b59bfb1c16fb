package com.md.utils;

import com.md.mqtt.event.DroneType;
import com.md.mqtt.event.TaskExecutionEvent;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 业务分布式锁工具类
 */
@Component
@Slf4j
public class BusinessDistributedLockUtils {

    // 复用项目现有的Redisson客户端
    private static final RedissonClient CLIENT = RedisUtils.getClient();

    /**
     * 不同业务场景的锁配置枚举
     * 根据业务特点设置合适的等待时间和持有时间
     */
    @Getter
    public enum LockType {
        /**
         * 任务状态锁：用于任务状态更新操作
         * 等待时间较长，因为任务状态更新比较重要
         * 持有时间较长，因为可能涉及多个Redis操作
         */
        TASK_STATUS(5, 30),

        /**
         * 轨迹记录锁：用于轨迹点记录操作
         * 等待时间较短，因为轨迹记录频率高，不能阻塞太久
         * 持有时间中等，因为主要是数据库写入操作
         */
        TRACK_RECORD(1, 10),

        /**
         * 事件发布锁：用于事件发布去重操作
         * 等待时间中等，事件发布需要保证不重复
         * 持有时间中等，主要是Redis检查和事件发布
         */
        EVENT_PUBLISH(2, 15);

        private final long waitTime;   // 等待获取锁的时间（秒）
        private final long leaseTime;  // 锁的持有时间（秒）

        LockType(long waitTime, long leaseTime) {
            this.waitTime = waitTime;
            this.leaseTime = leaseTime;
        }

    }

    /**
     * 执行任务状态相关的锁操作
     * 适用于：任务状态更新、任务信息修改等场景
     *
     * @param droneType 无人机类型（如：PX4、DJI等）
     * @param taskId    任务ID
     * @param action    需要在锁保护下执行的操作
     * @return true表示操作成功，false表示获取锁失败
     */
    public boolean executeTaskStatusOperation(String droneType, String taskId, Runnable action) {
        String lockKey = buildLockKey("task:status", droneType, taskId);
        return executeWithLock(lockKey, LockType.TASK_STATUS, action);
    }

    /**
     * 执行轨迹记录相关的锁操作
     * 适用于：轨迹点记录、轨迹数据清理等场景
     *
     * @param droneType 无人机类型
     * @param droneId   无人机ID
     * @param action    需要在锁保护下执行的操作
     * @return true表示操作成功，false表示获取锁失败
     */
    public boolean executeTrackRecordOperation(String droneType, String droneId, Runnable action) {
        String lockKey = buildLockKey("track:record", droneType, droneId);
        return executeWithLock(lockKey, LockType.TRACK_RECORD, action);
    }

    /**
     * 执行事件发布相关的锁操作
     * 适用于：事件发布去重、状态变化通知等场景
     *
     * @param eventKey 事件唯一标识（建议格式：droneId:taskId:eventType）
     * @param action   需要在锁保护下执行的操作
     * @return true表示操作成功，false表示获取锁失败
     */
    public boolean executeEventPublishOperation(String eventKey, Runnable action) {
        String lockKey = buildLockKey("event:publish", eventKey);
        return executeWithLock(lockKey, LockType.EVENT_PUBLISH, action);
    }

    /**
     * 安全发布TaskExecutionEvent的便捷方法
     * 自动处理去重逻辑，防止同一事件被重复发布
     *
     * @param eventPublisher Spring事件发布器
     * @param droneId        无人机ID
     * @param taskId         任务ID
     * @param isExecuting    是否正在执行
     * @param droneType      无人机类型
     * @param eventType      事件类型描述（"COMPLETED"等）
     * @return true表示事件发布成功或已被其他节点发布，false表示获取锁失败
     */
    public boolean publishTaskExecutionEventSafely(ApplicationEventPublisher eventPublisher, String droneId,
        String taskId, boolean isExecuting, DroneType droneType, String eventType) {

        // 构建事件唯一标识
        String eventKey = droneId + ":" + taskId + ":" + eventType + ":" + isExecuting;
        String redisEventKey = droneType.name().toLowerCase() + ":event:published:" + eventKey;

        boolean publishSuccess = executeEventPublishOperation(eventKey, () -> {
            // 检查事件是否已经发布过
            if (!RedisUtils.hasKey(redisEventKey)) {
                // 发布事件
                eventPublisher.publishEvent(new TaskExecutionEvent(droneId, taskId, isExecuting, droneType));

                // 标记事件已发布，设置短期过期时间防止重复
                RedisUtils.setCacheObject(redisEventKey, "published", java.time.Duration.ofMinutes(5));

                log.info("已发布{}任务{}事件: droneId={}, taskId={}, isExecuting={}", droneType.name(), eventType,
                    droneId, taskId, isExecuting);
            } else {
                log.debug("{}任务{}事件已被其他节点发布，跳过: droneId={}, taskId={}, isExecuting={}", droneType.name(),
                    eventType, droneId, taskId, isExecuting);
            }
        });

        if (!publishSuccess) {
            log.warn("发布{}任务{}事件失败，获取分布式锁失败: droneId={}, taskId={}, isExecuting={}", droneType.name(),
                eventType, droneId, taskId, isExecuting);
        }

        return publishSuccess;
    }

    /**
     * 通用的锁执行方法
     * 封装了完整的锁获取、执行、释放流程
     *
     * @param lockKey  锁的唯一标识
     * @param lockType 锁类型，决定等待时间和持有时间
     * @param action   需要在锁保护下执行的操作
     * @return true表示操作成功，false表示获取锁失败
     */
    private boolean executeWithLock(String lockKey, LockType lockType, Runnable action) {
        RLock lock = CLIENT.getLock(lockKey);

        try {
            // 尝试获取锁，设置等待时间和自动过期时间
            if (lock.tryLock(lockType.getWaitTime(), lockType.getLeaseTime(), TimeUnit.SECONDS)) {
                try {
                    // 执行业务操作
                    action.run();
                    log.debug("分布式锁操作成功: lockKey={}, lockType={}", lockKey, lockType.name());
                    return true;
                } finally {
                    // 确保锁被正确释放
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.debug("分布式锁已释放: lockKey={}", lockKey);
                    }
                }
            } else {
                // 获取锁失败，记录警告日志
                log.warn("获取分布式锁失败: lockKey={}, lockType={}, waitTime={}s", lockKey, lockType.name(),
                    lockType.getWaitTime());
                return false;
            }
        } catch (InterruptedException e) {
            // 线程被中断，恢复中断状态
            Thread.currentThread().interrupt();
            log.error("分布式锁操作被中断: lockKey={}, lockType={}", lockKey, lockType.name(), e);
            return false;
        } catch (Exception e) {
            // 其他异常，记录错误日志
            log.error("分布式锁操作异常: lockKey={}, lockType={}", lockKey, lockType.name(), e);
            return false;
        }
    }

    /**
     * 构建标准化的锁key
     * 格式：business:lock:{prefix}:{part1}:{part2}:...
     *
     * @param prefix 锁的前缀，表示业务类型
     * @param parts  锁的组成部分，用于唯一标识
     * @return 标准化的锁key
     */
    private String buildLockKey(String prefix, String... parts) {
        StringBuilder keyBuilder = new StringBuilder("business:lock:");
        keyBuilder.append(prefix);

        for (String part : parts) {
            if (part != null && !part.trim().isEmpty()) {
                keyBuilder.append(":").append(part.trim());
            }
        }

        return keyBuilder.toString();
    }
}
