package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.domain.bo.FenceDetectionBo;
import com.md.domain.bo.UavPositionBo;
import com.md.domain.dto.FenceDetectionResult;
import com.md.domain.vo.FenceVo;
import com.md.service.IFenceDetectionService;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.List;

/**
 * 围栏检测 控制器
 */
@RestController
@RequestMapping("/fence/detection")
@Validated
public class FenceDetectionController extends BaseController {

    @Autowired
    private IFenceDetectionService fenceDetectionService;

    /**
     * 检测无人机位置是否违反围栏规则
     */
    @SaCheckPermission("fence:detection:check")
    @PostMapping("/check")
    public R<FenceDetectionResult> checkPosition(@Valid @RequestBody UavPositionBo positionBo) {
        // 转换为业务对象
        FenceDetectionBo detectionBo =
            FenceDetectionBo.builder().taskId(positionBo.getTaskId()).uavCode(positionBo.getUavCode())
                .longitude(positionBo.getLongitude()).latitude(positionBo.getLatitude()).height(positionBo.getHeight())
                .build();

        // 检测位置
        FenceDetectionResult result = fenceDetectionService.detectUavPosition(detectionBo);

        return R.ok(result);
    }

    /**
     * 加载任务围栏到缓存
     */
    @SaCheckPermission("fence:detection:manage")
    @PostMapping("/load/{taskId}")
    public R<Void> loadFences(@PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        fenceDetectionService.loadFencesForTask(taskId);
        return R.ok();
    }

    /**
     * 移除任务围栏缓存
     */
    @SaCheckPermission("fence:detection:manage")
    @DeleteMapping("/unload/{taskId}")
    public R<Void> unloadFences(@PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        fenceDetectionService.removeFencesForTask(taskId);
        return R.ok();
    }

    /**
     * 更新围栏缓存
     */
    @SaCheckPermission("fence:detection:manage")
    @PutMapping("/update/{fenceId}")
    public R<Void> updateFenceCache(@PathVariable @NotBlank(message = "围栏ID不能为空") String fenceId) {
        fenceDetectionService.updateFenceCache(fenceId);
        return R.ok();
    }

    /**
     * 删除围栏缓存
     */
    @SaCheckPermission("fence:detection:manage")
    @DeleteMapping("/cache/{fenceId}")
    public R<Void> removeFenceCache(@PathVariable @NotBlank(message = "围栏ID不能为空") String fenceId) {
        fenceDetectionService.removeFenceFromCache(fenceId);
        return R.ok();
    }

    /**
     * 加载所有活跃围栏到缓存（仅限超级管理员）
     */
    @SaCheckPermission("admin")
    @PostMapping("/load-all-fences")
    public R<String> loadAllFencesToCache() {
        try {
            int count = fenceDetectionService.loadAllActiveFencesToCache();
            return R.ok("成功加载" + count + "个活跃围栏到缓存");
        } catch (Exception e) {
            return R.fail("加载所有围栏缓存失败：" + e.getMessage());
        }
    }

    /**
     * 清理过期围栏缓存（仅限超级管理员）
     */
    @SaCheckPermission("admin")
    @DeleteMapping("/clean-fence-cache")
    public R<String> cleanFenceCache() {
        try {
            fenceDetectionService.cleanUnusedFenceCache();
            return R.ok("围栏缓存清理任务已执行");
        } catch (Exception e) {
            return R.fail("清理围栏缓存失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定点位附近的围栏
     */
    @SaCheckPermission("fence:detection:query")
    @GetMapping("/nearby")
    public R<List<FenceVo>> getNearbyFences(@NotNull(message = "经度不能为空") BigDecimal longitude,
        @NotNull(message = "纬度不能为空") BigDecimal latitude,
        @RequestParam(required = false, defaultValue = "10000") Long radius) {

        FenceDetectionBo bo = FenceDetectionBo.builder().longitude(longitude).latitude(latitude).radius(radius).build();

        List<FenceVo> fences = fenceDetectionService.getNearbyFences(bo);
        return R.ok(fences);
    }

    /**
     * 获取指定点位附近的围栏（分页）
     */
    @SaCheckPermission("fence:detection:query")
    @GetMapping("/nearby/page")
    public TableDataInfo<FenceVo> getNearbyFencesPage(@NotNull(message = "经度不能为空") BigDecimal longitude,
        @NotNull(message = "纬度不能为空") BigDecimal latitude,
        @RequestParam(required = false, defaultValue = "10000") Long radius, PageQuery pageQuery) {

        FenceDetectionBo bo = FenceDetectionBo.builder().longitude(longitude).latitude(latitude).radius(radius).build();

        return fenceDetectionService.getNearbyFencesPage(bo, pageQuery);
    }
}