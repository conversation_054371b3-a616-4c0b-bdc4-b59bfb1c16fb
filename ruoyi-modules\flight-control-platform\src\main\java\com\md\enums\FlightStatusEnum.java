package com.md.enums;

import lombok.Data;
import lombok.Getter;

/**
 * 对接低空飞行管理服务平台的飞行状态枚举
 */
@Getter
public enum FlightStatusEnum {
    //起飞
    TAKEOFF("TakeOff",1 ),
    //飞行中
    INFLIGHT("Inflight", 2),
    //降落
    LAND("Land", 3),
    ;
    private String name;
    private Integer code;

    FlightStatusEnum(String name, Integer code)
    {
        this.name = name;
        this.code = code;
    }

    public static String getName(String code)
    {
        for (FlightStatusEnum c : FlightStatusEnum.values())
        {
            if (c.getCode().equals(code))
            {
                return c.name;
            }
        }
        return null;
    }

}
