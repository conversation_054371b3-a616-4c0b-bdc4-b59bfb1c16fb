package com.md.flight.lh.listener;

import com.alibaba.fastjson2.JSON;
import com.md.constant.MqttConstants;
import com.md.domain.bo.FlightTrackPointBo;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.mqtt.event.DroneType;
import com.md.mqtt.event.TaskExecutionEvent;
import com.md.service.IFlightTrackService;
import com.md.utils.TrackRecordRedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 联合飞机轨迹记录监听器
 * 监听联合飞机状态变化，在任务执行期间将轨迹数据保存到MongoDB
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LhTrackRecordListener {

    private final IFlightTrackService flightTrackService;
    private final TrackRecordRedisUtils trackRecordRedisUtils;

    // 最小记录间隔（毫秒）
    private static final long MIN_RECORD_INTERVAL = 800;

    // 任务超时时间（毫秒）- 2小时
    private static final long TASK_TIMEOUT = 2 * 60 * 60 * 1000;

    /**
     * 监听任务执行事件（仅处理联合飞机类型的事件）
     *
     * @param event 任务执行事件
     */
    @EventListener
    public void onTaskExecutionEvent(TaskExecutionEvent event) {
        // 只处理联合飞机类型的事件
        if (event.getDroneType() != DroneType.LH) {
            log.debug("忽略非联合飞机类型的任务执行事件: droneId={}, droneType={}", event.getDroneId(),
                event.getDroneType());
            return;
        }

        String droneId = event.getDroneId();
        String taskId = event.getTaskId();
        boolean isExecuting = event.isExecuting();

        log.info("收到联合飞机任务执行事件: droneId={}, taskId={}, isExecuting={}, droneType={}", droneId, taskId,
            isExecuting, event.getDroneType());

        if (isExecuting) {
            // 任务开始执行，记录任务ID
            trackRecordRedisUtils.setActiveTask("LH", droneId, taskId);
            trackRecordRedisUtils.setTaskStartTime("LH", droneId, System.currentTimeMillis());
            log.info("开始记录联合飞机[{}]的轨迹，任务ID：{}", droneId, taskId);
        } else {
            // 任务结束，移除记录
            trackRecordRedisUtils.cleanupTaskData("LH", droneId);
            log.info("停止记录联合飞机[{}]的轨迹", droneId);
        }
    }

    /**
     * 定期检查活动任务，记录轨迹点
     * 每秒执行一次，检查所有执行中的任务
     */
    @Scheduled(fixedRate = 1000)
    public void recordActiveTracks() {
        Map<String, String> activeTasks = trackRecordRedisUtils.getAllActiveTasks("LH");
        if (activeTasks.isEmpty()) {
            return;
        }

        log.debug("当前联合飞机活动任务数量: {}", activeTasks.size());
        long currentTime = System.currentTimeMillis();

        // 遍历所有执行中的任务
        for (Map.Entry<String, String> entry : activeTasks.entrySet()) {
            String droneId = entry.getKey();
            String taskId = entry.getValue();

            // 检查任务是否超时
            if (trackRecordRedisUtils.isTaskTimeout("LH", droneId, TASK_TIMEOUT)) {
                log.warn("联合飞机[{}]任务[{}]执行超时，自动停止轨迹记录", droneId, taskId);
                trackRecordRedisUtils.cleanupTaskData("LH", droneId);
                continue;
            }

            // 使用分布式锁安全记录轨迹点
            trackRecordRedisUtils.tryRecordTrackPoint("LH", droneId, MIN_RECORD_INTERVAL, () -> {
                try {
                    // 从Redis获取联合飞机当前状态
                    String statusData = TenantHelper.ignore(
                        () -> RedisUtils.getCacheObject(MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId));

                    if (statusData == null) {
                        log.warn("未获取到联合飞机[{}]的状态数据，跳过记录。Redis键: {}", droneId,
                            MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId);
                        return;
                    }

                    // 解析状态数据
                    DroneStatus droneStatus = JSON.parseObject(statusData, DroneStatus.class);
                    if (droneStatus == null) {
                        log.warn("联合飞机[{}]状态数据解析失败，跳过记录", droneId);
                        return;
                    }

                    // 转换为轨迹点并保存
                    FlightTrackPointBo trackPoint = convertToTrackPoint(taskId, droneId, droneStatus);
                    flightTrackService.saveTrackPoint(trackPoint);

                    log.debug("已记录联合飞机[{}]的轨迹点，任务ID：{}", droneId, taskId);
                } catch (Exception e) {
                    log.error("记录联合飞机[{}]轨迹点失败", droneId, e);
                    throw new RuntimeException(e);  // 让分布式锁机制处理异常
                }
            });
        }
    }

    /**
     * 将联合飞机状态数据转换为轨迹点
     *
     * @param taskId      任务ID
     * @param droneId     无人机ID
     * @param droneStatus 无人机状态
     * @return 轨迹点数据
     */
    private FlightTrackPointBo convertToTrackPoint(String taskId, String droneId, DroneStatus droneStatus) {
        FlightTrackPointBo trackPoint = new FlightTrackPointBo();

        // 设置基本信息
        trackPoint.setTaskId(taskId);
        trackPoint.setDroneId(droneId);
        trackPoint.setTimestamp(System.currentTimeMillis());

        // 设置位置信息
        if (droneStatus.getLatitude() != null) {
            trackPoint.setLatitude(droneStatus.getLatitude().doubleValue());
        }
        if (droneStatus.getLongitude() != null) {
            trackPoint.setLongitude(droneStatus.getLongitude().doubleValue());
        }
        trackPoint.setAltitude((double)droneStatus.getAbsoluteAltitude());
        trackPoint.setRelativeHeight((double)droneStatus.getRelativeAltitude());

        // 设置飞行信息
        trackPoint.setSpeed(droneStatus.getSpeed());
        trackPoint.setGroundSpeed((double)droneStatus.getGroundSpeed());
        trackPoint.setFlightDistance(droneStatus.getFlightDistance());
        trackPoint.setHeading((double)droneStatus.getHeading());
        trackPoint.setPitch((double)droneStatus.getPitch());
        trackPoint.setRoll((double)droneStatus.getRoll());
        trackPoint.setFlightMode(droneStatus.getFlightMode());

        // 设置RTK信息
        trackPoint.setRtkAltitude(droneStatus.getRtkAltitude());
        trackPoint.setIsRTKConnected(droneStatus.isRTKConnected());
        trackPoint.setIsRTKEnabled(droneStatus.isRTKEnabled());
        trackPoint.setIsRTKHealthy(droneStatus.isRTKHealthy());
        trackPoint.setRtkSatelliteCount(droneStatus.getRtkSatelliteCount());

        // 设置任务状态
        trackPoint.setMissionStatus(droneStatus.getCurrentWaypoint() > 0 ? "EXECUTING" : "IDLE");

        // 设置电池信息（如果有）
        if (droneStatus.getBatteryInfo() != null) {
            FlightTrackPointBo.BatteryInfo batteryInfo = new FlightTrackPointBo.BatteryInfo();
            batteryInfo.setChargeRemainingInPercent(droneStatus.getBatteryInfo().getChargeRemainingInPercent());
            batteryInfo.setChargeRemaining(droneStatus.getBatteryInfo().getChargeRemaining());
            batteryInfo.setTotalCapacity(droneStatus.getBatteryInfo().getTotalCapacity());
            trackPoint.setBatteryInfo(batteryInfo);
        }

        // 设置卫星信息（如果有）
        if (droneStatus.getSatelliteInfo() != null) {
            FlightTrackPointBo.SatelliteInfo satelliteInfo = new FlightTrackPointBo.SatelliteInfo();
            satelliteInfo.setGpsCount(droneStatus.getSatelliteInfo().getGpsCount());
            satelliteInfo.setBaseStationCount(droneStatus.getSatelliteInfo().getBaseStationCount());
            satelliteInfo.setMobileStation1Count(droneStatus.getSatelliteInfo().getMobileStation1Count());
            satelliteInfo.setMobileStation2Count(droneStatus.getSatelliteInfo().getMobileStation2Count());
            trackPoint.setSatelliteInfo(satelliteInfo);
        }

        return trackPoint;
    }
}
