package com.md.flight.mavlink.service.impl;

import com.md.constant.TaskCallbackConstants;
import com.md.domain.po.FlightTask;
import com.md.enums.TaskStatusEnum;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.flight.mavlink.model.Mission;
import com.md.flight.mavlink.model.MissionWaypoint;
import com.md.openfeign.TaskCallbackClient;
import com.md.flight.mavlink.service.MavlinkMissionService;
import com.md.mapper.SysApiConfigMapper;
import com.md.mapper.SysApiTenantBindingMapper;
import com.md.mqtt.event.DroneType;
import com.md.mqtt.event.TaskExecutionEvent;
import com.md.service.IFlightTaskService;
import com.md.service.IFlightTaskStatusService;
import com.md.service.ITaskCallbackService;
import com.md.utils.EncryptUtils;
import com.md.utils.MissionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * MAVLink航线任务服务实现
 */
@Slf4j
@Service
public class MavlinkMissionServiceImpl implements MavlinkMissionService {

    private static final float DEFAULT_SPEED = 6.0f; // 默认飞行速度（米/秒）
    private static final float DEFAULT_TAKEOFF_ALTITUDE = 10.0f; // 默认起飞高度（米）

    @Autowired
    private MavlinkCoreService coreService;

    @Autowired
    @Lazy
    private IFlightTaskService flightTaskService;

    @Autowired
    @Lazy
    private IFlightTaskStatusService flightTaskStatusService;

    @Autowired
    private TaskCallbackClient taskCallbackClient;

    @Autowired
    private EncryptUtils encryptUtils;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private ITaskCallbackService taskCallbackService;

    @Value("${task-callback.tenant-id:'000000'}")
    private String callbackTenantId;

    @Value("${mavlink.default.takeoff.altitude:8.0}")
    private float defaultTakeoffAltitude;

    @Value("${mavlink.mission.max-wait-seconds:30}")
    private int maxWaitSeconds;

    @Value("${mavlink.mission.altitude-threshold-ratio:0.7}")
    private float altitudeThresholdRatio;

    @Value("${mavlink.mission.default-accept-radius:5.0}")
    private float defaultAcceptRadius;

    @Value("${mavlink.mission.default-hold-time:1.0}")
    private float defaultHoldTime;

    @Autowired
    private SysApiConfigMapper sysApiConfigMapper;
    @Autowired
    private SysApiTenantBindingMapper sysApiTenantBindingMapper;

    @Override
    public CompletableFuture<Boolean> uploadMission(String droneId, Mission mission) {
        return coreService.uploadMission(droneId, mission);
    }

    /**
     * 开始执行航线任务
     *
     * @param droneId  无人机ID
     * @param speed    飞行速度（米/秒）
     * @param taskName 任务名称，用于状态更新
     */
    @Override
    public void startMission(String droneId, BigDecimal speed, String taskName) {
        log.info("无人机[{}]开始执行航线任务，速度: {}m/s，任务名称: {}", droneId, speed, taskName);

        try {
            coreService.startMission(droneId, speed, defaultTakeoffAltitude);

            // 3. 启动异步监控任务完成状态
            startAsyncMonitoring(droneId, taskName);

        } catch (Exception e) {
            handleMissionStartError(droneId, taskName, e);
        }
    }

    /**
     * 启动异步监控任务完成状态
     */
    private void startAsyncMonitoring(String droneId, String taskName) {
        if (taskName == null || taskName.isEmpty()) {
            log.warn("任务名称为空，跳过异步监控");
            return;
        }

        //        final String username = getCurrentUsername();

        // 异步监控任务完成状态
        CompletableFuture.runAsync(() -> monitorMissionCompletion(droneId, taskName, null));
    }

    /**
     * 监控任务完成状态的异步执行逻辑
     */
    private void monitorMissionCompletion(String droneId, String taskName, String username) {
        try {
            // 获取当前解锁状态
            DroneStatus initialStatus = coreService.getDroneStatus(droneId);
            if (initialStatus == null || !initialStatus.isArmed()) {
                log.warn("无法获取初始无人机[{}]状态或无人机未处于解锁状态", droneId);
                return;
            }

            log.info("开始异步监控无人机[{}]任务[{}]完成状态", droneId, taskName);

            // 持续监控状态变化
            while (true) {
                Thread.sleep(1000); // 每秒检查一次
                DroneStatus currentStatus = coreService.getDroneStatus(droneId);

                // 如果从解锁状态变为上锁状态，说明任务完成
                if (currentStatus != null && !currentStatus.isArmed()) {
                    log.info("检测到无人机[{}]已上锁，航线任务[{}]已完成", droneId, taskName);
                    updateTaskStatus(taskName, TaskStatusEnum.COMPLETED.getCode(), username); // 传入用户名

                    // 查询任务ID，用于回调
                    String taskId = getTaskIdByTaskName(taskName);
                    if (taskId != null) {
                        // 发送任务完成回调
                        sendTaskCallback(taskId, TaskCallbackConstants.CODE_SUCCESS, "任务执行成功");

                        // 发布任务结束事件，通知轨迹记录监听器停止记录
                        try {
                            eventPublisher.publishEvent(new TaskExecutionEvent(droneId, taskId, false, DroneType.MAVLINK));
                            log.info("已发布MAVLink任务自动完成事件: droneId={}, taskId={}", droneId, taskId);
                        } catch (Exception e) {
                            log.error("发布MAVLink任务自动完成事件失败: droneId={}, taskId={}, error={}", droneId,
                                taskId, e.getMessage(), e);
                        }
                    } else {
                        log.warn("无法获取任务ID，跳过发布任务结束事件: taskName={}", taskName);
                    }

                    break;
                }
            }
        } catch (InterruptedException e) {
            log.error("监控无人机[{}]任务[{}]状态时发生中断", droneId, taskName, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("监控无人机[{}]任务[{}]状态时发生异常", droneId, taskName, e);
        }
    }

    /**
     * 获取任务ID
     */
    private String getTaskIdByTaskName(String taskName) {
        try {
            // 查询任务信息获取ID
            FlightTask task =
                flightTaskService.lambdaQuery().eq(FlightTask::getTaskName, taskName).last("limit 1").one();
            return task != null ? task.getId() : null;
        } catch (Exception e) {
            log.error("根据任务名称[{}]查询任务ID失败", taskName, e);
            return null;
        }
    }

    /**
     * 发送任务回调
     */
    private void sendTaskCallback(String taskId, Integer code, String msg) {
        try {
            log.info("发送任务回调: taskId={}, code={}, msg={}", taskId, code, msg);

            // 使用任务回调服务发送回调
            boolean result = taskCallbackService.sendTaskCallback(taskId, code, msg);

            if (result) {
                log.info("任务回调发送成功: taskId={}", taskId);
            } else {
                log.warn("任务回调发送失败: taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("发送任务回调失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 处理任务启动过程中的错误
     */
    private void handleMissionStartError(String droneId, String taskName, Exception e) {
        log.error("启动无人机[{}]任务失败: {}", droneId, e.getMessage(), e);
        if (taskName != null && !taskName.isEmpty()) {
            updateTaskStatus(taskName, TaskStatusEnum.FAILED.getCode(), null);

            // 查询任务ID，用于回调
            String taskId = getTaskIdByTaskName(taskName);
            if (taskId != null) {
                // 发送任务失败回调
                sendTaskCallback(taskId, TaskCallbackConstants.CODE_FAILURE, "任务执行失败: " + e.getMessage());
            }
        }
        throw new RuntimeException("启动任务失败: " + e.getMessage(), e);
    }

    @Override
    public void pauseMission(String droneId) {
        coreService.pauseMission(droneId);
    }

    @Override
    public void resumeMission(String droneId) {
        coreService.resumeMission(droneId);
    }

    @Override
    public void stopMission(String droneId) {
        try {
            log.info("停止无人机[{}]执行的航线任务", droneId);

            // 1. 调用核心服务停止任务
            coreService.stopMission(droneId);
            log.info("MAVLink核心服务任务停止命令已发送: droneId={}", droneId);

            // 2. 发布任务结束事件，通知轨迹记录监听器停止记录
            try {
                // 尝试从DroneId获取关联的任务ID
                String taskId = getTaskIdByDroneId(droneId);

                // 发布任务结束事件
                eventPublisher.publishEvent(new TaskExecutionEvent(droneId, taskId, false));
                log.info("已发布MAVLink任务结束事件: droneId={}", droneId);

                // 如果找到关联的任务ID，发送任务停止回调
                if (taskId != null) {
                    sendTaskCallback(taskId, TaskCallbackConstants.CODE_SUCCESS, "任务已手动停止");
                    log.info("已发送任务停止回调: droneId={}, taskId={}", droneId, taskId);
                }
            } catch (Exception e) {
                log.error("发布MAVLink任务结束事件失败: droneId={}, error={}", droneId, e.getMessage(), e);
                // 继续执行，不因事件发布失败而中断任务停止
            }

            log.info("无人机[{}]航线任务停止完成", droneId);
        } catch (Exception e) {
            log.error("停止无人机[{}]航线任务失败: {}", droneId, e.getMessage(), e);
            throw new RuntimeException("停止航线任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据无人机ID查询关联的任务ID
     * 此方法假设系统中存在无人机ID与任务ID的关联关系
     */
    private String getTaskIdByDroneId(String droneId) {
        try {
            // 查询与无人机关联的最新任务，使用uavCode字段
            FlightTask task =
                flightTaskService.lambdaQuery().eq(FlightTask::getUavCode, droneId)  // 使用uavCode字段匹配droneId
                    .eq(FlightTask::getTaskStatus, TaskStatusEnum.EXECUTING.getCode()) // 获取正在执行中的任务
                    .orderByDesc(FlightTask::getCreateTime).last("limit 1").one();

            return task != null ? task.getId() : null;
        } catch (Exception e) {
            log.error("根据无人机ID[{}]查询任务ID失败", droneId, e);
            return null;
        }
    }

    @Override
    public CompletableFuture<Boolean> createRectangleMission(String droneId, Map<String, Object> params) {
        log.info("开始创建无人机[{}]矩形航线任务，参数：{}", droneId, params);

        try {
            // 提取并验证参数
            double centerLat = MissionUtils.getDoubleParam(params, "centerLat");
            double centerLon = MissionUtils.getDoubleParam(params, "centerLon");
            double width = MissionUtils.getDoubleParam(params, "width");
            double height = MissionUtils.getDoubleParam(params, "height");
            double altitude = MissionUtils.getDoubleParam(params, "altitude");
            String name = (String)params.getOrDefault("name", "矩形航线");

            // 创建任务
            MissionUtils.RectangleMissionParams missionParams =
                new MissionUtils.RectangleMissionParams(centerLat, centerLon, width, height, altitude, name);
            Mission mission = MissionUtils.createRectangleMission(missionParams);

            log.info("无人机[{}]矩形航线任务创建成功，开始上传", droneId);
            return uploadMission(droneId, mission);
        } catch (Exception e) {
            log.error("创建无人机[{}]矩形航线任务失败: {}", droneId, e.getMessage(), e);
            throw new RuntimeException("创建矩形航线任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public CompletableFuture<Boolean> createWaypointsMission(String droneId, Map<String, Object> params) {
        log.info("开始创建无人机[{}]多航点航线任务，参数：{}", droneId, params);

        try {
            if (!params.containsKey("waypoints") || !params.containsKey("altitude")) {
                throw new IllegalArgumentException("必须提供航点列表和高度参数");
            }

            String name = (String)params.getOrDefault("name", "多航点航线");
            float altitude = Float.parseFloat(params.get("altitude").toString());
            List<Map<String, Object>> waypointMaps = (List<Map<String, Object>>)params.get("waypoints");

            List<MissionUtils.WaypointParams> waypoints = new ArrayList<>();
            for (Map<String, Object> wp : waypointMaps) {
                double lat = Double.parseDouble(wp.get("lat").toString());
                double lon = Double.parseDouble(wp.get("lon").toString());
                float acceptRadius =
                    wp.containsKey("acceptRadius") ? Float.parseFloat(wp.get("acceptRadius").toString())
                        : defaultAcceptRadius;
                float holdTime =
                    wp.containsKey("holdTime") ? Float.parseFloat(wp.get("holdTime").toString()) : defaultHoldTime;

                waypoints.add(new MissionUtils.WaypointParams(lat, lon, acceptRadius, holdTime));
            }

            Mission mission = MissionUtils.createWaypointsMission(name, altitude, waypoints);

            log.info("无人机[{}]多航点航线创建成功，包含{}个航点，准备上传", droneId, mission.getItems().size());
            return uploadMission(droneId, mission);
        } catch (Exception e) {
            log.error("创建无人机[{}]多航点航线任务失败: {}", droneId, e.getMessage(), e);
            throw new RuntimeException("创建多航点航线任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    public CompletableFuture<List<MissionWaypoint>> getCurrentMission(String droneId) {
        return coreService.getCurrentMissionItems(droneId);
    }

    @Override
    public CompletableFuture<Boolean> setSpeed(String droneId, float speed) {
        return coreService.setSpeed(droneId, speed);
    }

    private void updateTaskStatus(String taskName, Integer status, String username) {
        try {
            flightTaskStatusService.updateTaskStatus(taskName, status, username);
        } catch (Exception e) {
            // 仅记录日志，不影响主流程
            log.error("更新任务[{}]状态为[{}]失败: {}", taskName, status, e.getMessage(), e);
        }
    }
}
