package com.md.flight.engine;

import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 任务执行引擎接口
 * 定义通用的任务执行方法，不同厂商的执行引擎实现此接口
 */
public interface TaskExecutionEngine {
    /**
     * 执行航线任务
     *
     * @param task   航线任务
     * @param points 航点列表
     * @return 执行结果
     */
    CompletableFuture<Boolean> executeTask(FlightTask task, List<FlightTaskPoint> points);

    /**
     * 获取引擎支持的厂商类型
     *
     * @return 厂商类型编码
     */
    String getSupportedManufacturer();

    /**
     * 获取引擎支持的协议类型
     *
     * @return 协议类型编码
     */
    String getSupportedProtocol();
}