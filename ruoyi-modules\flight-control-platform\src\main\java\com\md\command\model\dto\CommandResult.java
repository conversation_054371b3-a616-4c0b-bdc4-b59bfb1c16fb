package com.md.command.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class CommandResult {
    private boolean success;
    private String message;
    private String commandId;
    private Map<String, Object> data;

    public static CommandResult success() {
        return new CommandResult().setSuccess(true).setMessage("操作成功");
    }

    public static CommandResult success(String message) {
        return new CommandResult()
                .setSuccess(true)
                .setMessage(message);
    }

    public static CommandResult failed(String message) {
        return new CommandResult()
                .setSuccess(false)
                .setMessage("操作失败：" + message);
    }
} 