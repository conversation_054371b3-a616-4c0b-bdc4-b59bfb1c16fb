package com.md.service.impl;

import com.md.domain.bo.FlightTrackPointBo;
import com.md.domain.vo.FlightTrackPointVo;
import com.md.mongodb.document.FlightTrackPoint;
import com.md.mongodb.repository.FlightTrackPointRepository;
import com.md.service.IFlightTrackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 飞行轨迹服务实现类
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FlightTrackServiceImpl implements IFlightTrackService {

    private final FlightTrackPointRepository trackPointRepository;

    /**
     * 保存飞行轨迹点 将无人机的实时位置、姿态等信息保存到MongoDB数据库
     *
     * @param trackPointBo 包含无人机状态信息的轨迹点业务对象
     */
    @Override
    public void saveTrackPoint(FlightTrackPointBo trackPointBo) {
        try {
            // 将Bo转换为MongoDB文档对象
            FlightTrackPoint trackPoint = MapstructUtils.convert(trackPointBo, FlightTrackPoint.class);
            trackPointRepository.save(trackPoint);
        } catch (Exception e) {
            log.error("保存轨迹点数据失败: {}", trackPointBo, e);
        }
    }

    /**
     * 查询指定任务的所有轨迹点 按时间戳升序排序，确保轨迹点按时间顺序返回 用于轨迹回放和分析
     *
     * @param taskId 任务ID
     * @return 该任务的所有轨迹点视图对象列表
     */
    @Override
    public List<FlightTrackPointVo> getTaskTrackPoints(String taskId) {
        List<FlightTrackPoint> trackPoints = trackPointRepository.findByTaskIdOrderByTimestampAsc(taskId);
        return MapstructUtils.convert(trackPoints, FlightTrackPointVo.class);
    }

    /**
     * 查询指定任务的所有轨迹点（分页版本）
     *
     * @param taskId    任务ID
     * @param pageQuery 分页参数
     * @return 分页轨迹点视图对象列表
     */
    @Override
    public TableDataInfo<FlightTrackPointVo> getTaskTrackPointsPage(String taskId, PageQuery pageQuery) {
        PageRequest pageable = PageRequest.of(pageQuery.getPageNum() - 1, pageQuery.getPageSize(),
            Sort.by(Sort.Direction.ASC, "timestamp"));
        Page<FlightTrackPoint> page = trackPointRepository.findByTaskIdOrderByTimestampAsc(taskId, pageable);

        // 转换为视图对象列表
        List<FlightTrackPointVo> voList = MapstructUtils.convert(page.getContent(), FlightTrackPointVo.class);

        TableDataInfo<FlightTrackPointVo> dataInfo = new TableDataInfo<>();
        dataInfo.setRows(voList);
        dataInfo.setTotal(page.getTotalElements());
        return dataInfo;
    }

    /**
     * 查询指定任务在给定时间范围内的轨迹点 支持轨迹分段查询和分析
     *
     * @param taskId    任务ID
     * @param startTime 查询起始时间戳
     * @param endTime   查询结束时间戳
     * @return 符合时间范围的轨迹点视图对象列表
     */
    @Override
    public List<FlightTrackPointVo> getTaskTrackPointsByTimeRange(String taskId, Long startTime, Long endTime) {
        List<FlightTrackPoint> trackPoints =
            trackPointRepository.findByTaskIdAndTimestampBetweenOrderByTimestampAsc(taskId, startTime, endTime);
        return MapstructUtils.convert(trackPoints, FlightTrackPointVo.class);
    }

    /**
     * 查询指定任务在给定时间范围内的轨迹点（分页版本）
     *
     * @param taskId    任务ID
     * @param startTime 开始时间戳
     * @param endTime   结束时间戳
     * @param pageQuery 分页参数
     * @return 分页轨迹点视图对象列表
     */
    @Override
    public TableDataInfo<FlightTrackPointVo> getTaskTrackPointsByTimeRangePage(String taskId, Long startTime,
        Long endTime, PageQuery pageQuery) {
        PageRequest pageable = PageRequest.of(pageQuery.getPageNum() - 1, pageQuery.getPageSize(),
            Sort.by(Sort.Direction.ASC, "timestamp"));
        Page<FlightTrackPoint> page =
            trackPointRepository.findByTaskIdAndTimestampBetweenOrderByTimestampAsc(taskId, startTime, endTime,
                pageable);

        // 转换为视图对象列表
        List<FlightTrackPointVo> voList = MapstructUtils.convert(page.getContent(), FlightTrackPointVo.class);

        TableDataInfo<FlightTrackPointVo> dataInfo = new TableDataInfo<>();
        dataInfo.setRows(voList);
        dataInfo.setTotal(page.getTotalElements());
        return dataInfo;
    }

    /**
     * 查询指定无人机的所有轨迹点
     *
     * @param droneId 无人机ID
     * @return 轨迹点视图对象列表
     */
    @Override
    public List<FlightTrackPointVo> getDroneTrackPoints(String droneId) {
        List<FlightTrackPoint> trackPoints = trackPointRepository.findByDroneIdOrderByTimestampAsc(droneId);
        return MapstructUtils.convert(trackPoints, FlightTrackPointVo.class);
    }

    /**
     * 查询指定无人机的所有轨迹点（分页版本）
     *
     * @param droneId   无人机ID
     * @param pageQuery 分页参数
     * @return 分页轨迹点视图对象列表
     */
    @Override
    public TableDataInfo<FlightTrackPointVo> getDroneTrackPointsPage(String droneId, PageQuery pageQuery) {
        PageRequest pageable = PageRequest.of(pageQuery.getPageNum() - 1, pageQuery.getPageSize(),
            Sort.by(Sort.Direction.ASC, "timestamp"));
        Page<FlightTrackPoint> page = trackPointRepository.findByDroneIdOrderByTimestampAsc(droneId, pageable);

        // 转换为视图对象列表
        List<FlightTrackPointVo> voList = MapstructUtils.convert(page.getContent(), FlightTrackPointVo.class);

        TableDataInfo<FlightTrackPointVo> dataInfo = new TableDataInfo<>();
        dataInfo.setRows(voList);
        dataInfo.setTotal(page.getTotalElements());
        return dataInfo;
    }

    /**
     * 查询指定任务和无人机的所有轨迹点
     *
     * @param taskId  任务ID
     * @param droneId 无人机ID
     * @return 轨迹点视图对象列表
     */
    @Override
    public List<FlightTrackPointVo> getTaskDroneTrackPoints(String taskId, String droneId) {
        List<FlightTrackPoint> trackPoints =
            trackPointRepository.findByTaskIdAndDroneIdOrderByTimestampAsc(taskId, droneId);
        return MapstructUtils.convert(trackPoints, FlightTrackPointVo.class);
    }

    /**
     * 查询指定任务的最新轨迹点
     *
     * @param taskId 任务ID
     * @return 最新轨迹点视图对象，如果没有则返回null
     */
    @Override
    public FlightTrackPointVo getTaskLatestTrackPoint(String taskId) {
        FlightTrackPoint trackPoint = trackPointRepository.findFirstByTaskIdOrderByTimestampDesc(taskId);
        return trackPoint != null ? MapstructUtils.convert(trackPoint, FlightTrackPointVo.class) : null;
    }

    /**
     * 查询指定无人机的最新轨迹点
     *
     * @param droneId 无人机ID
     * @return 最新轨迹点视图对象，如果没有则返回null
     */
    @Override
    public FlightTrackPointVo getDroneLatestTrackPoint(String droneId) {
        FlightTrackPoint trackPoint = trackPointRepository.findFirstByDroneIdOrderByTimestampDesc(droneId);
        return trackPoint != null ? MapstructUtils.convert(trackPoint, FlightTrackPointVo.class) : null;
    }

    /**
     * 删除指定任务的所有轨迹点 用于清理历史数据，释放存储空间
     *
     * @param taskId 要删除轨迹点的任务ID
     */
    @Override
    public void deleteTrackPoints(String taskId) {
        trackPointRepository.deleteByTaskId(taskId);
    }

    /**
     * 删除指定任务和无人机的所有轨迹点
     *
     * @param taskId  任务ID
     * @param droneId 无人机ID
     */
    @Override
    public void deleteTrackPointsByTaskIdAndDroneId(String taskId, String droneId) {
        try {
            log.info("删除任务[{}]无人机[{}]的历史轨迹点", taskId, droneId);
            trackPointRepository.deleteByTaskIdAndDroneId(taskId, droneId);
        } catch (Exception e) {
            log.error("删除轨迹点数据失败: taskId={}, droneId={}", taskId, droneId, e);
        }
    }
}