package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.md.domain.po.SysApiConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;


import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 接口管理视图对象 sys_api_config
 *
 *
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysApiConfig.class)
public class SysApiConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 接口ID
     */
    @ExcelProperty(value = "接口ID")
    private Long id;

    /**
     * 目标平台
     */
    @ExcelProperty(value = "目标平台")
    private String targetPlatform;

    /**
     * 访问地址
     */
    @ExcelProperty(value = "访问地址")
    private String url;


    /**
     * 配置信息
     */
    private String encryptInfo;


    /**
     * 显示顺序
     */
    @ExcelProperty(value = "显示顺序")
    private Integer sort;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 状态（0正常 1停用）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;


}
