package com.md.flight.lh.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.md.constant.MqttConstants;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseOsdData;
import com.md.flight.lh.domain.dto.LhResponseOsdDataState;
import com.md.flight.mavlink.model.DroneStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;

/**
 * 联合基础数据响应处理器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LhOsdProcessor implements LhBaseProcessor {

    @Value("${mqtt.redis.expire-seconds:20}")  // 默认20秒，与其他无人机配置一致
    private int expireSeconds;

    @Override
    public void processMessage(String payload) {
        try {
            // 消息转换
            LhResponse<LhResponseOsdData> lhResponse = JSON.parseObject(payload, new TypeReference<>() {
            });

            DroneStatus droneStatus;

            // 使用与大疆相同的存储结构
            String redisKey = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + lhResponse.getGateway();

            // 获取Redis中的数据
            String statusData = TenantHelper.ignore(() -> RedisUtils.getCacheObject(redisKey));

            if (statusData == null) {
                droneStatus = new DroneStatus();
            } else {
                droneStatus = JSON.parseObject(statusData, DroneStatus.class);
            }
            // 添加DroneStatus数据
            droneStatusSet(droneStatus, lhResponse.getData(), lhResponse.getGateway());

            // 转换为JSON字符串
            String jsonStatus = JSON.toJSONString(droneStatus);

            // 存储到Redis，设置过期时间与其他无人机保持一致
            TenantHelper.ignore(
                () -> RedisUtils.setCacheObject(redisKey, jsonStatus, Duration.ofSeconds(expireSeconds)));
        } catch (Exception e) {
            log.error("处理联合基础数据响应失败: {}", e.getMessage(), e);
        }
    }

    // 联合基础数据响应转换成 DroneStatus
    public static void droneStatusSet(DroneStatus droneStatus, LhResponseOsdData osdData, String uavCode) {
        if (osdData == null) {
            return;
        }

        // 设置在线状态 - 收到状态数据说明设备在线
        droneStatus.setOnline(true);

        // 基础设备信息
        droneStatus.setDroneSN(uavCode);
        if (osdData.getUas_model() != null && !osdData.getUas_model().isEmpty()) {
            droneStatus.setDeviceModel(osdData.getUas_model());
        }

        // 位置信息
        droneStatus.setLatitude(osdData.getLatitude() != 0 ? BigDecimal.valueOf(osdData.getLatitude()) : null);
        droneStatus.setLongitude(osdData.getLongitude() != 0 ? BigDecimal.valueOf(osdData.getLongitude()) : null);
        droneStatus.setAbsoluteAltitude(osdData.getAltitude()); // 海拔高度
        droneStatus.setRelativeAltitude(osdData.getHeight());   // 相对高度

        // 飞行信息
        droneStatus.setSpeed(osdData.getGs());
        droneStatus.setHeading(osdData.getHeading());
        droneStatus.setFlightMode(osdData.getMode());
        droneStatus.setDistanceToHome(osdData.getHome_distance());
        droneStatus.setGroundSpeed(osdData.getGs());
        droneStatus.setFlightDistance(osdData.getFly_distance());
        droneStatus.setPitch(osdData.getPitch());
        if (osdData.getRoll() != null) {
            droneStatus.setRoll(osdData.getRoll().floatValue());
        }
        droneStatus.setVerticalSpeed(osdData.getVs());

        // 累计飞行时间（秒转分钟）
        if (osdData.getFly_time() > 0) {
            droneStatus.setTotalFlightTime((int)(osdData.getFly_time() / 60));
        }

        // 解锁状态（flying字段：1表示飞行中，0表示未飞行）
        droneStatus.setArmed(osdData.getFlying() == 1);

        // RTK 状态信息
        LhResponseOsdDataState state = osdData.getState();
        if (state != null) {
            // 避障信息
            DroneStatus.ObstacleAvoidanceInfo obstacleAvoidanceInfo = new DroneStatus.ObstacleAvoidanceInfo();
            obstacleAvoidanceInfo.setHorizontalAvoidance(state.getObs_enabled() == 1);
            droneStatus.setObstacleAvoidanceInfo(obstacleAvoidanceInfo);

            // RTK 状态信息
            droneStatus.setRTKEnabled(state.getRtk_enabled() == 1);
            droneStatus.setRTKConnected(state.getRtk_connected() == 1);
        }

        // 时间戳
        droneStatus.setTimestamp(osdData.getTimestamp());
        droneStatus.setLastUpdateTime(System.currentTimeMillis());
    }
}
