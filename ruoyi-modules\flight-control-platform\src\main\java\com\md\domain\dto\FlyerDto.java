package com.md.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlyerDto extends BaseEntity {

    /**
     * 飞手id
     */
    private Integer id;


    /**
     * 飞手姓名
     */
    private String flyerName;


    /**
     * 飞手手机号
     */
    private String flyerPhone;


    /**
     * 飞手性别
     */
    private String flyerSex;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 飞控平台名称
     */
    private String plateFormName;

    /**
     * 飞控平台id
     */
    private String plateFormId;

    private String tenantId;
}
