package com.md.domain.bo;

import com.md.domain.po.Flyer;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 飞手业务对象
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
@AutoMapper(target = Flyer.class)
public class FlyerBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 飞手id
     */
    private Long id;

    /**
     * 飞手姓名
     */
    @NotBlank(message = "飞手姓名不能为空")
    private String flyerName;

    /**
     * 飞手手机号
     */
    @NotBlank(message = "飞手手机号不能为空")
    private String flyerPhone;

    /**
     * 飞手性别
     */
    @NotNull(message = "飞手性别不能为空")
    private Integer flyerSex;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 数据来源标识（0手动添加，1同步接口获取）
     */
    private String dataSourceValue;

    /**
     * 无人机厂商id
     */
    private String plateformId;

    /**
     * 飞控平台名称
     */
    private String plateformName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private Long updateBy;
} 