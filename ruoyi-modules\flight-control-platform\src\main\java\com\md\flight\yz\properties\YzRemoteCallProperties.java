package com.md.flight.yz.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "yz-remote-call")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YzRemoteCallProperties {
    private String url;
    private String getToken;
    private String queryBaseInfo;
    private String uavApplyFlight;
    private String queryDroneStatusInfo;
    private String tokenKey;
    private Integer tokenOutTime;
}
