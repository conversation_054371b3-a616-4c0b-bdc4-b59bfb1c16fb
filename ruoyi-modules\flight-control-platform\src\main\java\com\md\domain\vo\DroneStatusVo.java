package com.md.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.md.domain.dto.DroneStatusDto;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * 无人机状态视图对象
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@AutoMapper(target = DroneStatusDto.class)
public class DroneStatusVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 无人机ID（序列号）
     */
    private String droneId;

    /**
     * 无人机名称
     */
    private String droneName;

    /**
     * 是否在线
     */
    private Boolean online;

    /**
     * 无人机状态JSON数据
     */
    private String statusData;

    /**
     * 虚拟摇杆状态
     */
    private Map<String, Object> virtualStickStatus;
}