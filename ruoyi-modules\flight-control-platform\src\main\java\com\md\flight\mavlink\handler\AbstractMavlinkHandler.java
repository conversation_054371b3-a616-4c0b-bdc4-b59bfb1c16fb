package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * MAVLink消息处理器抽象基类
 *
 * @param <T> MAVLink消息类型
 */
@Getter
@Slf4j
public abstract class AbstractMavlinkHandler<T> {

    protected final MavlinkCoreService coreService;

    public AbstractMavlinkHandler(MavlinkCoreService coreService) {
        this.coreService = coreService;
    }

    /**
     * 处理来自特定无人机的消息
     *
     * @param droneId 无人机ID
     * @param message 待处理的消息
     */
    public void handle(String droneId, T message) {
        try {
            doHandle(droneId, message);
        } catch (Exception e) {
            log.error("处理无人机[{}]消息时发生错误: 消息类型={}, 错误={}", droneId, message.getClass().getSimpleName(),
                e.getMessage(), e);
        }
    }

    /**
     * 具体的消息处理逻辑（带无人机ID）
     * 子类需要实现此方法以支持多无人机
     *
     * @param droneId 无人机ID
     * @param message 待处理的消息
     */
    protected abstract void doHandle(String droneId, T message);

    /**
     * 获取处理器支持的消息类型
     *
     * @return 消息类型的Class对象
     */
    public abstract Class<T> getMessageType();
}