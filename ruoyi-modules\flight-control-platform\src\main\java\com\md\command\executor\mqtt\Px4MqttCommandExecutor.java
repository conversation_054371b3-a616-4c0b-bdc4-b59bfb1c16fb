package com.md.command.executor.mqtt;

import com.md.command.constant.CommandType;
import com.md.command.core.DroneCommand;
import com.md.command.core.DroneCommandExecutor;
import com.md.command.model.dto.CommandResult;
import com.md.flight.px4.constant.Px4Constants;
import com.md.flight.px4.service.Px4DroneControlService;
import com.md.service.IFlightTaskExecutionService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * PX4 MQTT命令执行器
 * 负责将通用命令转发给PX4无人机执行
 * 该执行器通过Px4DroneControlService服务层来执行具体的业务逻辑和MQTT通信
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class Px4MqttCommandExecutor implements DroneCommandExecutor {

    // 摇杆最大输入值范围 - 使用Px4Constants中的默认值
    private static final float MAX_STICK_VALUE = Px4Constants.DefaultValues.MAX_STICK_VALUE;

    // 依赖注入业务服务层
    private final Px4DroneControlService droneControlService;

    @Lazy
    private final IFlightTaskExecutionService flightTaskExecutionService;

    // 命令处理器映射
    private final Map<CommandType, CommandHandler> commandHandlers = new EnumMap<>(CommandType.class);

    /**
     * 命令处理器函数式接口
     * 用于定义处理各种命令类型的方法
     */
    @FunctionalInterface
    private interface CommandHandler {
        /**
         * 处理特定类型的无人机命令
         *
         * @param command 无人机命令
         * @return 命令执行结果
         * @throws Exception 执行过程中可能发生的异常
         */
        CommandResult handle(DroneCommand command) throws Exception;
    }

    /**
     * 初始化命令处理器映射
     * 将各种命令类型映射到对应的处理方法
     */
    @PostConstruct
    private void initCommandHandlers() {
        // 航线任务相关
        commandHandlers.put(CommandType.EXECUTE_MINIO_MISSION, this::executeFlightTask);

        // 基本飞行控制
        commandHandlers.put(CommandType.ARM, this::executeArm);           // 解锁
        commandHandlers.put(CommandType.DISARM, this::executeDisarm);     // 上锁
        commandHandlers.put(CommandType.LAND, this::executeLand);         // 降落
        commandHandlers.put(CommandType.GO_HOME, this::executeGoHome);    // 返航

        // 虚拟摇杆控制
        commandHandlers.put(CommandType.VIRTUAL_STICK_START, this::executeVirtualStickStart);       // 开启虚拟摇杆
        commandHandlers.put(CommandType.VIRTUAL_STICK_STOP, this::executeVirtualStickStop);         // 停止虚拟摇杆
        commandHandlers.put(CommandType.VIRTUAL_STICK_CONTROL, this::executeVirtualStickControl);   // 虚拟摇杆控制
        commandHandlers.put(CommandType.VIRTUAL_STICK_TAKEOFF, this::executeTakeoff);               // 虚拟摇杆起飞
        commandHandlers.put(CommandType.VIRTUAL_STICK_LAND, this::executeLand);                     // 虚拟摇杆降落
        commandHandlers.put(CommandType.VIRTUAL_STICK_GO_HOME, this::executeGoHome);                // 虚拟摇杆返航

        // 航线任务控制
        commandHandlers.put(CommandType.PAUSE_MISSION, this::executePauseMission);                  // 暂停任务
        commandHandlers.put(CommandType.RESUME_MISSION, this::executeResumeMission);                // 恢复任务
        commandHandlers.put(CommandType.STOP_MISSION, this::executeStopMission);                    // 停止任务
    }

    /**
     * 执行无人机命令
     * 根据命令类型分发到对应的处理方法
     *
     * @param command 无人机命令
     * @return 命令执行结果
     */
    @Override
    public CommandResult executeCommand(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            CommandType commandType = command.getCommandType();

            log.info("收到PX4 MQTT命令: droneId={}, commandType={}, params={}", droneId, commandType,
                command.getParameters());

            // 查找对应的命令处理器
            CommandHandler handler = commandHandlers.get(commandType);
            if (handler != null) {
                log.debug("开始执行PX4 MQTT命令: droneId={}, commandType={}", droneId, commandType);
                CommandResult result = handler.handle(command);
                log.info("PX4 MQTT命令执行完成: droneId={}, commandType={}, 结果={}", droneId, commandType,
                    result.isSuccess() ? "成功" : "失败");
                return result;
            }

            // 如果没有找到特定处理器，返回不支持的命令错误
            log.warn("不支持的PX4命令类型: droneId={}, commandType={}", droneId, commandType);
            return CommandResult.failed("不支持的PX4命令类型: " + commandType);
        } catch (Exception e) {
            log.error("PX4 MQTT命令执行失败: command={}, error={}", command, e.getMessage(), e);
            return CommandResult.failed("命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行解锁命令
     * 通过服务层解锁无人机
     *
     * @param command 解锁命令
     * @return 命令执行结果
     */
    private CommandResult executeArm(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            boolean success = droneControlService.arm(droneId);

            if (success) {
                return CommandResult.success("PX4解锁命令已发送");
            } else {
                return CommandResult.failed("PX4解锁命令发送失败");
            }
        } catch (Exception e) {
            log.error("PX4解锁命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4解锁命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行虚拟摇杆起飞命令
     * 通过服务层控制无人机起飞到指定高度
     *
     * @param command 起飞命令，可包含altitude参数指定起飞高度
     * @return 命令执行结果
     */
    private CommandResult executeTakeoff(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            Map<String, Object> params = command.getParameters();

            // 提取起飞高度参数，默认为10米
            float altitude = 10.0f;
            if (params != null && params.containsKey("altitude") && params.get("altitude") != null) {
                try {
                    altitude = Float.parseFloat(params.get("altitude").toString());
                } catch (NumberFormatException e) {
                    log.warn("起飞高度参数格式不正确，使用默认值10米: {}", params.get("altitude"));
                }
            }

            boolean success = droneControlService.takeoff(droneId, altitude);

            if (success) {
                return CommandResult.success("PX4虚拟摇杆起飞命令已发送");
            } else {
                return CommandResult.failed("PX4虚拟摇杆起飞命令发送失败");
            }
        } catch (Exception e) {
            log.error("PX4虚拟摇杆起飞命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4虚拟摇杆起飞命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行上锁命令
     * 通过服务层锁定无人机
     *
     * @param command 上锁命令
     * @return 命令执行结果
     */
    private CommandResult executeDisarm(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            boolean success = droneControlService.disarm(droneId);

            if (success) {
                return CommandResult.success("PX4上锁命令已发送");
            } else {
                return CommandResult.failed("PX4上锁命令发送失败");
            }
        } catch (Exception e) {
            log.error("PX4上锁命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4上锁命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行降落命令
     * 通过服务层控制无人机降落
     *
     * @param command 降落命令
     * @return 命令执行结果
     */
    private CommandResult executeLand(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            boolean success = droneControlService.land(droneId);

            if (success) {
                return CommandResult.success("PX4降落命令已发送");
            } else {
                return CommandResult.failed("PX4降落命令发送失败");
            }
        } catch (Exception e) {
            log.error("PX4降落命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4降落命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行返航命令
     * 通过服务层控制无人机返回起飞点
     *
     * @param command 返航命令
     * @return 命令执行结果
     */
    private CommandResult executeGoHome(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            boolean success = droneControlService.goHome(droneId);

            if (success) {
                return CommandResult.success("PX4返航命令已发送");
            } else {
                return CommandResult.failed("PX4返航命令发送失败");
            }
        } catch (Exception e) {
            log.error("PX4返航命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4返航命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行开启虚拟摇杆命令
     * 通过服务层启用无人机的虚拟摇杆控制模式
     *
     * @param command 开启虚拟摇杆命令
     * @return 命令执行结果
     */
    private CommandResult executeVirtualStickStart(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            boolean success = droneControlService.startVirtualStick(droneId);

            if (success) {
                return CommandResult.success("PX4虚拟摇杆控制模式已启用");
            } else {
                return CommandResult.failed("PX4虚拟摇杆控制模式启用失败");
            }
        } catch (Exception e) {
            log.error("PX4开启虚拟摇杆命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4开启虚拟摇杆命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行停止虚拟摇杆命令
     * 通过服务层停用无人机的虚拟摇杆控制模式
     *
     * @param command 停止虚拟摇杆命令
     * @return 命令执行结果
     */
    private CommandResult executeVirtualStickStop(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            boolean success = droneControlService.stopVirtualStick(droneId);

            if (success) {
                return CommandResult.success("PX4虚拟摇杆控制模式已停用");
            } else {
                return CommandResult.failed("PX4虚拟摇杆控制模式停用失败");
            }
        } catch (Exception e) {
            log.error("PX4停止虚拟摇杆命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4停止虚拟摇杆命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行虚拟摇杆控制命令
     * 通过服务层发送虚拟摇杆控制指令，支持两种参数格式：
     * 1. 双摇杆控制格式（leftStick和rightStick）
     * 2. 原始控制格式（x, y, z, yaw）
     *
     * @param command 虚拟摇杆控制命令
     * @return 命令执行结果
     */
    private CommandResult executeVirtualStickControl(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            Map<String, Object> params = command.getParameters();

            // 控制参数，默认值为0
            float x = 0.0f;
            float y = 0.0f;
            float z = 0.0f;
            float yaw = 0.0f;

            // 判断是否使用新的双摇杆控制格式
            if (params != null && params.containsKey("leftStick") && params.containsKey("rightStick")) {
                // 使用新的双摇杆控制格式
                log.info("使用双摇杆控制格式: droneId={}", droneId);

                // 提取左摇杆参数
                Map<String, Object> leftStick = (Map<String, Object>)params.get("leftStick");
                if (leftStick != null) {
                    // 左摇杆垂直方向控制高度(z)，正值上升，负值下降
                    float verticalValue = getFloatParam(leftStick, "vertical", 0.0f);
                    // 将输入值映射到 -1.0 到 1.0 范围，并反转符号（因为z轴正值为下降）
                    z = -normalizeStickValue(verticalValue);

                    // 左摇杆水平方向控制偏航(yaw)，正值右旋转，负值左旋转
                    float horizontalValue = getFloatParam(leftStick, "horizontal", 0.0f);
                    // 将输入值映射到 -1.0 到 1.0 范围
                    yaw = normalizeStickValue(horizontalValue);
                }

                // 提取右摇杆参数
                Map<String, Object> rightStick = (Map<String, Object>)params.get("rightStick");
                if (rightStick != null) {
                    // 右摇杆垂直方向控制前后移动(x)，正值前进，负值后退
                    float verticalValue = getFloatParam(rightStick, "vertical", 0.0f);
                    // 将输入值映射到 -1.0 到 1.0 范围
                    x = normalizeStickValue(verticalValue);

                    // 右摇杆水平方向控制左右移动(y)，正值右移，负值左移
                    float horizontalValue = getFloatParam(rightStick, "horizontal", 0.0f);
                    // 将输入值映射到 -1.0 到 1.0 范围
                    y = normalizeStickValue(horizontalValue);
                }

                log.info(
                    "双摇杆控制参数解析: droneId={}, x(前后)={}, y(左右)={}, z(上下)={}, yaw(旋转)={} (已归一化到-1.0到1.0范围)",
                    droneId, String.format("%.2f", x), String.format("%.2f", y), String.format("%.2f", z),
                    String.format("%.2f", yaw));
            } else if (params != null) {
                // 使用原有的控制格式（假设已经是 -1.0 到 1.0 范围）
                x = getFloatParam(params, "x", 0.0f);
                y = getFloatParam(params, "y", 0.0f);
                z = getFloatParam(params, "z", 0.0f);
                yaw = getFloatParam(params, "yaw", 0.0f);

                // 确保值在 -1.0 到 1.0 范围内
                x = clampValue(x, -1.0f, 1.0f);
                y = clampValue(y, -1.0f, 1.0f);
                z = clampValue(z, -1.0f, 1.0f);
                yaw = clampValue(yaw, -1.0f, 1.0f);
            }

            // 提取坐标系统参数，默认为BODY
            String coordinate = Px4Constants.DefaultValues.DEFAULT_COORDINATE;
            if (params != null && params.containsKey("coordinate") && params.get("coordinate") != null) {
                coordinate = params.get("coordinate").toString();
            }

            // 使用服务层发送虚拟摇杆控制命令
            boolean success = droneControlService.controlVirtualStick(droneId, x, y, z, yaw, coordinate);

            if (success) {
                return CommandResult.success("PX4虚拟摇杆控制命令已发送");
            } else {
                return CommandResult.failed("PX4虚拟摇杆控制命令发送失败");
            }
        } catch (Exception e) {
            log.error("PX4虚拟摇杆控制命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4虚拟摇杆控制命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 将摇杆输入值（如 -30 到 30）归一化到 -1.0 到 1.0 范围
     *
     * @param value 摇杆输入值
     * @return 归一化后的值，范围在 -1.0 到 1.0 之间
     */
    private float normalizeStickValue(float value) {
        // 将值限制在 -MAX_STICK_VALUE 到 MAX_STICK_VALUE 范围内
        float clampedValue = clampValue(value, -MAX_STICK_VALUE, MAX_STICK_VALUE);
        // 归一化到 -1.0 到 1.0 范围
        return clampedValue / MAX_STICK_VALUE;
    }

    /**
     * 将值限制在指定范围内
     *
     * @param value 原始值
     * @param min   最小值
     * @param max   最大值
     * @return 限制在指定范围内的值
     */
    private float clampValue(float value, float min, float max) {
        return Math.max(min, Math.min(max, value));
    }

    /**
     * 从参数中获取浮点数值，如果获取失败则返回默认值
     *
     * @param params       参数映射
     * @param key          参数键
     * @param defaultValue 默认值
     * @return 浮点数值，获取失败时返回默认值
     */
    private float getFloatParam(Map<String, Object> params, String key, float defaultValue) {
        if (params.containsKey(key) && params.get(key) != null) {
            try {
                return Float.parseFloat(params.get(key).toString());
            } catch (NumberFormatException e) {
                log.warn("参数{}格式不正确，使用默认值{}: {}", key, defaultValue, params.get(key));
            }
        }
        return defaultValue;
    }

    /**
     * 获取执行器支持的无人机厂商
     *
     * @return 支持的厂商集合
     */
    @Override
    public Set<String> getSupportedManufacturers() {
        return Set.of(Px4Constants.MANUFACTURER_PX4);  // 支持PX4厂商
    }

    /**
     * 获取执行器支持的通信协议
     *
     * @return 通信协议
     */
    @Override
    public String getSupportedProtocol() {
        return Px4Constants.PROTOCOL_MQTT;  // 使用MQTT协议
    }

    /**
     * 获取执行器的优先级
     *
     * @return 优先级
     */
    @Override
    public int getPriority() {
        return 10;  // 默认优先级
    }

    /**
     * 执行暂停任务命令
     *
     * @param command 无人机命令
     * @return 命令执行结果
     */
    private CommandResult executePauseMission(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            log.info("执行PX4暂停任务命令: droneId={}", droneId);

            // 调用服务层暂停任务
            boolean success = droneControlService.pauseMission(droneId);

            if (success) {
                return CommandResult.success("PX4暂停任务命令已发送");
            } else {
                return CommandResult.failed("PX4暂停任务命令发送失败");
            }
        } catch (Exception e) {
            log.error("PX4暂停任务命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4暂停任务命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行恢复任务命令
     *
     * @param command 无人机命令
     * @return 命令执行结果
     */
    private CommandResult executeResumeMission(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            log.info("执行PX4恢复任务命令: droneId={}", droneId);

            // 调用服务层恢复任务
            boolean success = droneControlService.resumeMission(droneId);

            if (success) {
                return CommandResult.success("PX4恢复任务命令已发送");
            } else {
                return CommandResult.failed("PX4恢复任务命令发送失败");
            }
        } catch (Exception e) {
            log.error("PX4恢复任务命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4恢复任务命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行停止任务命令
     *
     * @param command 无人机命令
     * @return 命令执行结果
     */
    private CommandResult executeStopMission(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            log.info("执行PX4停止任务命令: droneId={}", droneId);

            // 调用服务层停止任务
            boolean success = droneControlService.stopMission(droneId);

            if (success) {
                return CommandResult.success("PX4停止任务命令已发送");
            } else {
                return CommandResult.failed("PX4停止任务命令发送失败");
            }
        } catch (Exception e) {
            log.error("PX4停止任务命令执行失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("PX4停止任务命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行航线任务命令
     * PX4直接通过executeFlightTask执行任务
     *
     * @param command 无人机命令
     * @return 命令执行结果
     */
    private CommandResult executeFlightTask(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            Map<String, Object> params = command.getParameters();

            log.info("执行PX4航线任务命令: droneId={}, params={}", droneId, params);

            // 从命令参数中获取taskId
            String taskId = getTaskIdFromCommand(command);
            if (taskId == null) {
                log.error("未找到任务ID: droneId={}", droneId);
                return CommandResult.failed("未找到任务ID");
            }

            // 使用PX4任务执行引擎执行任务，不需要处理missionFile参数
            log.info("开始通过PX4任务执行引擎执行航线任务: taskId={}", taskId);
            CompletableFuture<Boolean> future = flightTaskExecutionService.executeFlightTask(taskId);

            // 异步处理执行结果
            future.whenComplete((success, throwable) -> {
                if (throwable != null) {
                    log.error("PX4航线任务执行失败: taskId={}, error={}", taskId, throwable.getMessage(), throwable);
                } else if (success) {
                    log.info("PX4航线任务执行成功: taskId={}", taskId);
                } else {
                    log.warn("PX4航线任务执行失败: taskId={}", taskId);
                }
            });

            return CommandResult.success("PX4航线任务已开始执行");
        } catch (Exception e) {
            log.error("执行PX4航线任务命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行PX4航线任务命令失败: " + e.getMessage());
        }
    }

    /**
     * 从命令中获取任务ID
     * 优先从命令的taskId字段获取，如果没有再从参数中获取
     *
     * @param command 无人机命令
     * @return 任务ID，如果未找到则返回null
     */
    private String getTaskIdFromCommand(DroneCommand command) {
        // 优先从命令的taskId字段获取
        String taskId = command.getTaskId();
        if (taskId != null && !taskId.trim().isEmpty()) {
            return taskId;
        }

        // 如果命令级别没有taskId，再从参数中获取
        if (command.getParameters() != null) {
            Map<String, Object> params = command.getParameters();
            Object taskIdObj = params.get("taskId");
            if (taskIdObj != null) {
                return taskIdObj.toString();
            }
        }

        return null;
    }

}