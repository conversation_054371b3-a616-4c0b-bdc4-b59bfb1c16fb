package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

/**
 * 开放端接口实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_open_api_config")
public class OpenApiConfig extends TenantEntity {

    /**
     * 接口ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 开放路由地址
     */
    private String openAddress;

    /**
     * 备注
     */
    private String remark;

    /**
     * 显示排序
     */
    private String sort;

    /**
     * 配置信息
     */
    private String encryptInfo;

    /**
     * 状态
     */
    private String status;

    private String delFlag;

}
