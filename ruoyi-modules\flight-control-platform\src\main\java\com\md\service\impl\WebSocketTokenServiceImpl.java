package com.md.service.impl;

import com.md.service.IWebSocketTokenService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket Token服务实现类
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WebSocketTokenServiceImpl implements IWebSocketTokenService {

    private static final String WS_TOKEN_KEY = "ws:token:";  // Redis key前缀
    private static final String WS_TOKEN_REFRESH_KEY = "ws:token:refresh:";  // 刷新Token的Redis key前缀
    private static final int TOKEN_EXPIRE_MINUTES = 5;  // Token有效期（分钟）
    private static final int TOKEN_GRACE_PERIOD_SECONDS = 30;  // Token宽限期（秒）
    private static final int TOKEN_REFRESH_WINDOW_SECONDS = 60;  // Token刷新窗口（秒）

    @Override
    public Map<String, Object> generateToken(String droneId) {
        try {
            // 1. 获取当前用户信息（如果未登录会抛出异常）
            Long userId = LoginHelper.getUserId();
            String username = LoginHelper.getUsername();

            return TenantHelper.ignore(() -> {
                // 2. 检查是否存在可刷新的Token
                String refreshKey = WS_TOKEN_REFRESH_KEY + droneId + ":" + userId;
                String existingToken = RedisUtils.getCacheObject(refreshKey);

                if (existingToken != null) {
                    // 如果存在可刷新的Token，直接返回
                    Map<String, Object> tokenInfo = RedisUtils.getCacheMap(WS_TOKEN_KEY + existingToken);
                    if (tokenInfo != null && !tokenInfo.isEmpty()) {
                        Map<String, Object> result = new HashMap<>();
                        result.put("token", existingToken);
                        result.put("expireTime", TOKEN_EXPIRE_MINUTES * 60);
                        log.info("使用现有Token: userId={}, username={}, droneId={}, token={}", userId, username, droneId,
                            existingToken);
                        return result;
                    }
                }

                // 3. 生成新Token
                String token = IdUtil.fastSimpleUUID();

                // 4. 构建Token信息
                Map<String, Object> tokenInfo = new HashMap<>();
                tokenInfo.put("userId", userId);
                tokenInfo.put("username", username);
                tokenInfo.put("droneId", droneId);
                tokenInfo.put("createTime", System.currentTimeMillis());

                // 5. 存储到Redis
                String tokenKey = WS_TOKEN_KEY + token;
                RedisUtils.setCacheMap(tokenKey, tokenInfo);
                RedisUtils.expire(tokenKey, Duration.ofMinutes(TOKEN_EXPIRE_MINUTES));

                // 6. 存储刷新Token信息
                RedisUtils.setCacheObject(refreshKey, token);
                RedisUtils.expire(refreshKey, Duration.ofMinutes(TOKEN_EXPIRE_MINUTES));

                // 7. 构建返回数据
                Map<String, Object> result = new HashMap<>();
                result.put("token", token);
                result.put("expireTime", TOKEN_EXPIRE_MINUTES * 60);

                log.info("生成新Token: userId={}, username={}, droneId={}, token={}", userId, username, droneId, token);
                return result;
            });

        } catch (Exception e) {
            log.error("生成Token失败: droneId={}, error={}", droneId, e.getMessage(), e);
            throw new RuntimeException("获取Token失败：" + e.getMessage());
        }
    }

    @Override
    public boolean validateToken(String token, String droneId) {
        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(droneId)) {
            log.warn("Token验证失败：参数为空");
            return false;
        }

        try {
            return TenantHelper.ignore(() -> {
                // 1. 从Redis获取Token信息
                String tokenKey = WS_TOKEN_KEY + token;
                Map<String, Object> tokenInfo = RedisUtils.getCacheMap(tokenKey);

                // 2. 验证Token是否存在
                if (tokenInfo == null || tokenInfo.isEmpty()) {
                    log.warn("Token验证失败：Token不存在或已过期");
                    return false;
                }

                // 3. 验证无人机ID是否匹配
                String storedDroneId = (String)tokenInfo.get("droneId");
                if (!droneId.equals(storedDroneId)) {
                    log.warn("Token验证失败：无人机ID不匹配, expected={}, actual={}", storedDroneId, droneId);
                    return false;
                }

                // 4. 检查是否在刷新窗口期并自动延长有效期
                Long createTime = Convert.toLong(tokenInfo.get("createTime"));
                long currentTime = System.currentTimeMillis();
                long tokenAge = (currentTime - createTime) / 1000;

                if (tokenAge > (TOKEN_EXPIRE_MINUTES * 60 - TOKEN_REFRESH_WINDOW_SECONDS)) {
                    // 在刷新窗口期内自动延长Token和刷新Token的有效期
                    RedisUtils.expire(tokenKey, Duration.ofSeconds(TOKEN_GRACE_PERIOD_SECONDS));

                    // 同时更新刷新Token的过期时间
                    Long userId = Convert.toLong(tokenInfo.get("userId"));
                    String refreshKey = WS_TOKEN_REFRESH_KEY + droneId + ":" + userId;
                    RedisUtils.expire(refreshKey, Duration.ofSeconds(TOKEN_GRACE_PERIOD_SECONDS));

                    log.info("Token在刷新窗口期，延长有效期: token={}, droneId={}", token, droneId);
                }

                log.debug("Token验证成功: droneId={}, token={}", droneId, token);
                return true;
            });

        } catch (Exception e) {
            log.error("Token验证异常: token={}, droneId={}, error={}", token, droneId, e.getMessage(), e);
            return false;
        }
    }
}