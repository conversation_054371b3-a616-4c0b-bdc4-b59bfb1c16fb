package com.md.mqtt.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.constant.MqttConstants;
import com.md.mqtt.exception.MqttProcessException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 无人机直播状态消息处理器
 * 处理dji/livestream/status主题的消息
 */
@Component
@Slf4j
public class LiveStreamStatusMessageProcessor extends AbstractMqttMessageProcessor {
    @Value("${mqtt.redis.expire-seconds:20}")  // 默认20秒
    private int expireSeconds;

    @Value("${mqtt.state.min-update-interval:500}")  // 默认500毫秒
    private long minUpdateInterval;

    // 状态更新限流器，每架无人机每秒最多处理2次状态更新
    private final Map<String, Long> lastUpdateTimeMap = new ConcurrentHashMap<>();

    @Override
    protected String getTopicPattern() {
        return "dji/livestream/status";
    }

    @Override
    protected void doProcessMessage(String topic, String payload) {
        try {
            // 解析 payload 中的 droneSN
            JSONObject jsonPayload = JSON.parseObject(payload);
            String droneId = jsonPayload.getString("droneSN");
            if (StringUtils.isEmpty(droneId)) {
                log.error("无人机直播状态消息中未包含droneId字段，消息内容：{}", payload);
                return;
            }

            // 检查是否需要采样处理
            long currentTime = System.currentTimeMillis();
            Long lastTime = lastUpdateTimeMap.get(droneId);
            if (lastTime != null && currentTime - lastTime < minUpdateInterval) {
                // 距离上次更新时间太短，跳过此次处理
                log.debug("无人机直播状态更新过于频繁，跳过处理，无人机ID：{}", droneId);
                return;
            }
            lastUpdateTimeMap.put(droneId, currentTime);

            // 存储到Redis
            TenantHelper.ignore(() -> {
                RedisUtils.setCacheObject(MqttConstants.REDIS_LIVESTREAM_STATUS_KEY_PREFIX + droneId,
                    jsonPayload.toString(), Duration.ofSeconds(expireSeconds));
                return null;
            });

            log.debug("处理无人机直播状态消息成功，无人机ID：{}, 状态：{}", droneId, jsonPayload);
        } catch (Exception e) {
            log.error("处理无人机直播状态消息失败", e);
            throw new MqttProcessException("处理无人机直播状态消息失败", e);
        }
    }
}