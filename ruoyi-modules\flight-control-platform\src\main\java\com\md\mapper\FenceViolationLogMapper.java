package com.md.mapper;

import com.md.domain.po.FenceViolationLog;
import com.md.domain.vo.FenceViolationVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Date;
import java.util.List;

/**
 * 围栏违规记录Mapper接口
 */
public interface FenceViolationLogMapper extends BaseMapperPlus<FenceViolationLog, FenceViolationVo> {
    /**
     * 查询违规记录
     *
     * @param id 记录ID
     * @return 违规记录
     */
    FenceViolationLog selectViolationLogById(String id);

    /**
     * 根据任务ID查询违规记录
     *
     * @param taskId 任务ID
     * @return 违规记录列表
     */
    List<FenceViolationLog> selectByTaskId(@Param("taskId") String taskId);

    /**
     * 根据无人机编码查询违规记录
     *
     * @param uavCode 无人机编码
     * @return 违规记录列表
     */
    List<FenceViolationLog> selectByUavCode(@Param("uavCode") String uavCode);

    /**
     * 根据围栏ID查询违规记录
     *
     * @param fenceId 围栏ID
     * @return 违规记录列表
     */
    List<FenceViolationLog> selectByFenceId(@Param("fenceId") String fenceId);

    /**
     * 根据时间范围查询违规记录
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 违规记录列表
     */
    List<FenceViolationLog> selectByTimeRange(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 查询违规记录列表
     *
     * @param violationLog 违规记录
     * @return 违规记录集合
     */
    List<FenceViolationLog> selectViolationLogList(FenceViolationLog violationLog);

    /**
     * 新增违规记录
     *
     * @param violationLog 违规记录
     * @return 结果
     */
    int insertViolationLog(FenceViolationLog violationLog);

    /**
     * 批量新增违规记录
     *
     * @param violationLogs 违规记录列表
     * @return 结果
     */
    int batchInsertViolationLogs(@Param("list") List<FenceViolationLog> violationLogs);

    /**
     * 修改违规记录
     *
     * @param violationLog 违规记录
     * @return 结果
     */
    int updateViolationLog(FenceViolationLog violationLog);

    /**
     * 删除违规记录
     *
     * @param id 记录ID
     * @return 结果
     */
    int deleteViolationLogById(String id);

    /**
     * 批量删除违规记录
     *
     * @param ids 需要删除的记录ID数组
     * @return 结果
     */
    int deleteViolationLogByIds(String[] ids);
}