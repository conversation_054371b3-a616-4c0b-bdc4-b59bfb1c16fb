package com.md.flight.yz.openfeign.fallback;

import com.md.flight.yz.domain.dto.YzBaseReqDto;
import com.md.flight.yz.openfeign.YzGetTokenClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 获取token容错降级
 */
@Slf4j
@Component
public class YzGetTokenClientFallback implements FallbackFactory<YzGetTokenClient> {

    @Override
    public YzGetTokenClient create(Throwable throwable) {
        log.error("GetTokenClient异常原因:{}", throwable.getMessage(), throwable);
        return new YzGetTokenClient() {
            @Override
            public String getTokenClient(YzBaseReqDto baseReqDto) {
//                return "9176647213DFF06406938AB91C0254DD1ECB97AB36FD528AE7CEFC7EFEA92280A14352EB41241FA50EC4932C12819D853535C421B3F88EBDE86949084A1E5FE2F10D3344E748571C2D2620AA38C97000B322C34F41424E4F322F3AC77F731D9364DCFE67B8BE23325C201DCA9373966DA72CA6BB6637831D7E5DE61BAE83869061479A3278B012B32AAD91B7E90ADB2A1E167068BD5E363EA2216833AC9C4E7361CA126E323939F05DA215698A178EA8E3B8D5B77BC3AA85E20801FF333A97244AE1518A7D77C07C109C577265917E7D04E31E356EC01F84F1EE53547D0846EDF346541D8405B2EFAF0615C2C0D79A26295D78C3FD81C075928C19143FC01A9C6C051FE80EAA81C18640B3E465C5418B3734D15691398D00114552925782C756";
                return "exception";
            }
        };
    }
}