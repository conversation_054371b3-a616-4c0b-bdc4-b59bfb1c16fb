package com.md.domain.bo;

import com.md.domain.dto.FenceDetectionResult;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 围栏检测业务对象
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class FenceDetectionBo extends BaseEntity {
    /**
     * 围栏ID
     */
    private String fenceId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 无人机编码
     */
    private String uavCode;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 高度(米)
     */
    private BigDecimal height;

    /**
     * 是否违规
     */
    private boolean violated;

    /**
     * 违规区域ID
     */
    private String violatedAreaId;

    /**
     * 应执行的动作
     */
    private Integer actionToTake;

    /**
     * 违规原因
     */
    private String violationReason;

    /**
     * 围栏缓存查询半径(米)，默认10000米
     */
    private Long radius = 10000L;

    /**
     * 转换为检测结果DTO
     */
    public FenceDetectionResult toDetectionResult() {
        return FenceDetectionResult.builder().violated(this.isViolated()).fenceId(this.getFenceId())
            .violatedAreaId(this.getViolatedAreaId()).actionToTake(this.getActionToTake())
            .violationReason(this.getViolationReason()).build();
    }
}