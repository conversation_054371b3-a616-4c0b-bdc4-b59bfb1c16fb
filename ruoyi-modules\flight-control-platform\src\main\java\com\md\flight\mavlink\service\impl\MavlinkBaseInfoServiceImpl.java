package com.md.flight.mavlink.service.impl;

import com.md.service.IBaseInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * MAVLink基本信息实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MavlinkBaseInfoServiceImpl implements IBaseInfoService {

    @Override
    public void syncUAV() {
        log.info("MAVLink平台暂不支持同步无人机信息");
    }

    @Override
    public void syncFlyer() {
        log.info("MAVLink平台暂不支持同步飞手信息");
    }

    @Override
    public void syncFlightLine() {
        log.info("MAVLink平台暂不支持同步航线信息");
    }

    @Override
    public boolean buildKmzByFlightLine(String routeId) {
        log.info("MAVLink平台暂不支持生成KMZ文件，航线ID：{}", routeId);
        return true;
    }
}
