package com.md.utils;

/**
 * 坐标系转换工具类
 * 支持 WGS84/GCJ02/BD09 坐标系之间的转换
 */
public class CoordTransformUtils {

    private static final double BD_FACTOR = (Math.PI * 3000.0) / 180.0;
    private static final double PI = Math.PI;
    private static final double RADIUS = 6378245.0;
    private static final double EE = 0.00669342162296594323;

    /**
     * BD09 转 GCJ02
     *
     * @param lng 百度坐标系的经度
     * @param lat 百度坐标系的纬度
     * @return GCJ02 坐标系的经纬度
     */
    public static double[] BD09ToGCJ02(double lng, double lat) {
        double x = lng - 0.0065;
        double y = lat - 0.006;
        double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * BD_FACTOR);
        double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * BD_FACTOR);
        double ggLng = z * Math.cos(theta);
        double ggLat = z * Math.sin(theta);
        return new double[]{ggLng, ggLat};
    }

    /**
     * GCJ02 转 BD09
     *
     * @param lng GCJ02 坐标系的经度
     * @param lat GCJ02 坐标系的纬度
     * @return BD09 坐标系的经纬度
     */
    public static double[] GCJ02ToBD09(double lng, double lat) {
        double z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * BD_FACTOR);
        double theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * BD_FACTOR);
        double bdLng = z * Math.cos(theta) + 0.0065;
        double bdLat = z * Math.sin(theta) + 0.006;
        return new double[]{bdLng, bdLat};
    }

    /**
     * WGS84 转 GCJ02
     *
     * @param lng WGS84 坐标系的经度
     * @param lat WGS84 坐标系的纬度
     * @return GCJ02 坐标系的经纬度
     */
    public static double[] WGS84ToGCJ02(double lng, double lat) {
        if (outOfChina(lng, lat)) {
            return new double[]{lng, lat};
        }
        double[] d = delta(lng, lat);
        return new double[]{lng + d[0], lat + d[1]};
    }

    /**
     * GCJ02 转 WGS84
     *
     * @param lng GCJ02 坐标系的经度
     * @param lat GCJ02 坐标系的纬度
     * @return WGS84 坐标系的经纬度
     */
    public static double[] GCJ02ToWGS84(double lng, double lat) {
        if (outOfChina(lng, lat)) {
            return new double[]{lng, lat};
        }
        double[] d = delta(lng, lat);
        double mgLng = lng + d[0];
        double mgLat = lat + d[1];
        return new double[]{lng * 2 - mgLng, lat * 2 - mgLat};
    }

    private static double[] delta(double lng, double lat) {
        double dLng = transformLng(lng - 105.0, lat - 35.0);
        double dLat = transformLat(lng - 105.0, lat - 35.0);
        double radLat = lat / 180.0 * PI;
        double magic = Math.sin(radLat);
        magic = 1 - EE * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLng = (dLng * 180.0) / ((RADIUS / sqrtMagic) * Math.cos(radLat) * PI);
        dLat = (dLat * 180.0) / (((RADIUS * (1 - EE)) / (magic * sqrtMagic)) * PI);
        return new double[]{dLng, dLat};
    }

    private static double transformLng(double lng, double lat) {
        double ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng +
                0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
        ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
        ret += ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) / 3.0;
        ret += ((150.0 * Math.sin((lng / 12.0) * PI) + 300.0 * Math.sin((lng / 30.0) * PI)) * 2.0) / 3.0;
        return ret;
    }

    private static double transformLat(double lng, double lat) {
        double ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat +
                0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
        ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
        ret += ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) / 3.0;
        ret += ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) * 2.0) / 3.0;
        return ret;
    }

    /**
     * 判断坐标是否在中国范围内
     *
     * @param lng 经度
     * @param lat 纬度
     * @return true-不在中国范围内，false-在中国范围内
     */
    private static boolean outOfChina(double lng, double lat) {
        return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);
    }
}