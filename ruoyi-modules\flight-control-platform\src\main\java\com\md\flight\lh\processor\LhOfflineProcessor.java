package com.md.flight.lh.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.domain.po.UavInfo;
import com.md.flight.lh.constant.LhTopicConstantReq;
import com.md.flight.lh.domain.dto.LhBaseReq;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseOnlineData;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import com.md.mapper.UavInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 联合设备下线消息处理
 */
@Component
@Slf4j
public class LhOfflineProcessor implements LhBaseProcessor{
    @Autowired
    private UavInfoMapper uavInfoMapper;

    @Autowired
    private MqttMessageHandler messageHandler;

    @Override
    public void processMessage(String payload) {
        try {
            log.info("收到联合设备下线消息: {}", payload);
            // 消息转换
            LhBaseReq lhBaseReq = JSONObject.parseObject(payload, LhBaseReq.class);

            // 修改状态
            int result = uavInfoMapper.update(Wrappers.<UavInfo>lambdaUpdate()
                .eq(UavInfo::getUavCode, lhBaseReq.getGateway())
                .set(UavInfo::getStatus, 1));
            if (result == 0) {
                log.error("联合设备下线消息处理失败: {}", payload);
            }

            // 构建主题
            String topic = String.format(LhTopicConstantReq.ONLINE, lhBaseReq.getGateway());

            // 消息转换
            LhResponse<LhResponseOnlineData> lhResponse = JSON.parseObject(
                payload,
                new TypeReference<>() {});
            lhResponse.setData(new LhResponseOnlineData());
            // 响应消息
            messageHandler.sendToMqtt(topic, JSON.toJSONString(lhResponse));
        } catch (Exception e) {
            log.error("联合设备下线消息处理失败: {}", e.getMessage(), e);
        }
    }
}
