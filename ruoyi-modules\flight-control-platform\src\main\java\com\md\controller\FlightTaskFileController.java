package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.service.IFlightTaskFileService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 航线任务文件操作控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/flight/task/file")
@Validated
public class FlightTaskFileController extends BaseController {

    private final IFlightTaskFileService flightTaskFileService;

    /**
     * 根据航线ID生成kmz文件
     */
    @SaCheckPermission("flight:task:generateKmz")
    @RepeatSubmit
    @GetMapping(value = "/buildKmzByFlightLine")
    public R<Void> buildKmzByFlightLine(@RequestParam @NotBlank(message = "任务ID不能为空") String id) {
        return toAjax(flightTaskFileService.buildKmzByFlightLine(id));
    }

    /**
     * 导出航线KMZ文件
     */
    @GetMapping("/export/kmz")
    @SaCheckPermission("flight:task:export")
    public R<String> exportKmz(@RequestParam @NotBlank(message = "任务名称不能为空") String taskName,
        HttpServletResponse response) {
        String fileUrl = flightTaskFileService.exportKmz(taskName, response);
        return R.ok(fileUrl);
    }

    /**
     * 复制航线任务
     */
    @SaCheckPermission("flight:task:add")
    @Log(title = "航线任务管理", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{taskId}")
    public R<String> copyTask(@PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId) {
        String newTaskId = flightTaskFileService.copyFlightTask(taskId);
        return R.ok(newTaskId, "复制成功");
    }
}