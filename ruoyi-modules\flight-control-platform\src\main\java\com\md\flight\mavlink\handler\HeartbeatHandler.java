package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.minimal.Heartbeat;
import io.dronefleet.mavlink.minimal.MavType;
import io.dronefleet.mavlink.util.EnumValue;
import lombok.extern.slf4j.Slf4j;

/**
 * 心跳包处理器
 */
@Slf4j
public class HeartbeatHandler extends AbstractMavlinkHandler<Heartbeat> {

    // MAVLink中解锁标志位定义
    private static final int MAV_MODE_FLAG_SAFETY_ARMED = 128; // 0x80

    public HeartbeatHandler(MavlinkCoreService coreService) {
        super(coreService);
    }

    @Override
    protected void doHandle(String droneId, Heartbeat message) {
        // 保存最新的心跳包
        coreService.setLastHeartbeat(droneId, message);

        // 检查无人机是否解锁
        boolean armed = (message.baseMode().value() & MAV_MODE_FLAG_SAFETY_ARMED) != 0;
        
        // 获取无人机类型 - EnumValue<MavType>
        EnumValue<MavType> vehicleType = message.type();

        log.info("收到无人机[{}]心跳包: type={}, baseMode=0x{}, customMode={}, vehicleType={}, armed={}",
            droneId, message.type().entry(), Integer.toHexString(message.baseMode().value()),
            message.customMode(), vehicleType, armed);
    }

    @Override
    public Class<Heartbeat> getMessageType() {
        return Heartbeat.class;
    }
}