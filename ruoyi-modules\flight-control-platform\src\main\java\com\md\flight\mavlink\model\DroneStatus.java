package com.md.flight.mavlink.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 无人机完整状态信息模型
 */
@Data
@Accessors(chain = true)
public class DroneStatus implements Serializable {
    private static final long serialVersionUID = 1L;

    // 设备基本信息
    private String deviceModel;      // 设备型号
    private String droneName;        // 无人机名称
    private String droneSN;          // 无人机序列号
    private String batterySN;        // 电池序列号
    private int totalFlightTime;     // 总飞行时间(分钟)
    private int flightCount;         // 飞行次数
    private boolean online;          // 是否在线
    private boolean armed;           // 是否解锁

    // 位置信息
    private BigDecimal latitude;         // 纬度
    private BigDecimal longitude;        // 经度
    private float absoluteAltitude;      // 绝对高度(米)
    private float relativeAltitude;      // 相对高度(米)

    // 飞行信息
    private double speed;            // 飞行速度
    private float heading;           // 航向角(单位：度，0-360)
    private String flightMode;       // 飞行模式
    private double distanceToHome;   // 距离返航点距离
    private float groundSpeed;       // 地速(米/秒)
    private double airSpeed;         // 空速(米/秒)
    private double flightDistance;   // 航距(米)
    private float pitch;             // 俯仰角(度)
    private float roll;              // 横滚角(度)
    private float verticalSpeed;     // 垂直速度(米/秒)

    // 电池信息
    private BatteryInfo batteryInfo;

    // 任务信息
    private int currentWaypoint;     // 当前任务序号
    private int totalWaypoints;      // 总任务数量
    private String missionStatus;    // 任务状态

    // 卫星信息
    private int rtkSatelliteCount;   // RTK搜索到的卫星数量
    private int gpsFixType;          // GPS定位状态
    private int gpsSatellites;       // GPS卫星数量
    private SatelliteInfo satelliteInfo;

    // 导航灯信息
    private NavigationLightInfo navigationLightInfo;

    // 避障信息
    private ObstacleAvoidanceInfo obstacleAvoidanceInfo;

    // RTK相关信息
    private boolean isRTKEnabled;    // RTK是否启用
    private boolean isRTKHealthy;    // RTK是否健康
    private boolean isRTKConnected;  // RTK是否连接
    private double rtkAltitude;      // RTK海拔(米)

    // 其他信息
    private long lastUpdateTime;     // 最后更新时间
    private long timestamp;          // 时间戳

    /**
     * 无人机电池信息
     */
    @Data
    @Accessors(chain = true)
    public static class BatteryInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private int totalCapacity;              // 电池总容量(mAh)
        private int chargeRemaining;            // 剩余充电量(mAh)
        private int chargeRemainingInPercent;   // 剩余电量百分比(%)
        private int batteryLife;                // 电池寿命
        private float batteryVoltage;           // 电池电压
    }

    /**
     * 无人机夜航灯信息
     */
    @Data
    @Accessors(chain = true)
    public static class NavigationLightInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private boolean frontLEDsOn;            // 前灯是否开启
        private boolean statusIndicatorLEDsOn;  // 状态指示灯是否开启
        private boolean rearLEDsOn;             // 后灯是否开启
        private boolean navigationLEDsOn;       // 导航灯是否开启
        private int heightLimit;                // 飞行高度限制(米)
    }

    /**
     * 无人机避障信息
     */
    @Data
    @Accessors(chain = true)
    public static class ObstacleAvoidanceInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private boolean horizontalAvoidance;  // 水平避障是否开启
        private boolean upwardAvoidance;      // 上方避障是否开启
        private boolean downwardAvoidance;    // 下方避障是否开启
    }

    /**
     * 卫星信息数据类
     */
    @Data
    @Accessors(chain = true)
    public static class SatelliteInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private int mobileStation1Count;  // 移动站1接收到的卫星数量
        private int mobileStation2Count;  // 移动站2接收到的卫星数量
        private int baseStationCount;     // 基站接收到的卫星数量
        private int gpsCount;             // GPS卫星数量
    }
}