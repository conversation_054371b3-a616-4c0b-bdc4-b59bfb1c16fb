package com.md.flight.mavlink.model;

import lombok.Data;

/**
 * 航线任务航点实体类
 * 定义航线中的单个航点信息，包含位置坐标、命令类型等
 */
@Data
public class MissionWaypoint {
    /**
     * 航点序号
     * 航点在航线中的序列号，从0开始
     */
    private int seq;

    /**
     * 坐标系统
     * 0=全局坐标系(MAV_FRAME_GLOBAL)，相对于WGS84
     * 1=相对高度坐标系(MAV_FRAME_GLOBAL_RELATIVE_ALT)，相对于起飞点高度
     * 其他值参考MAV_FRAME枚举
     */
    private int frame;

    /**
     * MAV_CMD命令
     * 定义航点的动作类型，例如：
     * 16=MAV_CMD_NAV_WAYPOINT（普通航点）
     * 22=MAV_CMD_NAV_TAKEOFF（起飞点）
     * 21=MAV_CMD_NAV_LAND（降落点）
     * 更多命令参考MAV_CMD枚举
     */
    private int command;

    /**
     * 是否为当前航点
     * 1表示是当前正在执行的航点，0表示非当前航点
     */
    private int current;

    /**
     * 自动继续到下一个航点
     * 1表示自动继续到下一个航点，0表示在此航点等待进一步指令
     */
    private int autoContinue;

    /**
     * 参数1
     * 根据命令类型不同有不同含义：
     * 对于航点(16)：等待时间(秒)
     * 对于起飞(22)：俯仰角(度)
     * 对于降落(21)：中止高度(米)
     */
    private float param1;

    /**
     * 参数2
     * 根据命令类型不同有不同含义：
     * 对于航点(16)：接受半径(米)，如果为0则使用默认值
     * 对于盘旋(17)：盘旋半径(米)，正值顺时针，负值逆时针
     */
    private float param2;

    /**
     * 参数3
     * 根据命令类型不同有不同含义：
     * 对于航点(16)：通过半径(米)，如果为0则飞过航点
     */
    private float param3;

    /**
     * 参数4 (通常是偏航角)
     * 单位：度，范围：0-360
     * 根据命令类型不同有不同含义：
     * 对于航点(16)：期望到达时的偏航角(度)
     * 对于降落(21)：期望的降落方向(度)
     */
    private float param4;

    /**
     * 纬度
     * 单位：度，范围：-90到90
     * WGS84坐标系下的纬度值
     */
    private double latitude;

    /**
     * 经度
     * 单位：度，范围：-180到180
     * WGS84坐标系下的经度值
     */
    private double longitude;

    /**
     * 高度
     * 单位：米
     * 根据frame参数含义不同：
     * frame=0时，表示相对于海平面的绝对高度
     * frame=1时，表示相对于起飞点的相对高度
     */
    private float altitude;

    /**
     * 航点描述 (可选)
     * 用于描述此航点的作用或特殊说明
     */
    private String description;
}