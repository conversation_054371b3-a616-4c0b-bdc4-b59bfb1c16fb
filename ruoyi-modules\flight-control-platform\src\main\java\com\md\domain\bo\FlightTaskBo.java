package com.md.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 航线任务业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = FlightTask.class)
public class FlightTaskBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 飞控平台Id
     */
    @NotBlank(message = "飞控平台Id不能为空", groups = {AddGroup.class, EditGroup.class})
    private String flightControlNo;

    /**
     * 飞控平台名称
     */
    private String manufacturerName;

    /**
     * 航线ID
     */
    @NotBlank(message = "航线ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String lineId;

    /**
     * 航线名称
     */
    private String lineName;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 2, max = 100, message = "任务名称长度必须在 2-100 个字符之间",
        groups = {AddGroup.class, EditGroup.class})
    private String taskName;

    /**
     * 飞行器SN
     */
    @NotBlank(message = "飞行器SN不能为空", groups = {AddGroup.class, EditGroup.class})
    private String uavCode;

    /**
     * 飞手ID
     */
    private Long flyerId;

    /**
     * 飞手名称 - 查询用
     */
    private String flyerName;

    /**
     * 计划执飞时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightTime;

    /**
     * 开始时间-执飞开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginFlightTime;

    /**
     * 结束时间-执飞结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endFlightTime;

    /**
     * 开始时间-创建时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束时间-创建时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 开始时间-更新时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginUpdateTime;

    /**
     * 结束时间-更新时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endUpdateTime;

    /**
     * 执飞人ID
     */
    private Long operatorId;

    /**
     * 执飞人名称 - 查询用
     */
    private String operatorName;

    /**
     * 任务状态(0:待执行 1:执行中 2:已暂停 3:已恢复 4:已完成 5:执行失败 6:已取消)
     * 支持单个状态或逗号分隔的多个状态，如: "1" 或 "1,4"
     */
    private String taskStatus;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符", groups = {AddGroup.class, EditGroup.class})
    private String remark;

    /**
     * 飞行速度(m/s)
     */
    @NotNull(message = "飞行速度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal flightSpeed;

    /**
     * 飞行高度(m)
     */
    @NotNull(message = "飞行高度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal flightHeight;

    /**
     * 返航高度(m)
     */
    @NotNull(message = "返航高度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal returnHeight;

    /**
     * 任务结束后的动作(原地降落/返回起点等)
     */
    @NotBlank(message = "任务结束后的动作不能为空", groups = {AddGroup.class, EditGroup.class})
    private String finishedAction;

    /**
     * 网络失联后动作(返航/继续执行)
     */
    //    @NotBlank(message = "网络失联后动作不能为空", groups = {AddGroup.class, EditGroup.class})
    private String missingAction;

    /**
     * 航点列表
     */
    private List<FlightTaskPoint> points;

    /**
     * 任务创建人
     */
    private String createBy;

    /**
     * 任务更新人
     */
    private String updateBy;

    /**
     * 创建者名称
     */
    private String createByName;

    /**
     * 修改者名称
     */
    private String updateByName;

    /**
     * 协议类型（例如：MQTT、MAVLINK、SDK等）
     */
    private String protocolType;

    /**
     * 围栏ID（电子围栏关联）
     */
    private String fenceId;

    /**
     * 租户ID
     */
    private String tenantId;
}