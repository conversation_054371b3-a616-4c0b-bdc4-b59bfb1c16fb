package com.md.config;

import com.md.websocket.handler.DroneWebSocketHandler;
import com.md.websocket.interceptor.WebSocketAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.support.DefaultHandshakeHandler;

/**
 * WebSocket配置类
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Autowired
    private WebSocketAuthInterceptor authInterceptor;

    @Autowired
    private DroneWebSocketHandler droneWebSocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(droneWebSocketHandler, "/ws/drone/*").addInterceptors(authInterceptor)
            .setHandshakeHandler(new DefaultHandshakeHandler()).setAllowedOrigins("*");  // 允许所有来源，生产环境应该限制
    }
}