package com.md.service;

import com.md.domain.bo.SysApiTenantBindingBo;
import com.md.domain.vo.SysApiTenantBindingVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * 接口租户绑定关系 服务接口
 *
 *
 */
public interface ISysApiTenantBindingService {


    /**
     * 查询租户绑定列表
     *
     * @param bo 租户绑定查询条件
     * @return 租户绑定集合
     */
    TableDataInfo<SysApiTenantBindingVo> queryPageList(SysApiTenantBindingBo bo, PageQuery pageQuery);

    /**
     * 查询租户绑定列表
     *
     * @param bo 租户绑定查询条件
     * @return 租户绑定集合
     */
    List<SysApiTenantBindingVo> queryList(SysApiTenantBindingBo bo);

    /**
     * 新增租户绑定
     *
     * @param bo 租户绑定对象
     * @return 结果
     */
    Boolean insertByBo(SysApiTenantBindingBo bo);

    /**
     * 修改租户绑定
     *
     * @param bo 租户绑定对象
     * @return 结果
     */
    Boolean updateByBo(SysApiTenantBindingBo bo);

}
