package com.md.openfeign.fallback;

import com.md.openfeign.TaskCallbackClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.net.URI;

/**
 * 任务回调接口客户端降级工厂
 */
@Slf4j
@Component
public class TaskCallbackClientFallback implements FallbackFactory<TaskCallbackClient> {

    @Override
    public TaskCallbackClient create(Throwable cause) {
        log.error("任务回调服务调用失败: {}", cause.getMessage(), cause);

        return new TaskCallbackClient() {
            @Override
            public String taskCallback(URI uri, String requestData, String openTenantId, String apiTenantId) {
                log.error("任务回调服务降级处理: uri={}, tenantId={}, error={}",
                    uri, openTenantId, cause.getMessage());
                return "{\"code\":500,\"msg\":\"任务回调服务调用失败\"}";
            }
        };
    }
}
