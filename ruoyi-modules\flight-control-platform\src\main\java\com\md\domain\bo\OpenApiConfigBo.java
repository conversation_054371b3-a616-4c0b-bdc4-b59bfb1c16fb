package com.md.domain.bo;

import com.md.domain.po.OpenApiConfig;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 开放端接口管理业务对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OpenApiConfig.class, reverseConvertGenerate = false)
public class OpenApiConfigBo extends BaseEntity {

    /**
     * 接口ID
     */
    private Long id;

    /**
     * 开放路由地址
     */
    @NotBlank(message = "开放路由地址不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 200, message = "开放路由地址长度不能超过200个字符", groups = { AddGroup.class, EditGroup.class })
    private String openAddress;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符", groups = { AddGroup.class, EditGroup.class })
    private String remark;


    /**
     * 配置信息
     */
    private String encryptInfo;

    /**
     * 显示排序
     */
    private String sort;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 租户id
     */
    private String tenantId;
}
