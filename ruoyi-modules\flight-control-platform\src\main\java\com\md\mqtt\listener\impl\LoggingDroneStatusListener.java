package com.md.mqtt.listener.impl;

import com.md.mqtt.listener.DroneStatusListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 日志记录无人机状态变化监听器
 */
@Slf4j
@Component
public class LoggingDroneStatusListener implements DroneStatusListener {
    
    @Override
    public void onDroneOnline(String droneId, String statusData) {
        log.info("无人机上线: droneId={}, statusData={}", droneId, statusData);
    }
    
    @Override
    public void onDroneOffline(String droneId) {
        log.info("无人机离线: droneId={}", droneId);
    }
} 