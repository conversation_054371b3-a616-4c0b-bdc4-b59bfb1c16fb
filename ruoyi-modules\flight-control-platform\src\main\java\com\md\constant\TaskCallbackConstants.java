package com.md.constant;

/**
 * 任务回调常量
 */
public class TaskCallbackConstants {

    /**
     * Redis中存储任务平台编码的键前缀
     */
    public static final String TASK_PLATFORM_CODE_KEY_PREFIX = "task_platform_code:";

    /**
     * Redis中存储任务租户ID的键前缀
     */
    public static final String TASK_TENANT_ID_KEY_PREFIX = "task_tenant_id:";

    /**
     * 任务成功状态码
     */
    public static final Integer CODE_SUCCESS = 200;

    /**
     * 任务失败状态码
     */
    public static final Integer CODE_FAILURE = 500;
}