package com.md.utils;

import com.md.flight.mavlink.model.Mission;
import com.md.flight.mavlink.model.MissionWaypoint;
import io.dronefleet.mavlink.common.MavCmd;
import io.dronefleet.mavlink.common.MavFrame;
import io.dronefleet.mavlink.common.MavMissionType;
import io.dronefleet.mavlink.common.MissionItemInt;
import io.dronefleet.mavlink.util.EnumValue;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 航线任务工具类
 */
@Getter
public class MissionUtils {
    private static final Logger logger = LoggerFactory.getLogger(MissionUtils.class);

    // MAVLink命令常量
    public static final int MAV_CMD_NAV_WAYPOINT = 16;          // 航点
    public static final int MAV_CMD_NAV_LOITER_UNLIM = 17;      // 无限盘旋
    public static final int MAV_CMD_NAV_LOITER_TURNS = 18;      // 指定圈数盘旋
    public static final int MAV_CMD_NAV_LOITER_TIME = 19;       // 指定时间盘旋
    public static final int MAV_CMD_NAV_RETURN_TO_LAUNCH = 20;  // 返航
    public static final int MAV_CMD_NAV_LAND = 21;              // 降落
    public static final int MAV_CMD_NAV_TAKEOFF = 22;           // 起飞
    public static final int MAV_CMD_MISSION_CLEAR_ALL = 45;     // 清除所有任务

    // 坐标系统常量
    public static final int MAV_FRAME_GLOBAL = 0;               // 全球坐标系（绝对高度）
    public static final int MAV_FRAME_GLOBAL_RELATIVE_ALT = 1;  // 全球坐标系（相对高度）
    public static final int MAV_FRAME_LOCAL_NED = 2;            // 局部坐标系

    /**
     * 矩形航线任务参数类
     */
    @Getter
    public static class RectangleMissionParams {
        private final double centerLat;
        private final double centerLon;
        private final double width;
        private final double height;
        private final double altitude;
        private final String name;

        public RectangleMissionParams(double centerLat, double centerLon, double width, double height, double altitude,
            String name) {
            this.centerLat = centerLat;
            this.centerLon = centerLon;
            this.width = width;
            this.height = height;
            this.altitude = altitude;
            this.name = name;
            validate();
        }

        public void validate() {
            if (width <= 0 || height <= 0 || altitude <= 0) {
                throw new IllegalArgumentException("宽度、高度和飞行高度必须大于0");
            }
            if (Math.abs(centerLat) > 90) {
                throw new IllegalArgumentException("纬度值必须在-90到90之间");
            }
            if (Math.abs(centerLon) > 180) {
                throw new IllegalArgumentException("经度值必须在-180到180之间");
            }
        }
    }

    /**
     * 单个航点参数类
     */
    @Getter
    public static class WaypointParams {
        private final double latitude;
        private final double longitude;
        private final float acceptRadius;
        private final float holdTime;
        private final float altitude;

        public WaypointParams(double latitude, double longitude, float acceptRadius, float holdTime) {
            this(latitude, longitude, acceptRadius, holdTime, 0);
        }

        public WaypointParams(double latitude, double longitude, float acceptRadius, float holdTime, float altitude) {
            this.latitude = latitude;
            this.longitude = longitude;
            this.acceptRadius = acceptRadius;
            this.holdTime = holdTime;
            this.altitude = altitude;
            validate();
        }

        public void validate() {
            if (Math.abs(latitude) > 90) {
                throw new IllegalArgumentException("纬度值必须在-90到90之间");
            }
            if (Math.abs(longitude) > 180) {
                throw new IllegalArgumentException("经度值必须在-180到180之间");
            }
            if (acceptRadius < 0) {
                throw new IllegalArgumentException("接受半径必须大于等于0");
            }
            if (holdTime < 0) {
                throw new IllegalArgumentException("悬停时间必须大于等于0");
            }
            if (altitude < 0) {
                throw new IllegalArgumentException("高度必须大于等于0");
            }
        }
    }

    /**
     * 从Map中获取double类型参数
     *
     * @param params 参数Map
     * @param key    参数键名
     * @return double类型参数值
     */
    public static double getDoubleParam(Map<String, Object> params, String key) {
        Object value = params.get(key);
        if (value == null) {
            throw new IllegalArgumentException("缺少必要参数：" + key);
        }
        if (value instanceof Number) {
            return ((Number)value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("参数" + key + "的值无效：" + value);
        }
    }

    /**
     * 创建一个基本的航线任务
     *
     * @param name 航线名称
     * @return 航线任务
     */
    public static Mission createBasicMission(String name) {
        Mission mission = new Mission();
        mission.setName(name);
        mission.setDescription("使用工具创建的航线任务");
        mission.setItems(new ArrayList<>());
        mission.setStatus(Mission.MissionStatus.IDLE);
        return mission;
    }

    /**
     * 添加起飞航点
     *
     * @param mission  航线任务
     * @param altitude 起飞高度（米）
     * @return 航线任务
     */
    public static Mission addTakeoff(Mission mission, float altitude) {
        MissionWaypoint item = new MissionWaypoint();
        item.setCommand(MAV_CMD_NAV_TAKEOFF);
        item.setFrame(MAV_FRAME_GLOBAL_RELATIVE_ALT);
        item.setParam1(0);       // 俯仰角
        item.setParam2(0);       // 空
        item.setParam3(0);       // 空
        item.setParam4(0);       // 偏航角
        item.setLatitude(0);     // 在当前位置起飞，这些值将被忽略
        item.setLongitude(0);
        item.setAltitude(altitude);
        item.setAutoContinue(1);
        item.setDescription("起飞");

        List<MissionWaypoint> items = mission.getItems();
        items.add(item);

        return mission;
    }

    /**
     * 添加航点
     *
     * @param mission      航线任务
     * @param latitude     纬度
     * @param longitude    经度
     * @param altitude     高度（米）
     * @param acceptRadius 接受半径（米）
     * @param holdTime     停留时间（秒）
     * @return 航线任务
     */
    public static Mission addWaypoint(Mission mission, double latitude, double longitude, float altitude,
        float acceptRadius, float holdTime) {
        MissionWaypoint item = new MissionWaypoint();
        item.setCommand(MAV_CMD_NAV_WAYPOINT);
        item.setFrame(MAV_FRAME_GLOBAL_RELATIVE_ALT);
        item.setParam1(holdTime);      // 停留时间
        item.setParam2(acceptRadius);  // 接受半径
        item.setParam3(0);             // 经过方式（0=目标点上方）
        item.setParam4(0);             // 到达点时的偏航角
        item.setLatitude(latitude);
        item.setLongitude(longitude);
        item.setAltitude(altitude);
        item.setAutoContinue(1);
        item.setDescription("航点");

        List<MissionWaypoint> items = mission.getItems();
        items.add(item);

        return mission;
    }

    /**
     * 添加降落航点
     *
     * @param mission   航线任务
     * @param latitude  纬度
     * @param longitude 经度
     * @return 航线任务
     */
    public static Mission addLand(Mission mission, double latitude, double longitude) {
        MissionWaypoint item = new MissionWaypoint();
        item.setCommand(MAV_CMD_NAV_LAND);
        item.setFrame(MAV_FRAME_GLOBAL_RELATIVE_ALT);
        item.setParam1(0);       // 空
        item.setParam2(0);       // 空
        item.setParam3(0);       // 空
        item.setParam4(0);       // 到达点时的偏航角
        item.setLatitude(latitude);
        item.setLongitude(longitude);
        item.setAltitude(0);     // 降落高度
        item.setAutoContinue(1);
        item.setDescription("降落");

        List<MissionWaypoint> items = mission.getItems();
        items.add(item);

        return mission;
    }

    /**
     * 创建一个简单的矩形航线
     *
     * @param name      航线名称
     * @param centerLat 中心点纬度
     * @param centerLon 中心点经度
     * @param width     矩形宽度（米）
     * @param height    矩形高度（米）
     * @param altitude  飞行高度（米）
     * @return 航线任务
     */
    public static Mission createRectangleMission(String name, double centerLat, double centerLon, double width,
        double height, float altitude) {
        logger.info("开始创建矩形航线: name={}, centerLat={}, centerLon={}, width={}, height={}, altitude={}", name,
            centerLat, centerLon, width, height, altitude);

        // 验证参数
        if (Double.isNaN(centerLat) || Double.isNaN(centerLon) || Double.isNaN(width) || Double.isNaN(height) ||
            Float.isNaN(altitude)) {
            logger.error("矩形航线参数无效: 包含NaN值");
            throw new IllegalArgumentException("矩形航线参数无效: 包含NaN值");
        }

        if (centerLat < -90 || centerLat > 90) {
            logger.error("矩形航线参数无效: 纬度超出范围 (-90 到 90): {}", centerLat);
            throw new IllegalArgumentException("矩形航线参数无效: 纬度超出范围 (-90 到 90): " + centerLat);
        }

        if (centerLon < -180 || centerLon > 180) {
            logger.error("矩形航线参数无效: 经度超出范围 (-180 到 180): {}", centerLon);
            throw new IllegalArgumentException("矩形航线参数无效: 经度超出范围 (-180 到 180): " + centerLon);
        }

        if (width <= 0 || height <= 0) {
            logger.error("矩形航线参数无效: 宽度和高度必须大于0: width={}, height={}", width, height);
            throw new IllegalArgumentException(
                "矩形航线参数无效: 宽度和高度必须大于0: width=" + width + ", height=" + height);
        }

        if (altitude <= 0) {
            logger.error("矩形航线参数无效: 高度必须大于0: {}", altitude);
            throw new IllegalArgumentException("矩形航线参数无效: 高度必须大于0: " + altitude);
        }

        Mission mission = createBasicMission(name);

        try {
            // 计算矩形四个角点的坐标
            // 地球半径（米）
            final double EARTH_RADIUS = 6378137.0;

            // 将宽度和高度转换为度数
            double latChange = (height / 2) / EARTH_RADIUS * (180 / Math.PI);
            double lonChange = (width / 2) / (EARTH_RADIUS * Math.cos(centerLat * Math.PI / 180)) * (180 / Math.PI);

            logger.info("计算四个角点的坐标: latChange={}, lonChange={}", latChange, lonChange);

            double lat1 = centerLat + latChange;
            double lon1 = centerLon - lonChange;

            double lat2 = centerLat + latChange;
            double lon2 = centerLon + lonChange;

            double lat3 = centerLat - latChange;
            double lon3 = centerLon + lonChange;

            double lat4 = centerLat - latChange;
            double lon4 = centerLon - lonChange;

            logger.info("角点1: lat={}, lon={}", lat1, lon1);
            logger.info("角点2: lat={}, lon={}", lat2, lon2);
            logger.info("角点3: lat={}, lon={}", lat3, lon3);
            logger.info("角点4: lat={}, lon={}", lat4, lon4);

            // 添加起飞点
            logger.info("添加起飞点，高度: {}", altitude);
            addTakeoff(mission, altitude);

            // 添加四个角点
            logger.info("添加第一个角点");
            addWaypoint(mission, lat1, lon1, altitude, 5, 1);

            logger.info("添加第二个角点");
            addWaypoint(mission, lat2, lon2, altitude, 5, 1);

            logger.info("添加第三个角点");
            addWaypoint(mission, lat3, lon3, altitude, 5, 1);

            logger.info("添加第四个角点");
            addWaypoint(mission, lat4, lon4, altitude, 5, 1);

            // 返回起点
            logger.info("添加返回起点");
            addWaypoint(mission, lat1, lon1, altitude, 5, 1);

            // 降落
            logger.info("添加降落点");
            addLand(mission, lat1, lon1);

            logger.info("矩形航线创建成功，航点数量: {}", mission.getItems().size());

            return mission;
        } catch (Exception e) {
            logger.error("创建矩形航线时发生异常", e);
            throw e;
        }
    }

    /**
     * 创建矩形航线任务
     *
     * @param params 矩形航线参数
     * @return 任务对象
     */
    public static Mission createRectangleMission(RectangleMissionParams params) {
        try {
            return createRectangleMission(params.getName(), params.getCenterLat(), params.getCenterLon(),
                params.getWidth(), params.getHeight(), (float)params.getAltitude());
        } catch (Exception e) {
            throw new RuntimeException("创建矩形航线任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据航点列表创建航线任务
     *
     * @param name            航线名称
     * @param defaultAltitude 默认高度（米），当航点没有指定高度时使用
     * @param waypoints       航点列表
     * @return 航线任务
     */
    public static Mission createWaypointsMission(String name, float defaultAltitude, List<WaypointParams> waypoints) {
        if (waypoints == null || waypoints.isEmpty()) {
            throw new IllegalArgumentException("航点列表不能为空");
        }

        Mission mission = createBasicMission(name);

        // 添加起飞点
        addTakeoff(mission, defaultAltitude);

        // 添加航点
        for (WaypointParams wp : waypoints) {
            // 使用航点的高度，如果航点高度为0则使用默认高度
            float waypointAltitude = wp.getAltitude() > 0 ? wp.getAltitude() : defaultAltitude;

            addWaypoint(mission, wp.getLatitude(), wp.getLongitude(), waypointAltitude, wp.getAcceptRadius(),
                wp.getHoldTime());
        }

        // 添加降落点（在最后一个航点位置）
        WaypointParams lastWp = waypoints.get(waypoints.size() - 1);
        addLand(mission, lastWp.getLatitude(), lastWp.getLongitude());

        return mission;
    }

    /**
     * 将自定义航点转换为MAVLink航点
     *
     * @param item              自定义航点
     * @param seq               序号
     * @param targetSystemId    目标系统ID
     * @param targetComponentId 目标组件ID
     * @return MAVLink航点
     */
    public static MissionItemInt convertToMavlinkMissionItem(MissionWaypoint item, int seq, int targetSystemId,
        int targetComponentId) {
        if (item == null) {
            logger.error("无法转换航点：item为null");
            throw new IllegalArgumentException("航点不能为null");
        }

        logger.debug("开始转换航点: seq={}, command={}, frame={}, lat={}, lon={}, alt={}", seq, item.getCommand(),
            item.getFrame(), item.getLatitude(), item.getLongitude(), item.getAltitude());

        try {
            // 创建帧类型
            MavFrame frameType;
            switch (item.getFrame()) {
                case MAV_FRAME_GLOBAL:
                    frameType = MavFrame.MAV_FRAME_GLOBAL;
                    break;
                case MAV_FRAME_GLOBAL_RELATIVE_ALT:
                    frameType = MavFrame.MAV_FRAME_GLOBAL_RELATIVE_ALT;
                    break;
                case MAV_FRAME_LOCAL_NED:
                    frameType = MavFrame.MAV_FRAME_LOCAL_NED;
                    break;
                default:
                    logger.warn("未知的坐标系类型: {}，使用默认值 MAV_FRAME_GLOBAL_RELATIVE_ALT", item.getFrame());
                    frameType = MavFrame.MAV_FRAME_GLOBAL_RELATIVE_ALT;
            }

            // 创建命令类型
            MavCmd cmdType;
            switch (item.getCommand()) {
                case MAV_CMD_NAV_WAYPOINT:
                    cmdType = MavCmd.MAV_CMD_NAV_WAYPOINT;
                    break;
                case MAV_CMD_NAV_TAKEOFF:
                    cmdType = MavCmd.MAV_CMD_NAV_TAKEOFF;
                    break;
                case MAV_CMD_NAV_LAND:
                    cmdType = MavCmd.MAV_CMD_NAV_LAND;
                    break;
                case MAV_CMD_NAV_RETURN_TO_LAUNCH:
                    cmdType = MavCmd.MAV_CMD_NAV_RETURN_TO_LAUNCH;
                    break;
                default:
                    logger.warn("未知的命令类型: {}，使用默认值 MAV_CMD_NAV_WAYPOINT", item.getCommand());
                    cmdType = MavCmd.MAV_CMD_NAV_WAYPOINT;
            }

            // 检查坐标有效性
            if (Double.isNaN(item.getLatitude()) || Double.isNaN(item.getLongitude()) ||
                Float.isNaN(item.getAltitude())) {
                logger.error("航点坐标含有NaN值: lat={}, lon={}, alt={}", item.getLatitude(), item.getLongitude(),
                    item.getAltitude());
                throw new IllegalArgumentException("航点坐标含有NaN值");
            }

            // 检查坐标范围
            if (item.getLatitude() < -90 || item.getLatitude() > 90) {
                logger.error("航点纬度超出范围 (-90到90): {}", item.getLatitude());
                throw new IllegalArgumentException("航点纬度超出范围: " + item.getLatitude());
            }

            if (item.getLongitude() < -180 || item.getLongitude() > 180) {
                logger.error("航点经度超出范围 (-180到180): {}", item.getLongitude());
                throw new IllegalArgumentException("航点经度超出范围: " + item.getLongitude());
            }

            MissionItemInt missionItemInt =
                MissionItemInt.builder().targetSystem(targetSystemId).targetComponent(targetComponentId).seq(seq)
                    .frame(EnumValue.of(frameType)).command(EnumValue.of(cmdType)).current(item.getCurrent())
                    .autocontinue(item.getAutoContinue()).param1(item.getParam1()).param2(item.getParam2())
                    .param3(item.getParam3()).param4(item.getParam4()).x((int)(item.getLatitude() * 1.0e7))
                    .y((int)(item.getLongitude() * 1.0e7)).z(item.getAltitude())
                    .missionType(EnumValue.of(MavMissionType.MAV_MISSION_TYPE_MISSION)).build();

            logger.debug("航点转换成功: seq={}, x={}, y={}, z={}", seq, missionItemInt.x(), missionItemInt.y(),
                missionItemInt.z());

            return missionItemInt;
        } catch (Exception e) {
            logger.error("转换航点时发生异常: seq={}", seq, e);
            throw new RuntimeException("转换航点时发生异常: " + e.getMessage(), e);
        }
    }
}