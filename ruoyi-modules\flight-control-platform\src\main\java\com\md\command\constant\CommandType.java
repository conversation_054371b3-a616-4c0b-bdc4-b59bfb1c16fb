package com.md.command.constant;

import lombok.Getter;

/**
 * 指令类型枚举
 */
@Getter
public enum CommandType {
    // 航线文件操作
    LIST_MINIO_FILES("列出航线文件", "获取MinIO中可用的航线文件列表", "LIST_MINIO_FILES"),
    GET_MISSION_INFO("获取航线信息", "获取指定航线文件的详细信息", "GET_MISSION_INFO"),

    // 任务控制
    EXECUTE_MINIO_MISSION("开始任务", "启动无人机执行预设任务", "EXECUTE_MISSION"),
    PAUSE_MISSION("暂停任务", "暂停当前正在执行的任务", "PAUSE_MISSION"),
    RESUME_MISSION("恢复任务", "恢复之前暂停的任务", "RESUME_MISSION"),
    STOP_MISSION("停止任务", "立即停止当前任务", "STOP_MISSION"),

    // 飞行控制
    ARM("解锁", "解锁无人机准备起飞", "ARM"),
    DISARM("上锁", "上锁无人机电机", "DISARM"),
    GO_HOME("返航", "命令无人机返回起飞点", "GO_HOME"),
    GET_STATUS("获取状态", "获取无人机当前状态信息", "GET_STATUS"),
    LAND("降落", "执行自动降落操作", "LAND"),
    TAKEOFF("起飞", "执行自动起飞操作", "TAKEOFF"),

    // 虚拟摇杆控制
    VIRTUAL_STICK_START("开启虚拟摇杆", "启用虚拟摇杆控制模式", "VIRTUAL_STICK_START"),
    VIRTUAL_STICK_STOP("停止虚拟摇杆", "停用虚拟摇杆控制模式", "VIRTUAL_STICK_STOP"),
    VIRTUAL_STICK_CONTROL("虚拟摇杆控制", "发送虚拟摇杆控制指令", "VIRTUAL_STICK_CONTROL"),
    VIRTUAL_STICK_TAKEOFF("虚拟摇杆起飞", "使用虚拟摇杆模式起飞", "VIRTUAL_STICK_TAKEOFF"),
    VIRTUAL_STICK_LAND("虚拟摇杆降落", "使用虚拟摇杆模式降落", "VIRTUAL_STICK_LAND"),
    VIRTUAL_STICK_GO_HOME("虚拟摇杆返航", "使用虚拟摇杆模式返航", "VIRTUAL_STICK_GO_HOME"),
    VIRTUAL_STICK_EMERGENCY_STOP("虚拟摇杆紧急停止", "虚拟摇杆模式下紧急停止", "VIRTUAL_STICK_EMERGENCY_STOP"),
    VIRTUAL_STICK_NAVIGATE_TO_POINT("虚拟摇杆导航至点", "虚拟摇杆模式下导航到指定坐标点", "VIRTUAL_STICK_NAVIGATE_TO_POINT"),

    // 云台控制
    GIMBAL_CONTROL("云台控制", "控制无人机云台的俯仰、横滚和偏航角度", "GIMBAL_CONTROL"),

    /**
     * 相机控制
     */
    CAMERA_CONTROL("相机控制", "控制无人机相机拍照和录像功能", "CAMERA_CONTROL"),

    // 相机控制
    CAMERA_TAKE_PHOTO("拍照", "拍摄一张照片", "CAMERA_TAKE_PHOTO"),
    CAMERA_START_SHOOTING("开始连续拍照", "以指定的时间间隔连续拍摄照片", "CAMERA_START_SHOOTING"),
    CAMERA_STOP_SHOOTING("停止连续拍照", "停止连续拍摄模式", "CAMERA_STOP_SHOOTING"),
    CAMERA_START_RECORDING("开始录像", "开始视频录制", "CAMERA_START_RECORDING"),
    CAMERA_STOP_RECORDING("停止录像", "停止视频录制", "CAMERA_STOP_RECORDING"),

    // 直播控制
    START_LIVE_STREAM("开始直播", "开始无人机直播推流", "START_LIVE_STREAM"),
    STOP_LIVE_STREAM("停止直播", "停止无人机直播推流", "STOP_LIVE_STREAM"),
    LIVE_STREAM_CONFIG("配置直播参数", "配置无人机直播参数", "LIVE_STREAM_CONFIG"),

    // Agora直播控制
    AGORA_CONFIG("配置Agora参数", "配置Agora直播的参数设置", "AGORA_CONFIG"),
    AGORA_START("开始Agora直播", "启动Agora直播推流", "AGORA_START"),
    AGORA_STOP("停止Agora直播", "停止Agora直播推流", "AGORA_STOP");

    private final String description;
    private final String detail;
    private final String code;

    CommandType(String description, String detail, String code) {
        this.description = description;
        this.detail = detail;
        this.code = code;
    }

    @Override
    public String toString() {
        return this.description;
    }
}
