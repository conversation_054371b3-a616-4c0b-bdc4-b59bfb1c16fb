package com.md.domain.bo;

import com.md.domain.po.FlightTaskPointAction;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 航点动作业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = FlightTaskPointAction.class, reverseConvertGenerate = false)
public class FlightTaskPointActionBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 动作ID
     */
    @NotBlank(message = "动作ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 航点ID
     */
    @NotBlank(message = "航点ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String pointId;

    /**
     * 动作类型(1:拍照 2:录像 3:变焦 4:云台角度)
     */
    @NotNull(message = "动作类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer actionType;

    /**
     * 动作参数1
     */
    private BigDecimal actionParam1;

    /**
     * 动作参数2
     */
    private BigDecimal actionParam2;

    /**
     * 动作参数3
     */
    private BigDecimal actionParam3;

    /**
     * 动作参数4
     */
    private BigDecimal actionParam4;

    /**
     * 动作参数5
     */
    private BigDecimal actionParam5;
} 