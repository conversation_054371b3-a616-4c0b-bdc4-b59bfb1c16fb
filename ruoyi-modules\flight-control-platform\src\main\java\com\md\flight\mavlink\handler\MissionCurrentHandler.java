package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.MissionCurrent;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务当前项处理器
 * 处理MAVLink任务当前项消息，用于追踪任务执行过程中的当前航点
 */
@Slf4j
public class MissionCurrentHandler extends AbstractMavlinkHandler<MissionCurrent> {

    public MissionCurrentHandler(MavlinkCoreService coreService) {
        super(coreService);
    }
    
    @Override
    protected void doHandle(String droneId, MissionCurrent message) {
        try {
            log.info("收到无人机[{}]任务当前项消息: seq={}", droneId, message.seq());
            coreService.updateCurrentMission(droneId, message);
        } catch (Exception e) {
            log.error("处理无人机[{}]任务当前项消息失败: seq={}", droneId, message.seq(), e);
        }
    }

    @Override
    public Class<MissionCurrent> getMessageType() {
        return MissionCurrent.class;
    }
}