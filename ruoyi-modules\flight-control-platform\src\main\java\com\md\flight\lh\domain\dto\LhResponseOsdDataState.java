package com.md.flight.lh.domain.dto;

import lombok.Data;

/**
 * 返航点位置信息实体类
 */
@Data
public class LhResponseOsdDataState {

    /** 安全功能是否启用，0 表示关闭，1 表示开启 */
    private Integer safe_enabled;

    /** RTK 功能是否启用，0 表示关闭，1 表示开启 */
    private Integer rtk_enabled;

    /** RTK 是否连接，0 表示未连接，1 表示连接 */
    private Integer rtk_connected;

    /** 遥控器是否连接，0 表示未连接，1 表示连接 */
    private Integer rc_connected;

    /** 避障功能是否启用，0 表示关闭，1 表示开启 */
    private Integer obs_enabled;

    /** 加速计校准状态，0 表示未校准，1 表示已校准 */
    private Integer accel_cal;

    /** 陀螺仪校准状态，0 表示未校准，1 表示已校准 */
    private Integer gyro_cal;

    /** 水平校准状态，0 表示未校准，1 表示已校准 */
    private Integer hor_cal;

    /** 磁力计校准状态，0 表示未校准，1 表示已校准 */
    private Integer mag_cal;

    /** FPV 视频流是否开启，0 表示关闭，1 表示开启 */
    private Integer fpv_live;

    /** 主视频流是否开启，0 表示关闭，1 表示开启 */
    private Integer stream_live;
}

