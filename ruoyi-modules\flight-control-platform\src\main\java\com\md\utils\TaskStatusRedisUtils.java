package com.md.utils;

import com.md.flight.execution.context.TaskExecutionInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务状态 Redis 分布式存储工具类
 * 为所有无人机类型提供统一的任务状态管理
 * 使用BusinessDistributedLockUtils实现安全的分布式锁
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TaskStatusRedisUtils {

    // 使用统一的业务分布式锁工具类
    private final BusinessDistributedLockUtils lockUtils;

    // Redis 键前缀常量
    private static final String TASK_INFO_KEY_PREFIX = "task:info:";
    private static final String DRONE_TASK_KEY_PREFIX = "task:drone:";
    private static final String DRONE_EXECUTION_KEY_PREFIX = "task:execution:";

    // 过期时间常量
    private static final Duration TASK_INFO_EXPIRE = Duration.ofHours(3);
    private static final Duration DRONE_TASK_EXPIRE = Duration.ofHours(3);
    private static final Duration EXECUTION_STATE_EXPIRE = Duration.ofHours(1);

    // 无人机类型前缀映射
    private static final Map<String, String> DRONE_TYPE_PREFIX_MAP =
        Map.of("LH", "lh", "PX4", "px4", "MAVLINK", "mavlink", "DJI", "dji");

    /**
     * 生成 Redis 键
     */
    private String getTaskInfoKey(String droneType, String taskId) {
        String prefix = DRONE_TYPE_PREFIX_MAP.getOrDefault(droneType.toUpperCase(), droneType.toLowerCase());
        return TASK_INFO_KEY_PREFIX + prefix + ":" + taskId;
    }

    private String getDroneTaskKey(String droneType, String droneId) {
        String prefix = DRONE_TYPE_PREFIX_MAP.getOrDefault(droneType.toUpperCase(), droneType.toLowerCase());
        return DRONE_TASK_KEY_PREFIX + prefix + ":" + droneId;
    }

    private String getDroneExecutionKey(String droneType, String droneId) {
        String prefix = DRONE_TYPE_PREFIX_MAP.getOrDefault(droneType.toUpperCase(), droneType.toLowerCase());
        return DRONE_EXECUTION_KEY_PREFIX + prefix + ":" + droneId;
    }

    /**
     * 任务信息管理
     */
    public void setTaskInfo(String droneType, String taskId, TaskExecutionInfo taskInfo) {
        String key = getTaskInfoKey(droneType, taskId);
        TenantHelper.ignore(() -> RedisUtils.setCacheObject(key, taskInfo, TASK_INFO_EXPIRE));
        log.debug("已设置{}任务信息: taskId={}", droneType, taskId);
    }

    public TaskExecutionInfo getTaskInfo(String droneType, String taskId) {
        String key = getTaskInfoKey(droneType, taskId);
        return TenantHelper.ignore(() -> RedisUtils.getCacheObject(key));
    }

    public void removeTaskInfo(String droneType, String taskId) {
        String key = getTaskInfoKey(droneType, taskId);
        TenantHelper.ignore(() -> RedisUtils.deleteObject(key));
    }

    /**
     * 无人机任务映射管理
     */
    public void setDroneTask(String droneType, String droneId, String taskId) {
        String key = getDroneTaskKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.setCacheObject(key, taskId, DRONE_TASK_EXPIRE));
    }

    public String getDroneTask(String droneType, String droneId) {
        String key = getDroneTaskKey(droneType, droneId);
        return TenantHelper.ignore(() -> RedisUtils.getCacheObject(key));
    }

    public void removeDroneTask(String droneType, String droneId) {
        String key = getDroneTaskKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.deleteObject(key));
    }

    /**
     * 无人机执行状态管理
     */
    public void setDroneExecution(String droneType, String droneId, Boolean executing) {
        String key = getDroneExecutionKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.setCacheObject(key, executing, EXECUTION_STATE_EXPIRE));
    }

    public Boolean isDroneExecuting(String droneType, String droneId) {
        String key = getDroneExecutionKey(droneType, droneId);
        Boolean result = TenantHelper.ignore(() -> RedisUtils.getCacheObject(key));
        return Boolean.TRUE.equals(result);
    }

    public void removeDroneExecution(String droneType, String droneId) {
        String key = getDroneExecutionKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.deleteObject(key));
    }

    /**
     * 安全的任务状态更新（使用Redisson RLock）
     *
     * @param droneType    无人机类型
     * @param taskId       任务ID
     * @param updateAction 更新操作的回调函数
     * @return 是否成功更新
     */
    public boolean tryUpdateTaskStatus(String droneType, String taskId, Runnable updateAction) {
        // 使用统一的业务分布式锁工具类
        return lockUtils.executeTaskStatusOperation(droneType, taskId, () -> {
            updateAction.run();
            log.debug("成功更新{}任务状态: taskId={}", droneType, taskId);
        });
    }

    /**
     * 批量清理操作
     */
    public void cleanupTaskData(String droneType, String droneId) {
        String taskId = getDroneTask(droneType, droneId);
        if (taskId != null) {
            removeTaskInfo(droneType, taskId);
        }
        removeDroneTask(droneType, droneId);
        removeDroneExecution(droneType, droneId);
        log.info("已清理{}无人机任务数据: droneId={}", droneType, droneId);
    }

    /**
     * 获取指定类型的所有活动任务
     */
    public Map<String, String> getAllActiveTasks(String droneType) {
        Map<String, String> result = new HashMap<>();
        try {
            String prefix = DRONE_TYPE_PREFIX_MAP.getOrDefault(droneType.toUpperCase(), droneType.toLowerCase());
            String pattern = DRONE_TASK_KEY_PREFIX + prefix + ":*";
            Collection<String> keys = TenantHelper.ignore(() -> RedisUtils.keys(pattern));

            String keyPrefix = DRONE_TASK_KEY_PREFIX + prefix + ":";
            for (String key : keys) {
                String taskId = TenantHelper.ignore(() -> RedisUtils.getCacheObject(key));
                if (taskId != null) {
                    String droneId = key.substring(keyPrefix.length());
                    result.put(droneId, taskId);
                }
            }
        } catch (Exception e) {
            log.error("获取{}类型活动任务列表失败", droneType, e);
        }
        return result;
    }
}
