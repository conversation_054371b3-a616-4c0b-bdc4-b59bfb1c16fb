package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.GpsRawInt;
import lombok.extern.slf4j.Slf4j;

/**
 * GPS信息处理器
 */
@Slf4j
public class GpsHandler extends AbstractMavlinkHandler<GpsRawInt> {

    public GpsHandler(MavlinkCoreService coreService) {
        super(coreService);
    }

    @Override
    protected void doHandle(String droneId, GpsRawInt message) {
        // 保存最新的GPS信息
        coreService.setLastGpsInfo(droneId, message);

        // 检查是否是RTK模式（fixType >= 4）
        int fixType = message.fixType().value();
        if (fixType >= 4) { // 4表示RTK浮点解，6表示RTK固定解
            // 在RTK模式下，使用GPS高度作为RTK海拔高度
            // 直接获取高度信息
            double rtkAltitude = message.alt() / 1000.0; // 转换为米（从毫米）
            
            // 如果高度值有效（不为0或其他异常值），则设置RTK高度
            if (rtkAltitude != 0) {
                coreService.setRtkAltitude(droneId, rtkAltitude);
                
                log.info("无人机[{}]RTK信息: fixType={}, satellites={}, hdop={}, rtkAltitude={}m",
                    droneId,
                    message.fixType().entry(),
                    message.satellitesVisible(),
                    message.eph() / 100f,
                    rtkAltitude);
            } else {
                // 尝试使用altEllipsoid作为备选
                double altEllipsoid = message.altEllipsoid() / 1000.0; // 转换为米
                if (altEllipsoid != 0) {
                    coreService.setRtkAltitude(droneId, altEllipsoid);
                    
                    log.info("无人机[{}]RTK信息: fixType={}, satellites={}, hdop={}, rtkAltitude(椭球高)={}m",
                        droneId,
                        message.fixType().entry(),
                        message.satellitesVisible(),
                        message.eph() / 100f,
                        altEllipsoid);
                } else {
                    log.info("无人机[{}]RTK信息: fixType={}, satellites={}, hdop={}, 无法获取有效的RTK高度",
                        droneId,
                        message.fixType().entry(),
                        message.satellitesVisible(),
                        message.eph() / 100f);
                }
            }
        } else {
            log.info("无人机[{}]GPS信息: fixType={}, satellites={}, hdop={}",
                droneId,
                message.fixType().entry(),
                message.satellitesVisible(),
                message.eph() / 100f);
        }
    }

    @Override
    public Class<GpsRawInt> getMessageType() {
        return GpsRawInt.class;
    }
}