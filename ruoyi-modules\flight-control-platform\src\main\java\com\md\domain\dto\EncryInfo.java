package com.md.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 密钥配置信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EncryInfo implements Serializable {

    /**
     * 加密类型
     */
    private String encryptType;

    /**
     * 加密密钥
     */
    private String encryptKey;

    /**
     * 当前租户是否使用
     */
    private Boolean useFlag;


}
