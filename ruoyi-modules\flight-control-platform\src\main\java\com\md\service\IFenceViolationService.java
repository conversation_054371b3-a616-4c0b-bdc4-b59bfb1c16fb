package com.md.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.md.domain.bo.FenceViolationBo;
import com.md.domain.po.FenceViolationLog;
import com.md.domain.vo.FenceViolationVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * 围栏违规记录服务接口
 */
public interface IFenceViolationService extends IService<FenceViolationLog> {
    /**
     * 查询违规记录列表（不分页）
     *
     * @param bo 违规记录查询条件
     * @return 违规记录集合
     */
    List<FenceViolationVo> selectViolationList(FenceViolationBo bo);

    /**
     * 查询违规记录列表（分页）
     *
     * @param bo        违规记录查询条件
     * @param pageQuery 分页参数
     * @return 分页违规记录集合
     */
    TableDataInfo<FenceViolationVo> selectViolationList(FenceViolationBo bo, PageQuery pageQuery);

    /**
     * 查询违规记录详情
     *
     * @param id 违规记录ID
     * @return 违规记录详情
     */
    FenceViolationVo selectViolationById(String id);

    /**
     * 按任务ID查询违规记录
     *
     * @param taskId    任务ID
     * @param pageQuery 分页参数
     * @return 分页违规记录集合
     */
    TableDataInfo<FenceViolationVo> selectViolationByTaskId(String taskId, PageQuery pageQuery);

    /**
     * 按无人机编码查询违规记录
     *
     * @param uavCode   无人机编码
     * @param pageQuery 分页参数
     * @return 分页违规记录集合
     */
    TableDataInfo<FenceViolationVo> selectViolationByUavCode(String uavCode, PageQuery pageQuery);

    /**
     * 按围栏ID查询违规记录
     *
     * @param fenceId   围栏ID
     * @param pageQuery 分页参数
     * @return 分页违规记录集合
     */
    TableDataInfo<FenceViolationVo> selectViolationByFenceId(String fenceId, PageQuery pageQuery);

    /**
     * 删除违规记录
     *
     * @param ids 需要删除的违规记录ID数组
     * @return 结果
     */
    int deleteViolationByIds(String[] ids);
}