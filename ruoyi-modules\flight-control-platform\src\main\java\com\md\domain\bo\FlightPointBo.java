package com.md.domain.bo;

import com.md.domain.po.FlightPoint;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.util.List;

/**
 * 航点业务对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FlightPoint.class, reverseConvertGenerate = false)
public class FlightPointBo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 所属航线ID
     */
    @NotBlank(message = "所属航线ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String lineId;

    /**
     * 航点序号
     */
    @NotNull(message = "航点序号不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long pointIndex;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal latitude;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal longitude;

    /**
     * 高度(m)
     */
    @NotNull(message = "高度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal altitude;

    /**
     * 飞行速度(m/s)
     */
    private int speed;

    /**
     * 航点动作列表
     */
    private List<FlightPointActionBo> actions;

    /**
     * 是否为备降点(0:否 1:是)
     */
    private Integer isEmergencyLandingPoint;
}