package com.md.mqtt.processor;

import com.md.websocket.converter.UnifiedMessageConverter;
import com.md.websocket.handler.DroneWebSocketHandler;
import com.md.websocket.manager.DroneTopicManager;
import com.md.websocket.message.DroneMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 统一的WebSocket消息处理器 处理所有类型的无人机消息并转发到WebSocket
 */
@Slf4j
@Component
public class DroneWebSocketMessageProcessor extends AbstractMqttMessageProcessor {

    @Autowired
    private UnifiedMessageConverter messageConverter;

    @Autowired
    private DroneWebSocketHandler webSocketHandler;

    @Autowired
    private DroneTopicManager topicManager;

    @Override
    protected String getTopicPattern() {
        // 将DroneTopicManager中定义的WebSocket推送主题转换为正则表达式
        List<String> topics = topicManager.getWebSocketTopics();
        String regexPattern = topics.stream().map(topic -> {
            return topic.replace("+", "[^/]+");
        }).collect(Collectors.joining("|"));

        log.debug("WebSocket处理器主题模式: {}", regexPattern);
        return regexPattern;
    }

    @Override
    protected void doProcessMessage(String topic, String payload) {
        // 检查是否需要WebSocket推送
        if (!topicManager.shouldPushToWebSocket(topic)) {
            log.debug("主题不需要WebSocket推送，跳过处理: topic={}", topic);
            return;
        }

        try {
            // 使用统一的转换方法
            DroneMessage message = messageConverter.convertByTopic(topic, payload);
            if (message != null) {
                // 推送到WebSocket
                webSocketHandler.broadcastMessage(message);
                log.debug("WebSocket推送成功: topic={}, droneId={}, manufacturer={}, type={}", topic,
                    message.getDroneId(), message.getManufacturer(), message.getType());
            }
        } catch (Exception e) {
            log.error("WebSocket消息推送失败: topic={}, error={}", topic, e.getMessage(), e);
            if (log.isDebugEnabled()) {
                log.debug("WebSocket推送失败的消息内容: payload={}", payload, e);
            }
        }
    }
}