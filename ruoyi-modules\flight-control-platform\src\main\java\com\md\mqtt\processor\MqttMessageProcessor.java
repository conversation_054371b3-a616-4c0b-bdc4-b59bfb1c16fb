package com.md.mqtt.processor;

/**
 * MQTT消息处理器接口
 * 定义MQTT消息处理的标准方法
 */
public interface MqttMessageProcessor {

    /**
     * 判断处理器是否可以处理指定主题的消息
     *
     * @param topic MQTT消息主题
     * @return true-可以处理该主题，false-不能处理该主题
     */
    boolean canProcess(String topic);

    /**
     * 处理MQTT消息
     *
     * @param topic   MQTT消息主题
     * @param payload MQTT消息内容
     */
    void processMessage(String topic, String payload);
}