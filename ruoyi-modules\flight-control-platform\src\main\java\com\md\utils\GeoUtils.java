package com.md.utils;

import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.geom.LinearRing;
import com.md.domain.po.FenceVertex;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 地理位置计算工具类
 */
public class GeoUtils {
    /**
     * 地球半径（米）
     */
    private static final int EARTH_RADIUS = 6371000;

    /**
     * 每度对应的大约距离（米）
     */
    private static final double METERS_PER_DEGREE = 111000.0;

    /**
     * JTS几何工厂
     */
    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();

    /**
     * 使用Haversine公式计算两点之间的距离
     *
     * @param lat1 起点纬度
     * @param lon1 起点经度
     * @param lat2 终点纬度
     * @param lon2 终点经度
     * @return 两点之间的距离（米）
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2) +
            Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) * Math.sin(lonDistance / 2) *
                Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return EARTH_RADIUS * c;
    }

    /**
     * 使用Haversine公式计算两点之间的距离（BigDecimal版本）
     *
     * @param lat1 起点纬度
     * @param lon1 起点经度
     * @param lat2 终点纬度
     * @param lon2 终点经度
     * @return 两点之间的距离（米）
     */
    public static BigDecimal calculateDistance(BigDecimal lat1, BigDecimal lon1, BigDecimal lat2, BigDecimal lon2) {
        return BigDecimal.valueOf(
                calculateDistance(lat1.doubleValue(), lon1.doubleValue(), lat2.doubleValue(), lon2.doubleValue()))
            .setScale(2, RoundingMode.HALF_UP);  // 保留2位小数
    }

    /**
     * 将度数转换为弧度
     *
     * @param degree 度数
     * @return 弧度
     */
    public static double toRadians(double degree) {
        return degree * Math.PI / 180;
    }

    /**
     * 将弧度转换为度数
     *
     * @param radian 弧度
     * @return 度数
     */
    public static double toDegrees(double radian) {
        return radian * 180 / Math.PI;
    }

    /**
     * 创建JTS点对象
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return JTS点对象
     */
    public static Point createPoint(double longitude, double latitude) {
        return GEOMETRY_FACTORY.createPoint(new Coordinate(longitude, latitude));
    }

    /**
     * 创建圆形区域（使用32边形近似）
     *
     * @param centerLongitude 中心点经度
     * @param centerLatitude  中心点纬度
     * @param radiusMeters    半径（米）
     * @return 圆形区域的多边形表示
     */
    public static Polygon createCircle(double centerLongitude, double centerLatitude, double radiusMeters) {
        // 将半径从米转换为度（近似）
        double radiusDegrees = radiusMeters / METERS_PER_DEGREE;

        // 创建圆心点
        Point center = createPoint(centerLongitude, centerLatitude);

        // 使用buffer创建圆形（32边形近似）
        return (Polygon)center.buffer(radiusDegrees, 32);
    }

    /**
     * 计算点到线段的最短距离
     *
     * @param pointLat 点的纬度
     * @param pointLon 点的经度
     * @param lineLat1 线段起点纬度
     * @param lineLon1 线段起点经度
     * @param lineLat2 线段终点纬度
     * @param lineLon2 线段终点经度
     * @return 最短距离（米）
     */
    public static double calculateDistanceToLine(double pointLat, double pointLon, double lineLat1, double lineLon1,
        double lineLat2, double lineLon2) {
        // 计算线段两端点到目标点的距离
        double d1 = calculateDistance(pointLat, pointLon, lineLat1, lineLon1);
        double d2 = calculateDistance(pointLat, pointLon, lineLat2, lineLon2);

        // 计算线段长度
        double lineLength = calculateDistance(lineLat1, lineLon1, lineLat2, lineLon2);

        // 如果线段长度为0，返回到端点的距离
        if (lineLength == 0) {
            return Math.min(d1, d2);
        }

        // 计算点到线段的投影点
        double t = ((pointLon - lineLon1) * (lineLon2 - lineLon1) + (pointLat - lineLat1) * (lineLat2 - lineLat1)) /
            (Math.pow(lineLon2 - lineLon1, 2) + Math.pow(lineLat2 - lineLat1, 2));

        // 如果投影点在线段外，返回到端点的距离
        if (t < 0)
            return d1;
        if (t > 1)
            return d2;

        // 计算投影点坐标
        double projLon = lineLon1 + t * (lineLon2 - lineLon1);
        double projLat = lineLat1 + t * (lineLat2 - lineLat1);

        // 返回点到投影点的距离
        return calculateDistance(pointLat, pointLon, projLat, projLon);
    }

    /**
     * 计算方位角（正北为0度，顺时针增加）
     *
     * @param lat1 起点纬度
     * @param lon1 起点经度
     * @param lat2 终点纬度
     * @param lon2 终点经度
     * @return 方位角（度）
     */
    public static double calculateBearing(double lat1, double lon1, double lat2, double lon2) {
        double dLon = Math.toRadians(lon2 - lon1);
        lat1 = Math.toRadians(lat1);
        lat2 = Math.toRadians(lat2);

        double y = Math.sin(dLon) * Math.cos(lat2);
        double x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLon);
        double bearing = Math.toDegrees(Math.atan2(y, x));

        // 转换为0-360度
        return (bearing + 360) % 360;
    }

    /**
     * 计算给定点在指定距离和方位角上的新位置
     *
     * @param lat      起点纬度
     * @param lon      起点经度
     * @param distance 距离（米）
     * @param bearing  方位角（度）
     * @return 新位置的经纬度数组 [纬度, 经度]
     */
    public static double[] calculateDestinationPoint(double lat, double lon, double distance, double bearing) {
        double angularDistance = distance / EARTH_RADIUS;
        double bearingRad = Math.toRadians(bearing);
        lat = Math.toRadians(lat);
        lon = Math.toRadians(lon);

        double newLat = Math.asin(Math.sin(lat) * Math.cos(angularDistance) +
            Math.cos(lat) * Math.sin(angularDistance) * Math.cos(bearingRad));

        double newLon = lon + Math.atan2(Math.sin(bearingRad) * Math.sin(angularDistance) * Math.cos(lat),
            Math.cos(angularDistance) - Math.sin(lat) * Math.sin(newLat));

        // 转换回度数
        return new double[] {Math.toDegrees(newLat), Math.toDegrees(newLon)};
    }

    /**
     * 从顶点列表创建JTS多边形对象
     *
     * @param vertices 顶点列表
     * @return JTS多边形对象,如果顶点数量不足则返回null
     */
    public static Polygon createPolygonFromVertices(List<FenceVertex> vertices) {
        if (vertices == null || vertices.size() < 3) {
            return null;
        }

        // 按序号排序
        List<FenceVertex> sortedVertices = vertices.stream()
            .sorted((v1, v2) -> v1.getSequence().compareTo(v2.getSequence()))
            .collect(Collectors.toList());

        // 创建坐标数组，确保首尾相连
        Coordinate[] coordinates = new Coordinate[sortedVertices.size() + 1];
        for (int i = 0; i < sortedVertices.size(); i++) {
            FenceVertex vertex = sortedVertices.get(i);
            coordinates[i] = new Coordinate(vertex.getLongitude().doubleValue(), vertex.getLatitude().doubleValue());
        }
        // 闭合多边形
        coordinates[coordinates.length - 1] = coordinates[0];

        // 创建多边形
        LinearRing shell = GEOMETRY_FACTORY.createLinearRing(coordinates);
        return GEOMETRY_FACTORY.createPolygon(shell);
    }
}