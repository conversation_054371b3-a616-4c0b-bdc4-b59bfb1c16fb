package com.md.flight.mavlink.service.impl;

import com.alibaba.fastjson2.JSON;
import com.md.constant.MqttConstants;
import com.md.flight.mavlink.config.MavlinkConfig;
import com.md.flight.mavlink.handler.AbstractMavlinkHandler;
import com.md.flight.mavlink.handler.AttitudeHandler;
import com.md.flight.mavlink.handler.BatteryHandler;
import com.md.flight.mavlink.handler.GpsHandler;
import com.md.flight.mavlink.handler.HeartbeatHandler;
import com.md.flight.mavlink.handler.MissionAckHandler;
import com.md.flight.mavlink.handler.MissionCountHandler;
import com.md.flight.mavlink.handler.MissionCurrentHandler;
import com.md.flight.mavlink.handler.MissionItemIntHandler;
import com.md.flight.mavlink.handler.MissionRequestHandler;
import com.md.flight.mavlink.handler.MissionRequestIntHandler;
import com.md.flight.mavlink.handler.PositionHandler;
import com.md.flight.mavlink.handler.VfrHudHandler;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.flight.mavlink.model.Mission;
import com.md.flight.mavlink.model.MissionWaypoint;
import com.md.utils.MissionUtils;
import com.md.mqtt.dispatcher.MqttMessageHandler;

import io.dronefleet.mavlink.MavlinkConnection;
import io.dronefleet.mavlink.MavlinkMessage;
import io.dronefleet.mavlink.common.Attitude;
import io.dronefleet.mavlink.common.BatteryStatus;
import io.dronefleet.mavlink.common.CommandLong;
import io.dronefleet.mavlink.common.GlobalPositionInt;
import io.dronefleet.mavlink.common.GpsRawInt;
import io.dronefleet.mavlink.common.MavCmd;
import io.dronefleet.mavlink.common.MavFrame;
import io.dronefleet.mavlink.common.MavMissionResult;
import io.dronefleet.mavlink.common.MavMissionType;
import io.dronefleet.mavlink.common.MissionAck;
import io.dronefleet.mavlink.common.MissionClearAll;
import io.dronefleet.mavlink.common.MissionCount;
import io.dronefleet.mavlink.common.MissionCurrent;
import io.dronefleet.mavlink.common.MissionItemInt;
import io.dronefleet.mavlink.common.MissionRequest;
import io.dronefleet.mavlink.common.MissionRequestInt;
import io.dronefleet.mavlink.common.MissionRequestList;
import io.dronefleet.mavlink.common.PositionTargetTypemask;
import io.dronefleet.mavlink.common.RequestDataStream;
import io.dronefleet.mavlink.common.SetPositionTargetLocalNed;
import io.dronefleet.mavlink.common.VfrHud;
import io.dronefleet.mavlink.minimal.Heartbeat;
import io.dronefleet.mavlink.minimal.MavAutopilot;
import io.dronefleet.mavlink.minimal.MavState;
import io.dronefleet.mavlink.minimal.MavType;
import io.dronefleet.mavlink.util.EnumValue;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.Socket;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static cn.hutool.core.thread.ThreadUtil.sleep;

/**
 * MAVLink核心服务
 * 提供底层功能实现，被其他服务组合使用
 */
@Slf4j
@Service
public class MavlinkCoreService {

    @Autowired
    private MavlinkConfig config;

    @Autowired
    private MqttMessageHandler mqttMessageHandler;

    @Value("${mqtt.redis.expire-seconds:20}")  // 默认20秒，与大疆配置一致
    private int expireSeconds;

    // 多连接支持 - 替换单一连接变量
    private final Map<String, Socket> socketMap = new ConcurrentHashMap<>();
    private final Map<String, MavlinkConnection> connectionMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> runningMap = new ConcurrentHashMap<>();

    // 共享线程池
    private ExecutorService executorService;
    private ScheduledExecutorService scheduledExecutorService;

    // 状态数据 - 按无人机ID分组
    private final Map<String, Heartbeat> heartbeatMap = new ConcurrentHashMap<>();
    private final Map<String, GlobalPositionInt> positionMap = new ConcurrentHashMap<>();
    private final Map<String, BatteryStatus> batteryStatusMap = new ConcurrentHashMap<>();
    private final Map<String, GpsRawInt> gpsInfoMap = new ConcurrentHashMap<>();
    private final Map<String, Attitude> attitudeMap = new ConcurrentHashMap<>();
    private final Map<String, VfrHud> vfrHudMap = new ConcurrentHashMap<>();

    // 航距计算相关 - 按无人机ID分组
    private final Map<String, Double> flightDistanceMap = new ConcurrentHashMap<>();
    private final Map<String, GlobalPositionInt> lastPositionMap = new ConcurrentHashMap<>();

    // RTK相关 - 按无人机ID分组
    private final Map<String, Double> rtkAltitudeMap = new ConcurrentHashMap<>();

    private final Map<Class<?>, AbstractMavlinkHandler<?>> handlerMap = new ConcurrentHashMap<>();

    // 错误计数相关 - 按无人机ID分组
    private final Map<String, Integer> connectionErrorCountMap = new ConcurrentHashMap<>();
    private final Map<String, Long> lastErrorTimeMap = new ConcurrentHashMap<>();
    private static final int ERROR_THRESHOLD = 20; // 20次错误阈值
    private static final long ERROR_TIME_WINDOW = 10000; // 10秒时间窗口

    // 重连相关 - 按无人机ID分组
    private final Map<String, String> lastHostMap = new ConcurrentHashMap<>();
    private final Map<String, Integer> lastPortMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> autoReconnectingMap = new ConcurrentHashMap<>();
    private static final int MAX_RECONNECT_ATTEMPTS = 3;
    private static final long RECONNECT_DELAY = 5000; // 5秒重连延迟

    // 任务相关 - 按无人机ID分组
    private final Map<String, Mission> currentMissionMap = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> missionSequenceMap = new ConcurrentHashMap<>();
    private final Map<String, Map<Integer, CompletableFuture<MissionWaypoint>>> missionItemFuturesMap =
        new ConcurrentHashMap<>();
    private final Map<String, Map<Integer, CompletableFuture<MissionAck>>> missionAckFuturesMap =
        new ConcurrentHashMap<>();
    private final Map<String, MissionAck> lastMissionAckMap = new ConcurrentHashMap<>();
    private final Map<String, CompletableFuture<List<MissionWaypoint>>> currentDownloadFutureMap =
        new ConcurrentHashMap<>();
    private final Map<String, List<MissionWaypoint>> downloadedItemsMap = new ConcurrentHashMap<>();

    // MAVLink mode flags
    private static final int MAV_MODE_FLAG_SAFETY_ARMED = 128;          // 0b10000000
    private static final int MAV_MODE_FLAG_MANUAL_INPUT_ENABLED = 64;   // 0b01000000
    private static final int MAV_MODE_FLAG_AUTO_ENABLED = 4;           // 0b00000100

    // MAVLink command values
    private static final int MAV_CMD_MISSION_CLEAR_ALL = 45; // 添加缺失的常量定义

    // 设置速度命令
    private static final int MAV_CMD_DO_CHANGE_SPEED = 178;  // 速度变更命令

    // 在类的字段声明部分添加以下映射表
    private final Map<String, ScheduledFuture<?>> velocityControlTasks = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 初始化消息处理器
        registerHandler(new HeartbeatHandler(this));
        registerHandler(new PositionHandler(this));
        registerHandler(new BatteryHandler(this));
        registerHandler(new GpsHandler(this));
        registerHandler(new MissionAckHandler(this));
        registerHandler(new MissionCountHandler(this));
        registerHandler(new MissionCurrentHandler(this));
        registerHandler(new MissionItemIntHandler(this));
        registerHandler(new MissionRequestHandler(this));
        registerHandler(new MissionRequestIntHandler(this));
        registerHandler(new AttitudeHandler(this));
        registerHandler(new VfrHudHandler(this));

        // 初始化共享线程池
        executorService = Executors.newCachedThreadPool();
        scheduledExecutorService = Executors.newScheduledThreadPool(4);

        // 启动定时任务，每5秒更新一次所有已连接无人机的状态到Redis
        scheduledExecutorService.scheduleAtFixedRate(this::updateAllDroneStatusToRedis, 5, 1, TimeUnit.SECONDS);
    }

    private void registerHandler(AbstractMavlinkHandler<?> handler) {
        handlerMap.put(handler.getMessageType(), handler);
    }

    /**
     * 连接到无人机
     *
     * @param droneId 无人机ID
     * @param host    主机地址
     * @param port    端口
     */
    public void connect(String droneId, String host, int port) {
        try {
            AtomicBoolean isRunning = runningMap.computeIfAbsent(droneId, k -> new AtomicBoolean(false));

            if (isRunning.get()) {
                log.info("检测到无人机[{}]已有连接，先断开现有连接", droneId);
                disconnect(droneId);
            }

            // 保存连接信息，用于可能的重连
            lastHostMap.put(droneId, host);
            lastPortMap.put(droneId, port);

            // 确保线程池已初始化
            if (executorService == null || executorService.isShutdown() || executorService.isTerminated()) {
                executorService = Executors.newCachedThreadPool();
            }

            if (scheduledExecutorService == null || scheduledExecutorService.isShutdown() ||
                scheduledExecutorService.isTerminated()) {
                scheduledExecutorService = Executors.newScheduledThreadPool(4);
            }

            // 创建连接
            Socket socket = new Socket(host, port);
            MavlinkConnection connection = MavlinkConnection.create(socket.getInputStream(), socket.getOutputStream());

            // 保存连接
            socketMap.put(droneId, socket);
            connectionMap.put(droneId, connection);
            isRunning.set(true);

            // 重置错误计数
            connectionErrorCountMap.put(droneId, 0);
            lastErrorTimeMap.put(droneId, 0L);

            // 初始化任务数据
            missionSequenceMap.put(droneId, new AtomicInteger(0));
            missionItemFuturesMap.put(droneId, new ConcurrentHashMap<>());
            missionAckFuturesMap.put(droneId, new ConcurrentHashMap<>());
            downloadedItemsMap.put(droneId, new ArrayList<>());

            // 启动消息处理循环
            executorService.submit(() -> messageLoop(droneId));

            // 启动心跳发送
            startHeartbeat(droneId);

            // 请求数据流
            requestDataStream(droneId);

            log.info("已连接到无人机[{}]，地址: {}:{}", droneId, host, port);
        } catch (IOException e) {
            AtomicBoolean isRunning = runningMap.get(droneId);
            if (isRunning != null) {
                isRunning.set(false);
            }

            Socket socket = socketMap.remove(droneId);
            connectionMap.remove(droneId);

            if (socket != null) {
                try {
                    socket.close();
                } catch (IOException ex) {
                    log.warn("关闭socket失败", ex);
                }
            }
            log.error("无人机[{}]连接失败: {}", droneId, e.getMessage());
            throw new RuntimeException("无人机连接失败: " + e.getMessage(), e);
        }
    }

    /**
     * 断开连接
     *
     * @param droneId 无人机ID
     */
    public void disconnect(String droneId) {
        // 获取并更新运行状态
        AtomicBoolean isRunning = runningMap.get(droneId);
        if (isRunning != null) {
            isRunning.set(false);
        }

        // 关闭连接
        Socket socket = socketMap.remove(droneId);
        connectionMap.remove(droneId);

        if (socket != null) {
            try {
                socket.close();
                log.info("已关闭无人机[{}]连接", droneId);
            } catch (IOException e) {
                log.warn("关闭无人机[{}]连接失败: {}", droneId, e.getMessage());
            }
        }

        // 清理资源
        heartbeatMap.remove(droneId);
        positionMap.remove(droneId);
        lastPositionMap.remove(droneId);
        flightDistanceMap.remove(droneId);
        batteryStatusMap.remove(droneId);
        gpsInfoMap.remove(droneId);
        attitudeMap.remove(droneId);
        vfrHudMap.remove(droneId);
        rtkAltitudeMap.remove(droneId);
        currentMissionMap.remove(droneId);
        missionSequenceMap.remove(droneId);
        missionItemFuturesMap.remove(droneId);
        missionAckFuturesMap.remove(droneId);
        lastMissionAckMap.remove(droneId);
        currentDownloadFutureMap.remove(droneId);
        downloadedItemsMap.remove(droneId);
    }

    /**
     * 检查是否已连接
     *
     * @param droneId 无人机ID
     * @return 是否已连接
     */
    public boolean isConnected(String droneId) {
        AtomicBoolean isRunning = runningMap.get(droneId);
        MavlinkConnection connection = connectionMap.get(droneId);
        Socket socket = socketMap.get(droneId);

        return isRunning != null && isRunning.get() && connection != null && socket != null && !socket.isClosed();
    }

    /**
     * 设置无人机心跳信息
     *
     * @param droneId   无人机ID
     * @param heartbeat 心跳信息
     */
    public void setLastHeartbeat(String droneId, Heartbeat heartbeat) {
        heartbeatMap.put(droneId, heartbeat);
    }

    /**
     * 设置无人机位置信息
     *
     * @param droneId  无人机ID
     * @param position 位置信息
     */
    public void setLastPosition(String droneId, GlobalPositionInt position) {
        // 获取上一次位置
        GlobalPositionInt lastPosition = lastPositionMap.get(droneId);

        // 如果有上一次位置，计算距离并累加到总航距
        if (lastPosition != null) {
            // 提取经纬度（转换为度）
            double lat1 = lastPosition.lat() / 1e7;
            double lon1 = lastPosition.lon() / 1e7;
            double lat2 = position.lat() / 1e7;
            double lon2 = position.lon() / 1e7;

            // 计算距离（米）
            double distance = com.md.utils.GeoUtils.calculateDistance(lat1, lon1, lat2, lon2);

            // 如果距离合理（大于0.5米且小于100米，避免GPS抖动和异常值），则累加到总航距
            if (distance > 0.5 && distance < 100) {
                double totalDistance = flightDistanceMap.getOrDefault(droneId, 0.0);
                flightDistanceMap.put(droneId, totalDistance + distance);
            }
        }

        // 保存当前位置作为下一次计算的基准
        lastPositionMap.put(droneId, position);

        // 更新当前位置
        positionMap.put(droneId, position);
    }

    /**
     * 设置无人机电池状态
     *
     * @param droneId       无人机ID
     * @param batteryStatus 电池状态
     */
    public void setLastBatteryStatus(String droneId, BatteryStatus batteryStatus) {
        batteryStatusMap.put(droneId, batteryStatus);
    }

    /**
     * 设置无人机GPS信息
     *
     * @param droneId 无人机ID
     * @param gpsInfo GPS信息
     */
    public void setLastGpsInfo(String droneId, GpsRawInt gpsInfo) {
        gpsInfoMap.put(droneId, gpsInfo);
    }

    /**
     * 获取当前任务
     *
     * @param droneId 无人机ID
     * @return 当前任务
     */
    public Mission getCurrentMission(String droneId) {
        return currentMissionMap.get(droneId);
    }

    /**
     * 无人机解锁
     *
     * @param droneId 无人机ID
     * @param force   是否强制解锁
     */
    public void arm(String droneId, boolean force) {
        try {
            // 先设置为稳定模式（或其他合适的模式）
            setMode(droneId, 0); // 0 = STABILIZE mode for ArduPilot
            Thread.sleep(100); // 等待模式切换// 等待模式切换

            // 发送解锁命令
            CommandLong armCommand =
                CommandLong.builder().command(MavCmd.MAV_CMD_COMPONENT_ARM_DISARM).param1(1)    // 1 = 解锁
                    .param2(force ? 21196 : 0)  // 强制解锁魔术数字
                    .param3(0).targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId())
                    .build();

            sendCommand(droneId, armCommand);
            log.info("已发送无人机[{}]解锁命令，强制模式: {}", droneId, force);
        } catch (IOException e) {
            log.error("无人机[{}]解锁失败: {}", droneId, e.getMessage());
            throw new RuntimeException("解锁失败: " + e.getMessage(), e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 无人机上锁
     *
     * @param droneId 无人机ID
     */
    public void disarm(String droneId) {
        try {
            CommandLong disarmCommand =
                CommandLong.builder().command(MavCmd.MAV_CMD_COMPONENT_ARM_DISARM).param1(0)  // 0表示上锁
                    .param2(0)  // 不需要强制参数
                    .build();

            sendCommand(droneId, disarmCommand);
            log.info("已发送无人机[{}]上锁命令", droneId);
        } catch (IOException e) {
            log.error("无人机[{}]上锁失败: {}", droneId, e.getMessage());
            throw new RuntimeException("上锁失败: " + e.getMessage(), e);
        }
    }

    /**
     * 起飞
     *
     * @param droneId  无人机ID
     * @param altitude 目标高度增量(米)，将在当前相对高度基础上增加这个值
     */
    public void takeoff(String droneId, float altitude) {
        try {
            // 检查是否已解锁，如果未解锁，则先解锁
            if (!isArmed(droneId)) {
                arm(droneId, true);
                sleep(2000);
            }

            //  切换到STABILIZE模式
            setMode(droneId, 0); // 0 = STABILIZE模式
            sleep(1000);

            // 获取当前相对高度
            float currentRelativeAlt = 0;
            if (positionMap.get(droneId) != null) {
                currentRelativeAlt = positionMap.get(droneId).relativeAlt() / 1000f;  // 转换为米
            }

            // 计算最终目标高度（当前高度 + 增量）
            float targetAltitude = currentRelativeAlt + altitude;

            // 切换到GUIDED模式
            setMode(droneId, 4); // 4 = GUIDED模式 (适用于ArduPilot)

            CommandLong takeoffCommand =
                CommandLong.builder().command(MavCmd.MAV_CMD_NAV_TAKEOFF).param1(0)    // 俯仰角最小值
                    .param2(0)    // 空
                    .param3(0)    // 空
                    .param4(0)    // 偏航角
                    .param5(0)    // 纬度 (0 表示使用当前位置)
                    .param6(0)    // 经度 (0 表示使用当前位置)
                    .param7(targetAltitude)    // 相对高度（米）
                    .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId()).build();

            sendCommand(droneId, takeoffCommand);
            log.info("已发送无人机[{}]起飞指令, 当前高度: {}米, 目标增量: {}米, 最终目标高度: {}米", droneId,
                currentRelativeAlt, altitude, targetAltitude);
        } catch (IOException e) {
            log.error("发送无人机[{}]起飞指令失败: {}", droneId, e.getMessage());
        }
    }

    /**
     * 降落
     *
     * @param droneId 无人机ID
     */
    public void land(String droneId) {
        try {
            CommandLong landCommand = CommandLong.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId()).command(MavCmd.MAV_CMD_NAV_LAND).build();
            sendCommand(droneId, landCommand);
            log.info("已发送无人机[{}]降落指令", droneId);
        } catch (IOException e) {
            log.error("发送无人机[{}]降落指令失败: {}", droneId, e.getMessage());
        }
    }

    /**
     * 返航
     *
     * @param droneId 无人机ID
     */
    public void returnToHome(String droneId) {
        try {
            CommandLong rthCommand = CommandLong.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId()).command(MavCmd.MAV_CMD_NAV_RETURN_TO_LAUNCH).build();
            sendCommand(droneId, rthCommand);
            log.info("已发送无人机[{}]返航指令", droneId);
        } catch (IOException e) {
            log.error("发送无人机[{}]返航指令失败: {}", droneId, e.getMessage());
        }
    }

    /**
     * 设置飞行模式
     *
     * @param droneId 无人机ID
     * @param mode    模式值
     */
    public void setMode(String droneId, int mode) {
        try {
            CommandLong modeCommand = CommandLong.builder().command(MavCmd.MAV_CMD_DO_SET_MODE).param1(1)  // 基础模式 = 1
                .param2(mode)  // 自定义模式
                .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId()).build();
            sendCommand(droneId, modeCommand);
            log.info("已发送无人机[{}]设置模式指令: mode={}", droneId, mode);
        } catch (IOException e) {
            log.error("发送无人机[{}]设置模式指令失败: {}", droneId, e.getMessage());
        }
    }

    /**
     * 指点飞行
     *
     * @param droneId   无人机ID
     * @param latitude  目标纬度
     * @param longitude 目标经度
     * @param altitude  目标高度
     * @param speed     飞行速度
     */
    public void flyTo(String droneId, BigDecimal latitude, BigDecimal longitude, BigDecimal altitude, float speed) {
        try {
            // 1. 检查是否已解锁，如未解锁则自动解锁
            if (!isArmed(droneId)) {
                log.info("无人机[{}]未解锁，正在解锁...", droneId);
                arm(droneId, true);
                Thread.sleep(1000); // 等待解锁完成
            }

            // 2. 切换到GUIDED模式
            CommandLong modeCommand = CommandLong.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId()).command(MavCmd.MAV_CMD_DO_SET_MODE).param1(1)  // 基础模式
                .param2(4)  // 4 = GUIDED模式
                .build();
            sendCommand(droneId, modeCommand);
            log.info("已切换到无人机[{}]GUIDED模式", droneId);
            Thread.sleep(1000); // 等待模式切换完成

            // 3. 发送目标位置命令 - 使用MissionItemInt而非CommandLong
            MissionItemInt targetPoint = MissionItemInt.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId()).frame(MavFrame.MAV_FRAME_GLOBAL_RELATIVE_ALT)
                .command(MavCmd.MAV_CMD_NAV_WAYPOINT).current(2)  // 2表示这是一个GUIDED模式的目标点
                .autocontinue(1).param1(0)  // 到达时停留时间（秒）
                .param2(2)  // 接受半径（米）
                .param3(0)  // 通过半径（米）
                .param4(0)  // 偏航角（度）
                .x((int)(latitude.doubleValue() * 1.0e7)).y((int)(longitude.doubleValue() * 1.0e7))
                .z(altitude.floatValue()).missionType(MavMissionType.MAV_MISSION_TYPE_MISSION).build();

            // 发送目标点命令
            sendMessage(droneId, targetPoint);

            log.info("已发送无人机[{}]指点飞行命令: 目标位置: [{}, {}, {}], 速度: {}m/s", droneId, latitude, longitude,
                altitude, speed);
        } catch (Exception e) {
            log.error("发送无人机[{}]指点飞行指令失败: {}", droneId, e.getMessage(), e);
        }
    }

    /**
     * 消息处理循环
     */
    private void messageLoop(String droneId) {
        AtomicBoolean isRunning = runningMap.get(droneId);
        MavlinkConnection connection = connectionMap.get(droneId);

        if (isRunning == null || connection == null) {
            log.warn("无人机[{}]消息处理循环启动失败：连接未初始化", droneId);
            return;
        }

        while (isRunning.get() && !Thread.currentThread().isInterrupted()) {
            try {
                MavlinkMessage<?> message = connection.next();
                handleMessage(droneId, message);
            } catch (IOException e) {
                if (isRunning.get()) {
                    handleConnectionError(droneId, e);
                }
                break;
            }
        }
    }

    @SuppressWarnings("unchecked")
    private <T> void handleMessage(String droneId, MavlinkMessage<T> message) {
        AbstractMavlinkHandler<T> handler = (AbstractMavlinkHandler<T>)handlerMap.get(message.getPayload().getClass());
        if (handler != null) {
            handler.handle(droneId, message.getPayload());
        }
    }

    private void startHeartbeat(String droneId) {
        scheduledExecutorService.scheduleAtFixedRate(() -> {
            if (!isConnected(droneId)) {
                // 如果已断开连接，不发送心跳包
                return;
            }

            try {
                Heartbeat heartbeat =
                    Heartbeat.builder().type(MavType.MAV_TYPE_GCS).autopilot(MavAutopilot.MAV_AUTOPILOT_INVALID)
                        .systemStatus(MavState.MAV_STATE_ACTIVE).build();
                sendMessage(droneId, heartbeat);
            } catch (IOException e) {
                // 避免打印过多连接中断的日志
                Integer errorCount = connectionErrorCountMap.getOrDefault(droneId, 0);
                if (runningMap.getOrDefault(droneId, new AtomicBoolean(false)).get() && errorCount < ERROR_THRESHOLD) {
                    log.warn("发送无人机[{}]心跳包失败: {}", droneId, e.getMessage());
                    // 直接调用错误处理，计入错误次数
                    handleConnectionError(droneId, e);
                }
            }
        }, 0, config.getHeartbeatInterval(), TimeUnit.MILLISECONDS);
    }

    private void requestDataStream(String droneId) {
        try {
            // 请求位置数据
            requestStream(droneId, 0, config.getStreamRate());
            // 请求姿态数据
            requestStream(droneId, 1, config.getStreamRate());
            // 请求扩展状态数据
            requestStream(droneId, 2, config.getStreamRate());
            // 请求额外数据
            requestStream(droneId, 3, config.getStreamRate());
            // 请求额外2数据
            requestStream(droneId, 4, config.getStreamRate());
            // 请求位置数据
            requestStream(droneId, 6, config.getStreamRate());
            log.info("已请求无人机[{}]数据流，频率: {}Hz", droneId, config.getStreamRate());
        } catch (IOException e) {
            log.warn("请求无人机[{}]数据流失败: {}", droneId, e.getMessage());
        }
    }

    private void requestStream(String droneId, int streamId, int rateHz) throws IOException {
        RequestDataStream request = RequestDataStream.builder().targetSystem(config.getTargetSystemId())
            .targetComponent(config.getTargetComponentId()).reqStreamId(streamId).reqMessageRate(rateHz)
            .startStop(1)  // 1表示开始，0表示停止
            .build();

        sendMessage(droneId, request);
    }

    private void sendCommand(String droneId, CommandLong command) throws IOException {
        sendMessage(droneId, command);
    }

    private void sendMessage(String droneId, Object message) throws IOException {
        MavlinkConnection connection = connectionMap.get(droneId);
        if (connection == null || !isConnected(droneId)) {
            throw new IOException("无人机[" + droneId + "]连接已关闭或不可用");
        }

        try {
            // 使用与原始实现相同的send2方法
            connection.send2(config.getSystemId(), config.getComponentId(), message);
        } catch (IOException e) {
            // 对于可能出现的连接断开异常，统一处理
            if (e.getMessage().contains("中止了一个已建立的连接") || e.getMessage().contains("连接被对方重设") ||
                e.getMessage().contains("管道关闭") || e.getMessage().contains("套接字已关闭")) {
                // 连接已经断开，更新状态
                AtomicBoolean isRunning = runningMap.get(droneId);
                if (isRunning != null) {
                    isRunning.set(false);
                }
            }
            throw e;
        }
    }

    private boolean isArmed(String droneId) {
        Heartbeat heartbeat = heartbeatMap.get(droneId);
        if (heartbeat == null) {
            return false;
        }
        return (heartbeat.baseMode().value() & MAV_MODE_FLAG_SAFETY_ARMED) != 0;
    }

    private String getFlightMode(String droneId) {
        Heartbeat heartbeat = heartbeatMap.get(droneId);
        if (heartbeat == null) {
            return "UNKNOWN";
        }

        // 获取基础模式和自定义模式
        int baseMode = heartbeat.baseMode().value();
        long customMode = heartbeat.customMode();

        // 检查是否为ArduPilot (MAV_AUTOPILOT_ARDUPILOTMEGA = 3)
        // heartbeat.autopilot()返回的是EnumValue，需要通过entry()获取实际的枚举值
        if (heartbeat.autopilot().entry() == MavAutopilot.MAV_AUTOPILOT_ARDUPILOTMEGA) {
            switch ((int)customMode) {
                case 0:
                    return "STABILIZE";
                case 1:
                    return "ACRO";
                case 2:
                    return "ALT_HOLD";
                case 3:
                    return "AUTO";
                case 4:
                    return "GUIDED";
                case 5:
                    return "LOITER";
                case 6:
                    return "RTL";
                case 7:
                    return "CIRCLE";
                case 8:
                    return "POSITION";
                case 9:
                    return "LAND";
                case 10:
                    return "OF_LOITER";
                case 11:
                    return "DRIFT";
                case 13:
                    return "SPORT";
                case 14:
                    return "FLIP";
                case 15:
                    return "AUTOTUNE";
                case 16:
                    return "POSHOLD";
                case 17:
                    return "BRAKE";
                case 18:
                    return "THROW";
                case 19:
                    return "AVOID_ADSB";
                case 20:
                    return "GUIDED_NOGPS";
                case 21:
                    return "SMART_RTL";
                case 22:
                    return "FLOWHOLD";
                case 23:
                    return "FOLLOW";
                case 24:
                    return "ZIGZAG";
                case 25:
                    return "SYSTEMID";
                case 26:
                    return "AUTOROTATE";
                case 27:
                    return "AUTO_RTL";
                default:
                    return "UNKNOWN";
            }
        } else if (heartbeat.autopilot().equals(MavAutopilot.MAV_AUTOPILOT_PX4)) {
            // PX4的模式映射
            // 主模式
            int mainMode = (int)((customMode & 0xFF0000) >> 16);
            // 子模式
            int subMode = (int)((customMode & 0xFF000000) >> 24);

            switch (mainMode) {
                case 1:
                    return "MANUAL";
                case 2:
                    return "ALTITUDE";
                case 3:
                    return "POSITION";
                case 4:
                    switch (subMode) {
                        case 1:
                            return "READY";
                        case 2:
                            return "TAKEOFF";
                        case 3:
                            return "LOITER";
                        case 4:
                            return "MISSION";
                        case 5:
                            return "RTL";
                        case 6:
                            return "LAND";
                        case 7:
                            return "RTGS";
                        case 8:
                            return "FOLLOW_TARGET";
                        default:
                            return "AUTO";
                    }
                case 5:
                    return "ACRO";
                case 6:
                    return "OFFBOARD";
                case 7:
                    return "STABILIZED";
                default:
                    return "UNKNOWN";
            }
        } else {
            // 通用模式判断（基于标志位）
            if ((baseMode & MAV_MODE_FLAG_AUTO_ENABLED) != 0) {
                return "AUTO";
            } else if ((baseMode & MAV_MODE_FLAG_MANUAL_INPUT_ENABLED) != 0) {
                return "MANUAL";
            } else {
                return "STABILIZE";
            }
        }
    }

    private void handleConnectionError(String droneId, IOException e) {
        int errorCount = connectionErrorCountMap.getOrDefault(droneId, 0) + 1;
        connectionErrorCountMap.put(droneId, errorCount);

        long currentTime = System.currentTimeMillis();
        long lastTime = lastErrorTimeMap.getOrDefault(droneId, 0L);

        // 如果是新的错误窗口期，重置计数
        if (currentTime - lastTime > ERROR_TIME_WINDOW) {
            errorCount = 1;
            connectionErrorCountMap.put(droneId, errorCount);
        }

        lastErrorTimeMap.put(droneId, currentTime);

        // 判断是否触发重连
        if (errorCount >= ERROR_THRESHOLD &&
            !autoReconnectingMap.getOrDefault(droneId, new AtomicBoolean(false)).get()) {
            log.warn("无人机[{}]连接错误次数达到阈值，准备自动重连", droneId);
            // 启动重连
            AtomicBoolean isAutoReconnecting =
                autoReconnectingMap.computeIfAbsent(droneId, k -> new AtomicBoolean(false));
            if (isAutoReconnecting.compareAndSet(false, true)) {
                // 仅当未在重连时才启动新的重连任务
                executorService.submit(() -> attemptAutoReconnect(droneId));
            }
        } else {
            // 仅记录非重复的连接错误
            if (errorCount <= ERROR_THRESHOLD || errorCount % 10 == 0) {
                log.warn("无人机[{}]连接错误 ({}/{}): {}", droneId, errorCount, ERROR_THRESHOLD, e.getMessage());
            }

            // 如果达到一定错误次数，连接可能已经断开，需要更新状态
            if (errorCount >= 3) {
                AtomicBoolean isRunning = runningMap.get(droneId);
                if (isRunning != null && isRunning.get()) {
                    log.warn("无人机[{}]检测到连接可能已断开，更新连接状态", droneId);
                    disconnect(droneId);
                }
            }
        }
    }

    // 自动重连
    private void attemptAutoReconnect(String droneId) {
        // ... 其余方法也需要按相同方式修改 ...
    }

    /**
     * 重新连接到无人机
     *
     * @param droneId 无人机ID
     * @param host    主机地址
     * @param port    端口
     * @return 是否成功重连
     */
    public boolean reconnect(String droneId, String host, int port) {
        if (isConnected(droneId)) {
            disconnect(droneId);
        }

        try {
            connect(droneId, host, port);
            return true;
        } catch (Exception e) {
            log.error("无人机[{}]重连失败: {}", droneId, e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有已连接的无人机ID
     *
     * @return 无人机ID列表
     */
    public List<String> getConnectedDrones() {
        return connectionMap.keySet().stream().filter(this::isConnected).sorted()
            .collect(java.util.stream.Collectors.toList());
    }

    // 上传航线任务
    public CompletableFuture<Boolean> uploadMission(String droneId, Mission mission) {
        CompletableFuture<Boolean> result = new CompletableFuture<>();

        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法上传航线任务", droneId);
            result.completeExceptionally(new IllegalStateException("无人机未连接"));
            return result;
        }

        List<MissionWaypoint> items = mission.getItems();
        if (items == null || items.isEmpty()) {
            log.error("航线任务为空，无法上传");
            result.completeExceptionally(new IllegalArgumentException("航线任务为空"));
            return result;
        }

        log.info("开始上传无人机[{}]航线任务: name={}, description={}, 航点数量={}", droneId, mission.getName(),
            mission.getDescription(), items.size());

        // 打印航点详情
        for (int i = 0; i < items.size(); i++) {
            MissionWaypoint item = items.get(i);
            log.info("航点 {}: seq={}, command={}, frame={}, lat={}, lon={}, alt={}, current={}, autocontinue={}", i,
                item.getSeq(), item.getCommand(), item.getFrame(), item.getLatitude(), item.getLongitude(),
                item.getAltitude(), item.getCurrent(), item.getAutoContinue());
        }

        // 设置当前任务
        currentMissionMap.put(droneId, mission);
        currentMissionMap.get(droneId).setStatus(Mission.MissionStatus.UPLOADING);

        // 改为同步执行方便调试
        try {
            // 先清空现有任务
            log.info("清空现有任务");
            MissionAck clearAck = clearMission(droneId).get(5, TimeUnit.SECONDS);

            if (clearAck.type().entry() != MavMissionResult.MAV_MISSION_ACCEPTED) {
                log.warn("清空任务未收到接受确认，状态为: {}，尝试继续上传", clearAck.type().entry());
            }

            // 增加一个短暂延迟，让飞控有时间处理清空操作
            Thread.sleep(500);

            // 发送任务数量
            int seq = missionSequenceMap.get(droneId).incrementAndGet();
            CompletableFuture<MissionAck> ackFuture = new CompletableFuture<>();
            missionAckFuturesMap.get(droneId).put(seq, ackFuture);

            MissionCount missionCount = MissionCount.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId()).count(items.size())
                .missionType(EnumValue.of(MavMissionType.MAV_MISSION_TYPE_MISSION)).build();

            log.info("发送任务数量: count={}, targetSystem={}, targetComponent={}, seq={}", items.size(),
                config.getTargetSystemId(), config.getTargetComponentId(), seq);

            sendMessage(droneId, missionCount);
            log.info("等待任务上传完成...");

            // 等待任务上传完成
            MissionAck ack = ackFuture.get(30, TimeUnit.SECONDS);
            log.info("收到任务上传响应: type={}", ack.type().entry());

            if (ack.type().entry() == MavMissionResult.MAV_MISSION_ACCEPTED) {
                log.info("航线任务上传成功: {}", ack.type().entry());
                result.complete(true);
            } else if (ack.type().entry() == MavMissionResult.MAV_MISSION_OPERATION_CANCELLED) {
                log.error("航线任务上传被取消: {}", ack.type().entry());
                log.error("正在尝试重新上传...");

                // 简单的重试逻辑
                Thread.sleep(1000);  // 等待1秒后重试
                return uploadMission(droneId, mission);  // 递归调用实现重试
            } else {
                log.error("航线任务上传失败: {}", ack.type().entry());
                result.complete(false);
            }
        } catch (Exception e) {
            log.error("上传航线任务失败", e);
            currentMissionMap.get(droneId).setStatus(Mission.MissionStatus.FAILED);
            result.completeExceptionally(e);
        }

        return result;
    }

    /**
     * 开始执行任务
     */
    public void startMission(String droneId, BigDecimal speed, float altitude) {
        try {
            // 1. 检查是否已解锁并起飞
            if (!isArmed(droneId)) {
                log.info("无人机未解锁，正在解锁并起飞...");
                // 获取当前相对高度
                float currentRelativeAlt = 0;
                if (positionMap.get(droneId) != null) {
                    currentRelativeAlt = positionMap.get(droneId).relativeAlt() / 1000f;  // 转换为米
                }

                float targetAltitude = currentRelativeAlt + altitude;
                takeoff(droneId, targetAltitude);
                Thread.sleep(10000); // 等待起飞完成
            }

            // 2. 设置航线速度
            setSpeed(droneId, speed.floatValue()).get(5, TimeUnit.SECONDS);

            // 3. 切换到AUTO模式开始执行任务
            setMode(droneId, 3); // 3 = AUTO模式
            Thread.sleep(10000);

            CommandLong startCommand = CommandLong.builder().command(MavCmd.MAV_CMD_MISSION_START).param1(0) // 第一个航点
                .param2(0) // 最后一个航点
                .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId()).build();

            sendCommand(droneId, startCommand);

            // 保证速度设置有效
            setSpeed(droneId, speed.floatValue()).get(5, TimeUnit.SECONDS);
            log.info("已发送无人机[{}]开始任务指令", droneId);
        } catch (Exception e) {
            log.error("发送无人机[{}]开始任务指令失败: {}", droneId, e.getMessage());
        }
    }

    /**
     * 暂停任务
     */
    public void pauseMission(String droneId) {
        try {
            CommandLong pauseCommand = CommandLong.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId()).command(MavCmd.MAV_CMD_DO_PAUSE_CONTINUE)
                .param1(0) // 0 = 暂停
                .build();
            sendCommand(droneId, pauseCommand);
            log.info("已发送无人机[{}]暂停任务指令", droneId);
        } catch (IOException e) {
            log.error("发送无人机[{}]暂停任务指令失败: {}", droneId, e.getMessage());
        }
    }

    /**
     * 继续任务
     */
    public void resumeMission(String droneId) {
        try {
            CommandLong resumeCommand = CommandLong.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId()).command(MavCmd.MAV_CMD_DO_PAUSE_CONTINUE)
                .param1(1) // 1 = 继续
                .build();
            sendCommand(droneId, resumeCommand);
            log.info("已发送无人机[{}]继续任务指令", droneId);
        } catch (IOException e) {
            log.error("发送无人机[{}]继续任务指令失败: {}", droneId, e.getMessage());
        }
    }

    /**
     * 停止任务
     */
    public void stopMission(String droneId) {
        try {
            // 获取最新的心跳包以确定飞控类型
            Heartbeat heartbeat = heartbeatMap.get(droneId);
            if (heartbeat == null) {
                log.error("无人机[{}]心跳包不存在，无法确定飞控类型", droneId);
                throw new IllegalStateException("无人机心跳包不存在");
            }

            // 根据飞控类型选择不同的模式切换策略
            if (heartbeat.autopilot().value() == 3) { // APM
                // 1. 切换到LOITER模式 (模式值 = 5)
                CommandLong setModeCommand = CommandLong.builder().targetSystem(config.getTargetSystemId())
                    .targetComponent(config.getTargetComponentId()).command(MavCmd.MAV_CMD_DO_SET_MODE)
                    .param1(1)  // 基础模式
                    .param2(5)  // LOITER模式
                    .build();
                sendCommand(droneId, setModeCommand);
                log.info("APM - 已发送切换到LOITER模式命令");

                // 等待模式切换完成
                Thread.sleep(1000);

            } else { // PX4或其他
                // 1. 切换到HOLD模式 (主模式 = 4)
                CommandLong setModeCommand = CommandLong.builder().targetSystem(config.getTargetSystemId())
                    .targetComponent(config.getTargetComponentId()).command(MavCmd.MAV_CMD_DO_SET_MODE)
                    .param1(1)  // 基础模式
                    .param2(4)  // HOLD模式
                    .build();
                sendCommand(droneId, setModeCommand);
                log.info("PX4 - 已发送切换到HOLD模式命令");

                // 等待模式切换完成
                Thread.sleep(1000);
            }

            // 2. 清除任务列表
            clearMission(droneId).thenAccept(missionAck -> {
                if (missionAck.type().entry() == MavMissionResult.MAV_MISSION_ACCEPTED) {
                    log.info("无人机[{}]任务清除成功", droneId);

                    // 3. 更新任务状态
                    Mission currentMission = currentMissionMap.get(droneId);
                    if (currentMission != null) {
                        currentMission.setStatus(Mission.MissionStatus.COMPLETED);
                        log.info("已更新任务状态为已完成");
                    }
                } else {
                    log.warn("无人机[{}]任务清除失败或未收到确认", droneId);
                }
            }).exceptionally(e -> {
                log.error("清除任务时发生错误: {}", e.getMessage(), e);
                return null;
            });

            log.info("已完成无人机[{}]停止任务流程", droneId);
        } catch (Exception e) {
            log.error("停止任务过程中发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("停止任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取当前位置
     *
     * @param droneId 无人机ID
     * @return 当前位置
     */
    public GlobalPositionInt getCurrentPosition(String droneId) {
        return positionMap.get(droneId);
    }

    /**
     * 获取无人机状态
     *
     * @param droneId 无人机ID
     * @return 无人机状态
     */
    public DroneStatus getDroneStatus(String droneId) {
        try {
            long startTime = System.currentTimeMillis();

            // 构建状态对象
            DroneStatus status = buildDroneStatus(droneId);

            long buildTime = System.currentTimeMillis();
            log.debug("构建无人机[{}]状态对象耗时: {}ms", droneId, (buildTime - startTime));

            // 转换为JSON字符串并存储到Redis的操作可以异步执行，不阻塞主流程
            final DroneStatus finalStatus = status;
            // 使用线程池异步执行Redis存储和WebSocket推送
            executorService.submit(() -> {
                try {
                    // 转换为JSON字符串
                    String jsonStatus = JSON.toJSONString(finalStatus);

                    // 使用与大疆相同的存储结构
                    String redisKey = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId;

                    // 存储到Redis，设置相同的过期时间
                    RedisUtils.setCacheObject(redisKey, jsonStatus, Duration.ofSeconds(expireSeconds));

                    // 发布MQTT消息到mavlink/status主题，供WebSocket处理器处理
                    publishMqttStatusMessage(droneId, jsonStatus);

                    long endTime = System.currentTimeMillis();
                    log.debug("异步存储无人机[{}]状态到Redis和MQTT耗时: {}ms", droneId, (endTime - buildTime));
                } catch (Exception e) {
                    log.error("异步处理无人机[{}]状态失败: {}", droneId, e.getMessage());
                }
            });

            return status;
        } catch (Exception e) {
            log.error("构建无人机[{}]状态失败: {}", droneId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建无人机状态对象（复用现有逻辑，避免重复代码）
     */
    private DroneStatus buildDroneStatus(String droneId) {
        DroneStatus status = new DroneStatus().setOnline(isConnected(droneId)).setArmed(isArmed(droneId))
            .setFlightMode(getFlightMode(droneId)).setLastUpdateTime(System.currentTimeMillis());

        // 设置无人机ID (droneSN)，与大疆无人机保持一致的字段命名
        status.setDroneSN(droneId);

        if (positionMap.get(droneId) != null) {
            status.setLatitude(BigDecimal.valueOf(positionMap.get(droneId).lat() / 1e7))
                .setLongitude(BigDecimal.valueOf(positionMap.get(droneId).lon() / 1e7))
                .setRelativeAltitude(positionMap.get(droneId).relativeAlt() / 1000f)
                .setAbsoluteAltitude(positionMap.get(droneId).alt() / 1000f)
                .setHeading(positionMap.get(droneId).hdg() / 100f);
        }

        // 设置姿态信息
        if (attitudeMap.get(droneId) != null) {
            Attitude attitude = attitudeMap.get(droneId);
            status.setPitch((float)Math.toDegrees(attitude.pitch())).setRoll((float)Math.toDegrees(attitude.roll()));
        }

        // 设置飞行数据
        if (vfrHudMap.get(droneId) != null) {
            VfrHud vfrHud = vfrHudMap.get(droneId);
            status.setAirSpeed(vfrHud.airspeed()).setGroundSpeed(vfrHud.groundspeed()).setVerticalSpeed(vfrHud.climb());
        }

        // 设置航距
        status.setFlightDistance(flightDistanceMap.getOrDefault(droneId, 0.0));

        // 设置RTK海拔高度
        if (rtkAltitudeMap.containsKey(droneId)) {
            status.setRtkAltitude(rtkAltitudeMap.get(droneId));
            // 如果有RTK数据，则假设RTK已连接和启用
            status.setRTKConnected(true);
            status.setRTKEnabled(true);
            // RTK健康状态需要根据实际情况设置，这里默认为true
            status.setRTKHealthy(true);
        } else if (positionMap.get(droneId) != null) {
            // 如果没有RTK数据，使用普通GPS高度作为近似值
            status.setRtkAltitude(positionMap.get(droneId).alt() / 1000.0);
            status.setRTKConnected(false);
            status.setRTKEnabled(false);
            status.setRTKHealthy(false);
        }

        if (batteryStatusMap.get(droneId) != null) {
            // 确保BatteryInfo存在
            if (status.getBatteryInfo() == null) {
                status.setBatteryInfo(new DroneStatus.BatteryInfo());
            }
            // 设置电池百分比和电压到BatteryInfo中
            status.getBatteryInfo().setChargeRemainingInPercent(batteryStatusMap.get(droneId).batteryRemaining())
                .setBatteryVoltage(batteryStatusMap.get(droneId).voltages().get(0) / 1000f);
        }

        if (gpsInfoMap.get(droneId) != null) {
            status.setGpsFixType(gpsInfoMap.get(droneId).fixType().value())
                .setGpsSatellites(gpsInfoMap.get(droneId).satellitesVisible());

            // 设置RTK卫星数量，如果是RTK模式，则使用GPS卫星数量
            if (gpsInfoMap.get(droneId).fixType().value() >= 4) { // 4表示RTK浮点解，6表示RTK固定解
                status.setRtkSatelliteCount(gpsInfoMap.get(droneId).satellitesVisible());
            }
        }

        return status;
    }

    /**
     * 获取格式化的位置信息
     *
     * @param droneId 无人机ID
     * @return 位置信息Map，包含经纬度、高度等，如果无位置信息则返回null
     */
    public Map<String, Object> getFormattedPositionData(String droneId) {
        if (positionMap.get(droneId) == null) {
            return null;
        }

        Map<String, Object> positionData = new HashMap<>();
        positionData.put("latitude", positionMap.get(droneId).lat() / 1e7);
        positionData.put("longitude", positionMap.get(droneId).lon() / 1e7);
        positionData.put("relativeAltitude", positionMap.get(droneId).relativeAlt() / 1000f);
        positionData.put("absoluteAltitude", positionMap.get(droneId).alt() / 1000f);
        positionData.put("heading", positionMap.get(droneId).hdg() / 100f);

        return positionData;
    }

    // 清空航线任务
    public CompletableFuture<MissionAck> clearMission(String droneId) {
        CompletableFuture<MissionAck> result = new CompletableFuture<>();

        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法清空航线任务", droneId);
            result.completeExceptionally(new IllegalStateException("无人机未连接"));
            return result;
        }

        try {
            int seq = missionSequenceMap.get(droneId).incrementAndGet();
            missionAckFuturesMap.get(droneId).put(seq, result);

            MissionClearAll clearAll = MissionClearAll.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId())
                .missionType(EnumValue.of(MavMissionType.MAV_MISSION_TYPE_MISSION)).build();

            log.info("发送清空无人机[{}]航线任务命令: targetSystem={}, targetComponent={}, seq={}", droneId,
                config.getTargetSystemId(), config.getTargetComponentId(), seq);
            sendMessage(droneId, clearAll);

            // 设置一个超时处理器，避免无限等待
            executorService.submit(() -> {
                try {
                    Thread.sleep(5000);  // 5秒超时
                    if (!result.isDone()) {
                        log.warn("清空任务响应超时，强制完成");
                        // 创建一个超时的ACK，设置为操作取消
                        MissionAck timeoutAck = MissionAck.builder().targetSystem(config.getTargetSystemId())
                            .targetComponent(config.getTargetComponentId())
                            .type(EnumValue.of(MavMissionResult.MAV_MISSION_OPERATION_CANCELLED))
                            .missionType(EnumValue.of(MavMissionType.MAV_MISSION_TYPE_MISSION)).build();
                        result.complete(timeoutAck);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
        } catch (Exception e) {
            log.error("发送清空航线任务命令失败", e);
            result.completeExceptionally(e);
        }

        return result;
    }

    public void handleMissionAck(String droneId, MissionAck message) {
        lastMissionAckMap.put(droneId, message);
        CompletableFuture<MissionAck> future =
            missionAckFuturesMap.get(droneId).remove(missionSequenceMap.get(droneId).get());
        if (future != null) {
            future.complete(lastMissionAckMap.get(droneId));
        }
        updateMissionStatus(droneId, lastMissionAckMap.get(droneId));
    }

    // 更新任务状态
    private void updateMissionStatus(String droneId, MissionAck ack) {
        if (currentMissionMap.get(droneId) == null) {
            return;
        }

        MavMissionResult result = ack.type().entry();
        if (result == MavMissionResult.MAV_MISSION_ACCEPTED) {
            if (currentMissionMap.get(droneId).getStatus() == Mission.MissionStatus.UPLOADING) {
                currentMissionMap.get(droneId).setStatus(Mission.MissionStatus.UPLOADED);
                log.info("任务已被接受并成功上传");
            } else if (currentMissionMap.get(droneId).getStatus() == Mission.MissionStatus.DOWNLOADING) {
                currentMissionMap.get(droneId).setStatus(Mission.MissionStatus.IDLE);
                log.info("任务已被接受并成功下载");
            }
        } else if (result == MavMissionResult.MAV_MISSION_OPERATION_CANCELLED) {
            currentMissionMap.get(droneId).setStatus(Mission.MissionStatus.FAILED);
            log.error("任务操作被取消: targetSystem={}, targetComponent={}, missionType={}", ack.targetSystem(),
                ack.targetComponent(), ack.missionType().entry());
            log.error("可能原因: 超时、通信问题或飞行控制器内部取消");
        } else {
            currentMissionMap.get(droneId).setStatus(Mission.MissionStatus.FAILED);
            log.error("任务操作失败: {}", result);
        }
    }

    // 处理任务数量消息
    public void handleMissionCount(String droneId, MissionCount count) {
        log.info("收到无人机[{}]任务数量: count={}", droneId, count.count());

        if (count.count() == 0) {
            // 如果没有任务，直接完成
            if (currentDownloadFutureMap.get(droneId) != null) {
                currentDownloadFutureMap.get(droneId).complete(new ArrayList<>());
            }
            return;
        }

        // 创建一个列表来存储所有任务项
        downloadedItemsMap.put(droneId, new ArrayList<>(count.count()));
        for (int i = 0; i < count.count(); i++) {
            downloadedItemsMap.get(droneId).add(null);
        }

        // 开始请求每个任务项
        for (int i = 0; i < count.count(); i++) {
            final int seq = i;
            CompletableFuture<MissionWaypoint> itemFuture = new CompletableFuture<>();
            missionItemFuturesMap.get(droneId).put(seq, itemFuture);

            try {
                MissionRequestInt request = MissionRequestInt.builder().targetSystem(config.getTargetSystemId())
                    .targetComponent(config.getTargetComponentId()).seq(seq)
                    .missionType(EnumValue.of(MavMissionType.MAV_MISSION_TYPE_MISSION)).build();

                sendMessage(droneId, request);
                log.debug("请求任务项: seq={}", seq);

                // 为每个任务项单独设置超时
                executorService.submit(() -> {
                    try {
                        Thread.sleep(5000); // 5秒超时
                        if (!itemFuture.isDone()) {
                            log.warn("任务项请求超时: seq={}", seq);
                            itemFuture.completeExceptionally(new TimeoutException("任务项请求超时"));
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
            } catch (IOException e) {
                log.error("请求任务项失败: seq={}", seq, e);
                itemFuture.completeExceptionally(e);
            }
        }

        // 等待所有任务项下载完成或超时
        CompletableFuture.allOf(missionItemFuturesMap.get(droneId).values().toArray(new CompletableFuture[0]))
            .thenAccept(v -> {
                List<MissionWaypoint> items = new ArrayList<>();
                boolean hasError = false;

                for (int i = 0; i < count.count(); i++) {
                    try {
                        MissionWaypoint item = missionItemFuturesMap.get(droneId).get(i).get();
                        if (item != null) {
                            items.add(item);
                        } else {
                            hasError = true;
                            log.error("任务项为空: seq={}", i);
                        }
                    } catch (Exception e) {
                        hasError = true;
                        log.error("获取任务项失败: seq={}", i, e);
                    }
                }

                if (!hasError && items.size() == count.count()) {
                    if (currentDownloadFutureMap.get(droneId) != null) {
                        log.info("所有任务项下载完成，共{}个", items.size());
                        currentDownloadFutureMap.get(droneId).complete(items);
                    }
                } else {
                    if (currentDownloadFutureMap.get(droneId) != null) {
                        log.error("部分任务项下载失败，预期{}个，实际获取{}个", count.count(), items.size());
                        currentDownloadFutureMap.get(droneId)
                            .completeExceptionally(new RuntimeException("部分任务项下载失败"));
                    }
                }
            }).exceptionally(e -> {
                if (currentDownloadFutureMap.get(droneId) != null && !currentDownloadFutureMap.get(droneId).isDone()) {
                    log.error("任务下载过程发生异常", e);
                    currentDownloadFutureMap.get(droneId).completeExceptionally(e);
                }
                return null;
            });
    }

    public void updateCurrentMission(String droneId, MissionCurrent message) {
        if (currentMissionMap.get(droneId) != null) {
            currentMissionMap.get(droneId).setCurrentItemIndex(message.seq());
        }
    }

    // 处理任务项消息
    public void handleMissionItemInt(String droneId, MissionItemInt mavlinkItem) {
        int seq = mavlinkItem.seq();
        log.info("处理无人机[{}]任务项: seq={}", droneId, seq);

        try {
            MissionWaypoint item = convertToMissionItem(mavlinkItem);
            CompletableFuture<MissionWaypoint> future = missionItemFuturesMap.get(droneId).get(seq);
            if (future != null && !future.isDone()) {
                log.info("完成无人机[{}]任务项处理: seq={}", droneId, seq);
                future.complete(item);
            } else {
                log.warn("找不到对应的Future或Future已完成: seq={}", seq);
            }
        } catch (Exception e) {
            log.error("处理无人机[{}]任务项时发生错误: seq={}", droneId, seq, e);
            CompletableFuture<MissionWaypoint> future = missionItemFuturesMap.get(droneId).get(seq);
            if (future != null && !future.isDone()) {
                future.completeExceptionally(e);
            }
        }
    }

    // 转换MavLink航点到自定义航点模型
    private MissionWaypoint convertToMissionItem(MissionItemInt item) {
        MissionWaypoint missionWaypoint = new MissionWaypoint();
        missionWaypoint.setSeq(item.seq());
        missionWaypoint.setFrame(item.frame().value());
        missionWaypoint.setCommand(item.command().value());
        missionWaypoint.setCurrent(item.current());
        missionWaypoint.setAutoContinue(item.autocontinue());
        missionWaypoint.setParam1(item.param1());
        missionWaypoint.setParam2(item.param2());
        missionWaypoint.setParam3(item.param3());
        missionWaypoint.setParam4(item.param4());
        missionWaypoint.setLatitude(item.x() / 1.0e7);
        missionWaypoint.setLongitude(item.y() / 1.0e7);
        missionWaypoint.setAltitude(item.z());
        return missionWaypoint;
    }

    // 处理MissionRequest消息
    public void handleMissionRequest(String droneId, MissionRequest request) {
        if (currentMissionMap.get(droneId) == null) {
            log.error("收到任务请求但没有当前任务");
            sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ERROR);
            return;
        }

        List<MissionWaypoint> items = currentMissionMap.get(droneId).getItems();
        if (items == null) {
            log.error("收到任务请求但当前任务的航点列表为null");
            sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ERROR);
            return;
        }

        int seq = request.seq();
        if (seq >= items.size()) {
            log.error("请求的任务项索引超出范围: seq={}, items.size={}", seq, items.size());
            sendMissionAck(droneId, MavMissionResult.MAV_MISSION_INVALID_SEQUENCE);
            return;
        }

        try {
            // 获取请求的任务项
            MissionWaypoint item = items.get(seq);

            if (item == null) {
                log.error("任务项为null: seq={}", seq);
                sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ERROR);
                return;
            }

            log.info("收到任务项请求: seq={}, 当前航点: command={}, lat={}, lon={}, alt={}", seq, item.getCommand(),
                item.getLatitude(), item.getLongitude(), item.getAltitude());

            // 使用MissionUtils中的转换方法
            MissionItemInt missionItemInt =
                MissionUtils.convertToMavlinkMissionItem(item, seq, config.getTargetSystemId(),
                    config.getTargetComponentId());

            // 发送任务项
            log.debug("发送任务项: seq={}, targetSystem={}, targetComponent={}", seq, config.getTargetSystemId(),
                config.getTargetComponentId());
            sendMessage(droneId, missionItemInt);
            log.info("已发送任务项: seq={}", seq);

            // 如果是最后一个航点，发送一个额外的确认
            if (seq == items.size() - 1) {
                log.info("这是最后一个航点请求, 主动发送一个额外的任务确认");
                sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ACCEPTED);
            }
        } catch (Exception e) {
            log.error("发送任务项失败: seq={}", seq, e);
            sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ERROR);
        }
    }

    // 发送任务确认消息
    private void sendMissionAck(String droneId, MavMissionResult result) {
        try {
            MissionAck ack = MissionAck.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId()).type(EnumValue.of(result))
                .missionType(EnumValue.of(MavMissionType.MAV_MISSION_TYPE_MISSION)).build();

            sendMessage(droneId, ack);
            log.info("已发送无人机[{}]任务确认: {}", droneId, result);
        } catch (Exception e) {
            log.error("发送无人机[{}]任务确认失败: {}", droneId, result, e);
        }
    }

    // 处理MissionRequestInt消息
    public void handleMissionRequestInt(String droneId, MissionRequestInt request) {
        if (currentMissionMap.get(droneId) == null) {
            log.error("收到整数任务请求但没有当前任务");
            sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ERROR);
            return;
        }

        List<MissionWaypoint> items = currentMissionMap.get(droneId).getItems();
        if (items == null) {
            log.error("收到整数任务请求但当前任务的航点列表为null");
            sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ERROR);
            return;
        }

        int seq = request.seq();
        if (seq >= items.size()) {
            log.error("请求的整数任务项索引超出范围: seq={}, items.size={}", seq, items.size());
            sendMissionAck(droneId, MavMissionResult.MAV_MISSION_INVALID_SEQUENCE);
            return;
        }

        try {
            // 获取请求的任务项
            MissionWaypoint item = items.get(seq);

            if (item == null) {
                log.error("任务项为null: seq={}", seq);
                sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ERROR);
                return;
            }

            log.info("收到整数任务项请求: seq={}, 当前航点: command={}, lat={}, lon={}, alt={}", seq, item.getCommand(),
                item.getLatitude(), item.getLongitude(), item.getAltitude());

            // 使用MissionUtils中的转换方法
            MissionItemInt missionItemInt =
                MissionUtils.convertToMavlinkMissionItem(item, seq, config.getTargetSystemId(),
                    config.getTargetComponentId());

            // 发送任务项
            log.debug("发送整数任务项: seq={}, targetSystem={}, targetComponent={}", seq, config.getTargetSystemId(),
                config.getTargetComponentId());
            sendMessage(droneId, missionItemInt);
            log.info("已发送整数任务项: seq={}", seq);

            // 如果是最后一个航点，发送一个额外的确认
            if (seq == items.size() - 1) {
                log.info("这是最后一个整数航点请求, 主动发送一个额外的任务确认");
                sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ACCEPTED);
            }
        } catch (Exception e) {
            log.error("发送整数任务项失败: seq={}", seq, e);
            sendMissionAck(droneId, MavMissionResult.MAV_MISSION_ERROR);
        }
    }

    /**
     * 查询当前无人机航线任务
     *
     * @param droneId 无人机ID
     * @return 包含航线任务的Future对象
     */
    public CompletableFuture<List<MissionWaypoint>> getCurrentMissionItems(String droneId) {
        if (!isConnected(droneId)) {
            CompletableFuture<List<MissionWaypoint>> future = new CompletableFuture<>();
            future.completeExceptionally(new IllegalStateException("无人机未连接"));
            return future;
        }

        try {
            // 创建新的下载Future
            currentDownloadFutureMap.put(droneId, new CompletableFuture<>());

            // 清空之前的任务项缓存
            missionItemFuturesMap.get(droneId).clear();
            downloadedItemsMap.put(droneId, new ArrayList<>());

            // 请求任务列表
            MissionRequestList requestList = MissionRequestList.builder().targetSystem(config.getTargetSystemId())
                .targetComponent(config.getTargetComponentId())
                .missionType(EnumValue.of(MavMissionType.MAV_MISSION_TYPE_MISSION)).build();

            log.info("发送无人机[{}]任务列表请求", droneId);
            sendMessage(droneId, requestList);

            // 设置超时处理
            executorService.submit(() -> {
                try {
                    Thread.sleep(10000); // 10秒超时
                    if (!currentDownloadFutureMap.get(droneId).isDone()) {
                        log.warn("获取任务列表超时");
                        currentDownloadFutureMap.get(droneId)
                            .completeExceptionally(new TimeoutException("获取任务列表超时"));
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });

            return currentDownloadFutureMap.get(droneId);
        } catch (Exception e) {
            log.error("请求任务列表失败", e);
            CompletableFuture<List<MissionWaypoint>> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    /**
     * 设置飞行速度
     *
     * @param droneId 无人机ID
     * @param speed   速度值(米/秒)
     * @return 设置结果的Future对象
     */
    public CompletableFuture<Boolean> setSpeed(String droneId, float speed) {
        CompletableFuture<Boolean> result = new CompletableFuture<>();

        try {
            CommandLong command =
                CommandLong.builder().command(MavCmd.MAV_CMD_DO_CHANGE_SPEED).param1(0)       // 速度类型 (0=空速, 1=地速)
                    .param2(speed)   // 速度 [m/s]
                    .param3(-1)      // 油门 (-1 表示不变)
                    .param4(0)       // 绝对或相对 [0=绝对, 1=相对]
                    .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId()).build();

            sendCommand(droneId, command);
            log.info("已发送无人机[{}]设置速度指令: {}m/s", droneId, speed);
            result.complete(true);
        } catch (IOException e) {
            log.error("设置无人机[{}]速度指令失败: {}", droneId, e.getMessage());
            result.completeExceptionally(e);
        }

        return result;
    }

    /**
     * 定时任务：更新所有已连接无人机的状态信息到Redis
     */
    private void updateAllDroneStatusToRedis() {
        try {
            long startTime = System.currentTimeMillis();

            // 获取所有连接的无人机ID列表
            List<String> droneIds = getConnectedDrones();
            if (droneIds.isEmpty()) {
                return;
            }

            log.debug("开始更新{}架无人机状态到Redis", droneIds.size());

            // 为每个无人机创建一个异步任务
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (String droneId : droneIds) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        long droneStartTime = System.currentTimeMillis();

                        // 构建状态对象
                        DroneStatus status = buildDroneStatus(droneId);

                        long buildTime = System.currentTimeMillis();
                        log.debug("构建无人机[{}]状态对象耗时: {}ms", droneId, (buildTime - droneStartTime));

                        // 转换为JSON字符串
                        String jsonStatus = JSON.toJSONString(status);

                        // 使用与大疆相同的存储结构
                        String redisKey = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId;

                        // 存储到Redis，设置相同的过期时间
                        RedisUtils.setCacheObject(redisKey, jsonStatus, Duration.ofSeconds(expireSeconds));

                        // 发布MQTT消息到mavlink/status主题，供WebSocket处理器处理
                        publishMqttStatusMessage(droneId, jsonStatus);

                        long endTime = System.currentTimeMillis();
                        log.debug("无人机[{}]状态更新到Redis和MQTT完成，总耗时: {}ms", droneId,
                            (endTime - droneStartTime));
                    } catch (Exception e) {
                        log.error("更新无人机[{}]状态到Redis失败: {}", droneId, e.getMessage());
                    }
                }, executorService);

                futures.add(future);
            }

            // 等待所有任务完成（但设置超时，避免无限等待）
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(5, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.warn("等待所有无人机状态更新任务完成超时", e);
            }

            long endTime = System.currentTimeMillis();
            log.debug("更新所有无人机状态到Redis完成，总耗时: {}ms", (endTime - startTime));
        } catch (Exception e) {
            log.error("更新所有无人机状态到Redis失败: {}", e.getMessage());
        }
    }

    /**
     * 设置无人机姿态信息
     *
     * @param droneId  无人机ID
     * @param attitude 姿态信息
     */
    public void setLastAttitude(String droneId, Attitude attitude) {
        attitudeMap.put(droneId, attitude);
    }

    /**
     * 设置无人机飞行数据
     *
     * @param droneId 无人机ID
     * @param vfrHud  飞行数据
     */
    public void setLastVfrHud(String droneId, VfrHud vfrHud) {
        vfrHudMap.put(droneId, vfrHud);
    }

    /**
     * 设置无人机RTK海拔高度
     *
     * @param droneId  无人机ID
     * @param altitude RTK海拔高度(米)
     */
    public void setRtkAltitude(String droneId, double altitude) {
        rtkAltitudeMap.put(droneId, altitude);
    }

    /**
     * 获取无人机GPS信息
     *
     * @param droneId 无人机ID
     * @return GPS信息
     */
    public GpsRawInt getLastGpsInfo(String droneId) {
        return gpsInfoMap.get(droneId);
    }

    /**
     * 检查无人机是否不在GUIDED模式
     *
     * @param droneId 无人机ID
     * @return 是否不在GUIDED模式
     */
    private boolean isNotInGuidedMode(String droneId) {
        Heartbeat heartbeat = heartbeatMap.get(droneId);
        if (heartbeat == null) {
            return true;
        }

        // 对于ArduPilot，GUIDED模式的自定义模式值为4
        return heartbeat.customMode() != 4;
    }

    /**
     * 停止无人机速度控制
     *
     * @param droneId 无人机ID
     * @return 操作结果
     */
    public CompletableFuture<Boolean> stopVelocityControl(String droneId) {
        CompletableFuture<Boolean> result = new CompletableFuture<>();

        // 检查是否有活跃的速度控制任务
        ScheduledFuture<?> task = velocityControlTasks.remove(droneId);
        if (task != null && !task.isDone() && !task.isCancelled()) {
            task.cancel(false);
            log.info("已取消无人机[{}]的速度控制任务", droneId);
        }

        try {
            // 构建停止命令（速度设为0，保持当前偏航角）
            SetPositionTargetLocalNed stopMsg = createStopVelocityMessage(droneId);

            // 发送多次停止命令，确保无人机收到
            sendStopCommands(droneId, stopMsg);

            log.info("已发送无人机[{}]停止速度控制命令", droneId);
            result.complete(true);
        } catch (Exception e) {
            log.error("发送无人机[{}]停止速度控制命令失败: {}", droneId, e.getMessage());
            result.completeExceptionally(e);
        }

        return result;
    }

    /**
     * 创建停止速度控制的消息
     *
     * @param droneId 无人机ID
     * @return 速度设置为零的SetPositionTargetLocalNed消息
     */
    private SetPositionTargetLocalNed createStopVelocityMessage(String droneId) {
        // 获取当前偏航角
        float currentYaw = 0.0f;
        if (attitudeMap.get(droneId) != null) {
            currentYaw = attitudeMap.get(droneId).yaw();  // 偏航角，单位：弧度
        } else if (positionMap.get(droneId) != null) {
            // hdg 是以百分之一度为单位的，需要转换为弧度
            currentYaw = (float)Math.toRadians(positionMap.get(droneId).hdg() / 100.0);
        }

        return SetPositionTargetLocalNed.builder().targetSystem(config.getTargetSystemId())
            .targetComponent(config.getTargetComponentId()).coordinateFrame(EnumValue.of(MavFrame.MAV_FRAME_LOCAL_NED))
            .typeMask(EnumValue.create(PositionTargetTypemask.POSITION_TARGET_TYPEMASK_X_IGNORE,
                PositionTargetTypemask.POSITION_TARGET_TYPEMASK_Y_IGNORE,
                PositionTargetTypemask.POSITION_TARGET_TYPEMASK_Z_IGNORE,
                PositionTargetTypemask.POSITION_TARGET_TYPEMASK_AX_IGNORE,
                PositionTargetTypemask.POSITION_TARGET_TYPEMASK_AY_IGNORE,
                PositionTargetTypemask.POSITION_TARGET_TYPEMASK_AZ_IGNORE,
                PositionTargetTypemask.POSITION_TARGET_TYPEMASK_YAW_RATE_IGNORE)) // 移除YAW_IGNORE以保持偏航角控制
            .x(0).y(0).z(0).vx(0).vy(0).vz(0).afx(0).afy(0).afz(0).yaw(currentYaw) // 保持当前偏航角
            .yawRate(0).build();
    }

    /**
     * 发送多次停止命令
     *
     * @param droneId 无人机ID
     * @param stopMsg 停止命令消息
     * @throws Exception 如果发送失败或线程中断
     */
    private void sendStopCommands(String droneId, SetPositionTargetLocalNed stopMsg) throws Exception {
        // 发送多次停止命令，确保无人机收到
        for (int i = 0; i < 5; i++) {
            sendMessage(droneId, stopMsg);
            Thread.sleep(50); // 短暂等待后再次发送
        }
    }

    /**
     * 设置无人机偏航角
     *
     * @param droneId      无人机ID
     * @param yawAngle     目标偏航角，单位：度，范围：-180~180
     * @param angularSpeed 旋转速度，单位：度/秒
     * @param isRelative   是否为相对角度（相对于当前角度）
     * @return 操作结果
     */
    public CompletableFuture<Boolean> setYaw(String droneId, float yawAngle, float angularSpeed, boolean isRelative) {
        CompletableFuture<Boolean> result = new CompletableFuture<>();

        // 检查连接状态
        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法设置偏航角", droneId);
            result.completeExceptionally(new IllegalStateException("无人机未连接"));
            return result;
        }

        try {
            // 确保无人机在GUIDED模式
            if (isNotInGuidedMode(droneId)) {
                log.info("无人机[{}]不在GUIDED模式，正在切换...", droneId);
                setMode(droneId, 4); // 4 = GUIDED模式
                Thread.sleep(1000);

                if (isNotInGuidedMode(droneId)) {
                    log.error("无人机[{}]无法切换到GUIDED模式，偏航角控制失败", droneId);
                    result.completeExceptionally(new IllegalStateException("无法切换到GUIDED模式"));
                    return result;
                }
            }

            // 构建MAV_CMD_CONDITION_YAW命令
            // 参数详解：
            // param1: 目标偏航角（度）
            // param2: 旋转速度（度/秒）
            // param3: 旋转方向（-1=逆时针, 1=顺时针, 0=最短路径）
            // param4: 相对标志（0=绝对角度, 1=相对当前角度）
            // param5-7: 未使用

            // 根据角度和模式确定方向和实际角度值
            int direction;
            float actualYawAngle = yawAngle;

            if (isRelative) {
                // 相对角度模式：根据角度正负确定方向
                if (yawAngle > 0) {
                    // 正角度值，顺时针旋转
                    direction = 1;
                } else if (yawAngle < 0) {
                    // 负角度值，逆时针旋转
                    direction = -1;
                    // 在逆时针旋转时，需要使用角度的绝对值
                    actualYawAngle = Math.abs(yawAngle);
                } else {
                    // 角度为0，方向无关紧要，使用最短路径
                    direction = 0;
                }
            } else {
                // 绝对角度模式：使用最短路径
                direction = 0;
            }

            int relativeFlag = isRelative ? 1 : 0;

            CommandLong yawCommand = CommandLong.builder().command(EnumValue.of(MavCmd.MAV_CMD_CONDITION_YAW))
                .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId())
                .param1(actualYawAngle)  // 目标偏航角（绝对值）
                .param2(angularSpeed) // 旋转速度
                .param3(direction)  // 旋转方向
                .param4(relativeFlag) // 相对标志
                .param5(0) // 未使用
                .param6(0) // 未使用
                .param7(0) // 未使用
                .build();

            // 发送命令
            sendCommand(droneId, yawCommand);
            log.info("已发送无人机[{}]偏航角控制命令: angle={}, actualAngle={}, direction={}, isRelative={}, speed={}",
                droneId, yawAngle, actualYawAngle,
                direction == 1 ? "顺时针" : (direction == -1 ? "逆时针" : "最短路径"), isRelative, angularSpeed);

            // 设置短延迟，确保命令发送完成
            scheduledExecutorService.schedule(() -> result.complete(true), 500, TimeUnit.MILLISECONDS);

        } catch (Exception e) {
            log.error("无人机[{}]设置偏航角失败: {}", droneId, e.getMessage());
            result.completeExceptionally(e);
        }

        return result;
    }

    /**
     * 使用固定偏航角的速度控制
     * 该方法与moveByVelocity类似，但会保持无人机的偏航角不变
     *
     * @param droneId  无人机ID
     * @param vx       X轴速度(北向)，单位：米/秒
     * @param vy       Y轴速度(东向)，单位：米/秒
     * @param vz       Z轴速度(下向)，单位：米/秒，正值向下，负值向上
     * @param yawRad   要保持的偏航角(弧度)
     * @param duration 持续时间，单位：秒，0表示持续发送直到新命令
     * @return 操作结果
     */
    private CompletableFuture<Boolean> moveByVelocityWithFixedYaw(String droneId, float vx, float vy, float vz,
        float yawRad, float duration) {
        CompletableFuture<Boolean> result = new CompletableFuture<>();

        // 检查是否是零速度命令（停止命令）
        boolean isStopCommand = (vx == 0 && vy == 0 && vz == 0);

        // 如果是停止命令，直接调用停止方法
        if (isStopCommand) {
            return stopVelocityControl(droneId);
        }

        // 检查连接状态
        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法执行速度控制", droneId);
            result.completeExceptionally(new IllegalStateException("无人机未连接"));
            return result;
        }

        try {
            // 先取消现有的速度控制任务
            ScheduledFuture<?> existingTask = velocityControlTasks.remove(droneId);
            if (existingTask != null && !existingTask.isDone() && !existingTask.isCancelled()) {
                existingTask.cancel(false);
                log.info("已取消无人机[{}]之前的速度控制任务", droneId);
            }

            // 确保无人机在GUIDED模式
            if (isNotInGuidedMode(droneId)) {
                log.info("无人机[{}]不在GUIDED模式，正在切换...", droneId);
                setMode(droneId, 4); // 4 = GUIDED模式
                Thread.sleep(1000);

                if (isNotInGuidedMode(droneId)) {
                    log.error("无人机[{}]无法切换到GUIDED模式，移动控制失败", droneId);
                    result.completeExceptionally(new IllegalStateException("无法切换到GUIDED模式"));
                    return result;
                }
            }

            // 确保scheduledExecutorService可用
            if (scheduledExecutorService == null || scheduledExecutorService.isShutdown()) {
                log.info("重新初始化scheduledExecutorService");
                scheduledExecutorService = Executors.newScheduledThreadPool(4);
            }

            // 构建SET_POSITION_TARGET_LOCAL_NED消息
            // 类型掩码: 使用速度控制和偏航角控制，禁用位置控制和加速度控制
            // 注意：移除了POSITION_TARGET_TYPEMASK_YAW_IGNORE，以允许控制偏航角
            SetPositionTargetLocalNed targetMsg =
                SetPositionTargetLocalNed.builder().targetSystem(config.getTargetSystemId())
                    .targetComponent(config.getTargetComponentId())
                    .coordinateFrame(EnumValue.of(MavFrame.MAV_FRAME_LOCAL_NED)).typeMask(
                        EnumValue.create(PositionTargetTypemask.POSITION_TARGET_TYPEMASK_X_IGNORE,
                            PositionTargetTypemask.POSITION_TARGET_TYPEMASK_Y_IGNORE,
                            PositionTargetTypemask.POSITION_TARGET_TYPEMASK_Z_IGNORE,
                            PositionTargetTypemask.POSITION_TARGET_TYPEMASK_AX_IGNORE,
                            PositionTargetTypemask.POSITION_TARGET_TYPEMASK_AY_IGNORE,
                            PositionTargetTypemask.POSITION_TARGET_TYPEMASK_AZ_IGNORE,
                            PositionTargetTypemask.POSITION_TARGET_TYPEMASK_YAW_RATE_IGNORE)) // 保留yaw控制，忽略yaw_rate
                    .x(0)      // 位置 X (不使用)
                    .y(0)      // 位置 Y (不使用)
                    .z(0)      // 位置 Z (不使用)
                    .vx(vx)    // 速度 X
                    .vy(vy)    // 速度 Y
                    .vz(vz)    // 速度 Z
                    .afx(0)    // 加速度 X (不使用)
                    .afy(0)    // 加速度 Y (不使用)
                    .afz(0)    // 加速度 Z (不使用)
                    .yaw(yawRad) // 偏航角 (弧度)
                    .yawRate(0) // 偏航角速率 (不使用)
                    .build();

            // 创建一个原子引用以便在取消任务时使用
            final AtomicReference<ScheduledFuture<?>> commandTaskRef = new AtomicReference<>();

            // 创建一个任务，定期发送速度控制命令
            Runnable commandTask = () -> {
                try {
                    sendMessage(droneId, targetMsg);
                } catch (Exception e) {
                    log.error("无人机[{}]发送速度控制命令失败: {}", droneId, e.getMessage());
                    // 如果发送失败，尝试取消定时任务
                    ScheduledFuture<?> task = commandTaskRef.get();
                    if (task != null) {
                        task.cancel(false);
                    }
                    // 从任务映射中移除
                    velocityControlTasks.remove(droneId);
                }
            };

            // 以10Hz的频率发送命令（100毫秒发送一次）
            final ScheduledFuture<?> scheduledTask =
                scheduledExecutorService.scheduleAtFixedRate(commandTask, 0, 100, TimeUnit.MILLISECONDS);

            // 存储任务引用，以便在需要时取消
            commandTaskRef.set(scheduledTask);

            log.info(
                "开始以{}Hz的频率发送无人机[{}]速度控制命令(固定偏航角): vx={}, vy={}, vz={}, yaw={}度, duration={}",
                10, droneId, vx, vy, vz, Math.toDegrees(yawRad), duration);

            // 如果指定了持续时间，则在指定时间后停止
            if (duration > 0) {
                scheduledExecutorService.schedule(() -> {
                    try {
                        // 从任务映射中移除
                        velocityControlTasks.remove(droneId);

                        // 取消定期发送命令的任务
                        scheduledTask.cancel(false);

                        // 发送停止命令
                        SetPositionTargetLocalNed stopMsg = createStopVelocityMessage(droneId);

                        // 发送多次停止命令，确保无人机收到
                        sendStopCommands(droneId, stopMsg);

                        log.info("已发送无人机[{}]停止移动命令，持续时间{}秒已到", droneId, duration);
                        result.complete(true);
                    } catch (Exception e) {
                        log.error("无人机[{}]发送停止命令失败: {}", droneId, e.getMessage());
                        result.completeExceptionally(e);
                    }
                }, (long)(duration * 1000), TimeUnit.MILLISECONDS);
            } else {
                // 如果duration=0，表示持续发送直到手动停止
                // 将任务保存到映射中，以便后续可以手动停止
                velocityControlTasks.put(droneId, scheduledTask);
                log.info("无人机[{}]速度控制将持续进行，直到手动停止", droneId);
                result.complete(true);
            }
        } catch (Exception e) {
            log.error("无人机[{}]执行速度控制失败: {}", droneId, e.getMessage());
            result.completeExceptionally(e);
        }

        return result;
    }

    /**
     * 根据机头方向控制无人机移动
     *
     * @param droneId  无人机ID
     * @param vForward 前后速度，单位：米/秒，正值向前(机头方向)，负值向后
     * @param vRight   左右速度，单位：米/秒，正值向右，负值向左
     * @param vUp      上下速度，单位：米/秒，正值向上，负值向下
     * @param duration 持续时间，单位：秒，0表示持续发送直到新命令
     * @return 操作结果
     */
    public CompletableFuture<Boolean> moveRelativeToHeading(String droneId, float vForward, float vRight, float vUp,
        float duration) {
        CompletableFuture<Boolean> result = new CompletableFuture<>();

        // 检查连接状态
        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法执行相对机头方向的移动控制", droneId);
            result.completeExceptionally(new IllegalStateException("无人机未连接"));
            return result;
        }

        try {
            // 获取当前偏航角（获取失败则默认为0，即正北方向）
            float yawRad = 0.0f;
            boolean useAttitude = false;

            // 首先尝试从 Attitude 获取偏航角（更精确）
            if (attitudeMap.get(droneId) != null) {
                yawRad = attitudeMap.get(droneId).yaw();  // 偏航角，单位：弧度
                useAttitude = true;
            }
            // 然后尝试从 GlobalPositionInt 获取航向角
            else if (positionMap.get(droneId) != null) {
                // hdg 是以百分之一度为单位的，需要转换为弧度
                yawRad = (float)Math.toRadians(positionMap.get(droneId).hdg() / 100.0);
            }

            // 记录使用了哪种方式获取偏航角
            log.info("无人机[{}]当前偏航角: {}度，数据来源: {}", droneId, Math.toDegrees(yawRad),
                useAttitude ? "姿态信息" : "位置信息");

            // 执行坐标转换 - 从相对机头坐标系转换到 NED 坐标系
            float cosYaw = (float)Math.cos(yawRad);
            float sinYaw = (float)Math.sin(yawRad);

            // 北向分量 vx = vForward * cos(yaw) + vRight * sin(yaw)
            float vx = vForward * cosYaw + vRight * sinYaw;

            // 东向分量 vy = vForward * sin(yaw) - vRight * cos(yaw)
            float vy = vForward * sinYaw - vRight * cosYaw;

            // 下向分量 vz (MAVLink中，正值向下，负值向上，所以需要反转用户输入的符号)
            float vz = -vUp;

            log.info("无人机[{}]相对机头方向移动: 前进={}, 右移={}, 上升={} → NED坐标系: 北向={}, 东向={}, 下向={}",
                droneId, vForward, vRight, vUp, vx, vy, vz);

            // 调用带有固定偏航角的速度控制方法执行实际移动
            return moveByVelocityWithFixedYaw(droneId, vx, vy, vz, yawRad, duration);

        } catch (Exception e) {
            log.error("无人机[{}]执行相对机头方向的移动控制失败: {}", droneId, e.getMessage());
            result.completeExceptionally(e);
        }

        return result;
    }

    /**
     * 控制云台
     *
     * @param droneId    无人机ID
     * @param pitch      俯仰角（度），负值向下，正值向上
     * @param yaw        偏航角（度），从北顺时针方向
     * @param isAbsolute 是否为绝对角度模式
     * @return 操作结果
     */
    public CompletableFuture<Boolean> controlGimbal(String droneId, float pitch, float yaw, boolean isAbsolute) {
        CompletableFuture<Boolean> result = new CompletableFuture<>();

        // 检查连接状态
        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法控制云台", droneId);
            result.completeExceptionally(new IllegalStateException("无人机未连接"));
            return result;
        }

        try {
            // 构建MAV_CMD_DO_GIMBAL_MANAGER_PITCHYAW命令
            // 参数详解：
            // param1: 俯仰角（度，正值向上，相对于载具为FOLLOW模式，相对于地平线为LOCK模式）
            // param2: 偏航角（度，正值向右，相对于载具为FOLLOW模式，相对于北方为LOCK模式）
            // param3: 俯仰角速率（度/秒，正值向上）
            // param4: 偏航角速率（度/秒，正值向右）
            // param5: 云台管理器标志
            //   GIMBAL_MANAGER_FLAGS_RETRACT = 1 - 收起云台
            //   GIMBAL_MANAGER_FLAGS_NEUTRAL = 2 - 中立位置
            //   GIMBAL_MANAGER_FLAGS_ROLL_LOCK = 4 - 锁定横滚角
            //   GIMBAL_MANAGER_FLAGS_PITCH_LOCK = 8 - 锁定俯仰角
            //   GIMBAL_MANAGER_FLAGS_YAW_LOCK = 16 - 锁定偏航角
            // param7: 云台设备组件ID（1-6为非MAVLink云台），0表示所有云台设备组件

            // 设置云台管理器标志
            int gimbalManagerFlags = 0;
            if (isAbsolute) {
                // 使用锁定模式（绝对角度）
                gimbalManagerFlags |= 8; // GIMBAL_MANAGER_FLAGS_PITCH_LOCK
                gimbalManagerFlags |= 16; // GIMBAL_MANAGER_FLAGS_YAW_LOCK
            }

            CommandLong gimbalCommand =
                CommandLong.builder().command(EnumValue.of(MavCmd.MAV_CMD_DO_GIMBAL_MANAGER_PITCHYAW))
                    .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId())
                    .param1(pitch)  // 俯仰角
                    .param2(yaw)    // 偏航角
                    .param3(0)      // 俯仰角速率（不使用）
                    .param4(0)      // 偏航角速率（不使用）
                    .param5(gimbalManagerFlags) // 云台管理器标志
                    .param6(0)      // 未使用
                    .param7(0)      // 组件ID，0表示所有云台设备
                    .build();

            // 发送命令
            sendCommand(droneId, gimbalCommand);
            log.info("已发送无人机[{}]云台控制命令(新版): pitch={}, yaw={}, flags={}, mode={}", droneId, pitch, yaw,
                gimbalManagerFlags, isAbsolute ? "absolute" : "relative");

            // 设置短延迟，确保命令发送完成
            scheduledExecutorService.schedule(() -> result.complete(true), 500, TimeUnit.MILLISECONDS);

        } catch (Exception e) {
            log.error("无人机[{}]云台控制失败: {}", droneId, e.getMessage());
            result.completeExceptionally(e);
        }

        return result;
    }

    /**
     * 拍摄一张照片
     *
     * @param droneId 无人机ID
     */
    public void takePhoto(String droneId) {
        // 检查连接状态
        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法执行拍照", droneId);
            throw new IllegalStateException("无人机未连接");
        }

        try {
            // 创建相机控制命令 - MAV_CMD_DO_DIGICAM_CONTROL
            CommandLong cameraCommand = CommandLong.builder().command(EnumValue.of(MavCmd.MAV_CMD_DO_DIGICAM_CONTROL))
                .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId())
                .param1(0)   // 触发相机
                .param2(0)   // 快门速度
                .param3(1)   // 触发一次
                .param4(0)   // 未使用
                .param5(1)   // 拍照(1)而非录像(0)
                .param6(0)   // 未使用
                .param7(0)   // 未使用
                .build();

            sendCommand(droneId, cameraCommand);
            log.info("已发送无人机[{}]拍照命令", droneId);
        } catch (Exception e) {
            log.error("无人机[{}]拍照失败: {}", droneId, e.getMessage());
            throw new RuntimeException("拍照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 开始连续拍照
     *
     * @param droneId  无人机ID
     * @param interval 拍摄间隔（秒）
     * @param count    拍摄张数，0表示无限拍摄直到收到停止命令
     */
    public void startContinuousPhotos(String droneId, float interval, int count) {
        // 检查连接状态
        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法执行连续拍照", droneId);
            throw new IllegalStateException("无人机未连接");
        }

        try {
            // 设置触发间隔 - MAV_CMD_DO_SET_CAM_TRIGG_INTERVAL
            CommandLong intervalCommand =
                CommandLong.builder().command(EnumValue.of(MavCmd.MAV_CMD_DO_SET_CAM_TRIGG_INTERVAL))
                    .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId())
                    .param1(interval * 1000)  // 触发间隔（毫秒）
                    .param2(count)            // 总触发次数，0表示无限次
                    .build();

            sendCommand(droneId, intervalCommand);
            log.info("已发送无人机[{}]开始连续拍照命令，间隔: {}秒，数量: {}", droneId, interval,
                count == 0 ? "无限" : count);
        } catch (Exception e) {
            log.error("无人机[{}]开始连续拍照失败: {}", droneId, e.getMessage());
            throw new RuntimeException("开始连续拍照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 停止连续拍照
     *
     * @param droneId 无人机ID
     */
    public void stopContinuousPhotos(String droneId) {
        // 检查连接状态
        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法停止连续拍照", droneId);
            throw new IllegalStateException("无人机未连接");
        }

        try {
            // 设置触发间隔为0，停止连续拍照
            CommandLong stopCommand =
                CommandLong.builder().command(EnumValue.of(MavCmd.MAV_CMD_DO_SET_CAM_TRIGG_INTERVAL))
                    .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId())
                    .param1(0)  // 触发间隔为0表示停止
                    .param2(0)  // 触发次数不再重要
                    .build();

            sendCommand(droneId, stopCommand);
            log.info("已发送无人机[{}]停止连续拍照命令", droneId);
        } catch (Exception e) {
            log.error("无人机[{}]停止连续拍照失败: {}", droneId, e.getMessage());
            throw new RuntimeException("停止连续拍照失败: " + e.getMessage(), e);
        }
    }

    /**
     * 开始录像
     *
     * @param droneId 无人机ID
     */
    public void startVideoRecording(String droneId) {
        // 检查连接状态
        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法开始录像", droneId);
            throw new IllegalStateException("无人机未连接");
        }

        try {
            // 开始录像命令 - MAV_CMD_VIDEO_START_CAPTURE
            CommandLong startVideoCommand =
                CommandLong.builder().command(EnumValue.of(MavCmd.MAV_CMD_VIDEO_START_CAPTURE))
                    .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId())
                    .param1(0)  // 摄像头ID (0 = 主摄像头)
                    .param2(0)  // 流ID (0 = 默认)
                    .build();

            sendCommand(droneId, startVideoCommand);
            log.info("已发送无人机[{}]开始录像命令", droneId);
        } catch (Exception e) {
            log.error("无人机[{}]开始录像失败: {}", droneId, e.getMessage());
            throw new RuntimeException("开始录像失败: " + e.getMessage(), e);
        }
    }

    /**
     * 停止录像
     *
     * @param droneId 无人机ID
     */
    public void stopVideoRecording(String droneId) {
        // 检查连接状态
        if (!isConnected(droneId)) {
            log.error("无人机[{}]未连接，无法停止录像", droneId);
            throw new IllegalStateException("无人机未连接");
        }

        try {
            // 停止录像命令 - MAV_CMD_VIDEO_STOP_CAPTURE
            CommandLong stopVideoCommand =
                CommandLong.builder().command(EnumValue.of(MavCmd.MAV_CMD_VIDEO_STOP_CAPTURE))
                    .targetSystem(config.getTargetSystemId()).targetComponent(config.getTargetComponentId())
                    .param1(0)  // 摄像头ID (0 = 主摄像头)
                    .param2(0)  // 流ID (0 = 默认)
                    .build();

            sendCommand(droneId, stopVideoCommand);
            log.info("已发送无人机[{}]停止录像命令", droneId);
        } catch (Exception e) {
            log.error("无人机[{}]停止录像失败: {}", droneId, e.getMessage());
            throw new RuntimeException("停止录像失败: " + e.getMessage(), e);
        }
    }

    /**
     * 发布MAVLink状态消息到MQTT主题
     *
     * @param droneId    无人机ID
     * @param jsonStatus 状态JSON字符串
     */
    private void publishMqttStatusMessage(String droneId, String jsonStatus) {
        try {
            // 发布到mavlink/status主题，供统一的WebSocket处理器处理
            mqttMessageHandler.sendToMqtt("mavlink/status", jsonStatus);
            log.debug("MAVLink无人机[{}]状态已发布到MQTT主题: mavlink/status", droneId);
        } catch (Exception e) {
            log.error("发布MAVLink无人机[{}]状态到MQTT失败: {}", droneId, e.getMessage(), e);
        }
    }
}
