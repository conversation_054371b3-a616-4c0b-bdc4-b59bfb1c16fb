package com.md.utils;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;

public class RSAUtil {

    /**
     * 加密
     *
     * @param plaintext    需要加密的明文
     * @param publicKeyStr RSA公钥的base64加密串
     * @return 加密内容的base64加密串
     */
    public static String encrypt(String plaintext, String publicKeyStr) throws Exception {
        RSA rsa = new RSA(null, publicKeyStr);
        // 公钥加密
        return rsa.encryptBase64(plaintext, KeyType.PublicKey);
    }

    /**
     * 加密
     *
     * @param ciphertext    需要解密的base64密文
     * @param privateKeyStr RSA私钥钥的base64加密串
     * @return 数据明文
     */
    public static String decrypt(String ciphertext, String privateKeyStr) throws Exception {
        RSA rsa = new RSA(privateKeyStr, null);
        // 私钥解密
        return rsa.decryptStr(ciphertext, KeyType.PrivateKey);
    }
}
