package com.md.mongodb.document;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 飞行轨迹点实体类 用于记录无人机飞行过程中的位置、姿态等状态信息 数据存储在MongoDB中
 */
@Data
@Document(collection = "flight_track_points")
@CompoundIndexes({@CompoundIndex(name = "task_time_idx", def = "{'taskId': 1, 'timestamp': 1}")  // 任务ID和时间戳的复合索引，用于快速查询
})
public class FlightTrackPoint {
    @Id
    private String id;

    private String taskId;        // 任务ID
    private String droneId;       // 无人机ID
    private Long timestamp;       // 时间戳，记录数据采集时间
    private Double latitude;      // 纬度，单位：度
    private Double longitude;     // 经度，单位：度
    private Double altitude;      // 海拔高度，单位：米
    private Double relativeHeight;// 相对高度（相对起飞点），单位：米
    private Double speed;         // 速度，单位：米/秒
    private Double airSpeed;      // 空速，单位：米/秒
    private Double groundSpeed;   // 地速，单位：米/秒
    private Double flightDistance;// 飞行距离，单位：米
    private Double heading;       // 航向角，单位：度，范围0-360
    private Double pitch;         // 俯仰角，单位：度
    private Double roll;          // 横滚角，单位：度
    private String flightMode;    // 飞行模式，如：定点模式、航线模式等
    private BatteryInfo batteryInfo; // 电池信息
    private String missionStatus; // 任务状态，如：执行中、已完成等
    
    // RTK相关信息
    private Boolean isRTKConnected;  // RTK是否连接
    private Boolean isRTKEnabled;    // RTK是否启用
    private Boolean isRTKHealthy;    // RTK是否健康
    private Double rtkAltitude;      // RTK高度
    private Integer rtkSatelliteCount; // RTK卫星数量
    
    // 卫星信息
    private SatelliteInfo satelliteInfo; // 卫星信息

    /**
     * 电池信息内部类 记录无人机电池的详细状态
     */
    @Data
    public static class BatteryInfo {
        private Integer chargeRemaining;           // 剩余电量，单位：毫安时
        private Integer chargeRemainingInPercent;  // 剩余电量百分比，范围：0-100
        private Integer totalCapacity;             // 电池总容量，单位：毫安时
    }
    
    /**
     * 卫星信息内部类 记录无人机卫星接收状态
     */
    @Data
    public static class SatelliteInfo {
        private Integer baseStationCount;     // 基站卫星数量
        private Integer gpsCount;             // GPS卫星数量 
        private Integer mobileStation1Count;  // 移动站1卫星数量
        private Integer mobileStation2Count;  // 移动站2卫星数量
    }
}