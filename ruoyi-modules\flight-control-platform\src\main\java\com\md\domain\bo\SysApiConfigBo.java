package com.md.domain.bo;


import com.md.domain.po.SysApiConfig;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 接口管理业务对象 sys_api_config
 *
 *
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysApiConfig.class, reverseConvertGenerate = false)
public class SysApiConfigBo extends BaseEntity {

    /**
     * 接口ID
     */
    private Long id;

    /**
     * 目标平台
     */
    @NotBlank(message = "目标平台不能为空")
    @Size(min = 0, max = 100, message = "目标平台长度不能超过{max}个字符")
    private String targetPlatform;

    /**
     * 访问地址
     */
    @NotBlank(message = "访问地址不能为空")
    @Size(min = 0, max = 500, message = "访问地址长度不能超过{max}个字符")
    private String url;


    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 配置信息
     */
    private String encryptInfo;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

}
