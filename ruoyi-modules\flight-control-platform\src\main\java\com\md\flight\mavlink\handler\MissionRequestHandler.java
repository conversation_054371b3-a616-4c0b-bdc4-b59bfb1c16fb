package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.MissionRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务请求处理器
 * 处理MAVLink任务请求消息
 */
@Slf4j
public class MissionRequestHandler extends AbstractMavlinkHandler<MissionRequest> {

    public MissionRequestHandler(MavlinkCoreService coreService) {
        super(coreService);
    }
    
    @Override
    protected void doHandle(String droneId, MissionRequest message) {
        log.info("收到无人机[{}]任务请求: seq={}", droneId, message.seq());
        coreService.handleMissionRequest(droneId, message);
    }

    @Override
    public Class<MissionRequest> getMessageType() {
        return MissionRequest.class;
    }
}