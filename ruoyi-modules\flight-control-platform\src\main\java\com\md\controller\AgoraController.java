package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.md.service.IAgoraTokenService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Agora Token管理接口
 */
@Slf4j
@RestController
@RequestMapping("/api/agora")
public class AgoraController {

    @Autowired
    private IAgoraTokenService agoraTokenService;

    /**
     * 获取直播Token
     */
    @SaCheckPermission(value = {"flight:livestream:config", "flight:task:execute"}, mode = SaMode.OR)
    @GetMapping("/channel/token")
    public R<Object> getToken(@RequestParam String droneSN, @RequestParam(defaultValue = "0") String uid) {
        return agoraTokenService.getLiveToken(droneSN, uid);
    }

    /**
     * 查询直播房间状态
     */
    @SaCheckPermission(value = {"flight:livestream:list", "flight:task:execute"}, mode = SaMode.OR)
    @GetMapping("/channel/status")
    public R<Object> getChannelStatus(@RequestParam String droneSN) {
        return agoraTokenService.getChannelStatus(droneSN);
    }

    /**
     * 关闭直播房间
     */
    @SaCheckPermission("flight:livestream:config")
    @PostMapping("/channel/close")
    public R<Object> closeChannel(@RequestParam String droneSN) {
        return agoraTokenService.closeChannel(droneSN);
    }
}
