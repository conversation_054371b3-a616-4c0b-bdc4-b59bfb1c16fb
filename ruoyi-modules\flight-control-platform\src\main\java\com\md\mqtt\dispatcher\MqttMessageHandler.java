package com.md.mqtt.dispatcher;

import com.alibaba.fastjson2.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.annotation.MessagingGateway;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

@Component
@MessagingGateway(defaultRequestChannel = "mqttOutboundChannel")
public class MqttMessageHandler {

    @Autowired
    private MessageChannel mqttOutboundChannel;

    public void sendToMqttObject(String topic, byte[] payload) {
        Message<byte[]> message = MessageBuilder.withPayload(payload)
            .setHeader(MqttHeaders.TOPIC, topic)
            .build();
        mqttOutboundChannel.send(message);
    }

    public void sendToMqtt(String topic, String payload) {
        Message<String> message = MessageBuilder.withPayload(payload)
            .setHeader(MqttHeaders.TOPIC, topic)
            .build();
        mqttOutboundChannel.send(message);
    }

    public void sendToMqtt(String topic, int qos, String payload) {
        Message<String> message = MessageBuilder.withPayload(payload)
            .setHeader(MqttHeaders.TOPIC, topic)
            .setHeader(MqttHeaders.QOS, qos)
            .build();
        mqttOutboundChannel.send(message);
    }

    public void sendToMqtt(String topic, Object payload) {
        String json = JSON.toJSONString(payload);
        Message<String> message = MessageBuilder.withPayload(json)
            .setHeader(MqttHeaders.TOPIC, topic)
            .build();
        mqttOutboundChannel.send(message);
    }

    public void sendToMqtt(String topic, byte[] payload) {
        Message<byte[]> message = MessageBuilder.withPayload(payload)
            .setHeader(MqttHeaders.TOPIC, topic)
            .build();
        mqttOutboundChannel.send(message);
    }

    public void sendToMqtt(Message<?> message){
        mqttOutboundChannel.send(message);
    }
}
