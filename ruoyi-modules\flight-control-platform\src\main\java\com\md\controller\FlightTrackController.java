package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.domain.vo.FlightTrackPointVo;
import com.md.service.IFlightTrackService;
import jakarta.validation.constraints.NotBlank;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 飞行轨迹控制器 提供轨迹数据的查询接口
 */
@RestController
@RequestMapping("/flight/track")
@Validated
public class FlightTrackController extends BaseController {

    @Autowired
    private IFlightTrackService flightTrackService;

    /**
     * 获取指定任务的所有轨迹点
     *
     * @param taskId 任务ID
     * @return 轨迹点列表
     */
    @SaCheckPermission("flight:track:list")
    @GetMapping("/list/{taskId}")
    public R<List<FlightTrackPointVo>> getTaskTrackPoints(
        @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        List<FlightTrackPointVo> trackPoints = flightTrackService.getTaskTrackPoints(taskId);
        return R.ok(trackPoints);
    }

    /**
     * 获取指定任务的所有轨迹点（分页版本）
     *
     * @param taskId    任务ID
     * @param pageQuery 分页参数
     * @return 轨迹点分页列表
     */
    @SaCheckPermission("flight:track:list")
    @GetMapping("/page/{taskId}")
    public TableDataInfo<FlightTrackPointVo> getTaskTrackPointsPage(
        @PathVariable @NotBlank(message = "任务ID不能为空") String taskId, PageQuery pageQuery) {
        return flightTrackService.getTaskTrackPointsPage(taskId, pageQuery);
    }

    /**
     * 获取指定任务在给定时间范围内的轨迹点
     *
     * @param taskId    任务ID
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 符合条件的轨迹点列表
     */
    @SaCheckPermission("flight:track:list")
    @GetMapping("/list/range/{taskId}")
    public R<List<FlightTrackPointVo>> getTaskTrackPointsByTimeRange(
        @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
        @RequestParam @NotBlank(message = "开始时间不能为空") String startTime,
        @RequestParam @NotBlank(message = "结束时间不能为空") String endTime) {
        try {
            Long startTimestamp = parseDate(startTime);
            Long endTimestamp = parseDate(endTime);

            List<FlightTrackPointVo> trackPoints =
                flightTrackService.getTaskTrackPointsByTimeRange(taskId, startTimestamp, endTimestamp);
            return R.ok(trackPoints);
        } catch (Exception e) {
            return R.fail("日期格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
        }
    }

    /**
     * 获取指定任务在给定时间范围内的轨迹点（分页版本）
     *
     * @param taskId    任务ID
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param pageQuery 分页参数
     * @return 符合条件的轨迹点分页列表
     */
    @SaCheckPermission("flight:track:list")
    @GetMapping("/page/range/{taskId}")
    public TableDataInfo<FlightTrackPointVo> getTaskTrackPointsByTimeRangePage(
        @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
        @RequestParam @NotBlank(message = "开始时间不能为空") String startTime,
        @RequestParam @NotBlank(message = "结束时间不能为空") String endTime, PageQuery pageQuery) {
        try {
            Long startTimestamp = parseDate(startTime);
            Long endTimestamp = parseDate(endTime);

            return flightTrackService.getTaskTrackPointsByTimeRangePage(taskId, startTimestamp, endTimestamp,
                pageQuery);
        } catch (Exception e) {
            throw new RuntimeException("日期格式错误，请使用yyyy-MM-dd HH:mm:ss格式");
        }
    }

    /**
     * 获取指定无人机的所有轨迹点
     *
     * @param droneId 无人机ID
     * @return 轨迹点列表
     */
    @SaCheckPermission("flight:track:list")
    @GetMapping("/drone/{droneId}")
    public R<List<FlightTrackPointVo>> getDroneTrackPoints(
        @PathVariable @NotBlank(message = "无人机ID不能为空") String droneId) {
        List<FlightTrackPointVo> trackPoints = flightTrackService.getDroneTrackPoints(droneId);
        return R.ok(trackPoints);
    }

    /**
     * 获取指定无人机的所有轨迹点（分页版本）
     *
     * @param droneId   无人机ID
     * @param pageQuery 分页参数
     * @return 轨迹点分页列表
     */
    @SaCheckPermission("flight:track:list")
    @GetMapping("/drone/page/{droneId}")
    public TableDataInfo<FlightTrackPointVo> getDroneTrackPointsPage(
        @PathVariable @NotBlank(message = "无人机ID不能为空") String droneId, PageQuery pageQuery) {
        return flightTrackService.getDroneTrackPointsPage(droneId, pageQuery);
    }

    /**
     * 获取指定任务和无人机的所有轨迹点
     *
     * @param taskId  任务ID
     * @param droneId 无人机ID
     * @return 轨迹点列表
     */
    @SaCheckPermission("flight:track:list")
    @GetMapping("/list/{taskId}/{droneId}")
    public R<List<FlightTrackPointVo>> getTaskDroneTrackPoints(
        @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
        @PathVariable @NotBlank(message = "无人机ID不能为空") String droneId) {
        List<FlightTrackPointVo> trackPoints = flightTrackService.getTaskDroneTrackPoints(taskId, droneId);
        return R.ok(trackPoints);
    }

    /**
     * 获取指定任务的最新轨迹点
     *
     * @param taskId 任务ID
     * @return 最新轨迹点
     */
    @SaCheckPermission("flight:track:list")
    @GetMapping("/latest/{taskId}")
    public R<FlightTrackPointVo> getTaskLatestTrackPoint(
        @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        FlightTrackPointVo trackPoint = flightTrackService.getTaskLatestTrackPoint(taskId);
        return R.ok(trackPoint);
    }

    /**
     * 获取指定无人机的最新轨迹点
     *
     * @param droneId 无人机ID
     * @return 最新轨迹点
     */
    @SaCheckPermission("flight:track:list")
    @GetMapping("/drone/latest/{droneId}")
    public R<FlightTrackPointVo> getDroneLatestTrackPoint(
        @PathVariable @NotBlank(message = "无人机ID不能为空") String droneId) {
        FlightTrackPointVo trackPoint = flightTrackService.getDroneLatestTrackPoint(droneId);
        return R.ok(trackPoint);
    }

    /**
     * 删除指定任务的所有轨迹点
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    @SaCheckPermission("flight:track:remove")
    @Log(title = "飞行轨迹", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskId}")
    public R<Void> deleteTaskTrackPoints(@PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        flightTrackService.deleteTrackPoints(taskId);
        return R.ok();
    }

    /**
     * 删除指定任务和无人机的所有轨迹点
     *
     * @param taskId  任务ID
     * @param droneId 无人机ID
     * @return 操作结果
     */
    @SaCheckPermission("flight:track:remove")
    @Log(title = "飞行轨迹", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskId}/{droneId}")
    public R<Void> deleteTaskDroneTrackPoints(@PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
        @PathVariable @NotBlank(message = "无人机ID不能为空") String droneId) {
        flightTrackService.deleteTrackPointsByTaskIdAndDroneId(taskId, droneId);
        return R.ok();
    }

    /**
     * 解析日期字符串为时间戳
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd HH:mm:ss）
     * @return 时间戳（毫秒）
     * @throws Exception 解析异常
     */
    private Long parseDate(String dateStr) throws Exception {
        return DateUtils.parseDate(dateStr).getTime();
    }
}
