package com.md.flight.yz.openfeign;

import com.md.flight.yz.domain.dto.YzBaseReqDto;
import com.md.flight.yz.openfeign.fallback.YzQueryBaseInfoClientFallback;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 获取亿航无人机基础信息
 */
@FeignClient(
        name = "queryBaseInfoClient",
        url = "${yz-remote-call.url}",
        fallbackFactory = YzQueryBaseInfoClientFallback.class)
public interface YzQueryBaseInfoClient {
    @PostMapping(value = "${yz-remote-call.queryBaseInfo}")
    @Headers({"Content-Type: application/json;charset=UTF-8"})
    String queryBaseInfo(@RequestBody YzBaseReqDto baseReqDto, @RequestHeader("X-Access-Token") String token);
}
