package com.md.websocket.converter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.flight.px4.converter.Px4StatusConverter;
import com.md.websocket.message.DroneMessage;
import com.md.websocket.message.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * PX4无人机消息转换器
 */
@Slf4j
@Component
public class Px4MessageConverter implements DroneMessageConverter {

    private static final String MANUFACTURER = "PX4";

    @Autowired
    private Px4StatusConverter px4StatusConverter;

    @Override
    public DroneMessage convert(String topic, String payload, MessageType messageType) {
        try {
            JSONObject px4Data = JSON.parseObject(payload);
            String droneId = extractDroneId(px4Data, topic);

            // 将PX4嵌套数据转换为统一的DroneStatus格式
            DroneStatus unifiedStatus = px4StatusConverter.convertToUnifiedFormat(px4Data);

            return DroneMessage.builder().type(messageType.name()).droneId(droneId).data(unifiedStatus)  // 使用转换后的统一格式
                .timestamp(System.currentTimeMillis()).topic(topic).manufacturer(MANUFACTURER).build();

        } catch (Exception e) {
            log.error("PX4消息转换失败: topic={}, error={}", topic, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean supports(String manufacturer) {
        return MANUFACTURER.equals(manufacturer);
    }

    @Override
    public String getManufacturer() {
        return MANUFACTURER;
    }

    /**
     * 从消息数据或主题中提取无人机ID
     *
     * @param data  消息数据
     * @param topic MQTT主题
     * @return 无人机ID
     */
    private String extractDroneId(JSONObject data, String topic) {
        // 优先从消息数据中获取
        String droneId = data.getString("droneSN");
        if (droneId == null || droneId.isEmpty()) {
            droneId = data.getString("droneId");
        }

        // 如果消息中没有，从主题中提取
        // 主题格式：px4/{droneId}/status
        if (droneId == null || droneId.isEmpty()) {
            droneId = extractDroneIdFromTopic(topic);
        }

        return droneId;
    }

    /**
     * 从主题中提取无人机ID
     * 例如：从 "px4/drone_001/status" 中提取 "drone_001"
     *
     * @param topic MQTT主题
     * @return 无人机ID
     */
    private String extractDroneIdFromTopic(String topic) {
        try {
            String[] parts = topic.split("/");
            if (parts.length >= 3 && "px4".equals(parts[0])) {
                return parts[1];
            }
        } catch (Exception e) {
            log.warn("从主题提取无人机ID失败: topic={}, error={}", topic, e.getMessage());
        }
        return null;
    }
}
