package com.md.domain.po;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 无人机对象 fk_uav_info
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "uav_info")
public class UavInfo extends BaseEntity {
    /**
     * 无人机ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 机身码
     */
    @TableField(value = "uav_code")
    private String uavCode;

    /**
     * 无人机类型编码
     */
    @TableField(value = "category_code")
    private String categoryCode;

    /**
     * 飞控平台名称
     */
    @TableField(value = "manufacturer_name")
    private String manufacturerName;

    /**
     * 飞控平台编码
     */
    @TableField(value = "flight_control_no")
    private String flightControlNo;

    /**
     * 最大承重
     */
    @TableField(value = "max_load")
    private Integer maxLoad;

    /**
     * 数据来源
     */
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 飞行器名称
     */
    @TableField(value = "uav_name")
    private String uavName;

    /**
     * 设备型号
     */
    @TableField(value = "device_model")
    private String deviceModel;

    /**
     * 电池SN
     */
    @TableField(value = "battery_sn")
    private String batterySn;

    /**
     * 是否启用（0正常  1停用）
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 是否删除（0正常  2删除）
     */
    @TableField(value = "del_flag")
    private Integer delFlag;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 批次号
     */
    @TableField(value = "batch_id")
    private String batchId;

    /**
     * 同步时间
     */
    @TableField(value = "sync_time")
    private Date syncTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * HTTP地址端口
     */
    @TableField(value = "http_endpoint")
    private String httpEndpoint;

    /**
     * MQTT地址端口
     */
    @TableField(value = "mqtt_endpoint")
    private String mqttEndpoint;

    /**
     * MAVLink连接IP地址
     */
    @TableField(value = "mavlink_ip")
    private String mavlinkIp;

    /**
     * MAVLink连接端口
     */
    @TableField(value = "mavlink_port")
    private Integer mavlinkPort;

    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 协议类型
     */
    @TableField(value = "protocol_type")
    private String protocolType;

    @TableField(value = "tenant_id")
    private String tenantId;

    private static final long serialVersionUID = 1L;
}
