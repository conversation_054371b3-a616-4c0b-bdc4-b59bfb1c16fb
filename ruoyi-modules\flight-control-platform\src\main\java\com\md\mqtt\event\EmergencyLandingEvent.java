package com.md.mqtt.event;

import lombok.Getter;

import java.math.BigDecimal;

/**
 * 紧急降落事件
 */
@Getter
public class EmergencyLandingEvent {
    private final String taskId;
    private final String droneId;
    private final BigDecimal currentLatitude;
    private final BigDecimal currentLongitude;
    private final BigDecimal currentAltitude;

    public EmergencyLandingEvent(String taskId, String droneId, BigDecimal currentLatitude, BigDecimal currentLongitude,
        BigDecimal currentAltitude) {
        this.taskId = taskId;
        this.droneId = droneId;
        this.currentLatitude = currentLatitude;
        this.currentLongitude = currentLongitude;
        this.currentAltitude = currentAltitude;
    }
}