package com.md.flight.engine.impl;

import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.flight.engine.TaskExecutionEngine;
import com.md.flight.px4.constant.Px4Constants;
import com.md.flight.px4.service.Px4DroneControlService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * PX4 MQTT任务执行引擎
 * 基于MQTT协议与PX4无人机进行航线任务通信
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class Px4MqttExecutionEngine implements TaskExecutionEngine {

    // 使用Px4Constants中的常量
    private static final String MANUFACTURER_PX4 = Px4Constants.MANUFACTURER_PX4;
    private static final String PROTOCOL_MQTT = Px4Constants.PROTOCOL_MQTT;

    private final Px4DroneControlService px4DroneControlService;

    @Override
    public CompletableFuture<Boolean> executeTask(FlightTask task, List<FlightTaskPoint> points) {
        try {
            log.info("使用PX4 MQTT引擎执行航线任务: taskId={}, taskName={}", task.getId(), task.getTaskName());

            // 1. 检查无人机编码
            String uavCode = task.getUavCode();
            if (uavCode == null || uavCode.isEmpty()) {
                throw new ServiceException("任务未关联无人机");
            }

            // 2. 检查航点数据
            if (points == null || points.isEmpty()) {
                throw new ServiceException("航线任务没有航点数据");
            }

            log.info("PX4航线任务详情: 无人机={}, 航点数量={}, 飞行速度={}m/s", 
                     uavCode, points.size(), task.getFlightSpeed());

            // 3. 调用PX4服务执行航线任务
            CompletableFuture<Boolean> result = px4DroneControlService.executeMission(uavCode, task, points);

            // 4. 处理执行结果
            return result.whenComplete((success, throwable) -> {
                if (throwable != null) {
                    log.error("PX4 MQTT航线任务执行失败: taskId={}, error={}", task.getId(), throwable.getMessage(), throwable);
                } else if (success) {
                    log.info("PX4 MQTT航线任务执行成功: taskId={}", task.getId());
                } else {
                    log.warn("PX4 MQTT航线任务执行失败: taskId={}", task.getId());
                }
            });

        } catch (Exception e) {
            log.error("PX4 MQTT执行航线任务失败: taskId={}", task.getId(), e);
            CompletableFuture<Boolean> future = new CompletableFuture<>();
            future.completeExceptionally(new ServiceException("执行航线失败: " + e.getMessage()));
            return future;
        }
    }

    @Override
    public String getSupportedManufacturer() {
        return MANUFACTURER_PX4;
    }

    @Override
    public String getSupportedProtocol() {
        return PROTOCOL_MQTT;
    }
}
