package com.md.flight.lh.enums;

import lombok.Getter;

/**
 * LH航线完成动作枚举
 */
@Getter
public enum LhStatusEnum {
    SENT("已发送", "sent"),
    IN_PROGRESS("处理中", "in_progress"),
    OK("完成", "ok"),
    PAUSED("暂停", "paused"),
    REJECTED("拒绝", "rejected"),
    FAILED("失败", "failed"),
    CANCELED("取消", "canceled"),
    TIMEOUT("超时", "timeout"),
    ;

    // 成员变量
    private String name;
    private String code;

    // 构造方法
    private LhStatusEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    // 普通方法
    public static String getName(String code) {
        for (LhStatusEnum c : LhStatusEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    //获取枚举
    public static LhStatusEnum parseEnum(String code) {
        for (LhStatusEnum platformInfoEnum : LhStatusEnum.values()) {
            if (platformInfoEnum.getCode().equals(code)) {
                return platformInfoEnum;
            }
        }
        return null;
    }
}
