package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 飞手列表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("flyer_list")
public class Flyer extends BaseEntity {

    /**
     * 飞手id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 飞手姓名
     */
    @TableField("flyer_name")
    private String flyerName;

    /**
     * 飞手手机号
     */
    @TableField("flyer_phone")
    private String flyerPhone;

    /**
     * 飞手性别
     */
    @TableField("flyer_sex")
    private int flyerSex;

    /**
     * 数据来源
     */
    @TableField("data_source")
    private String dataSource;

    /**
     * 数据来源标识（0手动添加，1同步接口获取）
     */
    @TableField("data_source_value")
    private String dataSourceValue;

    /**
     * 同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    /**
     * 无人机厂商id
     */
    @TableField("plateform_id")
    private String plateformId;

    /**
     * 飞控平台名称
     */
    @TableField("plateform_name")
    private String plateformName;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    @TableField("update_time")
    private Date updateTime;

    @TableField("update_by")
    private Long updateBy;
}
