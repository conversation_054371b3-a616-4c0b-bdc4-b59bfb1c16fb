package com.md.command.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.md.command.constant.ExecutorType;
import com.md.command.core.DroneCommand;
import com.md.command.model.dto.CommandResult;
import com.md.domain.po.DroneCommandLog;

import java.util.List;

/**
 * 无人机指令日志Service接口
 */
public interface DroneCommandLogService extends IService<DroneCommandLog> {
    /**
     * 创建指令日志
     */
    DroneCommandLog createCommandLog(DroneCommand command, ExecutorType executorType);

    /**
     * 更新指令执行结果
     */
    void updateCommandResult(String commandId, CommandResult result, long executionTime);

    /**
     * 查询指令日志列表
     */
    List<DroneCommandLog> queryCommandLogs(DroneCommandLog query);
}