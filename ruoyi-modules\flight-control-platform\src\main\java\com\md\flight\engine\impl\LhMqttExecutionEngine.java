package com.md.flight.engine.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.domain.po.UavInfo;
import com.md.domain.vo.UavInfoVo;
import com.md.enums.TaskStatusEnum;
import com.md.flight.engine.TaskExecutionEngine;
import com.md.flight.lh.constant.LhTopicConstantReq;
import com.md.flight.lh.domain.dto.LhBaseReq;
import com.md.flight.lh.domain.dto.LhRouteUploadDto;
import com.md.flight.lh.service.impl.LhDroneControlServiceImpl;
import com.md.mapper.UavInfoMapper;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import com.md.mqtt.event.DroneType;
import com.md.mqtt.event.TaskExecutionEvent;
import com.md.service.IFlightTaskStatusService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.redisson.api.RCountDownLatch;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * LH MQTT任务执行引擎
 * 基于厂商和协议动态选择执行器执行LH无人机航线任务
 */
@Slf4j
@Component
public class LhMqttExecutionEngine implements TaskExecutionEngine {

    /**
     * 厂商类型编码 - LH
     */
    private static final String MANUFACTURER_LH = "LH";

    /**
     * 协议类型编码 - MQTT
     */
    private static final String PROTOCOL_MQTT = "MQTT";

    private final String LH_FLIGHT_LINE_UPLOAD = "lh:flight_line_upload:";

    private final String LH_FLIGHT_LINE_info = "lh:flight_line_info:";

    private final String LH_FLIGHT_CODE = "lh:flight_code:";

    @Autowired
    private MqttMessageHandler messageHandler;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private LhDroneControlServiceImpl droneControlService;

    @Autowired
    private UavInfoMapper uavInfoMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private IFlightTaskStatusService flightTaskStatusService;

    //线程阻塞时间
    private final Integer awaitTime = 30;

    @Override
    public CompletableFuture<Boolean> executeTask(FlightTask task, List<FlightTaskPoint> points) {
        CompletableFuture<Boolean> future = new CompletableFuture<>();

        try {
            log.info("使用LH MQTT引擎执行航线任务: taskId={}", task.getId());

            UavInfoVo uavInfoVo =
                uavInfoMapper.selectVoOne(Wrappers.<UavInfo>lambdaQuery().eq(UavInfo::getUavCode, task.getUavCode()));

            if (uavInfoVo.getStatus().equals(1)) {
                log.error("无人机：{}未上线", task.getUavCode());
                throw new ServiceException("无人机：" + task.getUavCode() + "未上线");
            }

            // 获取航线信息
            LhBaseReq<LhRouteUploadDto> lhBaseReq =
                TenantHelper.ignore(() -> RedisUtils.getCacheObject(LH_FLIGHT_LINE_UPLOAD + task.getLineId()));

            if (lhBaseReq == null) {
                throw new ServiceException("航线数据为空");
            }

            lhBaseReq.setGateway(task.getUavCode());
            String bid = IdUtil.fastSimpleUUID();
            lhBaseReq.setBid(IdUtil.fastSimpleUUID());
            lhBaseReq.getData().getMission_config().setAction_value(task.getUavCode() + "-01");
            lhBaseReq.getData().getMission_config().setFinish_action("preLand");
            // 上传航线
            String topic = String.format(LhTopicConstantReq.SERVICES, lhBaseReq.getGateway());
            String lhBaseReqString = JSON.toJSONString(lhBaseReq);
            messageHandler.sendToMqtt(topic, lhBaseReqString);

            // 航线信息存至redis
            TenantHelper.ignore(() -> {
                Map<String, String> params = new HashMap<>();
                params.put("lineId", lhBaseReq.getData().getWayline());
                params.put("uavCode", task.getUavCode());
                params.put("routeExecuteBid", bid);
                RedisUtils.setCacheObject(LH_FLIGHT_LINE_info + lhBaseReq.getBid(), params,
                    Duration.ofMinutes(awaitTime));
            });

            // 创建Redis中的RCountDownLatch，使用TenantHelper.ignore忽略租户信息
            String latchName = droneControlService.getLatchNameForBid(bid);
            RCountDownLatch latch = TenantHelper.ignore(() -> redissonClient.getCountDownLatch(latchName));
            TenantHelper.ignore(() -> {
                latch.trySetCount(1);
                return null;
            });

            // 存储latch名称与bid的映射关系
            droneControlService.bidLatchNameMap.put(bid, latchName);

            // 等待响应，使用TenantHelper.ignore忽略租户信息
            boolean success = TenantHelper.ignore(() -> {
                try {
                    return latch.await(awaitTime, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("等待响应时被中断: droneId={}, bid={}, error={}", task.getUavCode(), bid, e.getMessage(),
                        e);
                    return false;
                }
            });

            if (success) {
                // 执飞任务存储到Redis
                TenantHelper.ignore(() -> RedisUtils.setCacheObject(LH_FLIGHT_CODE + task.getUavCode(), task.getId()));
                log.info("航线执飞成功");

                // 发布任务开始执行事件，触发轨迹记录
                try {
                    eventPublisher.publishEvent(
                        new TaskExecutionEvent(task.getUavCode(), task.getId(), true, DroneType.LH));
                    log.info("已发布联合飞机任务开始执行事件: taskId={}, uavCode={}", task.getId(), task.getUavCode());
                } catch (Exception e) {
                    log.error("发布联合飞机任务开始执行事件失败: taskId={}, uavCode={}, error={}", task.getId(),
                        task.getUavCode(), e.getMessage(), e);
                }

                // 更新任务状态为执行中
                try {
                    flightTaskStatusService.updateTaskStatus(task.getTaskName(), TaskStatusEnum.EXECUTING.getCode(),
                        null);
                    log.info("已更新联合飞机任务状态为执行中: taskName={}", task.getTaskName());
                } catch (Exception e) {
                    log.error("更新联合飞机任务状态失败: taskName={}, error={}", task.getTaskName(), e.getMessage(), e);
                }

                // 更新任务操作人（如果operatorId不为空）
                try {
                    Long operatorId = LoginHelper.getUserId();
                    if (operatorId != null) {
                        flightTaskStatusService.updateTaskOperator(task.getTaskName(), operatorId);
                        log.info("已更新联合飞机任务[{}]的执飞人ID为: {}", task.getTaskName(), operatorId);
                    } else {
                        log.info("执飞人ID为空，跳过更新联合飞机任务[{}]的执飞人", task.getTaskName());
                    }
                } catch (Exception e) {
                    log.error("更新联合飞机任务操作人失败: taskName={}, error={}", task.getTaskName(), e.getMessage(),
                        e);
                }

                future.complete(true);
            } else {
                log.warn("航线执飞超时或未收到成功响应");
                //  删除缓存
                RedisUtils.deleteObject(LH_FLIGHT_LINE_info + lhBaseReq.getBid());
                throw new ServiceException("航线执飞超时或未收到成功响应");
            }
        } catch (Exception e) {
            log.error("LH MQTT执行航线任务失败: taskId={}", task.getId(), e);
            future.completeExceptionally(e);
            throw new ServiceException(e.getMessage());
        }

        return future;
    }

    @Override
    public String getSupportedManufacturer() {
        return MANUFACTURER_LH;
    }

    @Override
    public String getSupportedProtocol() {
        return PROTOCOL_MQTT;
    }

}
