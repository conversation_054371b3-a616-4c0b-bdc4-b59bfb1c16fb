package com.md.domain.bo;

import com.md.domain.po.UavInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 无人机业务对象
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = UavInfo.class, reverseConvertGenerate = false)
public class UavInfoBo extends BaseEntity {

    /**
     * 无人机ID
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Integer id;

    /**
     * 机身码
     */
    @NotBlank(message = "机身码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String uavCode;

    /**
     * 无人机类型编码
     */
    private String categoryCode;

    /**
     * 飞控平台名称
     */
    private String manufacturerName;

    /**
     * 飞控平台编码
     */
    private String flightControlNo;

    /**
     * 最大承重
     */
    private Integer maxLoad;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 飞行器名称
     */
    //    @NotBlank(message = "飞行器名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String uavName;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 电池SN
     */
    private String batterySn;

    /**
     * 是否启用（0正常 1停用）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 同步时间
     */
    private String syncTime;

    /**
     * HTTP端点
     */
    private String httpEndpoint;

    /**
     * MQTT端点
     */
    private String mqttEndpoint;

    /**
     * Mavlink IP
     */
    private String mavlinkIp;

    /**
     * Mavlink端口
     */
    private Integer mavlinkPort;

    /**
     * 创建者名称
     */
    private String createByName;

}