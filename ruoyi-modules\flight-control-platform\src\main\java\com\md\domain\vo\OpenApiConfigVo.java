package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.md.domain.po.OpenApiConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 开放端接口管理视图对象
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OpenApiConfig.class)
public class OpenApiConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 接口ID
     */
    @ExcelProperty(value = "接口ID")
    private Long id;

    /**
     * 开放路由地址
     */
    @ExcelProperty(value = "开放路由地址")
    private String openAddress;

    /**
     * 配置信息
     */
    private String encryptInfo;


    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 显示排序
     */
    @ExcelProperty(value = "显示排序")
    private String sort;

    /**
     * 状态（0正常 1停用）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 租户id
     */
    @ExcelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
