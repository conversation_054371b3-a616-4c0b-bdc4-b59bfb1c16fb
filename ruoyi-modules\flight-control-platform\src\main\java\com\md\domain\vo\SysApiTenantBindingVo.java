package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import com.md.domain.po.SysApiTenantBinding;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 接口租户绑定关系视图对象 sys_api_tenant_binding
 *
 *
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysApiTenantBinding.class)
public class SysApiTenantBindingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 绑定ID
     */
    @ExcelProperty(value = "绑定ID")
    private Long id;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;


    /**
     * 绑定关系的租户编号
     */
    private String bindTenantId;


    /**
     * 接口ID
     */
    @ExcelProperty(value = "接口ID")
    private Long apiId;

    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    private String companyName;

    /**
     * 目标系统租户编号
     */
    @ExcelProperty(value = "目标系统租户编号")
    private String targetTenantId;

    /**
     * 配置信息
     */
    private String configInfo;

    /**
     * 是否生效（0生效 1失效）
     */
    @ExcelProperty(value = "是否生效", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;
}
