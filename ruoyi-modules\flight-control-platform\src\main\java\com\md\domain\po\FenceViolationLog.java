package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.vo.FenceViolationVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 围栏违规记录实体 fence_violation_log
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FenceViolationVo.class, reverseConvertGenerate = false)
@TableName(value = "fence_violation_log")
public class FenceViolationLog extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 围栏ID
     */
    @TableField(value = "fence_id")
    private String fenceId;

    /**
     * 任务ID
     */
    @TableField(value = "task_id")
    private String taskId;

    /**
     * 无人机编码
     */
    @TableField(value = "uav_code")
    private String uavCode;

    /**
     * 违规位置经度
     */
    @TableField(value = "longitude")
    private BigDecimal longitude;

    /**
     * 违规位置纬度
     */
    @TableField(value = "latitude")
    private BigDecimal latitude;

    /**
     * 违规位置高度
     */
    @TableField(value = "height")
    private BigDecimal height;

    /**
     * 执行的动作
     */
    @TableField(value = "action_taken")
    private Integer actionTaken;

    /**
     * 违规时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "violation_time")
    private Date violationTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    // 显式标记BaseEntity中的这些字段在当前表中不存在
    @TableField(exist = false)
    private Long createBy;

    @TableField(exist = false)
    private Long createDept;

    @TableField(exist = false)
    private Date createTime;

    @TableField(exist = false)
    private Long updateBy;

    @TableField(exist = false)
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
