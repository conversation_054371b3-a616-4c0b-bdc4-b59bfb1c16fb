package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.MissionAck;
import lombok.extern.slf4j.Slf4j;

/**
 * 任务确认处理器
 * 处理MAVLink任务确认消息
 */
@Slf4j
public class MissionAckHandler extends AbstractMavlinkHandler<MissionAck> {
    
    public MissionAckHandler(MavlinkCoreService coreService) {
        super(coreService);
    }
    
    @Override
    protected void doHandle(String droneId, MissionAck message) {
        log.info("收到无人机[{}]任务确认消息: type={}, missionType={}", droneId, message.type().entry(), message.missionType());
        coreService.handleMissionAck(droneId, message);
    }

    @Override
    public Class<MissionAck> getMessageType() {
        return MissionAck.class;
    }
}