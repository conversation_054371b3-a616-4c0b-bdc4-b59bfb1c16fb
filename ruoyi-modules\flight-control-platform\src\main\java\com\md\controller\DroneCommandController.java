package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.md.command.model.dto.CommandRequest;
import com.md.command.model.dto.CommandResult;
import com.md.command.service.DroneCommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 无人机控制接口
 */
@RestController
@RequestMapping("/api/drone/command")
@Slf4j
public class DroneCommandController {

    @Autowired
    private DroneCommandService commandService;

    /**
     * 指令执行
     *
     * @param request 命令请求
     * @return 命令执行结果
     */
    @SaCheckPermission(value = {"drone:control:execute", "flight:task:execute"}, mode = SaMode.OR)
    @PostMapping("/execute")
    public ResponseEntity<CommandResult> executeCommand(@Validated @RequestBody CommandRequest request) {
        log.info("收到指令请求: {}", request);
        CommandResult result = commandService.executeCommand(request.toCommand());
        return ResponseEntity.ok(result);
    }
}