package org.dromara.common.rabbitmq.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ配置类
 */
@Slf4j
@Configuration
public class RabbitMQConfig {

    @Value("${spring.rabbitmq.host}")
    private String host;

    @Value("${spring.rabbitmq.port}")
    private int port;

    @Value("${spring.rabbitmq.username}")
    private String username;

    @Value("${spring.rabbitmq.password}")
    private String password;

    @Value("${spring.rabbitmq.virtual-host}")
    private String virtualHost;

    @Value("${spring.rabbitmq.publisher-confirm-type:correlated}")
    private String publisherConfirmType;

    @Value("${spring.rabbitmq.publisher-returns:true}")
    private boolean publisherReturns;

    @Value("${rabbitmq.flight-dynamics.exchange}")
    private String flightDynamicsExchange;

    @Value("${rabbitmq.flight-dynamics.routing-key}")
    private String flightDynamicsRoutingKey;

    /**
     * 创建连接工厂
     */
    @Bean
    public ConnectionFactory connectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(virtualHost);

        // 设置发布确认类型
        connectionFactory.setPublisherConfirmType(
            CachingConnectionFactory.ConfirmType.valueOf(publisherConfirmType.toUpperCase()));
        // 设置发布返回
        connectionFactory.setPublisherReturns(publisherReturns);

        return connectionFactory;
    }

    /**
     * 配置RabbitTemplate
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);

        // 设置消息发送失败时返回
        rabbitTemplate.setMandatory(true);

        // 消息发送到交换机后触发回调
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.info("消息发送成功：correlationData={}", correlationData);
            } else {
                log.error("消息发送失败：correlationData={}, cause={}", correlationData, cause);
            }
        });

        // 消息从交换机发送到队列失败时触发回调
        rabbitTemplate.setReturnsCallback(returned -> {
            log.error("消息发送到队列失败：exchange={}, routingKey={}, message={}, replyCode={}, replyText={}",
                returned.getExchange(), returned.getRoutingKey(), new String(returned.getMessage().getBody()),
                returned.getReplyCode(), returned.getReplyText());
        });

        return rabbitTemplate;
    }
}
