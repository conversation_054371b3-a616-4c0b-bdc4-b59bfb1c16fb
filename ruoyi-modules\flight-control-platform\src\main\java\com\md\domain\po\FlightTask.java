package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.tenant.core.TenantEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 航线任务表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "flight_task")
public class FlightTask extends TenantEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 厂商ID
     */
    @TableField(value = "flight_control_no")
    private String flightControlNo;

    /**
     * 航线模板ID
     */
    @TableField(value = "line_id")
    private String lineId;

    /**
     * 航线名称
     */
    @TableField(value = "line_name")
    private String lineName;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 飞行器SN
     */
    @TableField(value = "uav_code")
    private String uavCode;

    /**
     * 飞手ID
     */
    @TableField(value = "flyer_id")
    private Long flyerId;

    /**
     * 计划执飞时间
     */
    @TableField(value = "flight_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date flightTime;

    /**
     * 执飞人ID
     */
    @TableField(value = "operator_id")
    private Long operatorId;

    /**
     * 任务状态(0:未执行 1:执行中 2:已完成 3:已取消)
     */
    @TableField(value = "task_status")
    private Integer taskStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 飞行速度(m/s)
     */
    @TableField(value = "flight_speed")
    private BigDecimal flightSpeed;

    /**
     * 飞行高度(m)
     */
    @TableField(value = "flight_height")
    private BigDecimal flightHeight;

    /**
     * 围栏ID
     */
    @TableField(value = "fence_id")
    private String fenceId;

    /**
     * 返航高度(m)
     */
    @TableField(value = "return_height")
    private BigDecimal returnHeight;

    /**
     * 任务结束后的动作(原地降落/返回起点等)
     */
    @TableField(value = "finished_action")
    private String finishedAction;

    /**
     * 网络失联后动作(返航/继续执行)
     */
    @TableField(value = "missing_action")
    private String missingAction;

    /**
     * 文件生成状态(0:未生成 1:生成中 2:已生成 3:生成失败)
     */
    @TableField(value = "file_generate_status")
    private Integer fileGenerateStatus;

    /**
     * 航线文件名称
     */
    @TableField(value = "kmz_file_name")
    private String kmzFileName;

    /**
     * 航线文件存储路径
     */
    @TableField(value = "kmz_file_path")
    private String kmzFilePath;

    /**
     * 文件大小(字节)
     */
    @TableField(value = "file_size")
    private Long fileSize;

    /**
     * 文件生成时间
     */
    @TableField(value = "file_generate_time")
    private Date fileGenerateTime;

    /**
     * 文件生成错误信息
     */
    @TableField(value = "file_generate_error")
    private String fileGenerateError;

    /**
     * 是否已上传(0:未上传 1:已上传)
     */
    @TableField(value = "is_uploaded")
    private Integer isUploaded;

    /**
     * 协议类型（例如：MQTT、MAVLINK、SDK等）
     */
    @TableField(value = "protocol_type")
    private String protocolType;
}
