package com.md.flight.lh.domain.dto;

import lombok.Data;

/**
 * 联合飞机 RTK 数据实体类，用于接收和解析 RTK 定位信息
 */
@Data
public class LhResponseRtkData {

    /**
     * GPS 定位类型
     * 0：无定位
     * 1：单点定位
     * 2：差分定位
     * 3：RTK 浮点解
     * 4：RTK 固定解
     * 6：RTK 固定解（高精度）
     */
    private Integer fix_type;

    /** 纬度，单位为度 */
    private Double latitude;

    /** 经度，单位为度 */
    private Double longitude;

    /** 海拔高度，单位为米 */
    private Double altitude;

    /** 椭球高度，单位为米 */
    private Double ellipsoid_height;

    /** 可见卫星数量 */
    private Integer satellite_visible;

    /** 水平位置精度，单位为米 */
    private Double eph;

    /** 垂直位置精度，单位为米 */
    private Double epv;

    /** 速度，单位为 m/s */
    private Double velocity;

    /** 偏航角，单位为度 */
    private Double yaw;

    /** 航向角，单位为度 */
    private Double cog;

    /** 水平精度，单位为米 */
    private Double horizontal_acc;

    /** 垂直精度，单位为米 */
    private Double vertical_acc;

    /** 速度精度，单位为 m/s */
    private Double velocity_acc;

    /** 航向精度，单位为度 */
    private Double hdg_acc;
}
