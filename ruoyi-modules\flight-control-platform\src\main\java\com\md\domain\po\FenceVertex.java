package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 围栏顶点实体 fence_vertex
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "fence_vertex")
public class FenceVertex extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 围栏区域ID
     */
    @TableField(value = "fence_area_id")
    private String fenceAreaId;

    /**
     * 顶点序号
     */
    @TableField(value = "sequence")
    private Integer sequence;

    /**
     * 经度
     */
    @TableField(value = "longitude")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField(value = "latitude")
    private BigDecimal latitude;

    private static final long serialVersionUID = 1L;
}
