package com.md.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 围栏区域VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FenceAreaVO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 围栏ID
     */
    private String fenceId;

    /**
     * 区域类型(1:圆形 2:多边形 3:矩形)
     */
    private Integer areaType;

    /**
     * 中心点经度
     */
    private BigDecimal centerLongitude;

    /**
     * 中心点纬度
     */
    private BigDecimal centerLatitude;

    /**
     * 半径(米)
     */
    private BigDecimal radius;

    /**
     * 最小高度(米)
     */
    private BigDecimal minHeight;

    /**
     * 最大高度(米)
     */
    private BigDecimal maxHeight;

    /**
     * 触发动作(1:警告 2:返航 3:悬停 4:降落)
     */
    private Integer actionType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 顶点列表（多边形区域使用）
     */
    private List<FenceVertexVO> vertices;
}