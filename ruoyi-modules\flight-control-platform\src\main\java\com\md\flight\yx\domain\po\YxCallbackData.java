package com.md.flight.yx.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 亿迅回调数据实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("yx_callback_data")
public class YxCallbackData extends BaseEntity {

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务状态 0-执行中、1-执行成功、2-执行失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String errorMsg;

    /**
     * 任务开始时间
     */
    private Date beginTime;

    /**
     * 任务结束时间
     */
    private Date finishTime;

    /**
     * 飞行编号
     */
    private String flightCode;

    /**
     * 无人机编号
     */
    private String uavCode;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 客户端名称
     */
    private String clientName;
}
