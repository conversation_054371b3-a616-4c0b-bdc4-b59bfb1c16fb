package com.md.flight.yx.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 签名工具类 用于计算API请求签名
 */
@Slf4j
public class SignUtil {
    private static final String HMAC_SHA256 = "HmacSHA512";

    /**
     * @param secretValue
     * @param message
     * @return
     */
    private static String siHmacSHA512(String secretValue, String message) {
        try {
            // 生成密钥
            SecretKeySpec secretKey = new SecretKeySpec(secretValue.getBytes(), HMAC_SHA256);
            // 创建HMAC-SHA256的Mac实例
            Mac mac = Mac.getInstance(HMAC_SHA256);
            // 初始化Mac实例，并设置密钥
            mac.init(secretKey);

            // 计算认证码
            byte[] hmac = mac.doFinal(message.getBytes());

            String hex = bytesToHex(hmac);
            return hex.toUpperCase();
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("梳理数据失败,参数：{}", message);
            throw new RuntimeException("数据处理失败", e);
        }
    }

    /**
     * byte转16进制
     *
     * @param bytes
     * @return
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * 计算API请求签名
     *
     * @param secret    密钥
     * @param timestamp 时间戳
     * @param jsonParam 请求体的JSON字符串，如果没有请求体则为空字符串
     * @return 16进制编码的签名结果（大写）
     */
    public static String sign(String secret, String timestamp, String jsonParam) {
        //数据先AES加密，secret作为签名
        String secretParam = YxAESUtil.encrypt(jsonParam, secret);
        //签名，uppercase(hex(hmac(appSecret, timestamp+参数))
        String data = timestamp + secretParam;
        String hexResult = siHmacSHA512(secret, data);
        return hexResult;
    }

    /**
     * 获取时间戳
     *
     * @return 返回时间戳字符串
     */
    public static String getTimeStampStr() {
        return String.valueOf(System.currentTimeMillis());
    }
}