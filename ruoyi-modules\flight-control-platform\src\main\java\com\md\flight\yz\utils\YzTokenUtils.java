package com.md.flight.yz.utils;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.md.domain.po.PlatformInfo;
import com.md.enums.PlatformInfoEnum;
import com.md.flight.yz.domain.YzRequestResult;
import com.md.flight.yz.domain.YzTokenResult;
import com.md.flight.yz.domain.dto.YzBaseReqDto;
import com.md.flight.yz.openfeign.YzGetTokenClient;
import com.md.flight.yz.properties.YzRemoteCallProperties;
import com.md.service.IPlatformInfoService;
import com.md.utils.EncryptUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取请求token工具类
 */
@Component
public class YzTokenUtils {
    @Autowired
    private YzRemoteCallProperties yzRemoteCallProperties;
    @Autowired
    private YzGetTokenClient getTokenClient;
    @Autowired
    private IPlatformInfoService platformInfoService;

    private final String TOKEN = "yz_token";

    /**
     * 获取token值
     *
     * @return token值
     */
    public String getToken() {
        String token = null;
        Map<String, String> stringObjectMap = checkTokenValidity();
        if (stringObjectMap == null) {
            token = applyToken();
        } else {
            token = stringObjectMap.get(TOKEN);
        }
        return token;
    }

    /**
     * 检查token是否存在或有效
     *
     * @return 检查结果map值，包括是否存在，以及存在时候token值
     */
    public Map<String, String> checkTokenValidity() {
        Map<String, String> map = new HashMap<>(2);
        String tenant = TenantHelper.getTenantId();
        String tokenKey = yzRemoteCallProperties.getTokenKey();
        String cacheKey = tenant + ":" + tokenKey;
        String token = RedisUtils.getCacheObject(cacheKey);
        if (StringUtils.isNotEmpty(token)) {
            PlatformInfo platform = platformInfoService.selectPlatformByCode(PlatformInfoEnum.YZ.getCode());
            String decrypt = EncryptUtils.decrypt(token, platform.getAesKey());
            YzRequestResult requestResult =
                com.alibaba.fastjson2.JSONObject.parseObject(decrypt, YzRequestResult.class);
            YzTokenResult yzTokenResult =
                com.alibaba.fastjson2.JSONObject.parseObject(requestResult.getData(), YzTokenResult.class);
            map.put(TOKEN, yzTokenResult.getToken());
            return map;
        }
        return null;
    }

    /**
     * 申请token
     *
     * @return token
     */
    public String applyToken() {
        YzBaseReqDto baseReqYhDto = new YzBaseReqDto();
        JSONObject json = new JSONObject();
        PlatformInfo platform = platformInfoService.selectPlatformByCode(PlatformInfoEnum.YZ.getCode());
        json.set("accessKey", platform.getAccount());
        json.set("secretKey", platform.getPassword());
        String jsonString = JSON.toJSONString(json);
        String encrypt = EncryptUtils.encrypt(jsonString, platform.getAesKey());
        baseReqYhDto.setParams(encrypt);
        String tokenClient = getTokenClient.getTokenClient(baseReqYhDto);
        // 不使用多数据源，直接使用当前租户标识
        String tenant = TenantHelper.getTenantId();
        String cacheKey = tenant + ":" + yzRemoteCallProperties.getTokenKey();
        // 使用Duration设置超时时间
        RedisUtils.setCacheObject(cacheKey, tokenClient, Duration.ofMinutes(yzRemoteCallProperties.getTokenOutTime()));
        String decrypt = EncryptUtils.decrypt(tokenClient, platform.getAesKey());
        YzRequestResult requestResult = com.alibaba.fastjson2.JSONObject.parseObject(decrypt, YzRequestResult.class);
        YzTokenResult yzTokenResult =
            com.alibaba.fastjson2.JSONObject.parseObject(requestResult.getData(), YzTokenResult.class);

        return yzTokenResult.getToken();
    }
}
