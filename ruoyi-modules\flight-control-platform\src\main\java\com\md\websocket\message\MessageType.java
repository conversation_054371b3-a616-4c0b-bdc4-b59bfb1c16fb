package com.md.websocket.message;

import lombok.Getter;

/**
 * WebSocket消息类型枚举
 */
@Getter
public enum MessageType {
    /**
     * 大疆无人机状态消息
     */
    DRONE_STATUS("dji/status", "DJI"),

    /**
     * 大疆无人机响应消息
     */
    DRONE_RESPONSE("dji/response", "DJI"),

    /**
     * 大疆虚拟摇杆响应消息
     */
    VIRTUAL_STICK_RESPONSE("dji/virtual_stick/response", "DJI"),

    /**
     * MAVLink无人机状态消息
     */
    MAVLINK_DRONE_STATUS("mavlink/status", "MAVLINK"),

    /**
     * PX4无人机状态消息（支持动态主题格式 px4/{droneId}/status）
     */
    PX4_DRONE_STATUS("px4/+/status", "PX4"),

    /**
     * 联合飞机状态消息（支持动态主题格式 thing/device/{droneId}/osd）
     * 包含OSD、RTK、电池等多种消息类型，通过method字段区分
     */
    LH_STATUS("thing/device/+/osd", "LH");

    private final String topic;
    private final String manufacturer;

    MessageType(String topic, String manufacturer) {
        this.topic = topic;
        this.manufacturer = manufacturer;
    }

    public static MessageType fromTopic(String topic) {
        for (MessageType type : values()) {
            if (matchesTopic(type.getTopic(), topic)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 匹配主题，支持通配符 +
     * 例如：px4/+/status 可以匹配 px4/drone001/status
     */
    private static boolean matchesTopic(String pattern, String topic) {
        // 如果模式中没有通配符，直接比较
        if (!pattern.contains("+")) {
            return pattern.equals(topic);
        }

        // 将模式转换为正则表达式
        String regex = pattern.replace("+", "[^/]+");
        return topic.matches(regex);
    }
}