package com.md.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.md.constant.FenceConstants;
import com.md.domain.bo.FenceDetectionBo;
import com.md.domain.dto.FenceDetectionResult;
import com.md.domain.po.FenceArea;
import com.md.domain.po.FenceInfo;
import com.md.domain.po.FenceVertex;
import com.md.domain.po.FenceViolationLog;
import com.md.domain.vo.FenceVo;
import com.md.enums.FenceActionTypeEnum;
import com.md.enums.FenceAreaTypeEnum;
import com.md.enums.FenceTypeEnum;
import com.md.mapper.FenceAreaMapper;
import com.md.mapper.FenceInfoMapper;
import com.md.mapper.FenceVertexMapper;
import com.md.mapper.FenceViolationLogMapper;
import com.md.mapper.FlightLineTaskMapper;
import com.md.service.IFenceDetectionService;
import com.md.utils.GeoUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LinearRing;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import com.md.domain.vo.FenceAreaVO;
import com.md.domain.vo.FenceVertexVO;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 围栏检测服务实现 - 使用JTS库优化几何计算
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FenceDetectionServiceImpl implements IFenceDetectionService {

    private final FenceInfoMapper fenceInfoMapper;
    private final FenceAreaMapper fenceAreaMapper;
    private final FenceVertexMapper fenceVertexMapper;
    private final FenceViolationLogMapper fenceViolationLogMapper;
    private final FlightLineTaskMapper flightLineTaskMapper;

    // JTS几何工厂
    private final GeometryFactory geometryFactory = new GeometryFactory();

    // 缓存JTS几何对象
    private final Map<String, Geometry> geometryCache = new HashMap<>();

    /**
     * 新增：使用业务对象检测无人机位置是否违反围栏规则
     */
    @Override
    public FenceDetectionResult detectUavPosition(FenceDetectionBo bo) {
        if (bo == null || StringUtils.isEmpty(bo.getTaskId()) || StringUtils.isEmpty(bo.getUavCode()) ||
            bo.getLongitude() == null || bo.getLatitude() == null || bo.getHeight() == null) {
            log.error("检测位置参数不完整: bo={}", bo);
            return FenceDetectionResult.builder().violated(false).build();
        }

        return detectUavPosition(bo.getTaskId(), bo.getUavCode(), bo.getLongitude(), bo.getLatitude(), bo.getHeight());
    }

    @Override
    public FenceDetectionResult detectUavPosition(String taskId, String uavCode, BigDecimal longitude,
        BigDecimal latitude, BigDecimal height) {
        // 参数验证
        if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(uavCode) || longitude == null || latitude == null ||
            height == null) {
            log.error("检测位置参数不完整: taskId={}, uavCode={}, longitude={}, latitude={}, height={}", taskId,
                uavCode, longitude, latitude, height);
            return FenceDetectionResult.builder().violated(false).build();
        }

        try {
            // 获取任务关联的围栏ID列表
            List<String> fenceIds = getFenceIdForTask(taskId);

            // 如果任务没有关联围栏，直接返回未违规
            if (fenceIds.isEmpty()) {
                log.debug("任务[{}]未关联围栏，跳过围栏检测", taskId);
                return FenceDetectionResult.builder().violated(false).build();
            }

            // 创建无人机位置点
            Point uavPoint = createPoint(longitude.doubleValue(), latitude.doubleValue());

            // 用于存储最严格的违规结果（需要采取最严格的动作）
            FenceDetectionResult mostSevereResult = null;

            // 遍历所有关联的围栏进行检测
            for (String fenceId : fenceIds) {
                // 获取围栏信息
                FenceInfo fenceInfo = getCachedFenceInfo(fenceId);

                // 如果围栏不存在或被禁用，跳过此围栏
                if (fenceInfo == null) {
                    log.warn("任务[{}]关联的围栏[{}]不存在", taskId, fenceId);
                    continue;
                }

                if (fenceInfo.getStatus() == 0) {
                    log.debug("任务[{}]关联的围栏[{}]已禁用，跳过围栏检测", taskId, fenceId);
                    continue;
                }

                // 获取围栏区域
                List<FenceArea> areas = getCachedFenceAreas(fenceId);
                if (areas == null || areas.isEmpty()) {
                    log.warn("围栏[{}]没有定义区域，跳过围栏检测", fenceId);
                    continue;
                }

                // 执行围栏检测
                FenceDetectionResult result = checkFence(fenceInfo, areas, uavPoint, height);

                // 如果违规，并且是最严格的结果，记录下来
                if (result.isViolated() &&
                    (mostSevereResult == null || result.getActionToTake() > mostSevereResult.getActionToTake())) {
                    mostSevereResult = result;
                }
            }

            // 如果有违规结果，记录违规日志并返回
            if (mostSevereResult != null) {
                recordViolation(mostSevereResult.getFenceId(), taskId, uavCode, longitude, latitude, height,
                    mostSevereResult.getActionToTake());
                log.info("无人机[{}]在任务[{}]中违反围栏[{}]规则，动作：{}", uavCode, taskId,
                    mostSevereResult.getFenceId(), mostSevereResult.getActionToTake());
                return mostSevereResult;
            }

            // 没有违规，返回未违规结果
            return FenceDetectionResult.builder().violated(false).build();
        } catch (Exception e) {
            log.error("执行围栏检测异常: taskId={}, uavCode={}", taskId, uavCode, e);
            // 异常情况下，为安全起见返回未违规，避免错误地干扰飞行
            return FenceDetectionResult.builder().violated(false).build();
        }
    }

    @Override
    public void loadFencesForTask(String taskId) {
        if (StringUtils.isEmpty(taskId)) {
            log.warn("任务ID为空，无法加载围栏数据");
            return;
        }

        log.info("开始加载任务[{}]围栏数据到缓存", taskId);

        try {
            // 从数据库获取任务关联的围栏ID列表
            List<String> fenceIds = getFenceIdForTask(taskId);

            if (fenceIds == null || fenceIds.isEmpty()) {
                log.info("任务[{}]未关联围栏", taskId);
                // 设置一个空标记，防止重复查询
                String taskFenceCacheKey = FenceConstants.REDIS_FENCE_PREFIX + "task:" + taskId;
                RedisUtils.setCacheObject(taskFenceCacheKey, "", Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));
                return;
            }

            // 加载所有关联的围栏数据到缓存
            for (String fenceId : fenceIds) {
                loadFenceToCache(fenceId);
                log.info("已加载围栏[{}]数据到缓存", fenceId);
            }

            // 记录任务已加载围栏
            String taskFenceCacheKey = FenceConstants.REDIS_FENCE_PREFIX + "task:" + taskId;
            RedisUtils.setCacheObject(taskFenceCacheKey, String.join(",", fenceIds),
                Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));
            log.info("任务[{}]围栏数据已加载到缓存, fenceIds: {}", taskId, fenceIds);
        } catch (Exception e) {
            log.error("加载任务围栏数据发生异常: taskId={}", taskId, e);
        }
    }

    @Override
    public void removeFencesForTask(String taskId) {
        log.info("开始从缓存中移除任务围栏数据, taskId: {}", taskId);
        String taskFenceCacheKey = FenceConstants.REDIS_FENCE_PREFIX + "task:" + taskId;
        RedisUtils.deleteObject(taskFenceCacheKey);
        log.info("任务围栏数据已从缓存中移除, taskId: {}", taskId);
    }

    /**
     * 获取任务关联的围栏ID列表
     * 从flight_task表中查询fence_id字段，支持逗号分隔的多个围栏ID
     */
    private List<String> getFenceIdForTask(String taskId) {
        // 从缓存获取
        String taskFenceCacheKey = FenceConstants.REDIS_FENCE_PREFIX + "task:" + taskId;
        String fenceIdStr = RedisUtils.getCacheObject(taskFenceCacheKey);

        List<String> fenceIds = new ArrayList<>();

        // 如果缓存中没有，从数据库查询
        if (StringUtils.isEmpty(fenceIdStr)) {
            try {
                // 使用FlightLineTaskMapper查询任务关联的围栏ID字符串
                fenceIdStr = flightLineTaskMapper.selectFenceIdByTaskId(taskId);

                // 如果查询到有效的围栏ID，则缓存
                if (!StringUtils.isEmpty(fenceIdStr)) {
                    RedisUtils.setCacheObject(taskFenceCacheKey, fenceIdStr,
                        Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));
                    log.debug("为任务{}查询到围栏ID字符串: {}", taskId, fenceIdStr);
                } else {
                    log.debug("任务{}没有关联围栏", taskId);
                    // 缓存空值，避免重复查询
                    RedisUtils.setCacheObject(taskFenceCacheKey, "",
                        Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));
                }
            } catch (Exception e) {
                log.error("查询任务围栏关联异常", e);
            }
        }

        // 如果有围栏ID字符串，将其拆分为围栏ID列表
        if (!StringUtils.isEmpty(fenceIdStr)) {
            String[] idArray = fenceIdStr.split(",");
            for (String id : idArray) {
                if (!StringUtils.isEmpty(id.trim())) {
                    fenceIds.add(id.trim());
                }
            }
            log.debug("任务{}关联的围栏ID列表: {}", taskId, fenceIds);
        }

        return fenceIds;
    }

    /**
     * 加载围栏数据到缓存
     *
     * @param fenceId 围栏ID
     */
    private void loadFenceToCache(String fenceId) {
        // 加载围栏基本信息
        FenceInfo fenceInfo = fenceInfoMapper.selectFenceInfoById(fenceId);
        if (fenceInfo == null) {
            log.warn("围栏不存在, fenceId: {}", fenceId);
            return;
        }

        // 缓存围栏基本信息
        RedisUtils.setCacheObject(FenceConstants.REDIS_FENCE_INFO_KEY + fenceId, fenceInfo,
            Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));

        // 加载围栏区域
        List<FenceArea> areas = fenceAreaMapper.selectByFenceId(fenceId);
        if (areas == null || areas.isEmpty()) {
            log.warn("围栏无区域定义, fenceId: {}", fenceId);
            return;
        }

        // 缓存围栏区域
        RedisUtils.setCacheObject(FenceConstants.REDIS_FENCE_AREAS_KEY + fenceId, areas,
            Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));

        // 加载顶点和预计算几何对象（对多边形类型区域）
        for (FenceArea area : areas) {
            if (FenceAreaTypeEnum.POLYGON.getCode() == area.getAreaType() ||
                FenceAreaTypeEnum.RECTANGLE.getCode() == area.getAreaType()) {
                List<FenceVertex> vertices = fenceVertexMapper.selectByAreaId(area.getId());
                if (vertices != null && !vertices.isEmpty()) {
                    // 缓存顶点
                    RedisUtils.setCacheObject(FenceConstants.REDIS_FENCE_VERTICES_KEY + area.getId(), vertices,
                        Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));

                    // 创建并缓存JTS多边形对象
                    Geometry polygon = createPolygonFromVertices(vertices);
                    if (polygon != null) {
                        geometryCache.put(area.getId(), polygon);
                    }
                }
            } else if (FenceAreaTypeEnum.CIRCLE.getCode() == area.getAreaType()) {
                // 创建并缓存JTS圆形对象
                Geometry circle =
                    createCircle(area.getCenterLongitude().doubleValue(), area.getCenterLatitude().doubleValue(),
                        area.getRadius().doubleValue());
                if (circle != null) {
                    geometryCache.put(area.getId(), circle);
                }
            }
        }
    }

    /**
     * 从缓存获取围栏信息
     *
     * @param fenceId 围栏ID
     * @return 围栏信息
     */
    private FenceInfo getCachedFenceInfo(String fenceId) {
        Object cacheObject = RedisUtils.getCacheObject(FenceConstants.REDIS_FENCE_INFO_KEY + fenceId);
        if (cacheObject == null) {
            FenceInfo fenceInfo = fenceInfoMapper.selectFenceInfoById(fenceId);
            if (fenceInfo != null) {
                RedisUtils.setCacheObject(FenceConstants.REDIS_FENCE_INFO_KEY + fenceId, fenceInfo,
                    Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));
            }
            return fenceInfo;
        }

        // 处理不同类型的缓存对象
        if (cacheObject instanceof FenceInfo) {
            return (FenceInfo)cacheObject;
        } else if (cacheObject instanceof JSONObject) {
            return ((JSONObject)cacheObject).to(FenceInfo.class);
        } else if (cacheObject instanceof String) {
            return JSON.parseObject((String)cacheObject, FenceInfo.class);
        } else {
            // 其他类型，尝试通过JSON转换
            String jsonString = JSON.toJSONString(cacheObject);
            return JSON.parseObject(jsonString, FenceInfo.class);
        }
    }

    /**
     * 从缓存获取围栏区域列表
     *
     * @param fenceId 围栏ID
     * @return 围栏区域列表
     */
    private List<FenceArea> getCachedFenceAreas(String fenceId) {
        Object cacheObject = RedisUtils.getCacheObject(FenceConstants.REDIS_FENCE_AREAS_KEY + fenceId);
        if (cacheObject == null) {
            List<FenceArea> areas = fenceAreaMapper.selectByFenceId(fenceId);
            if (areas != null && !areas.isEmpty()) {
                RedisUtils.setCacheObject(FenceConstants.REDIS_FENCE_AREAS_KEY + fenceId, areas,
                    Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));
            }
            return areas;
        }

        // 处理不同类型的缓存对象
        if (cacheObject instanceof List) {
            List<?> list = (List<?>)cacheObject;
            if (!list.isEmpty()) {
                if (list.get(0) instanceof FenceArea) {
                    return (List<FenceArea>)list;
                }
            }
            // 空列表情况
            return new ArrayList<>();
        } else if (cacheObject instanceof String) {
            // 字符串JSON
            return JSON.parseArray((String)cacheObject, FenceArea.class);
        } else {
            // 其他类型，尝试通过JSON转换
            String jsonString = JSON.toJSONString(cacheObject);
            return JSON.parseArray(jsonString, FenceArea.class);
        }
    }

    /**
     * 从缓存获取围栏顶点列表
     *
     * @param areaId 区域ID
     * @return 顶点列表
     */
    private List<FenceVertex> getCachedVertices(String areaId) {
        Object cacheObject = RedisUtils.getCacheObject(FenceConstants.REDIS_FENCE_VERTICES_KEY + areaId);
        if (cacheObject == null) {
            List<FenceVertex> vertices = fenceVertexMapper.selectByAreaId(areaId);
            if (vertices != null && !vertices.isEmpty()) {
                RedisUtils.setCacheObject(FenceConstants.REDIS_FENCE_VERTICES_KEY + areaId, vertices,
                    Duration.ofHours(FenceConstants.CACHE_TIMEOUT_HOURS));
            }
            return vertices;
        }

        // 处理不同类型的缓存对象
        if (cacheObject instanceof List) {
            List<?> list = (List<?>)cacheObject;
            if (!list.isEmpty()) {
                if (list.get(0) instanceof FenceVertex) {
                    return (List<FenceVertex>)list;
                }
            }
            // 空列表情况
            return new ArrayList<>();
        } else if (cacheObject instanceof String) {
            // 字符串JSON
            return JSON.parseArray((String)cacheObject, FenceVertex.class);
        } else {
            // 其他类型，尝试通过JSON转换
            String jsonString = JSON.toJSONString(cacheObject);
            return JSON.parseArray(jsonString, FenceVertex.class);
        }
    }

    /**
     * 获取几何对象（从缓存或根据围栏区域构建）
     *
     * @param area 围栏区域
     * @return 几何对象
     */
    private Geometry getGeometry(FenceArea area) {
        // 首先从内存缓存中获取
        Geometry geometry = geometryCache.get(area.getId());
        if (geometry != null) {
            return geometry;
        }

        // 根据区域类型创建几何对象
        if (FenceAreaTypeEnum.POLYGON.getCode() == area.getAreaType() ||
            FenceAreaTypeEnum.RECTANGLE.getCode() == area.getAreaType()) {
            // 获取多边形顶点
            List<FenceVertex> vertices = getCachedVertices(area.getId());
            if (vertices != null && !vertices.isEmpty()) {
                geometry = createPolygonFromVertices(vertices);
            }
        } else if (FenceAreaTypeEnum.CIRCLE.getCode() == area.getAreaType()) {
            // 创建圆形
            geometry = createCircle(area.getCenterLongitude().doubleValue(), area.getCenterLatitude().doubleValue(),
                area.getRadius().doubleValue());
        }

        // 缓存并返回几何对象
        if (geometry != null) {
            geometryCache.put(area.getId(), geometry);
        }
        return geometry;
    }

    /**
     * 检查围栏规则
     *
     * @param fence    围栏信息
     * @param areas    围栏区域列表
     * @param uavPoint 无人机位置点
     * @param height   高度
     * @return 检测结果
     */
    private FenceDetectionResult checkFence(FenceInfo fence, List<FenceArea> areas, Point uavPoint, BigDecimal height) {
        // 根据围栏类型执行不同的检查逻辑
        int fenceType = fence.getFenceType();

        // 包含型围栏：必须在围栏内
        if (FenceTypeEnum.INCLUSIVE.getCode() == fenceType) {
            return checkInclusiveFence(fence, areas, uavPoint, height);
        }
        // 排除型围栏：必须在围栏外
        else if (FenceTypeEnum.EXCLUSIVE.getCode() == fenceType) {
            return checkExclusiveFence(fence, areas, uavPoint, height);
        }
        // 高度限制围栏：必须在高度范围内
        else if (FenceTypeEnum.HEIGHT.getCode() == fenceType) {
            return checkHeightFence(fence, areas, height);
        }
        // 复合型围栏：包含、排除和高度限制的组合
        else if (FenceTypeEnum.COMPOUND.getCode() == fenceType) {
            return checkCompoundFence(fence, areas, uavPoint, height);
        }

        return FenceDetectionResult.builder().violated(false).build();
    }

    /**
     * 检查包含型围栏
     */
    private FenceDetectionResult checkInclusiveFence(FenceInfo fence, List<FenceArea> areas, Point uavPoint,
        BigDecimal height) {
        boolean insideAnyArea = false;

        for (FenceArea area : areas) {
            // 检查高度是否在范围内
            if (height.compareTo(area.getMinHeight()) < 0 || height.compareTo(area.getMaxHeight()) > 0) {
                continue;
            }

            // 检查平面位置是否在区域内
            Geometry areaGeometry = getGeometry(area);
            if (areaGeometry != null && areaGeometry.contains(uavPoint)) {
                insideAnyArea = true;
                break;
            }
        }

        // 如果不在任何区域内，则违反规则
        if (!insideAnyArea) {
            FenceArea mostRestrictiveArea = findMostRestrictiveArea(areas);
            if (mostRestrictiveArea != null) {
                FenceActionTypeEnum actionEnum = FenceActionTypeEnum.getByCode(mostRestrictiveArea.getActionType());
                String actionDesc = actionEnum != null ? actionEnum.getInfo() : "未知动作";

                return FenceDetectionResult.builder().violated(true).fenceId(fence.getId())
                    .violatedAreaId(mostRestrictiveArea.getId()).actionToTake(mostRestrictiveArea.getActionType())
                    .violationReason("无人机位置不在包含型围栏内，将执行" + actionDesc).build();
            }
        }

        return FenceDetectionResult.builder().violated(false).build();
    }

    /**
     * 检查排除型围栏
     */
    private FenceDetectionResult checkExclusiveFence(FenceInfo fence, List<FenceArea> areas, Point uavPoint,
        BigDecimal height) {
        for (FenceArea area : areas) {
            // 检查高度是否在范围内
            if (height.compareTo(area.getMinHeight()) < 0 || height.compareTo(area.getMaxHeight()) > 0) {
                continue;
            }

            // 检查平面位置是否在区域内
            Geometry areaGeometry = getGeometry(area);
            if (areaGeometry != null && areaGeometry.contains(uavPoint)) {
                FenceActionTypeEnum actionEnum = FenceActionTypeEnum.getByCode(area.getActionType());
                String actionDesc = actionEnum != null ? actionEnum.getInfo() : "未知动作";

                return FenceDetectionResult.builder().violated(true).fenceId(fence.getId()).violatedAreaId(area.getId())
                    .actionToTake(area.getActionType())
                    .violationReason("无人机位置进入排除型围栏区域，将执行" + actionDesc).build();
            }
        }

        return FenceDetectionResult.builder().violated(false).build();
    }

    /**
     * 检查高度限制围栏
     */
    private FenceDetectionResult checkHeightFence(FenceInfo fence, List<FenceArea> areas, BigDecimal height) {
        for (FenceArea area : areas) {
            // 检查高度是否超出范围
            if (height.compareTo(area.getMinHeight()) < 0) {
                FenceActionTypeEnum actionEnum = FenceActionTypeEnum.getByCode(area.getActionType());
                String actionDesc = actionEnum != null ? actionEnum.getInfo() : "未知动作";

                return FenceDetectionResult.builder().violated(true).fenceId(fence.getId()).violatedAreaId(area.getId())
                    .actionToTake(area.getActionType())
                    .violationReason("无人机高度低于最小高度限制: " + area.getMinHeight() + "米，将执行" + actionDesc)
                    .build();
            }

            if (height.compareTo(area.getMaxHeight()) > 0) {
                FenceActionTypeEnum actionEnum = FenceActionTypeEnum.getByCode(area.getActionType());
                String actionDesc = actionEnum != null ? actionEnum.getInfo() : "未知动作";

                return FenceDetectionResult.builder().violated(true).fenceId(fence.getId()).violatedAreaId(area.getId())
                    .actionToTake(area.getActionType())
                    .violationReason("无人机高度超过最大高度限制: " + area.getMaxHeight() + "米，将执行" + actionDesc)
                    .build();
            }
        }

        return FenceDetectionResult.builder().violated(false).build();
    }

    /**
     * 检查复合型围栏
     */
    private FenceDetectionResult checkCompoundFence(FenceInfo fence, List<FenceArea> areas, Point uavPoint,
        BigDecimal height) {
        // 划分不同类型的区域
        List<FenceArea> inclusiveAreas = new ArrayList<>();
        List<FenceArea> exclusiveAreas = new ArrayList<>();
        List<FenceArea> heightAreas = new ArrayList<>();

        for (FenceArea area : areas) {
            if (area.getAreaType().equals(FenceAreaTypeEnum.CIRCLE.getCode()) ||
                area.getAreaType().equals(FenceAreaTypeEnum.POLYGON.getCode()) ||
                area.getAreaType().equals(FenceAreaTypeEnum.RECTANGLE.getCode())) {

                // 判断是排除区域还是包含区域（根据实际业务逻辑调整）
                if (area.getMinHeight().compareTo(BigDecimal.ZERO) == 0 &&
                    area.getMaxHeight().compareTo(new BigDecimal("10000")) >= 0) {
                    // 高度范围非常大，可能是普通区域
                    exclusiveAreas.add(area);
                } else {
                    // 有具体高度限制，可能是高度区域
                    heightAreas.add(area);
                }
            }
        }

        // 检查排除区域
        for (FenceArea area : exclusiveAreas) {
            Geometry areaGeometry = getGeometry(area);
            if (areaGeometry != null && areaGeometry.contains(uavPoint) && height.compareTo(area.getMinHeight()) >= 0 &&
                height.compareTo(area.getMaxHeight()) <= 0) {
                FenceActionTypeEnum actionEnum = FenceActionTypeEnum.getByCode(area.getActionType());
                String actionDesc = actionEnum != null ? actionEnum.getInfo() : "未知动作";

                return FenceDetectionResult.builder().violated(true).fenceId(fence.getId()).violatedAreaId(area.getId())
                    .actionToTake(area.getActionType())
                    .violationReason("无人机位置进入复合围栏的排除区域，将执行" + actionDesc).build();
            }
        }

        // 检查高度限制
        for (FenceArea area : heightAreas) {
            Geometry areaGeometry = getGeometry(area);
            if (areaGeometry != null && areaGeometry.contains(uavPoint)) {
                if (height.compareTo(area.getMinHeight()) < 0) {
                    FenceActionTypeEnum actionEnum = FenceActionTypeEnum.getByCode(area.getActionType());
                    String actionDesc = actionEnum != null ? actionEnum.getInfo() : "未知动作";

                    return FenceDetectionResult.builder().violated(true).fenceId(fence.getId())
                        .violatedAreaId(area.getId()).actionToTake(area.getActionType()).violationReason(
                            "无人机高度低于复合围栏的最小高度限制: " + area.getMinHeight() + "米，将执行" + actionDesc)
                        .build();
                }
                if (height.compareTo(area.getMaxHeight()) > 0) {
                    FenceActionTypeEnum actionEnum = FenceActionTypeEnum.getByCode(area.getActionType());
                    String actionDesc = actionEnum != null ? actionEnum.getInfo() : "未知动作";

                    return FenceDetectionResult.builder().violated(true).fenceId(fence.getId())
                        .violatedAreaId(area.getId()).actionToTake(area.getActionType()).violationReason(
                            "无人机高度超过复合围栏的最大高度限制: " + area.getMaxHeight() + "米，将执行" + actionDesc)
                        .build();
                }
            }
        }

        return FenceDetectionResult.builder().violated(false).build();
    }

    /**
     * 查找最严格的区域
     */
    private FenceArea findMostRestrictiveArea(List<FenceArea> areas) {
        if (areas == null || areas.isEmpty()) {
            return null;
        }

        // 按照动作类型的严格程度排序（降落 > 悬停 > 返航 > 警告）
        // 如果枚举获取失败，则使用原始整数比较
        // 使用枚举的code值进行比较，code值越大表示动作越严格
        return areas.stream().min((a1, a2) -> {
            FenceActionTypeEnum action1 = FenceActionTypeEnum.getByCode(a1.getActionType());
            FenceActionTypeEnum action2 = FenceActionTypeEnum.getByCode(a2.getActionType());
            // 如果枚举获取失败，则使用原始整数比较
            if (action1 == null || action2 == null) {
                return Integer.compare(a2.getActionType(), a1.getActionType());
            }
            // 使用枚举的code值进行比较，code值越大表示动作越严格
            return Integer.compare(action2.getCode(), action1.getCode());
        }).orElse(areas.get(0));
    }

    /**
     * 记录违规日志
     */
    private void recordViolation(String fenceId, String taskId, String uavCode, BigDecimal longitude,
        BigDecimal latitude, BigDecimal height, Integer actionTaken) {
        try {
            // 获取动作类型枚举
            FenceActionTypeEnum actionEnum = FenceActionTypeEnum.getByCode(actionTaken);
            String actionDesc = actionEnum != null ? actionEnum.getInfo() : "未知动作";

            // 创建违规日志记录
            FenceViolationLog violationLog = new FenceViolationLog();
            violationLog.setId(IdWorker.getIdStr());
            violationLog.setFenceId(fenceId);
            violationLog.setTaskId(taskId);
            violationLog.setUavCode(uavCode);
            violationLog.setLongitude(longitude);
            violationLog.setLatitude(latitude);
            violationLog.setHeight(height);
            violationLog.setActionTaken(actionTaken);
            violationLog.setViolationTime(new Date());

            // 如果有备注字段，可以将动作描述添加到备注中
            violationLog.setRemark("执行动作: " + actionDesc);

            // 保存到数据库
            fenceViolationLogMapper.insertViolationLog(violationLog);
            log.info("记录围栏违规日志成功, id: {}, fenceId: {}, taskId: {}, uavCode: {}, 执行动作: {}",
                violationLog.getId(), fenceId, taskId, uavCode, actionDesc);
        } catch (Exception e) {
            log.error("记录围栏违规日志失败", e);
        }
    }

    /**
     * 创建JTS点对象
     */
    private Point createPoint(double longitude, double latitude) {
        return GeoUtils.createPoint(longitude, latitude);
    }

    /**
     * 从顶点列表创建JTS多边形对象
     */
    private Polygon createPolygonFromVertices(List<FenceVertex> vertices) {
        return GeoUtils.createPolygonFromVertices(vertices);
    }

    /**
     * 创建JTS圆形对象（使用多边形近似）
     */
    private Geometry createCircle(double centerLongitude, double centerLatitude, double radiusMeters) {
        return GeoUtils.createCircle(centerLongitude, centerLatitude, radiusMeters);
    }

    @Override
    public void updateFenceCache(String fenceId) {
        log.info("更新围栏缓存, fenceId: {}", fenceId);
        loadFenceToCache(fenceId);
    }

    @Override
    public void removeFenceFromCache(String fenceId) {
        log.info("从缓存中移除围栏, fenceId: {}", fenceId);
        // 删除围栏信息缓存
        RedisUtils.deleteObject(FenceConstants.REDIS_FENCE_INFO_KEY + fenceId);
        // 删除围栏区域缓存
        RedisUtils.deleteObject(FenceConstants.REDIS_FENCE_AREAS_KEY + fenceId);
        // 获取围栏区域列表
        List<FenceArea> areas = fenceAreaMapper.selectByFenceId(fenceId);
        if (areas != null && !areas.isEmpty()) {
            // 删除围栏顶点和多边形缓存
            for (FenceArea area : areas) {
                RedisUtils.deleteObject(FenceConstants.REDIS_FENCE_VERTICES_KEY + area.getId());
                RedisUtils.deleteObject(FenceConstants.REDIS_FENCE_POLYGON_KEY + area.getId());
                // 删除JTS几何对象缓存
                geometryCache.remove(area.getId());
            }
        }
    }

    @Override
    public int loadAllActiveFencesToCache() {
        log.info("开始加载所有活动围栏到缓存");
        int count = 0;
        try {
            // 1. 从数据库获取所有状态为"启用"的围栏
            List<FenceInfo> activeFences = fenceInfoMapper.selectActiveFences();
            if (activeFences.isEmpty()) {
                log.info("没有启用状态的围栏");
                return 0;
            }

            // 2. 从Redis获取所有已缓存的围栏信息
            Collection<String> fenceInfoKeysCollection = RedisUtils.keys(FenceConstants.REDIS_FENCE_INFO_KEY + "*");
            if (fenceInfoKeysCollection == null || fenceInfoKeysCollection.isEmpty()) {
                log.debug("Redis中没有缓存的围栏信息");
                fenceInfoKeysCollection = new ArrayList<>();
            }

            // 转换为围栏ID集合
            Set<String> cachedFenceIds = new HashSet<>();
            String prefix = FenceConstants.REDIS_FENCE_INFO_KEY;
            for (String key : fenceInfoKeysCollection) {
                if (key.startsWith(prefix)) {
                    cachedFenceIds.add(key.substring(prefix.length()));
                }
            }

            // 3. 加载未缓存的活动围栏
            for (FenceInfo fence : activeFences) {
                if (!cachedFenceIds.contains(fence.getId())) {
                    loadFenceToCache(fence.getId());
                    count++;
                    log.debug("已加载围栏: id={}, name={}", fence.getId(), fence.getFenceName());
                }
            }

            log.info("已加载{}个新的活动围栏到缓存", count);
        } catch (Exception e) {
            log.error("加载活动围栏到缓存时发生异常", e);
        }
        return count;
    }

    @Override
    public void cleanUnusedFenceCache() {
        log.info("开始清理未使用的围栏缓存");
        // 获取所有任务相关的围栏缓存键
        Collection<String> taskKeys = RedisUtils.keys(FenceConstants.REDIS_FENCE_PREFIX + "task:*");
        // 抽取所有活动任务关联的围栏ID
        Set<String> activeFenceIds = new HashSet<>();
        for (String key : taskKeys) {
            String fenceIdsStr = RedisUtils.getCacheObject(key);
            if (!StringUtils.isEmpty(fenceIdsStr)) {
                String[] fenceIdArray = fenceIdsStr.split(",");
                for (String id : fenceIdArray) {
                    if (!StringUtils.isEmpty(id.trim())) {
                        activeFenceIds.add(id.trim());
                    }
                }
            }
        }

        // 获取所有缓存的围栏信息键
        Collection<String> fenceInfoKeys = RedisUtils.keys(FenceConstants.REDIS_FENCE_INFO_KEY + "*");
        int cleanCount = 0;
        for (String key : fenceInfoKeys) {
            String fenceId = key.substring(FenceConstants.REDIS_FENCE_INFO_KEY.length());
            if (!activeFenceIds.contains(fenceId)) {
                // 移除未使用的围栏缓存
                removeFenceFromCache(fenceId);
                cleanCount++;
            }
        }
        log.info("已清理{}个未使用的围栏缓存", cleanCount);
    }

    /**
     * 新增：使用业务对象获取附近围栏
     */
    @Override
    public List<FenceVo> getNearbyFences(FenceDetectionBo bo) {
        if (bo == null || bo.getLongitude() == null || bo.getLatitude() == null) {
            return new ArrayList<>();
        }

        Long radius = bo.getRadius() != null ? bo.getRadius() : 10000L;
        return getNearbyFences(bo.getLongitude(), bo.getLatitude(), radius);
    }

    /**
     * 新增：使用业务对象获取附近围栏（分页版本）
     */
    @Override
    public TableDataInfo<FenceVo> getNearbyFencesPage(FenceDetectionBo bo, PageQuery pageQuery) {
        if (bo == null || bo.getLongitude() == null || bo.getLatitude() == null) {
            return TableDataInfo.build(new ArrayList<>());
        }

        Long radius = bo.getRadius() != null ? bo.getRadius() : 10000L;
        return getNearbyFences(bo.getLongitude(), bo.getLatitude(), radius, pageQuery);
    }

    @Override
    public List<FenceVo> getNearbyFences(BigDecimal longitude, BigDecimal latitude, Long radius) {
        List<FenceVo> result = new ArrayList<>();

        try {
            // 1. 创建查询点的JTS Point对象
            Point queryPoint = createPoint(longitude.doubleValue(), latitude.doubleValue());

            // 2. 从Redis获取所有已缓存的围栏信息
            Collection<String> fenceInfoKeysCollection = RedisUtils.keys(FenceConstants.REDIS_FENCE_INFO_KEY + "*");
            if (fenceInfoKeysCollection == null || fenceInfoKeysCollection.isEmpty()) {
                log.debug("Redis中没有缓存的围栏信息");
                return result;
            }

            // 3. 遍历所有围栏
            for (String key : fenceInfoKeysCollection) {
                String fenceId = key.substring(FenceConstants.REDIS_FENCE_INFO_KEY.length());
                FenceInfo fenceInfo = getCachedFenceInfo(fenceId);

                // 跳过禁用的围栏
                if (fenceInfo == null || fenceInfo.getStatus() == 0) {
                    continue;
                }

                // 获取围栏区域
                List<FenceArea> areas = getCachedFenceAreas(fenceId);
                if (areas == null || areas.isEmpty()) {
                    continue;
                }

                // 4. 检查每个区域是否在范围内
                boolean isNearby = false;
                double minDistance = Double.MAX_VALUE;
                List<FenceAreaVO> areaVOList = new ArrayList<>();

                for (FenceArea area : areas) {
                    double distance;
                    FenceAreaVO areaVO = new FenceAreaVO();
                    BeanUtils.copyProperties(area, areaVO);

                    if (area.getAreaType() == FenceAreaTypeEnum.CIRCLE.getCode()) {
                        // 圆形区域
                        distance = calculateDistanceToCircle(queryPoint, area.getCenterLongitude().doubleValue(),
                            area.getCenterLatitude().doubleValue());
                    } else {
                        // 多边形或矩形区域
                        List<FenceVertex> vertices = getCachedVertices(area.getId());
                        if (vertices != null && !vertices.isEmpty()) {
                            distance = calculateDistanceToPolygon(queryPoint, vertices);
                            // 转换顶点列表
                            List<FenceVertexVO> vertexVOList = vertices.stream().map(vertex -> {
                                FenceVertexVO vertexVO = new FenceVertexVO();
                                BeanUtils.copyProperties(vertex, vertexVO);
                                return vertexVO;
                            }).collect(Collectors.toList());
                            areaVO.setVertices(vertexVOList);
                        } else {
                            continue;
                        }
                    }

                    // 更新最小距离
                    if (distance < minDistance) {
                        minDistance = distance;
                    }

                    // 如果区域在范围内，添加到区域列表
                    if (distance <= radius) {
                        isNearby = true;
                        areaVOList.add(areaVO);
                    }
                }

                // 5. 如果围栏在范围内，添加到结果集
                if (isNearby) {
                    FenceVo fenceVO = new FenceVo();
                    BeanUtils.copyProperties(fenceInfo, fenceVO);
                    fenceVO.setAreas(areaVOList);
                    result.add(fenceVO);
                }
            }

            // 6. 按照创建时间降序排序
            result.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));

        } catch (Exception e) {
            log.error("获取附近围栏失败", e);
        }

        return result;
    }

    @Override
    public TableDataInfo<FenceVo> getNearbyFences(BigDecimal longitude, BigDecimal latitude, Long radius,
        PageQuery pageQuery) {
        // 获取所有符合条件的围栏
        List<FenceVo> allNearbyFences = getNearbyFences(longitude, latitude, radius);

        // 执行内存分页
        Page<FenceVo> page = new Page<>();
        int pageSize = pageQuery.getPageSize() == null ? PageQuery.DEFAULT_PAGE_SIZE : pageQuery.getPageSize();
        int pageNum = pageQuery.getPageNum() == null ? PageQuery.DEFAULT_PAGE_NUM : pageQuery.getPageNum();

        // 计算总记录数和分页结果
        int total = allNearbyFences.size();
        List<FenceVo> records;

        if (pageSize == Integer.MAX_VALUE) {
            records = allNearbyFences;
        } else {
            int fromIndex = (pageNum - 1) * pageSize;
            if (fromIndex >= total) {
                records = new ArrayList<>();
            } else {
                int toIndex = Math.min(fromIndex + pageSize, total);
                records = allNearbyFences.subList(fromIndex, toIndex);
            }
        }

        page.setRecords(records);
        page.setTotal(total);

        // 构建TableDataInfo对象并返回
        return TableDataInfo.build(page);
    }

    /**
     * 计算点到圆形区域的最短距离
     */
    private double calculateDistanceToCircle(Point point, double centerLon, double centerLat) {
        return GeoUtils.calculateDistance(point.getY(), point.getX(), centerLat, centerLon);
    }

    /**
     * 计算点到多边形的最短距离
     */
    private double calculateDistanceToPolygon(Point point, List<FenceVertex> vertices) {
        // 创建多边形
        Coordinate[] coordinates = new Coordinate[vertices.size() + 1];
        for (int i = 0; i < vertices.size(); i++) {
            FenceVertex vertex = vertices.get(i);
            coordinates[i] = new Coordinate(vertex.getLongitude().doubleValue(), vertex.getLatitude().doubleValue());
        }
        // 闭合多边形
        coordinates[vertices.size()] = coordinates[0];

        LinearRing ring = geometryFactory.createLinearRing(coordinates);
        Polygon polygon = geometryFactory.createPolygon(ring);

        // 如果点在多边形内，距离为0
        if (polygon.contains(point)) {
            return 0;
        }

        // 计算点到多边形边界的最短距离
        double minDistance = Double.MAX_VALUE;
        for (int i = 0; i < vertices.size(); i++) {
            FenceVertex v1 = vertices.get(i);
            FenceVertex v2 = vertices.get((i + 1) % vertices.size());

            double distance = calculateDistanceToLine(point.getX(), point.getY(), v1.getLongitude().doubleValue(),
                v1.getLatitude().doubleValue(), v2.getLongitude().doubleValue(), v2.getLatitude().doubleValue());

            if (distance < minDistance) {
                minDistance = distance;
            }
        }

        return minDistance;
    }

    /**
     * 计算点到线段的最短距离
     */
    private double calculateDistanceToLine(double px, double py, double x1, double y1, double x2, double y2) {
        return GeoUtils.calculateDistanceToLine(py, px, y1, x1, y2, x2);
    }
}
