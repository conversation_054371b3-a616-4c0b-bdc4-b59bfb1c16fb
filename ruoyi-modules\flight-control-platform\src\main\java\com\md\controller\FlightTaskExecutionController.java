package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.md.service.IFlightTaskExecutionService;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 航线任务执行控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/flight/task/execution")
@Validated
public class FlightTaskExecutionController extends BaseController {

    private final IFlightTaskExecutionService flightTaskExecutionService;

    /**
     * 查询无人机当前任务状态
     */
    @GetMapping("/current/{uavCode}")
    @SaCheckPermission(value = {"flight:task:query", "flight:task:execute"}, mode = SaMode.OR)
    public R<Object> getCurrentTask(@PathVariable("uavCode") @NotBlank(message = "无人机编码不能为空") String uavCode) {
        return R.ok(flightTaskExecutionService.getCurrentTask(uavCode));
    }

    /**
     * 执行航线任务
     */
    @SaCheckPermission("flight:task:execute")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.UPDATE)
    @PostMapping("/execute/{taskId}")
    public R<String> executeTask(@PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId) {
        try {
            CompletableFuture<Boolean> future = flightTaskExecutionService.executeFlightTask(taskId);
            // 等待任务执行完成
            Boolean result = future.get();
            if (result) {
                return R.ok("任务执行成功");
            } else {
                return R.fail("任务执行失败");
            }
        } catch (Exception e) {
            return R.fail("执行航线任务失败：" + e.getMessage());
        }
    }

    /**
     * 根据航线ID和无人机编码创建航线任务
     *
     * @param executeImmediately 是否立即执行任务，默认false
     */
    @SaCheckPermission("flight:task:add")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.INSERT)
    @PostMapping("/createFromLine")
    public R<String> createTaskFromLine(@RequestParam @NotBlank(message = "航线ID不能为空") String lineId,
        @RequestParam @NotBlank(message = "无人机编码不能为空") String uavCode,
        @RequestParam(required = false, defaultValue = "false") Boolean executeImmediately) {
        try {
            String taskId = flightTaskExecutionService.createTaskFromLine(lineId, uavCode, executeImmediately);
            if (StringUtils.isEmpty(taskId)) {
                return R.fail("创建航线任务失败：未返回有效的任务ID");
            }
            return R.ok(taskId, "创建航线任务成功" + (executeImmediately ? "并已开始执行" : ""));
        } catch (ServiceException e) {
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("创建航线任务异常", e);
            return R.fail("创建航线任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取厂商支持的协议类型列表
     *
     * @param manufacturerType 厂商类型
     * @return 协议类型列表
     */
    @SaCheckPermission("flight:task:query")
    @GetMapping("/protocols/{manufacturerType}")
    public R<Set<String>> getSupportedProtocols(
        @PathVariable("manufacturerType") @NotBlank(message = "厂商类型不能为空") String manufacturerType) {
        try {
            Set<String> protocols = flightTaskExecutionService.getSupportedProtocols(manufacturerType);
            return R.ok(protocols);
        } catch (IllegalArgumentException e) {
            log.error("获取协议类型列表失败", e);
            return R.fail("不支持的厂商类型: " + e.getMessage());
        }
    }
}