package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 飞控平台对象 fk_platform_info
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "platform_info")
public class PlatformInfo extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 飞控厂商名称
     */
    @TableField(value = "manufacturer_name")
    private String manufacturerName;

    /**
     * 飞控编号
     */
    @TableField(value = "flight_control_no")
    private String flightControlNo;

    /**
     * 飞控网络地址
     */
    @TableField(value = "network_address")
    private String networkAddress;

    /**
     * 账号
     */
    @TableField(value = "account")
    private String account;

    /**
     * 密码
     */
    @TableField(value = "`password`")
    private String password;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 部门ID
     */
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 秘钥
     */
    @TableField(value = "aes_key")
    private String aesKey;

    private static final long serialVersionUID = 1L;
}
