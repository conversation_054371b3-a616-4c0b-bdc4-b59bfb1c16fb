package com.md.command.aspect;

import com.md.command.annotation.CommandLog;
import com.md.command.constant.CommandType;
import com.md.command.constant.ExecutorType;
import com.md.command.core.DroneCommand;
import com.md.command.core.DroneCommandExecutor;
import com.md.command.core.DroneCommandFactory;
import com.md.command.model.dto.CommandResult;
import com.md.domain.po.DroneCommandLog;
import com.md.command.service.DroneCommandLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.UUID;

/**
 * 无人机指令切面
 */
@Slf4j
@Aspect
@Component
public class DroneCommandAspect {

    @Autowired
    private DroneCommandLogService commandLogService;

    @Autowired
    private DroneCommandFactory commandFactory;

    @Around("@annotation(com.md.command.annotation.CommandLog)")
    public Object aroundExecuteCommand(ProceedingJoinPoint point) throws Throwable {
        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 获取注解信息
        MethodSignature signature = (MethodSignature)point.getSignature();
        Method method = signature.getMethod();
        CommandLog commandLog = method.getAnnotation(CommandLog.class);

        // 获取参数
        Object[] args = point.getArgs();
        DroneCommand command = (DroneCommand)args[0];

        // 检查是否提供了ExecutorType参数
        ExecutorType executorType = null;
        if (args.length > 1 && args[1] instanceof ExecutorType) {
            executorType = (ExecutorType)args[1];
        } else {
            // 如果没有提供ExecutorType，则通过DroneCommandFactory获取
            try {
                // 获取无人机对应的执行器
                DroneCommandExecutor executor = commandFactory.getExecutor(command.getDroneId());
                // 根据执行器的协议类型确定ExecutorType
                String protocol = executor.getSupportedProtocol();
                executorType = getExecutorTypeFromProtocol(protocol);
                log.info("未提供ExecutorType，根据无人机ID自动确定为: {}，协议类型: {}", executorType, protocol);
            } catch (Exception e) {
                log.error("无法确定无人机的执行器类型: {}", e.getMessage());
            }
        }

        // 生成commandId
        final String commandId = UUID.randomUUID().toString();

        // 包装原始命令，注入commandId
        DroneCommand wrappedCommand = new DroneCommand() {
            @Override
            public String getDroneId() {
                return command.getDroneId();
            }

            @Override
            public CommandType getCommandType() {
                return command.getCommandType();
            }

            @Override
            public Map<String, Object> getParameters() {
                return command.getParameters();
            }

            @Override
            public String getCommandId() {
                return commandId;
            }

            @Override
            public String getTaskId() {
                return command.getTaskId();
            }
        };

        // 创建指令日志
        DroneCommandLog logEntity = null;
        if (commandLog.isSaveRequestData()) {
            logEntity = commandLogService.createCommandLog(wrappedCommand, executorType);
            // 设置描述信息，使用CommandType中的detail
            String remark = wrappedCommand.getCommandType().getDetail();
            logEntity.setRemark(remark);
            commandLogService.updateById(logEntity);
        }

        try {
            // 使用包装后的命令执行原方法
            args[0] = wrappedCommand;
            CommandResult result = (CommandResult)point.proceed(args);

            // 更新执行结果
            if (logEntity != null && commandLog.isSaveResponseData()) {
                commandLogService.updateCommandResult(commandId, result, System.currentTimeMillis() - startTime);
            }

            return result;
        } catch (Exception e) {
            // 更新失败结果
            if (logEntity != null && commandLog.isSaveResponseData()) {
                CommandResult failResult = CommandResult.failed(e.getMessage());
                commandLogService.updateCommandResult(commandId, failResult, System.currentTimeMillis() - startTime);
            }
            throw e;
        }
    }

    /**
     * 根据协议类型获取ExecutorType
     *
     * @param protocol 协议类型
     * @return ExecutorType
     */
    private ExecutorType getExecutorTypeFromProtocol(String protocol) {
        return switch (protocol.toUpperCase()) {
            case "MAVLINK" -> ExecutorType.MAVLINK;
            case "MQTT" -> ExecutorType.MQTT;
            case "HTTP" -> ExecutorType.HTTP;
            case "WEBSOCKET" -> ExecutorType.WEBSOCKET;
            default -> {
                log.warn("未知的协议类型: {}，使用默认类型MAVLINK", protocol);
                yield ExecutorType.MAVLINK;
            }
        };
    }
}