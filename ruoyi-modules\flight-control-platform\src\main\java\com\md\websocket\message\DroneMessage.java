package com.md.websocket.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WebSocket消息实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DroneMessage {
    /**
     * 消息类型
     */
    private String type;

    /**
     * 无人机ID
     */
    private String droneId;

    /**
     * 消息数据
     */
    private Object data;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 消息来源主题
     */
    private String topic;

    /**
     * 厂商名称
     */
    private String manufacturer;
}