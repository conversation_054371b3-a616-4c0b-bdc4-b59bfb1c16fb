package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.vo.FlightLineVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 航线管理对象 flight_line
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMapper(target = FlightLineVo.class, reverseConvertGenerate = false)
@TableName(value = "flight_line")
public class FlightLine extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 飞控平台ID
     */
    //    @Excel(name = "飞控平台ID")
    @TableField(value = "flight_control_no")
    private String flightControlNo;

    /**
     * 航线名称
     */
    //    @Excel(name = "航线名称")
    @TableField(value = "line_name")
    private String lineName;

    /**
     * 飞行速度(m/s)
     */
    //    @Excel(name = "飞行速度(m/s)")
    @TableField(value = "flight_speed")
    private BigDecimal flightSpeed;

    /**
     * 飞行高度(m)
     */
    //    @Excel(name = "飞行高度(m)")
    @TableField(value = "flight_height")
    private BigDecimal flightHeight;

    /**
     * 围栏ID
     */
    @TableField(value = "fence_id")
    private String fenceId;

    /**
     * 返航高度(m)
     */
    //    @Excel(name = "返航高度(m)")
    @TableField(value = "return_height")
    private BigDecimal returnHeight;

    /**
     * 数据来源
     */
    //    @Excel(name = "数据来源")
    @TableField(value = "data_source")
    private String dataSource;

    /**
     * 网络失联后动作(返航/继续执行)
     */
    @TableField(value = "missing_action")
    private String missingAction;

    /**
     * 任务结束后的动作(原地降落/返回起点等)
     */
    @TableField(value = "finished_action")
    private String finishedAction;



    /**
     * 文件生成状态(0:未生成 1:生成中 2:已生成 3:生成失败)
     */
    @TableField(value = "file_generate_status")
    private Integer fileGenerateStatus;

    /**
     * 航线文件名称
     */
    @TableField(value = "kmz_file_name")
    private String kmzFileName;

    /**
     * 航线文件存储路径
     */
    @TableField(value = "kmz_file_path")
    private String kmzFilePath;

    /**
     * 文件大小(字节)
     */
    @TableField(value = "file_size")
    private Long fileSize;

    /**
     * 文件生成时间
     */
    @TableField(value = "file_generate_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fileGenerateTime;

    /**
     * 文件生成错误信息
     */
    @TableField(value = "file_generate_error")
    private String fileGenerateError;

    /**
     * 是否已上传(0:未上传 1:已上传)
     */
    @TableField(value = "is_uploaded")
    private Integer isUploaded;

    @TableField(exist = false)
    private List<FlightPoint> points;  // 航点列表

    /**
     * 租户编号
     */
    private String tenantId;
}
