package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 围栏区域实体 fence_area
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "fence_area")
public class FenceArea extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 围栏ID
     */
    @TableField(value = "fence_id")
    private String fenceId;

    /**
     * 区域类型(1:圆形 2:多边形 3:矩形)
     */
    @TableField(value = "area_type")
    private Integer areaType;

    /**
     * 中心点经度
     */
    @TableField(value = "center_longitude")
    private BigDecimal centerLongitude;

    /**
     * 中心点纬度
     */
    @TableField(value = "center_latitude")
    private BigDecimal centerLatitude;

    /**
     * 半径(米)
     */
    @TableField(value = "radius")
    private BigDecimal radius;

    /**
     * 最小高度(米)
     */
    @TableField(value = "min_height")
    private BigDecimal minHeight;

    /**
     * 最大高度(米)
     */
    @TableField(value = "max_height")
    private BigDecimal maxHeight;

    /**
     * 触发动作(1:警告 2:返航 3:悬停 4:降落)
     */
    @TableField(value = "action_type")
    private Integer actionType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}
