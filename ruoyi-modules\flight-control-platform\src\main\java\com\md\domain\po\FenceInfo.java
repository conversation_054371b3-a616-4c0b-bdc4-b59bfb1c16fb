package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.vo.FenceVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 电子围栏信息实体 fence_info
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@AutoMapper(target = FenceVo.class, reverseConvertGenerate = false)
@TableName(value = "fence_info")
public class FenceInfo extends BaseEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 围栏名称
     */
    @TableField(value = "fence_name")
    private String fenceName;

    /**
     * 围栏类型(1:包含 2:排除 3:高度 4:复合)
     */
    @TableField(value = "fence_type")
    private Integer fenceType;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 状态(0:禁用 1:启用)
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    private static final long serialVersionUID = 1L;
}
