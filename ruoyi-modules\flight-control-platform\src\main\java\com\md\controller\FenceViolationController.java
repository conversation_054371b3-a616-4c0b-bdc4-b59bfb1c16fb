package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.domain.bo.FenceViolationBo;
import com.md.domain.vo.FenceViolationVo;
import com.md.service.IFenceViolationService;
import lombok.RequiredArgsConstructor;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;

import java.util.List;

/**
 * 围栏违规记录 控制器
 */
@RestController
@RequestMapping("/fence/violation")
@Validated
@RequiredArgsConstructor
public class FenceViolationController extends BaseController {

    private final IFenceViolationService fenceViolationService;

    /**
     * 查询违规记录列表
     */
    @SaCheckPermission("fence:violation:list")
    @GetMapping("/list")
    public TableDataInfo<FenceViolationVo> list(FenceViolationBo bo, PageQuery pageQuery) {
        return fenceViolationService.selectViolationList(bo, pageQuery);
    }

    /**
     * 导出违规记录列表
     */
    @SaCheckPermission("fence:violation:export")
    @Log(title = "围栏违规记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FenceViolationBo bo) {
        List<FenceViolationVo> list = fenceViolationService.selectViolationList(bo);
        ExcelUtil.exportExcel(list, "围栏违规记录", FenceViolationVo.class, response);
    }

    /**
     * 获取违规记录详细信息
     */
    @SaCheckPermission("fence:violation:query")
    @GetMapping("/{id}")
    public R<FenceViolationVo> getInfo(@PathVariable("id") @NotBlank(message = "记录ID不能为空") String id) {
        return R.ok(fenceViolationService.selectViolationById(id));
    }

    /**
     * 按任务ID查询违规记录
     */
    @SaCheckPermission("fence:violation:query")
    @GetMapping("/task/{taskId}")
    public TableDataInfo<FenceViolationVo> getByTaskId(
        @PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId, PageQuery pageQuery) {
        return fenceViolationService.selectViolationByTaskId(taskId, pageQuery);
    }

    /**
     * 按无人机编码查询违规记录
     */
    @SaCheckPermission("fence:violation:query")
    @GetMapping("/uav/{uavCode}")
    public TableDataInfo<FenceViolationVo> getByUavCode(
        @PathVariable("uavCode") @NotBlank(message = "无人机编码不能为空") String uavCode, PageQuery pageQuery) {
        return fenceViolationService.selectViolationByUavCode(uavCode, pageQuery);
    }

    /**
     * 按围栏ID查询违规记录
     */
    @SaCheckPermission("fence:violation:query")
    @GetMapping("/fence/{fenceId}")
    public TableDataInfo<FenceViolationVo> getByFenceId(
        @PathVariable("fenceId") @NotBlank(message = "围栏ID不能为空") String fenceId, PageQuery pageQuery) {
        return fenceViolationService.selectViolationByFenceId(fenceId, pageQuery);
    }

    /**
     * 删除违规记录
     */
    @SaCheckPermission("fence:violation:remove")
    @Log(title = "围栏违规记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable @NotEmpty(message = "记录ID不能为空") String[] ids) {
        return toAjax(fenceViolationService.deleteViolationByIds(ids));
    }
}