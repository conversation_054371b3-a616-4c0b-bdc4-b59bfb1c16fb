package com.md.flight.lh.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.domain.po.FlightLine;
import com.md.domain.vo.FlightLineVo;
import com.md.flight.lh.constant.LhTopicConstantReq;
import com.md.flight.lh.constant.LhTopicConstantRsp;
import com.md.flight.lh.domain.dto.*;
import com.md.flight.lh.enums.LhFinishActionEnum;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import com.md.service.IBaseInfoService;
import com.md.service.impl.FlightLineServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Deprecated 联合基本信息实现类
 * @date 2024年12月17日 10:38
 */
@Service
@Slf4j
public class LhBaseInfoServiceImpl implements IBaseInfoService {

    @Autowired
    private FlightLineServiceImpl flightLineService;

    private final String LH_FLIGHT_LINE_UPLOAD="lh:flight_line_upload:";

    @Override
    public void syncUAV() {

    }

    @Override
    public void syncFlyer() {

    }

    @Override
    public void syncFlightLine() {

    }

    @Override
    public boolean buildKmzByFlightLine(String routeId) {
        // 1. 获取航线和航点信息
        FlightLineVo flightLine = flightLineService.selectFlightLineById(routeId);

        long count =TenantHelper.ignore(() -> {return flightLineService.count(Wrappers.<FlightLine>lambdaQuery()
            .eq(FlightLine::getFlightControlNo, "LH"));});

        //2. 构建请求参数
        LhBaseReq<LhRouteUploadDto> lhBaseReq = new LhBaseReq<>();
        LhRouteUploadDto lhRouteUploadDto = new LhRouteUploadDto();
        LhRouteUploadFolderDto lhRouteUploadFolderDto= new LhRouteUploadFolderDto();
        List<LhRouteUploadPlacemarkDto> lhRouteUploadPlacemarkDtos = new ArrayList<>();

        // 3.1 设置航点参数
        flightLine.getPoints().forEach(flightPoint -> {
            //  构建航点信息
            LhRouteUploadPlacemarkDto lhRouteUploadPlacemarkDto= new LhRouteUploadPlacemarkDto();
            // 设置危险点
            lhRouteUploadPlacemarkDto.setIs_risky(0);
            // 设置航点索引
            lhRouteUploadPlacemarkDto.setIndex(flightPoint.getPointIndex().longValue());
            // 设置经纬度信息
            Map<String, String> point = new HashMap<>();
            String result = String.join(",",
                flightPoint.getLongitude().toString(),
                flightPoint.getLatitude().toString()
            );
            point.put("coordinates", result);
            lhRouteUploadPlacemarkDto.setPoint(point);
            // 设置高度信息
            lhRouteUploadPlacemarkDto.setExecute_height(flightPoint.getAltitude().floatValue());
            // 设置航点速度
            lhRouteUploadPlacemarkDto.setWaypoint_speed(flightPoint.getSpeed());
            // 设置是否使用全局速度
            lhRouteUploadPlacemarkDto.setUse_global_speed(0);
            // 添加航点
            lhRouteUploadPlacemarkDtos.add(lhRouteUploadPlacemarkDto);
        });

        // 3.2 设置航线信息
        // 设置航线速度
        lhRouteUploadFolderDto.setAuto_flight_speed(flightLine.getFlightSpeed().floatValue());
        // 设置航线高度模式
        lhRouteUploadFolderDto.setExecute_height_mode("WGS84");
        // 设置航线航点
        lhRouteUploadFolderDto.setPlacemark(lhRouteUploadPlacemarkDtos);
        // 设置航线id
        lhRouteUploadFolderDto.setTemplate_id(count);
        // 设置模板id
        lhRouteUploadFolderDto.setWayline_id(count);

        // 3.3 设置航线上传信息
        // 设置航线信息
        lhRouteUploadDto.setFolder(lhRouteUploadFolderDto);
        // 设置航线id
        lhRouteUploadDto.setWayline(flightLine.getId());
        // 设置航线任务信息
        LhRouteMissionConfigDto lhRouteMissionConfigDto=new LhRouteMissionConfigDto();
        lhRouteMissionConfigDto.setFinish_action(LhFinishActionEnum.getName(flightLine.getFinishedAction()));
        lhRouteUploadDto.setMission_config(lhRouteMissionConfigDto);

        // 3.3 设置航线请求信息
        lhBaseReq.setData(lhRouteUploadDto);
        lhBaseReq.setMethod("route_upload");
        lhBaseReq.setTimestamp(System.currentTimeMillis());
        lhBaseReq.setTid(IdUtil.fastSimpleUUID());
        lhBaseReq.setNeed_reply(1);

        // 4 上传航线至redis
        TenantHelper.ignore(() -> {
            RedisUtils.setCacheObject(LH_FLIGHT_LINE_UPLOAD+flightLine.getId(), lhBaseReq);
        });
        return true;
    }
}
