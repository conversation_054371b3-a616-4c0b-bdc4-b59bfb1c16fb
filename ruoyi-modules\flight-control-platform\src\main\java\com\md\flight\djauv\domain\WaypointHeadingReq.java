package com.md.flight.djauv.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Author:www
 * Date: 2024/12/22 13:05
 **/
@Data
@Accessors(chain = true)
public class WaypointHeadingReq implements Serializable {

    /**
     * 偏航角模式
     */
    private String waypointHeadingMode;

    /**
     * 偏航角度
     */
    private Double waypointHeadingAngle;

    /**
     * 兴趣点
     */
    private String waypointPoiPoint;
}
