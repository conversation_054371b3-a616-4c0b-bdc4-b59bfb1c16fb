package com.md.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 轨迹记录 Redis 分布式存储工具类
 * 为所有轨迹记录监听器提供统一的分布式状态管理
 * 使用BusinessDistributedLockUtils实现安全的分布式锁
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TrackRecordRedisUtils {

    // 使用统一的业务分布式锁工具类
    private final BusinessDistributedLockUtils lockUtils;

    // Redis 键前缀常量
    private static final String ACTIVE_TASKS_KEY_PREFIX = "track:active:";
    private static final String LAST_RECORD_TIME_KEY_PREFIX = "track:lasttime:";
    private static final String TASK_START_TIME_KEY_PREFIX = "track:starttime:";

    // 过期时间常量
    private static final Duration ACTIVE_TASK_EXPIRE = Duration.ofHours(3);
    private static final Duration RECORD_TIME_EXPIRE = Duration.ofMinutes(10);
    private static final Duration START_TIME_EXPIRE = Duration.ofHours(3);

    // 无人机类型前缀映射
    private static final Map<String, String> DRONE_TYPE_PREFIX_MAP =
        Map.of("LH", "lh", "PX4", "px4", "MAVLINK", "mavlink", "DJI", "dji");

    /**
     * 生成 Redis 键
     */
    private String getActiveTaskKey(String droneType, String droneId) {
        String prefix = DRONE_TYPE_PREFIX_MAP.getOrDefault(droneType.toUpperCase(), droneType.toLowerCase());
        return ACTIVE_TASKS_KEY_PREFIX + prefix + ":" + droneId;
    }

    private String getLastRecordTimeKey(String droneType, String droneId) {
        String prefix = DRONE_TYPE_PREFIX_MAP.getOrDefault(droneType.toUpperCase(), droneType.toLowerCase());
        return LAST_RECORD_TIME_KEY_PREFIX + prefix + ":" + droneId;
    }

    private String getTaskStartTimeKey(String droneType, String droneId) {
        String prefix = DRONE_TYPE_PREFIX_MAP.getOrDefault(droneType.toUpperCase(), droneType.toLowerCase());
        return TASK_START_TIME_KEY_PREFIX + prefix + ":" + droneId;
    }

    /**
     * 活动任务管理
     */
    public void setActiveTask(String droneType, String droneId, String taskId) {
        String key = getActiveTaskKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.setCacheObject(key, taskId, ACTIVE_TASK_EXPIRE));
    }

    public String getActiveTask(String droneType, String droneId) {
        String key = getActiveTaskKey(droneType, droneId);
        return TenantHelper.ignore(() -> RedisUtils.getCacheObject(key));
    }

    public void removeActiveTask(String droneType, String droneId) {
        String key = getActiveTaskKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.deleteObject(key));
    }

    /**
     * 获取指定类型的所有活动任务
     */
    public Map<String, String> getAllActiveTasks(String droneType) {
        Map<String, String> result = new HashMap<>();
        try {
            String prefix = DRONE_TYPE_PREFIX_MAP.getOrDefault(droneType.toUpperCase(), droneType.toLowerCase());
            String pattern = ACTIVE_TASKS_KEY_PREFIX + prefix + ":*";
            Collection<String> keys = TenantHelper.ignore(() -> RedisUtils.keys(pattern));

            String keyPrefix = ACTIVE_TASKS_KEY_PREFIX + prefix + ":";
            for (String key : keys) {
                String taskId = TenantHelper.ignore(() -> RedisUtils.getCacheObject(key));
                if (taskId != null) {
                    String droneId = key.substring(keyPrefix.length());
                    result.put(droneId, taskId);
                }
            }
        } catch (Exception e) {
            log.error("获取{}类型活动任务列表失败", droneType, e);
        }
        return result;
    }

    /**
     * 最后记录时间管理
     */
    public void setLastRecordTime(String droneType, String droneId, Long time) {
        String key = getLastRecordTimeKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.setCacheObject(key, time, RECORD_TIME_EXPIRE));
    }

    public Long getLastRecordTime(String droneType, String droneId) {
        String key = getLastRecordTimeKey(droneType, droneId);
        return TenantHelper.ignore(() -> RedisUtils.getCacheObject(key));
    }

    public void removeLastRecordTime(String droneType, String droneId) {
        String key = getLastRecordTimeKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.deleteObject(key));
    }

    /**
     * 任务开始时间管理
     */
    public void setTaskStartTime(String droneType, String droneId, Long time) {
        String key = getTaskStartTimeKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.setCacheObject(key, time, START_TIME_EXPIRE));
    }

    public Long getTaskStartTime(String droneType, String droneId) {
        String key = getTaskStartTimeKey(droneType, droneId);
        return TenantHelper.ignore(() -> RedisUtils.getCacheObject(key));
    }

    public void removeTaskStartTime(String droneType, String droneId) {
        String key = getTaskStartTimeKey(droneType, droneId);
        TenantHelper.ignore(() -> RedisUtils.deleteObject(key));
    }

    /**
     * 批量清理操作
     */
    public void cleanupTaskData(String droneType, String droneId) {
        removeActiveTask(droneType, droneId);
        removeLastRecordTime(droneType, droneId);
        removeTaskStartTime(droneType, droneId);
    }

    /**
     * 检查记录间隔
     */
    public boolean canRecordTrackPoint(String droneType, String droneId, long minInterval) {
        Long lastTime = getLastRecordTime(droneType, droneId);
        if (lastTime == null) {
            return true;
        }
        return (System.currentTimeMillis() - lastTime) >= minInterval;
    }

    /**
     * 检查任务是否超时
     */
    public boolean isTaskTimeout(String droneType, String droneId, long timeoutMillis) {
        Long startTime = getTaskStartTime(droneType, droneId);
        if (startTime == null) {
            return false;
        }
        return (System.currentTimeMillis() - startTime) > timeoutMillis;
    }

    /**
     * 安全的轨迹点记录检查（带分布式锁）
     *
     * @param droneType    无人机类型
     * @param droneId      无人机ID
     * @param minInterval  最小记录间隔（毫秒）
     * @param recordAction 记录操作的回调函数
     */
    public void tryRecordTrackPoint(String droneType, String droneId, long minInterval, Runnable recordAction) {
        // 使用统一的业务分布式锁工具类
        lockUtils.executeTrackRecordOperation(droneType, droneId, () -> {
            try {
                // 在锁保护下检查记录间隔
                if (!canRecordTrackPoint(droneType, droneId, minInterval)) {
                    log.debug("{}无人机[{}]记录间隔不足，跳过记录", droneType, droneId);
                    return;
                }

                // 执行记录操作
                recordAction.run();

                // 更新最后记录时间
                setLastRecordTime(droneType, droneId, System.currentTimeMillis());

                log.debug("成功记录{}无人机[{}]的轨迹点", droneType, droneId);
            } catch (Exception e) {
                log.error("记录{}无人机[{}]轨迹点时发生异常", droneType, droneId, e);
                throw new RuntimeException(e); // 重新抛出异常，让lockUtils处理
            }
        });
    }
}
