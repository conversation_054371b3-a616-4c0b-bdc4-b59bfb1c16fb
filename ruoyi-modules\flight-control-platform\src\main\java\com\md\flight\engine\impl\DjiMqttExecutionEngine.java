package com.md.flight.engine.impl;

import com.md.command.constant.CommandType;
import com.md.command.constant.ExecutorType;
import com.md.command.core.DroneCommand;
import com.md.command.core.DroneCommandFactory;
import com.md.command.model.dto.CommandResult;
import com.md.command.model.impl.SimpleCommand;
import com.md.command.service.DroneCommandLogService;
import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.enums.TaskStatusEnum;
import com.md.flight.engine.TaskExecutionEngine;
import com.md.service.IFlightTaskStatusService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * DJI MQTT任务执行引擎
 * 基于厂商和协议动态选择执行器执行DJI无人机航线任务
 */
@Slf4j
@Component
public class DjiMqttExecutionEngine implements TaskExecutionEngine {

    @Lazy
    @Autowired
    private DroneCommandFactory droneCommandFactory;

    @Autowired
    private DroneCommandLogService commandLogService;

    private final IFlightTaskStatusService flightTaskStatusService;

    /**
     * 厂商类型编码 - DJI
     */
    private static final String MANUFACTURER_DJI = "DJI";

    /**
     * 协议类型编码 - MQTT
     */
    private static final String PROTOCOL_MQTT = "MQTT";

    @Autowired
    public DjiMqttExecutionEngine(IFlightTaskStatusService flightTaskStatusService) {
        this.flightTaskStatusService = flightTaskStatusService;
    }

    @Override
    public CompletableFuture<Boolean> executeTask(FlightTask task, List<FlightTaskPoint> points) {
        CompletableFuture<Boolean> future = new CompletableFuture<>();

        try {
            log.info("使用DJI MQTT引擎执行航线任务: taskId={}", task.getId());

            // 1. 检查无人机编码
            String uavCode = task.getUavCode();
            if (uavCode == null || uavCode.isEmpty()) {
                throw new ServiceException("任务未关联无人机");
            }

            // 2. 构造命令参数
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("missionFile", "wayline/" + task.getKmzFileName());
            parameters.put("speed", task.getFlightSpeed());

            // 3. 创建命令
            String commandId = UUID.randomUUID().toString();
            DroneCommand command = new SimpleCommand(uavCode, CommandType.EXECUTE_MINIO_MISSION, parameters, commandId);

            // 4. 创建命令日志记录（开放端口执行路径）
            try {
                commandLogService.createCommandLog(command, ExecutorType.MQTT);
                log.info("已创建开放端口航线任务命令日志: commandId={}, taskId={}", commandId, task.getId());
            } catch (Exception e) {
                log.error("创建命令日志失败: commandId={}, taskId={}, error={}", commandId, task.getId(),
                    e.getMessage(), e);
            }

            // 5. 根据无人机ID获取合适的执行器并执行命令
            long startTime = System.currentTimeMillis();
            CommandResult result = droneCommandFactory.getExecutor(uavCode).executeCommand(command);

            // 6. 更新命令执行结果到日志
            try {
                long executionTime = System.currentTimeMillis() - startTime;
                commandLogService.updateCommandResult(commandId, result, executionTime);
                log.info("已更新开放端口航线任务命令执行结果: commandId={}, success={}, executionTime={}ms", commandId,
                    result.isSuccess(), executionTime);
            } catch (Exception e) {
                log.error("更新命令执行结果失败: commandId={}, error={}", commandId, e.getMessage(), e);
            }

            if (!result.isSuccess()) {
                log.error("DJI航线上传失败: {}", result.getMessage());
                throw new ServiceException("航线上传失败: " + result.getMessage());
            }

            log.info("DJI航线已成功上传并开始执行: taskId={}, uavCode={}, commandId={}", task.getId(), uavCode,
                commandId);

            // 7. 任务开始执行后，更新任务状态为执行中
            flightTaskStatusService.updateTaskStatus(task.getTaskName(), TaskStatusEnum.EXECUTING.getCode(), null);

            // 8. 更新任务操作人（如果operatorId不为空）
            Long operatorId = LoginHelper.getUserId();
            if (operatorId != null) {
                flightTaskStatusService.updateTaskOperator(task.getTaskName(), operatorId);
                log.info("已更新任务[{}]的执飞人ID为: {}", task.getTaskName(), operatorId);
            } else {
                log.info("执飞人ID为空，跳过更新任务[{}]的执飞人", task.getTaskName());
            }

            future.complete(true);
            return future;
        } catch (Exception e) {
            log.error("DJI MQTT执行航线任务失败: taskId={}", task.getId(), e);
            future.completeExceptionally(e);
            throw new ServiceException("执行航线失败: " + e.getMessage());
        }
    }

    @Override
    public String getSupportedManufacturer() {
        return MANUFACTURER_DJI;
    }

    @Override
    public String getSupportedProtocol() {
        return PROTOCOL_MQTT;
    }
}