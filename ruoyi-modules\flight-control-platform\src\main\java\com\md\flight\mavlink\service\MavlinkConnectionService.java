package com.md.flight.mavlink.service;

import com.md.flight.mavlink.model.DroneStatus;

/**
 * MAVLink连接管理服务
 * 负责管理与无人机的连接状态
 */
public interface MavlinkConnectionService {
    /**
     * 连接到无人机
     *
     * @param droneId 无人机ID
     * @param host 主机地址
     * @param port 端口
     */
    void connect(String droneId, String host, int port);

    /**
     * 断开连接
     *
     * @param droneId 无人机ID
     */
    void disconnect(String droneId);

    /**
     * 检查是否已连接
     *
     * @param droneId 无人机ID
     * @return 是否已连接
     */
    boolean isConnected(String droneId);
    
    /**
     * 重新连接到无人机
     *
     * @param droneId 无人机ID
     * @param host 主机地址
     * @param port 端口
     * @return 是否成功重连
     */
    boolean reconnect(String droneId, String host, int port);
    
    /**
     * 获取无人机状态
     *
     * @param droneId 无人机ID
     * @return 无人机状态
     */
    DroneStatus getDroneStatus(String droneId);
    
    /**
     * 获取所有已连接的无人机ID
     *
     * @return 无人机ID列表
     */
    java.util.List<String> getConnectedDrones();
} 