package com.md.config;

import io.minio.MinioClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "minio")
public class MinioConfig {
    /**
     * 服务地址
     */
    private String endpoint;

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 默认存储桶
     */
    private String bucket;

    /**
     * 航线文件存储路径
     */
    private String waylinePath;

    /**
     * 连接配置
     */
    private Connect connect = new Connect();

    @Data
    public static class Connect {
        /**
         * 连接超时时间（秒）
         */
        private Integer connectTimeout = 10;

        /**
         * 写超时时间（秒）
         */
        private Integer writeTimeout = 60;

        /**
         * 读超时时间（秒）
         */
        private Integer readTimeout = 60;

        /**
         * 最大空闲连接数
         */
        private Integer maxIdleConnections = 100;
    }

    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
            .endpoint(endpoint)
            .credentials(accessKey, secretKey)
            .build();
    }
} 