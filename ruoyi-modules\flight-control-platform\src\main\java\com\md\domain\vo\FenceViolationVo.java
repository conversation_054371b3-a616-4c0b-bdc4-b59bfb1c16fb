package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.po.FenceViolationLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 围栏违规记录视图对象
 */
@Data
@NoArgsConstructor
@ExcelIgnoreUnannotated
@AutoMapper(target = FenceViolationLog.class)
public class FenceViolationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "记录ID")
    private String id;

    /**
     * 围栏ID
     */
    @ExcelProperty(value = "围栏ID")
    private String fenceId;

    /**
     * 围栏名称（非数据库字段）
     */
    private String fenceName;

    /**
     * 任务ID
     */
    @ExcelProperty(value = "任务ID")
    private String taskId;

    /**
     * 任务名称（非数据库字段）
     */
    private String taskName;

    /**
     * 无人机编码
     */
    @ExcelProperty(value = "无人机编码")
    private String uavCode;

    /**
     * 违规位置经度
     */
    @ExcelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 违规位置纬度
     */
    @ExcelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * 违规位置高度
     */
    @ExcelProperty(value = "高度")
    private BigDecimal height;

    /**
     * 执行的动作
     */
    @ExcelProperty(value = "动作代码")
    private Integer actionTaken;

    /**
     * 执行的动作（名称）
     */
    @ExcelProperty(value = "执行动作")
    private String actionName;

    /**
     * 违规时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "违规时间")
    private Date violationTime;

    /**
     * 开始时间（用于时间范围查询）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 结束时间（用于时间范围查询）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
