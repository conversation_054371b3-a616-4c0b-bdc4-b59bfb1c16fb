<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.FenceAreaMapper">
    <resultMap type="com.md.domain.po.FenceArea" id="FenceAreaResult">
        <id property="id" column="id"/>
        <result property="fenceId" column="fence_id"/>
        <result property="areaType" column="area_type"/>
        <result property="centerLongitude" column="center_longitude"/>
        <result property="centerLatitude" column="center_latitude"/>
        <result property="radius" column="radius"/>
        <result property="minHeight" column="min_height"/>
        <result property="maxHeight" column="max_height"/>
        <result property="actionType" column="action_type"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectFenceAreaVo">
        select id,
               fence_id,
               area_type,
               center_longitude,
               center_latitude,
               radius,
               min_height,
               max_height,
               action_type,
               create_time
        from fence_area
    </sql>

    <select id="selectFenceAreaList" parameterType="com.md.domain.po.FenceArea" resultMap="FenceAreaResult">
        <include refid="selectFenceAreaVo"/>
        <where>
            <if test="fenceId != null and fenceId != ''">
                AND fence_id = #{fenceId}
            </if>
            <if test="areaType != null">
                AND area_type = #{areaType}
            </if>
        </where>
    </select>

    <select id="selectFenceAreaById" parameterType="String" resultMap="FenceAreaResult">
        <include refid="selectFenceAreaVo"/>
        where id = #{id}
    </select>

    <select id="selectByFenceId" parameterType="String" resultMap="FenceAreaResult">
        <include refid="selectFenceAreaVo"/>
        where fence_id = #{fenceId}
        order by create_time asc
    </select>

    <insert id="insertFenceArea" parameterType="com.md.domain.po.FenceArea">
        insert into fence_area (
        id,
        fence_id,
        area_type,
        center_longitude,
        center_latitude,
        radius,
        min_height,
        max_height,
        action_type,
        create_time
        ) values (
        #{id},
        #{fenceId},
        #{areaType},
        #{centerLongitude},
        #{centerLatitude},
        #{radius},
        #{minHeight},
        #{maxHeight},
        #{actionType},
        #{createTime}
        )
    </insert>

    <update id="updateFenceArea" parameterType="com.md.domain.po.FenceArea">
        update fence_area
        <set>
            <if test="areaType != null">
                area_type = #{areaType},
            </if>
            <if test="centerLongitude != null">
                center_longitude = #{centerLongitude},
            </if>
            <if test="centerLatitude != null">
                center_latitude = #{centerLatitude},
            </if>
            <if test="radius != null">
                radius = #{radius},
            </if>
            <if test="minHeight != null">
                min_height = #{minHeight},
            </if>
            <if test="maxHeight != null">
                max_height = #{maxHeight},
            </if>
            <if test="actionType != null">
                action_type = #{actionType},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteFenceAreaById" parameterType="String">
        delete from fence_area where id = #{id}
    </delete>

    <delete id="deleteFenceAreaByIds" parameterType="String">
        delete from fence_area where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByFenceId" parameterType="String">
        delete from fence_area where fence_id = #{fenceId}
    </delete>
</mapper>