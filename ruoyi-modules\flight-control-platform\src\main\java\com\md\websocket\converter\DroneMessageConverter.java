package com.md.websocket.converter;

import com.md.websocket.message.DroneMessage;
import com.md.websocket.message.MessageType;

/**
 * 无人机消息转换器接口
 * 定义厂商特定的消息转换规范
 */
public interface DroneMessageConverter {

    /**
     * 转换消息
     *
     * @param topic       MQTT主题
     * @param payload     消息载荷
     * @param messageType 消息类型
     * @return 转换后的无人机消息
     */
    DroneMessage convert(String topic, String payload, MessageType messageType);

    /**
     * 检查是否支持指定厂商
     *
     * @param manufacturer 厂商名称
     * @return 是否支持
     */
    boolean supports(String manufacturer);

    /**
     * 获取支持的厂商名称
     *
     * @return 厂商名称
     */
    String getManufacturer();
}
