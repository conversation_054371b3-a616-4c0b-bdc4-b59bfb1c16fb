package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

/**
 * 开放端接口租户绑定关系表 sys_open_api_tenant_binding
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_open_api_tenant_binding")
public class OpenApiTenantBinding extends TenantEntity {

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 绑定关系的租户编号(本平台租户id)
     */
    private String bindTenantId;

    /**
     * 接口ID
     */
    private Long apiId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 目标系统租户编号
     */
    private String targetTenantId;

    /**
     * 配置信息
     */
    private String configInfo;

    /**
     * 是否生效（0生效 1失效）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
