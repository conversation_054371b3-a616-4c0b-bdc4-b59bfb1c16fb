package com.md.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.md.domain.po.FenceArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 围栏区域Mapper接口
 */
public interface FenceAreaMapper extends BaseMapper<FenceArea> {
    /**
     * 查询围栏区域
     *
     * @param id 围栏区域ID
     * @return 围栏区域
     */
    FenceArea selectFenceAreaById(String id);

    /**
     * 根据围栏ID查询围栏区域列表
     *
     * @param fenceId 围栏ID
     * @return 围栏区域列表
     */
    List<FenceArea> selectByFenceId(@Param("fenceId") String fenceId);

    /**
     * 查询围栏区域列表
     *
     * @param fenceArea 围栏区域
     * @return 围栏区域集合
     */
    List<FenceArea> selectFenceAreaList(FenceArea fenceArea);

    /**
     * 新增围栏区域
     *
     * @param fenceArea 围栏区域
     * @return 结果
     */
    int insertFenceArea(FenceArea fenceArea);

    /**
     * 修改围栏区域
     *
     * @param fenceArea 围栏区域
     * @return 结果
     */
    int updateFenceArea(FenceArea fenceArea);

    /**
     * 删除围栏区域
     *
     * @param id 围栏区域ID
     * @return 结果
     */
    int deleteFenceAreaById(String id);

    /**
     * 批量删除围栏区域
     *
     * @param ids 需要删除的围栏区域ID数组
     * @return 结果
     */
    int deleteFenceAreaByIds(String[] ids);

    /**
     * 根据围栏ID删除区域
     *
     * @param fenceId 围栏ID
     * @return 结果
     */
    int deleteByFenceId(@Param("fenceId") String fenceId);
}