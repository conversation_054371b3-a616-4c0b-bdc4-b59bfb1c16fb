package com.md.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.core.convert.NoOpDbRefResolver;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.lang.NonNull;
import lombok.extern.slf4j.Slf4j;

/**
 * MongoDB配置类 启用MongoDB的Repository扫描
 */
@Slf4j
@Configuration
@EnableMongoRepositories(basePackages = "com.md.mongodb.repository")
public class MongoConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.host:*************}")
    private String host;

    @Value("${spring.data.mongodb.port:27017}")
    private int port;

    @Value("${spring.data.mongodb.database:flight_control_system}")
    private String database;

    @Value("${spring.data.mongodb.username:Expressfly}")
    private String username;

    @Value("${spring.data.mongodb.password:Express@Fly&Maide*}")
    private String password;

    @Override
    @NonNull
    protected String getDatabaseName() {
        return database;
    }

    @Override
    @NonNull
    @Bean
    public MongoClient mongoClient() {
        try {
            String connStr = String.format("mongodb://%s:%d/%s", host, port, database);

            // 创建连接设置
            MongoClientSettings settings =
                MongoClientSettings.builder().applyConnectionString(new ConnectionString(connStr))
                    .credential(com.mongodb.MongoCredential.createCredential(username, "admin", password.toCharArray()))
                    .build();

            log.info("MongoDB连接初始化成功: host={}, port={}, database={}", host, port, database);
            return MongoClients.create(settings);
        } catch (Exception e) {
            log.error("MongoDB连接初始化失败", e);
            throw new RuntimeException("MongoDB连接初始化失败: " + e.getMessage());
        }
    }

    @Bean
    @NonNull
    @Override
    public MappingMongoConverter mappingMongoConverter(@NonNull MongoDatabaseFactory databaseFactory,
        @NonNull MongoCustomConversions customConversions, @NonNull MongoMappingContext mappingContext) {
        // 创建MappingMongoConverter
        MappingMongoConverter converter = new MappingMongoConverter(NoOpDbRefResolver.INSTANCE, mappingContext);
        // 设置自定义转换器
        converter.setCustomConversions(customConversions);
        // 设置typeMapper为null，禁用_class字段
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        return converter;
    }

    @Bean
    @NonNull
    public MongoTemplate mongoTemplate(@NonNull MongoDatabaseFactory factory,
        @NonNull MappingMongoConverter converter) {
        return new MongoTemplate(factory, converter);
    }
}