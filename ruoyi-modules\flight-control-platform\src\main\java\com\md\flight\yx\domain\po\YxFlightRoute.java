package com.md.flight.yx.domain.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 亿讯航线对象 yx_flight_route
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@Data
@TableName("yx_flight_route")
public class YxFlightRoute {
    /** 亿讯航线表ID */
    private Long id;

    /** 航线名称 */
    private String planeLineName;

    /** 起飞点ID */
    private String takeOffPointId;

    /** 降落点ID */
    private String landingPointId;

    /** 起飞点经度 */
    private Double takeOffPointLng;

    /** 起飞点纬度 */
    private Double takeOffPointLat;

    /** 起飞点海拔 */
    private Float takeOffPointAltitude;

    /** 自动飞行速度(单位:米/秒，范围0~15)，默认8) */
    private Long autoSpeed;

    /** 最大飞行速度((单位:米/秒，范围0~15)，默认15) */
    private Long maxSpeed;

    /** 网络失联后动作(1=返回起飞点，2=继续航线) */
    private Long missingAction;

    /** 结束航线后动作(1=返航，2=原地悬停，3=原地降落，4=原地绕飞) */
    private Long finishedAction;

    /** 批次号 */
    private String batchId;

    /** 同步时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncTime;

    /** 航点数组 */
    @TableField(exist = false)
    private List<YxRoutePoint> pointDatas;
}
