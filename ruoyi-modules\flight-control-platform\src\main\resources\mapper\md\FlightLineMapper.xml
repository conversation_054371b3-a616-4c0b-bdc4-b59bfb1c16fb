<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.FlightLineMapper">
    <resultMap type="com.md.domain.po.FlightLine" id="FlightLineResult">
        <result property="id" column="id"/>
        <result property="flightControlNo" column="flight_control_no"/>
        <result property="lineName" column="line_name"/>
        <result property="flightSpeed" column="flight_speed"/>
        <result property="flightHeight" column="flight_height"/>
        <result property="returnHeight" column="return_height"/>
        <result property="dataSource" column="data_source"/>
        <result property="missingAction" column="missing_action"/>
        <result property="finishedAction" column="finished_action"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="fileGenerateStatus" column="file_generate_status"/>
        <result property="kmzFileName" column="kmz_file_name"/>
        <result property="kmzFilePath" column="kmz_file_path"/>
        <result property="fileSize" column="file_size"/>
        <result property="fileGenerateTime" column="file_generate_time"/>
        <result property="fileGenerateError" column="file_generate_error"/>
        <result property="isUploaded" column="is_uploaded"/>
    </resultMap>

    <sql id="selectFlightLineVo">
        select id,
               flight_control_no,
               line_name,
               flight_speed,
               flight_height,
               return_height,
               data_source,
               missing_action,
               finished_action,
               create_by,
               create_time,
               update_by,
               update_time,
               file_generate_status,
               kmz_file_name,
               kmz_file_path,
               file_size,
               file_generate_time,
               file_generate_error,
               is_uploaded
        from flight_line
    </sql>

    <select id="selectFlightLineList" parameterType="FlightLineDto" resultType="com.md.domain.dto.FlightLineDto">
        SELECT f.*, p.manufacturer_name
        FROM flight_line f
        LEFT JOIN platform_info p ON f.flight_control_no = p.flight_control_no
        <where>
            <if test="flightControlNo != null">
                AND f.flight_control_no = #{flightControlNo}
            </if>
            <if test="lineName != null and lineName != ''">
                AND f.line_name like concat('%', #{lineName}, '%')
            </if>
            <if test="flightSpeed != null">
                AND f.flight_speed = #{flightSpeed}
            </if>
            <if test="flightHeight != null">
                AND f.flight_height = #{flightHeight}
            </if>
            <if test="returnHeight != null">
                AND f.return_height = #{returnHeight}
            </if>
            <if test="dataSource != null and dataSource != ''">
                AND f.data_source = #{dataSource}
            </if>
            <if test="createBy != null">
                AND f.create_by like concat('%', #{createBy}, '%')
            </if>
            <if test="updateBy != null">
                AND f.update_by like concat('%', #{updateBy}, '%')
            </if>
            <if test="startTime != null">
                AND f.create_time <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND f.create_time <![CDATA[ < ]]> DATE_ADD(#{endTime}, INTERVAL 1 DAY)
            </if>
            <if test="startUpdateTime != null">
                AND f.update_time <![CDATA[ >= ]]> #{startUpdateTime}
            </if>
            <if test="endUpdateTime != null">
                AND f.update_time <![CDATA[ < ]]> DATE_ADD(#{endUpdateTime}, INTERVAL 1 DAY)
            </if>
        </where>
        ORDER BY f.id DESC
    </select>
    <select id="selectFlightLineById" parameterType="String" resultMap="FlightLineResult">
        <include refid="selectFlightLineVo"/>
        where id = #{id}
    </select>

    <insert id="insertFlightLine" parameterType="com.md.domain.po.FlightLine">
        insert into flight_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="flightControlNo != null">
                flight_control_no,
            </if>
            <if test="lineName != null">
                line_name,
            </if>
            <if test="flightSpeed != null">
                flight_speed,
            </if>
            <if test="flightHeight != null">
                flight_height,
            </if>
            <if test="returnHeight != null">
                return_height,
            </if>
            <if test="dataSource != null">
                data_source,
            </if>
            <if test="missingAction != null">
                missing_action,
            </if>
            <if test="finishedAction != null">
                finished_action,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="fileGenerateStatus != null">
                file_generate_status,
            </if>
            <if test="kmzFileName != null">
                kmz_file_name,
            </if>
            <if test="kmzFilePath != null">
                kmz_file_path,
            </if>
            <if test="fileSize != null">
                file_size,
            </if>
            <if test="fileGenerateTime != null">
                file_generate_time,
            </if>
            <if test="fileGenerateError != null">
                file_generate_error,
            </if>
            <if test="isUploaded != null">
                is_uploaded,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="flightControlNo != null">
                #{flightControlNo},
            </if>
            <if test="lineName != null">
                #{lineName},
            </if>
            <if test="flightSpeed != null">
                #{flightSpeed},
            </if>
            <if test="flightHeight != null">
                #{flightHeight},
            </if>
            <if test="returnHeight != null">
                #{returnHeight},
            </if>
            <if test="dataSource != null">
                #{dataSource},
            </if>
            <if test="missingAction != null">
                #{missingAction},
            </if>
            <if test="finishedAction != null">
                #{finishedAction},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="fileGenerateStatus != null">
                #{fileGenerateStatus},
            </if>
            <if test="kmzFileName != null">
                #{kmzFileName},
            </if>
            <if test="kmzFilePath != null">
                #{kmzFilePath},
            </if>
            <if test="fileSize != null">
                #{fileSize},
            </if>
            <if test="fileGenerateTime != null">
                #{fileGenerateTime},
            </if>
            <if test="fileGenerateError != null">
                #{fileGenerateError},
            </if>
            <if test="isUploaded != null">
                #{isUploaded},
            </if>
        </trim>
    </insert>

    <update id="updateFlightLine" parameterType="com.md.domain.po.FlightLine">
        update flight_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightControlNo != null">
                flight_control_no = #{flightControlNo},
            </if>
            <if test="lineName != null">
                line_name = #{lineName},
            </if>
            <if test="flightSpeed != null">
                flight_speed = #{flightSpeed},
            </if>
            <if test="flightHeight != null">
                flight_height = #{flightHeight},
            </if>
            <if test="returnHeight != null">
                return_height = #{returnHeight},
            </if>
            <if test="dataSource != null">
                data_source = #{dataSource},
            </if>
            <if test="missingAction != null">
                missing_action = #{missingAction},
            </if>
            <if test="finishedAction != null">
                finished_action = #{finishedAction},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="fileGenerateStatus != null">
                file_generate_status = #{fileGenerateStatus},
            </if>
            <if test="kmzFileName != null">
                kmz_file_name = #{kmzFileName},
            </if>
            <if test="kmzFilePath != null">
                kmz_file_path = #{kmzFilePath},
            </if>
            <if test="fileSize != null">
                file_size = #{fileSize},
            </if>
            <if test="fileGenerateTime != null">
                file_generate_time = #{fileGenerateTime},
            </if>
            <if test="fileGenerateError != null">
                file_generate_error = #{fileGenerateError},
            </if>
            <if test="isUploaded != null">
                is_uploaded = #{isUploaded},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFlightLineById" parameterType="String">
        delete from flight_line where id = #{id}
    </delete>

    <delete id="deleteFlightLineByIds" parameterType="String">
        delete from flight_line where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>