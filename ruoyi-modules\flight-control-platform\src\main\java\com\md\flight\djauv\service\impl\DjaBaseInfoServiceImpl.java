package com.md.flight.djauv.service.impl;

import com.md.domain.po.FlightLine;
import com.md.domain.po.FlightPoint;
import com.md.domain.vo.FlightLineVo;
import com.md.flight.djauv.domain.RoutePointReq;
import com.md.flight.djauv.domain.UavRouteReq;
import com.md.flight.djauv.service.UavRouteService;
import com.md.mapper.FlightLineMapper;
import com.md.mapper.FlightPointMapper;
import com.md.service.IBaseInfoService;
import com.md.service.IFlightLineService;
import com.md.service.IFlightPointActionService;
import com.md.service.IPlatformInfoService;
import com.md.service.impl.FlightLineServiceImpl;
import com.md.utils.MinioUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * 大疆基本信息实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DjaBaseInfoServiceImpl implements IBaseInfoService {

    private final UavRouteService uavRouteService;
    private final FlightLineMapper flightLineMapper;
    private final FlightLineServiceImpl flightLineService;
    private final MinioUtils minioUtils;

    @Override
    public void syncUAV() {

    }

    @Override
    public void syncFlyer() {

    }

    @Override
    public void syncFlightLine() {

    }

    @Override
    public boolean buildKmzByFlightLine(String id) {
        try {
            // 1. 获取航线和航点信息
            FlightLineVo flightLine = flightLineService.selectFlightLineById(id);
            if (flightLine == null) {
                throw new ServiceException("航线不存在");
            }
            List<FlightPoint> points = (List<FlightPoint>)flightLine.getPoints();

            // 2. 更新任务状态为生成中
            FlightLine updateLine = new FlightLine();
            updateLine.setId(id);
            updateLine.setFileGenerateStatus(1);
            updateLine.setFileGenerateTime(DateUtils.getNowDate());
            updateLine.setUpdateTime(DateUtils.getNowDate());
            updateLine.setUpdateBy(LoginHelper.getUserId());
            flightLineMapper.updateById(updateLine);

            try {
                // 3. 构建UavRouteReq对象
                UavRouteReq uavRouteReq = new UavRouteReq();

                // 设置航线基本属性
                uavRouteReq.setFinishAction(flightLine.getFinishedAction());
                uavRouteReq.setExitOnRcLostAction(flightLine.getMissingAction());

                // 设置飞行速度，为空则使用默认值
                BigDecimal flightSpeed = flightLine.getFlightSpeed();
                uavRouteReq.setAutoFlightSpeed(flightSpeed != null ? flightSpeed.doubleValue() : 2.0);

                // 设置全局高度，为空则使用默认值80
                BigDecimal flightHeight = flightLine.getFlightHeight();
                uavRouteReq.setGlobalHeight(flightHeight != null ? flightHeight.doubleValue() : 20.0);

                // 4. 构建航点列表
                List<RoutePointReq> routePoints = flightLineService.buildRoutePoints(points, uavRouteReq);
                uavRouteReq.setRoutePointList(routePoints);

                // 5. 设置默认值
                flightLineService.setDefaultValues(uavRouteReq);

                // 6. 生成kmz文件
                String kmzFilePath = uavRouteService.buildKmz(uavRouteReq, flightLine.getLineName());

                // 7. 上传文件到MinIO
                File kmzFile = new File(kmzFilePath);
                if (!kmzFile.exists()) {
                    throw new ServiceException("KMZ文件不存在");
                }

                try (FileInputStream inputStream = new FileInputStream(kmzFile)) {
                    // 直接使用原始文件名，不做任何修改
                    String fileName = kmzFile.getName();
                    String objectName = "flightline/" + fileName;

                    // 创建更新对象并设置共同属性
                    updateLine = new FlightLine();
                    updateLine.setId(id);
                    updateLine.setFileGenerateStatus(2);
                    updateLine.setKmzFileName(fileName);
                    updateLine.setFileSize(kmzFile.length());
                    updateLine.setFileGenerateTime(DateUtils.getNowDate());
                    updateLine.setUpdateTime(DateUtils.getNowDate());
                    updateLine.setUpdateBy(LoginHelper.getUserId());

                    // 仅为DJI厂家上传文件
                    String fileUrl =
                        minioUtils.uploadFile(minioUtils.getDefaultBucketName(), objectName, inputStream,
                            "application/vnd.google-earth.kmz");

                    // 设置文件路径和上传状态
                    updateLine.setKmzFilePath(fileUrl);
                    updateLine.setIsUploaded(1); // 设置为已上传
                    log.info("航线文件生成并上传成功，文件路径：{}", fileUrl);

                    // 执行更新
                    flightLineMapper.updateById(updateLine);
                    return true;

                } finally {
                    // 清理临时文件
                    try {
                        Files.deleteIfExists(Paths.get(kmzFilePath));
                    } catch (IOException e) {
                        log.warn("清理临时文件失败：{}", kmzFilePath, e);
                    }
                }

            } catch (Exception e) {
                // 9. 更新任务状态为生成失败
                updateLine = new FlightLine();
                updateLine.setId(id);
                updateLine.setFileGenerateStatus(3);
                updateLine.setFileGenerateError(e.getMessage());
                updateLine.setUpdateTime(DateUtils.getNowDate());
                updateLine.setUpdateBy(LoginHelper.getUserId());
                flightLineMapper.updateById(updateLine);

                throw e;
            }

        } catch (Exception e) {
            log.error("生成或上传航线文件失败，航线ID：{}", id, e);
            throw new ServiceException("生成航线文件失败：" + e.getMessage());
        }
    }
}
