package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

/**
 * 接口租户绑定关系表 sys_api_tenant_binding
 *
 *
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_api_tenant_binding")
public class SysApiTenantBinding extends TenantEntity {

    /**
     * 绑定ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 绑定关系的租户编号
     */
    private String bindTenantId;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 配置信息
     */
    private String configInfo;

    /**
     * 接口ID
     */
    private Long apiId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 目标系统租户编号
     */
    private String targetTenantId;

    /**
     * 是否生效（0生效 1失效）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
