package com.md.flight.yz.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 亿航航线对象 yh_flight_route
 *
 * <AUTHOR>
 * @date 2024-11-12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("yz_flight_route")
public class YzFlightRoute {
    private static final long serialVersionUID=1L;

    /** 航线ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 航线标识 */
    @TableField("air_line_id")
    private String airLineId;

    /** 起飞点经度 */
    @TableField("take_off_apron_longitude")
    private Double takeOffApronLongitude;

    /** 起飞点编码 */
    @TableField("take_off_apron_code")
    private String takeoffApronCode;

    /** 起飞点纬度 */
    @TableField("take_off_apron_latitude")
    private Double takeOffApronLatitude;

    /** 起飞点名称 */
    @TableField("take_off_apron_name")
    private String takeoffApronName;

    /** 起飞点高度 */
    @TableField("take_off_apron_altitude")
    private Long takeOffApronAltitude;

    /** 降落点编码 */
    @TableField("land_apron_code")
    private String landApronCode;

    /** 降落点名称 */
    @TableField("land_apron_name")
    private String landApronName;

    /** 降落点纬度 */
    @TableField("land_apron_latitude")
    private Double landApronLatitude;

    /** 降落点经度 */
    @TableField("land_apron_longitude")
    private Double landApronLongitude;

    /** 降落点高度 */
    @TableField("land_apron_altitude")
    private Long landApronAltitude;

    /** 状态 */
    @TableField("status")
    private String status;

    /** 同步时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("sync_time")
    private Date syncTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;

    /** 调用批次 */
    @TableField("batch")
    private String batch;
}
