package com.md.mqtt.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.constant.MqttConstants;
import com.md.mqtt.exception.MqttProcessException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 虚拟摇杆状态消息处理器
 * 处理dji/virtual_stick/response主题的消息
 */
@Component
@Slf4j
public class VirtualStickStatusMessageProcessor extends AbstractMqttMessageProcessor {
    @Value("${mqtt.virtual-stick.expire-seconds:300}")  // 默认5分钟
    private int expireSeconds;

    @Value("${mqtt.state.min-update-interval:500}")  // 默认500毫秒
    private long minUpdateInterval;

    private final Map<String, Long> lastUpdateTimeMap = new ConcurrentHashMap<>();

    @Override
    protected String getTopicPattern() {
        return "dji/virtual_stick/response";
    }

    @Override
    protected void doProcessMessage(String topic, String payload) {
        try {
            JSONObject jsonPayload = JSON.parseObject(payload);
            String droneId = jsonPayload.getString("droneSN");
            if (StringUtils.isEmpty(droneId)) {
                log.error("虚拟摇杆状态消息中未包含droneSN字段，消息内容：{}", payload);
                return;
            }

            // 限流处理
            if (!shouldProcessUpdate(droneId)) {
                return;
            }

            String cacheKey = MqttConstants.REDIS_VIRTUAL_STICK_STATUS_PREFIX + droneId;
            boolean success = jsonPayload.getBooleanValue("success");
            String message = jsonPayload.getString("message");

            if (success) {
                if (isEnableMessage(message)) {
                    // 保存状态
                    TenantHelper.ignore(() -> {
                        RedisUtils.setCacheObject(cacheKey, jsonPayload.toString(), Duration.ofSeconds(expireSeconds));
                        return null;
                    });
                    log.info("无人机[{}]虚拟摇杆已启用", droneId);
                } else if (isDisableMessage(message)) {
                    // 移除状态
                    // todo 实现虚拟摇杆禁用调用
                    TenantHelper.ignore(() -> {
                        RedisUtils.deleteObject(cacheKey);
                        return null;
                    });
                    log.info("无人机[{}]虚拟摇杆已禁用", droneId);
                } else {
                    // 刷新过期时间
                    refreshExpireTime(cacheKey);
                }
            }
        } catch (Exception e) {
            log.error("处理虚拟摇杆状态消息失败", e);
            throw new MqttProcessException("处理虚拟摇杆状态消息失败", e);
        }
    }

    private boolean shouldProcessUpdate(String droneId) {
        long currentTime = System.currentTimeMillis();
        Long lastTime = lastUpdateTimeMap.get(droneId);
        if (lastTime != null && currentTime - lastTime < minUpdateInterval) {
            return false;
        }
        lastUpdateTimeMap.put(droneId, currentTime);
        return true;
    }

    private boolean isEnableMessage(String message) {
        return message != null && (message.contains("已启用") || message.contains("开启"));
    }

    private boolean isDisableMessage(String message) {
        return message != null && (message.contains("已禁用") || message.contains("停止") || message.contains("关闭"));
    }

    private void refreshExpireTime(String cacheKey) {
        TenantHelper.ignore(() -> {
            if (RedisUtils.hasKey(cacheKey)) {
                RedisUtils.expire(cacheKey, Duration.ofSeconds(expireSeconds));
                log.debug("虚拟摇杆状态过期时间已刷新");
            }
            return null;
        });
    }
}