package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * 飞控平台
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("platform_info")
public class Platform extends BaseEntity {
    //主键id
    @TableId(type = IdType.AUTO)
    private Long id;

    //飞控编号
    @TableField("flight_control_no")
    private String platformId;

    //飞控平台名称
    @TableField("manufacturer_name")
    private String platformName;

    //飞控网络地址
    @TableField("network_address")
    private String platformIp;

    //用户名
    @TableField("account")
    private String username;

    //密码
    @TableField("password")
    private String password;

    //aes密钥
    @TableField("aes_key")
    private String aesKey;

    //备注
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;

    /**
     * 部门id
     */
    @TableField("dept_id")
    private Long deptId;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long user_id;

}
