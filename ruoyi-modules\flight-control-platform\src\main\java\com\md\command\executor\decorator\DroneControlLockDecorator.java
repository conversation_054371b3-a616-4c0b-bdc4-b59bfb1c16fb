package com.md.command.executor.decorator;

import com.md.command.constant.CommandType;
import com.md.command.core.DroneCommand;
import com.md.command.core.DroneCommandExecutor;
import com.md.command.model.dto.CommandResult;
import com.md.utils.DroneLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 无人机控制锁装饰器
 */
@Component
@Slf4j
public class DroneControlLockDecorator implements DroneCommandExecutor {

    private final DroneCommandExecutor delegate;
    private final DroneLockUtil droneLockUtil;
    private final ISysUserService sysUserService;

    @Autowired
    public DroneControlLockDecorator(DroneCommandExecutor delegate, DroneLockUtil droneLockUtil,
        ISysUserService sysUserService) {
        this.delegate = delegate;
        this.droneLockUtil = droneLockUtil;
        this.sysUserService = sysUserService;
    }

    @Override
    public CommandResult executeCommand(DroneCommand command) {
        try {
            // 检查是否需要获取控制权
            if (needsControlLock(command)) {
                Long operatorId = LoginHelper.getUserId();

                // 尝试获取控制权
                if (!droneLockUtil.tryLock(command.getDroneId(), operatorId)) {
                    Long lockedOperatorId = droneLockUtil.getLockedOperatorId(command.getDroneId());
                    if (lockedOperatorId != null) {
                        String operatorName = sysUserService.selectUserById(lockedOperatorId).getUserName();
                        log.warn("无人机[{}]已被操作员[{}]控制中，当前操作员[{}]无法获取控制权", command.getDroneId(),
                            operatorName, LoginHelper.getUsername());
                        return CommandResult.failed("无人机已被操作员[" + operatorName + "]控制中");
                    }
                }
            }

            // 执行原有命令逻辑
            CommandResult result = delegate.executeCommand(command);

            // 如果是返航或降落命令，释放控制权
            if (result.isSuccess() &&
                (command.getCommandType() == CommandType.GO_HOME || command.getCommandType() == CommandType.LAND) ||
                command.getCommandType() == CommandType.VIRTUAL_STICK_LAND ||
                command.getCommandType() == CommandType.VIRTUAL_STICK_STOP) {
                droneLockUtil.unlock(command.getDroneId());
            }

            return result;

        } catch (Exception e) {
            // 发生异常时释放控制权
            if (needsControlLock(command)) {
                droneLockUtil.unlock(command.getDroneId());
            }
            log.error("执行命令失败", e);
            return CommandResult.failed("指令发送失败: " + e.getMessage());
        }
    }

    @Override
    public Set<String> getSupportedManufacturers() {
        return delegate.getSupportedManufacturers();
    }
    
    @Override
    public String getSupportedProtocol() {
        return delegate.getSupportedProtocol();
    }
    
    @Override
    public int getPriority() {
        return delegate.getPriority();
    }

    /**
     * 判断命令是否需要获取控制权
     */
    private boolean needsControlLock(DroneCommand command) {
        return command.getCommandType() == CommandType.EXECUTE_MINIO_MISSION ||
            command.getCommandType() == CommandType.RESUME_MISSION ||
            command.getCommandType() == CommandType.PAUSE_MISSION ||
            command.getCommandType() == CommandType.STOP_MISSION || command.getCommandType() == CommandType.GO_HOME ||
            command.getCommandType() == CommandType.LAND ||
            command.getCommandType() == CommandType.VIRTUAL_STICK_START ||
            command.getCommandType() == CommandType.VIRTUAL_STICK_STOP ||
            command.getCommandType() == CommandType.VIRTUAL_STICK_CONTROL ||
            command.getCommandType() == CommandType.VIRTUAL_STICK_TAKEOFF ||
            command.getCommandType() == CommandType.VIRTUAL_STICK_LAND ||
            command.getCommandType() == CommandType.VIRTUAL_STICK_EMERGENCY_STOP ||
            command.getCommandType() == CommandType.VIRTUAL_STICK_NAVIGATE_TO_POINT;
    }
}