package com.md.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.md.domain.bo.FlyerBo;
import com.md.domain.dto.FlyerDto;
import com.md.domain.po.Flyer;
import com.md.domain.vo.FlyerVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * 飞手管理接口
 */
public interface IFlyerService extends IService<Flyer> {

    /**
     * 查询飞手列表
     * @param flyerDto 查询参数
     * @return 飞手列表
     */
    List<FlyerVo> selectFlyerList(FlyerDto flyerDto);

    /**
     * 分页查询飞手列表
     *
     * @param flyerDto 查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<FlyerVo> queryPageList(FlyerDto flyerDto, PageQuery pageQuery);

    /**
     * 同步飞手信息
     * @param plateformId
     * @return
     */
    boolean syncFlyer(String plateformId);

    /**
     * 新增飞手信息
     * @param flyerBo 飞手业务对象
     * @return 操作结果
     */
    boolean insertFlyer(FlyerBo flyerBo);

    /**
     * 修改飞手信息
     * @param flyerBo 飞手业务对象
     * @return 操作结果
     */
    boolean updateFlyer(FlyerBo flyerBo);

    /**
     * 根据ID获取飞手视图对象
     * @param id 飞手ID
     * @return 飞手视图对象
     */
    FlyerVo getById(String id);

    /**
     * 同步飞手详情
     * @param id
     * @return
     */
//    boolean syncFlyerDetail(Long id);

}
