package com.md.flight.mavlink.service;

import com.md.flight.mavlink.model.Mission;
import com.md.flight.mavlink.model.MissionWaypoint;
import java.util.concurrent.CompletableFuture;
import java.util.Map;
import java.util.List;
import java.math.BigDecimal;

/**
 * MAVLink航线任务服务
 * 负责航线任务的管理和执行
 */
public interface MavlinkMissionService {
    /**
     * 上传航线任务
     *
     * @param droneId 无人机ID
     * @param mission 航线任务
     * @return 上传结果
     */
    CompletableFuture<Boolean> uploadMission(String droneId, Mission mission);

    /**
     * 创建矩形航线任务
     *
     * @param droneId 无人机ID
     * @param params 航线参数，必须包含：centerLat(中心点纬度)、centerLon(中心点经度)、
     *              width(宽度)、height(高度)、altitude(飞行高度)，
     *              可选：name(航线名称)
     * @return 创建并上传结果
     */
    CompletableFuture<Boolean> createRectangleMission(String droneId, Map<String, Object> params);

    /**
     * 创建多航点航线
     *
     * @param droneId 无人机ID
     * @param params 航线参数，必须包含：
     *              waypoints(航点列表，每个航点包含lat纬度和lon经度)
     *              altitude(飞行高度)
     *              可选参数：
     *              name(航线名称)
     *              acceptRadius(接受半径，默认5米)
     *              holdTime(停留时间，默认1秒)
     * @return 创建并上传结果
     */
    CompletableFuture<Boolean> createWaypointsMission(String droneId, Map<String, Object> params);

    /**
     * 开始执行任务
     * 
     * @param droneId 无人机ID
     * @param speed 飞行速度(米/秒)
     * @param taskName 任务名称，用于状态更新
     */
    void startMission(String droneId, BigDecimal speed, String taskName);

    /**
     * 暂停任务
     * 
     * @param droneId 无人机ID
     */
    void pauseMission(String droneId);

    /**
     * 继续任务
     * 
     * @param droneId 无人机ID
     */
    void resumeMission(String droneId);

    /**
     * 停止任务
     * 
     * @param droneId 无人机ID
     */
    void stopMission(String droneId);

    /**
     * 获取当前无人机航线任务
     *
     * @param droneId 无人机ID
     * @return 包含航点列表的Future对象
     */
    CompletableFuture<List<MissionWaypoint>> getCurrentMission(String droneId);
    
    /**
     * 设置飞行速度
     * 发送指令修改无人机的飞行速度
     *
     * @param droneId 无人机ID
     * @param speed 飞行速度(米/秒)
     * @return 设置结果
     */
    CompletableFuture<Boolean> setSpeed(String droneId, float speed);
} 