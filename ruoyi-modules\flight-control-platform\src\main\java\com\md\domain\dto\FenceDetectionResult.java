package com.md.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 围栏检测结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FenceDetectionResult {
    /**
     * 是否违规
     */
    private boolean violated;

    /**
     * 围栏ID
     */
    private String fenceId;

    /**
     * 违规区域ID
     */
    private String violatedAreaId;

    /**
     * 应执行的动作
     */
    private Integer actionToTake;

    /**
     * 违规原因
     */
    private String violationReason;
}