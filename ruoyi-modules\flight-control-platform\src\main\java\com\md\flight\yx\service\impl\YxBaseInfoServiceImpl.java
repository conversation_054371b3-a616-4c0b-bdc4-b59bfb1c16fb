package com.md.flight.yx.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.domain.po.*;
import com.md.enums.BaseInfoSyncEnum;
import com.md.enums.DataSourceEnum;
import com.md.flight.yx.domain.po.YxFlightRoute;
import com.md.flight.yx.domain.po.YxRoutePoint;
import com.md.flight.yx.model.drone.DroneInfo;
import com.md.flight.yx.model.flyer.FlyerInfo;
import com.md.flight.yx.model.task.RoutePoineTaskRequest;
import com.md.flight.yx.service.IYxDroneService;
import com.md.flight.yx.service.IYxFlightRouteService;
import com.md.flight.yx.service.IYxFlyerInfoService;
import com.md.mapper.FlightControlMapper;
import com.md.mapper.FlightPointMapper;
import com.md.service.IBaseInfoService;
import com.md.service.IFlightLineService;
import com.md.service.IFlyerService;
import com.md.service.IPlatformInfoService;
import com.md.service.IUavInfoService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.IdUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Deprecated 亿讯基本信息实现类
 * @date 2024年12月17日 10:38
 */
@Service
@RequiredArgsConstructor
public class YxBaseInfoServiceImpl implements IBaseInfoService {

    private final IYxDroneService yxDroneService;

    private final IUavInfoService uavInfoService;

    private final IPlatformInfoService platformInfoService;

    private final IYxFlightRouteService yxFlightRouteService;

    private final IFlightLineService flightLineService;

    private final FlightControlMapper flightControlMapper;

    private final IFlyerService flyerService;

    private final IYxFlyerInfoService yxFlyerInfoService;

    private final FlightPointMapper flightPointMapper;


    @Override
    public void syncUAV() {
        String syncType = BaseInfoSyncEnum.YX_SYNC.getSyncType();
        LambdaQueryWrapper<PlatformInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PlatformInfo::getFlightControlNo, syncType).last("limit 1");
        PlatformInfo platform = platformInfoService.getOne(queryWrapper);
        //获取亿讯数据
        List<DroneInfo> droneList = yxDroneService.getDroneList();

        //获取原业务表中亿讯数据
        List<UavInfo> uavs = uavInfoService.list(Wrappers.<UavInfo>lambdaQuery().eq(UavInfo::getManufacturerName, platform.getManufacturerName()));

        //构建业务数据
        Date nowDate = DateUtils.getNowDate();
        String simpleUUID = IdUtil.fastSimpleUUID();
        List<UavInfo> uavList = new ArrayList<>();
        List<DroneInfo> baseInfoListStream = droneList.stream()
                .map(baseInfo -> {
                    UavInfo uav = new UavInfo();
                    uav.setUavName(baseInfo.getName());
                    uav.setUavCode(baseInfo.getSn());
                    uav.setCategoryCode(baseInfo.getModel());
                    uav.setManufacturerName(platform.getManufacturerName());
                    uav.setFlightControlNo(platform.getFlightControlNo());
                    uav.setBatchId(simpleUUID);
                    uav.setSyncTime(DateUtils.getNowDate());
                    uav.setCreateTime(baseInfo.getManufactureDate() == null ? nowDate : baseInfo.getManufactureDate());
                    uav.setCreateTime(nowDate);
                    uav.setCreateBy(LoginHelper.getUserId());
                    UavInfo tmpUav = uavs.stream().filter(u -> u.getUavCode().equals(baseInfo.getSn())).findAny().orElse(null);
                    if (ObjectUtil.isNotEmpty(tmpUav)) {
                        uav.setRemark(tmpUav.getRemark());
                        uav.setMaxLoad(tmpUav.getMaxLoad());
                    }
                    uav.setDataSource(DataSourceEnum.SYNC.getCode());
                    uavList.add(uav);
                    baseInfo.setSyncTime(nowDate);
                    baseInfo.setBatchId(simpleUUID);
                    return baseInfo;
                })
                .collect(Collectors.toList());

        //添加数据到亿讯日志表中
        yxDroneService.saveBatch(baseInfoListStream);
        //删除业务表中亿讯数据
        uavInfoService.remove(Wrappers.<UavInfo>lambdaQuery().eq(UavInfo::getManufacturerName, platform.getManufacturerName()));
        //在业务表插入无人机信息
        uavInfoService.saveBatch(uavList);
    }

    /**
     * 同步飞手信息
     */
    @Override
    public void syncFlyer() {
        String syncType = BaseInfoSyncEnum.YX_SYNC.getSyncType();
        LambdaQueryWrapper<Platform> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Platform::getPlatformId, syncType);
        Platform platform = flightControlMapper.selectOne(queryWrapper);
        //获取亿讯的数据
        List<FlyerInfo> flyerInfoList = yxDroneService.getFlyerList();
        //获取原业务表中亿讯数据
        List<Flyer> flyerOld = flyerService.list(Wrappers.<Flyer>lambdaQuery().eq(Flyer::getPlateformId, platform.getPlatformId()));


        //构建业务数据
        List<Flyer> flyerList = new ArrayList<>();
        List<FlyerInfo> collect = flyerInfoList.stream()
                .map(col -> {
                    Flyer flyer = new Flyer();
                    flyer.setId(col.getId());
                    flyer.setFlyerName(col.getName());
                    flyer.setFlyerPhone(col.getPhone());
                    flyer.setFlyerSex(col.getSex());
                    flyer.setCreateTime(DateUtils.getNowDate());
                    flyer.setPlateformId(platform.getPlatformId());
                    flyer.setPlateformName(platform.getPlatformName());
                    flyer.setDataSourceValue(DataSourceEnum.SYNC.getValue());
                    flyer.setDataSource(DataSourceEnum.SYNC.getName());
                    Flyer tmpFlyer = flyerOld.stream().filter(f -> f.getId().equals(col.getId())).findAny().orElse(null);
                    if (ObjectUtil.isNotEmpty(tmpFlyer)) {
                        flyer.setRemark(tmpFlyer.getRemark());
                        flyer.setCreateBy(flyer.getCreateBy());
                    }
                    flyerList.add(flyer);

                    col.setCreateTime(DateUtils.getNowDate());
                    col.setPlateformId(platform.getPlatformId());
                    col.setPlateformName(platform.getPlatformName());
                    col.setDataSourceValue(DataSourceEnum.SYNC.getValue());
                    return col;
                }).collect(Collectors.toList());

        //添加数据到亿讯的飞手表中
        yxFlyerInfoService.remove(Wrappers.<FlyerInfo>lambdaQuery().eq(FlyerInfo::getPlateformId, platform.getPlatformId())
                .eq(FlyerInfo::getDataSourceValue, DataSourceEnum.SYNC.getValue()));
        yxFlyerInfoService.saveBatch(collect);

        //添加数据到fk亿讯飞手表中
        flyerService.remove(Wrappers.<Flyer>lambdaQuery().eq(Flyer::getPlateformId, platform.getPlatformId())
                .eq(Flyer::getDataSourceValue, DataSourceEnum.SYNC.getValue()));
        flyerService.saveBatch(flyerList);
    }

    /**
     * 同步亿讯航线数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncFlightLine() {
        // 1. 获取飞控平台信息
        String syncType = BaseInfoSyncEnum.YX_SYNC.getSyncType();
        LambdaQueryWrapper<PlatformInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PlatformInfo::getFlightControlNo, syncType);
        PlatformInfo platform = platformInfoService.getOne(queryWrapper);

        // 2. 获取原业务表中亿讯数据
        List<FlightLine> existingLines = flightLineService.list(
                Wrappers.<FlightLine>lambdaQuery()
                        .eq(FlightLine::getFlightControlNo, platform.getFlightControlNo())
        );

        // 3. 获取亿讯航线列表并构建新数据
        List<FlightLine> flightLines = new ArrayList<>();
        List<FlightPoint> allPoints = new ArrayList<>();
        Date nowDate = DateUtils.getNowDate();

        List<YxFlightRoute> yxRouteList = yxFlightRouteService.getLineList();
        for (YxFlightRoute yxRoute : yxRouteList) {
            // 3.1 生成雪花ID并获取航线详情
            long snowflakeId = IdWorker.getId();
            YxFlightRoute lineDetail = yxFlightRouteService.getLineDetail(
                    new RoutePoineTaskRequest(yxRoute.getId())
            );

            // 3.2 构建航线数据
            FlightLine flightLine = buildFlightLine(platform, lineDetail, snowflakeId, nowDate, LoginHelper.getUserId());

            // 3.3 保留原有备注信息
            FlightLine existingLine = existingLines.stream()
                    .filter(line -> line.getLineName().equals(lineDetail.getPlaneLineName()))
                    .findFirst()
                    .orElse(null);

            // 3.4 构建航点数据
            if (lineDetail.getPointDatas() != null) {
                buildFlightPoints(lineDetail, snowflakeId, nowDate, allPoints);
            }

            flightLines.add(flightLine);
        }

        // 4. 删除旧数据
        List<String> oldLineIds = flightLineService.list(
                Wrappers.<FlightLine>lambdaQuery()
                        .eq(FlightLine::getFlightControlNo, platform.getFlightControlNo())
                        .eq(FlightLine::getDataSource, DataSourceEnum.SYNC.getCode())
        ).stream().map(FlightLine::getId).collect(Collectors.toList());

        if (!oldLineIds.isEmpty()) {
            flightPointMapper.deletePointsByLineIds(oldLineIds);
        }

        flightLineService.remove(
                Wrappers.<FlightLine>lambdaQuery()
                        .eq(FlightLine::getFlightControlNo, platform.getFlightControlNo())
                        .eq(FlightLine::getDataSource, DataSourceEnum.SYNC.getCode())
        );

        // 5. 保存新数据
        if (!flightLines.isEmpty()) {
            flightLineService.saveBatch(flightLines);
            if (!allPoints.isEmpty()) {
                flightPointMapper.batchInsertPoints(allPoints);
            }
        }
    }

    @Override
    public boolean buildKmzByFlightLine(String routeId) {
        return true;
    }

    /**
     * 构建航线数据
     */
    private FlightLine buildFlightLine(PlatformInfo platform, YxFlightRoute lineDetail,
                                       long snowflakeId, Date nowDate, Long currentUser) {
        FlightLine flightLine = new FlightLine();
        flightLine.setId(String.valueOf(snowflakeId));
        flightLine.setFlightControlNo(platform.getFlightControlNo());
        flightLine.setLineName(lineDetail.getPlaneLineName());
        flightLine.setFlightSpeed(new BigDecimal(lineDetail.getMaxSpeed()));
        flightLine.setDataSource(DataSourceEnum.SYNC.getCode());
        flightLine.setMissingAction(String.valueOf(lineDetail.getMissingAction()));
        flightLine.setFinishedAction(String.valueOf(lineDetail.getFinishedAction()));
        flightLine.setCreateTime(nowDate);
        flightLine.setCreateBy(currentUser);
        return flightLine;
    }

    /**
     * 构建航点数据
     */
    private void buildFlightPoints(YxFlightRoute lineDetail, long lineId, Date nowDate,
                                   List<FlightPoint> allPoints) {
        for (int i = 0; i < lineDetail.getPointDatas().size(); i++) {
            YxRoutePoint yxPoint = lineDetail.getPointDatas().get(i);
            FlightPoint point = new FlightPoint();
            point.setId(String.valueOf(IdWorker.getId()));
            point.setLineId(String.valueOf(lineId));
            point.setPointIndex(Long.valueOf(i));
            point.setLatitude(new BigDecimal(yxPoint.getLatitude()));
            point.setLongitude(new BigDecimal(yxPoint.getLongitude()));
            point.setAltitude(new BigDecimal(yxPoint.getHeight()));
            point.setCreateTime(nowDate);
            allPoints.add(point);
        }
    }
}
