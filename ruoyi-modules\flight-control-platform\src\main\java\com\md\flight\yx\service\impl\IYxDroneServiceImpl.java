package com.md.flight.yx.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.domain.po.Platform;
import com.md.domain.po.PlatformInfo;
import com.md.enums.PlatformInfoEnum;
import com.md.flight.yx.domain.dto.YxRequsetResult;
import com.md.flight.yx.mapper.YxDroneInfoMapper;
import com.md.flight.yx.model.drone.DroneInfo;
import com.md.flight.yx.model.flyer.FlyerInfo;
import com.md.flight.yx.openfeign.UavApiClient;
import com.md.flight.yx.service.IYxDroneService;
import com.md.flight.yx.util.SignUtil;
import com.md.flight.yx.util.YxAESUtil;
import com.md.mapper.FlightControlMapper;
import com.md.service.IPlatformInfoService;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.stereotype.Service;
import cn.hutool.core.collection.CollUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月17日 10:53
 */

@Service
@RequiredArgsConstructor
public class IYxDroneServiceImpl extends ServiceImpl<YxDroneInfoMapper, DroneInfo> implements IYxDroneService {

    private final UavApiClient uavApiClient;

    private final IPlatformInfoService platformInfoService;

    private final FlightControlMapper flightControlMapper;

    @Override
    public List<DroneInfo> getDroneList() {
        String timestamp = SignUtil.getTimeStampStr();
        PlatformInfo platform = platformInfoService.getOne(Wrappers.<PlatformInfo>lambdaQuery().eq(PlatformInfo::getFlightControlNo, PlatformInfoEnum.YX.getCode()));
        String sign = SignUtil.sign(platform.getPassword(), timestamp, "");

        String encrypt = YxAESUtil.encrypt("", platform.getPassword());
        String interfaceResult = uavApiClient.getDroneList(encrypt, timestamp, sign, platform.getAccount());
        YxRequsetResult requsetResult = JSON.parseObject(interfaceResult, YxRequsetResult.class);
        String decrypt = YxAESUtil.decrypt(requsetResult.getResult(), platform.getPassword());
        List<DroneInfo> droneInfos = JSON.parseArray(decrypt, DroneInfo.class);

        if (CollUtil.isEmpty(droneInfos)) {
            throw new RuntimeException("获取无人机列表失败");
        }
        return droneInfos;
    }

    /**
     * 获取飞手列表
     *
     * @return
     */
    @Override
    public List<FlyerInfo> getFlyerList() {
        String timestamp = SignUtil.getTimeStampStr();
        Platform platform = flightControlMapper.selectOne(Wrappers.<Platform>lambdaQuery().eq(Platform::getPlatformId, PlatformInfoEnum.YX.getCode()));
        //请求数据需要加密
        String sign = SignUtil.sign(platform.getPassword(), timestamp, "");

        String encrypt = YxAESUtil.encrypt("", platform.getPassword());
        String flyerList = uavApiClient.getFlyerList(encrypt, timestamp, sign, platform.getUsername());
        YxRequsetResult requsetResult = JSON.parseObject(flyerList, YxRequsetResult.class);
        //响应数据需要解密
        String decrypt = YxAESUtil.decrypt(requsetResult.getResult(), platform.getPassword());
        List<FlyerInfo> flyerInfos = JSON.parseArray(decrypt, FlyerInfo.class);
        if (ObjectUtil.isEmpty(flyerInfos) || flyerInfos.size() == 0) {
            throw new RuntimeException("获取飞手列表失败");
        }
        return flyerInfos;
    }
}
