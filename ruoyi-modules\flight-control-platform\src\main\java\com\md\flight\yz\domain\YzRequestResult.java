package com.md.flight.yz.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 邮政返回数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YzRequestResult {
    /**
     * AES加密后的数据
     */
    private String data;

    /**
     * 响应值
     */
    private String retCode;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date retDate;

    /**
     * 响应说明
     */
    private String retMsg;
}
