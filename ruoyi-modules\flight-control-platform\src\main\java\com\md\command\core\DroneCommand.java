package com.md.command.core;

import com.md.command.constant.CommandType;
import java.util.Map;

/**
 * 无人机指令接口
 */
public interface DroneCommand {
    /**
     * 获取无人机ID
     */
    String getDroneId();

    /**
     * 获取指令类型
     */
    CommandType getCommandType();

    /**
     * 获取指令参数
     */
    Map<String, Object> getParameters();
    
    /**
     * 获取指令ID
     * 如果未设置，返回null
     */
    default String getCommandId() {
        return null;
    }

    /**
     * 获取任务ID
     * 如果未设置，返回null
     */
    default String getTaskId() {
        return null;
    }
} 