package com.md.flight.yx.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.md.flight.yx.model.drone.DroneInfo;
import com.md.flight.yx.model.flyer.FlyerInfo;

import java.util.List;

/**
 * 无人机服务接口
 */
public interface IYxDroneService extends IService<DroneInfo> {

    /**
     * 获取无人机列表
     *
     * @return 无人机信息列表
     * @throws RuntimeException 当API调用失败时抛出
     */
    List<DroneInfo> getDroneList();

    List<FlyerInfo> getFlyerList();

}
