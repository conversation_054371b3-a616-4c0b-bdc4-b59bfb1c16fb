package com.md.flight.lh.processor;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.md.constant.TaskCallbackConstants;
import com.md.enums.TaskStatusEnum;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseArrivalData;
import com.md.mqtt.event.DroneType;
import com.md.mqtt.event.TaskExecutionEvent;
import com.md.service.IFlightTaskStatusService;
import com.md.service.ITaskCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 联合飞行器着陆事件处理
 */
@Component
@Slf4j
public class LhArrivalProcessor implements LhBaseProcessor {
    @Autowired
    private ITaskCallbackService taskCallbackService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private IFlightTaskStatusService flightTaskStatusService;

    private final String LH_FLIGHT_CODE = "lh:flight_code:";

    @Override
    public void processMessage(String payload) {
        try {
            log.info("收到联合飞行器着陆事件消息: {}", payload);
            // 消息转换
            LhResponse<LhResponseArrivalData> lhResponse = JSON.parseObject(payload, new TypeReference<>() {
            });
            String taskId =
                TenantHelper.ignore(() -> RedisUtils.getCacheObject(LH_FLIGHT_CODE + lhResponse.getGateway()));

            if (ObjectUtil.isEmpty(taskId)) {
                return;
            }

            // 更新任务状态为已完成
            try {
                boolean updateResult =
                    flightTaskStatusService.updateTaskStatusById(taskId, TaskStatusEnum.COMPLETED.getCode(),
                        "任务完成");
                if (updateResult) {
                    log.info("联合飞机任务状态已更新为已完成: taskId={}", taskId);
                } else {
                    log.warn("联合飞机任务状态更新失败: taskId={}", taskId);
                }
            } catch (Exception e) {
                log.error("更新联合飞机任务状态失败: taskId={}, error={}", taskId, e.getMessage(), e);
            }

            // 发布任务结束事件，通知轨迹记录监听器停止记录
            try {
                eventPublisher.publishEvent(
                    new TaskExecutionEvent(lhResponse.getGateway(), taskId, false, DroneType.LH));
                log.info("已发布联合飞机任务结束事件: droneId={}, taskId={}", lhResponse.getGateway(), taskId);
            } catch (Exception e) {
                log.error("发布联合飞机任务结束事件失败: droneId={}, taskId={}, error={}", lhResponse.getGateway(),
                    taskId, e.getMessage(), e);
            }

            // 检查是否需要发送回调（统一智能判断逻辑）
            String platformCode = TenantHelper.ignore(
                () -> RedisUtils.getCacheObject(TaskCallbackConstants.TASK_PLATFORM_CODE_KEY_PREFIX + taskId));

            if (StringUtils.isNotEmpty(platformCode)) {
                // 只有开放API创建的任务才发送回调
                log.info("检测到开放API创建的联合飞机任务，发送着陆回调: taskId={}, platformCode={}", taskId,
                    platformCode);
                boolean result = taskCallbackService.sendTaskCallback(taskId, 200, "降落成功");
                log.info("联合飞机着陆回调发送结果: taskId={}, result={}", taskId, result);
            } else {
                // 前端创建的任务不发送回调
                log.info("检测到前端创建的联合飞机任务，跳过着陆回调: taskId={}", taskId);
            }

            TenantHelper.ignore(() -> {
                RedisUtils.deleteObject(LH_FLIGHT_CODE + lhResponse.getGateway());
                return null;
            });
        } catch (Exception e) {
            log.error("收到联合飞行器着陆事件处理失败: {}", e.getMessage(), e);
        }
    }
}
