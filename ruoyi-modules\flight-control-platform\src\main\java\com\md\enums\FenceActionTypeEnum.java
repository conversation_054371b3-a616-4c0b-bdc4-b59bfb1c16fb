package com.md.enums;

import lombok.Getter;

/**
 * 围栏触发动作类型枚举
 */
@Getter
public enum FenceActionTypeEnum {
    /**
     * 警告
     */
    WARNING(1, "警告"),

    /**
     * 返航
     */
    RETURN_HOME(2, "返航"),

    /**
     * 悬停
     */
    HOVER(3, "悬停"),

    /**
     * 降落
     */
    LAND(4, "降落");

    private final int code;
    private final String info;

    FenceActionTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static FenceActionTypeEnum getByCode(int code) {
        for (FenceActionTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}