package com.md.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

import com.md.domain.po.FlightPoint;
import org.dromara.common.core.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.md.mapper.FlightPointMapper;
import com.md.service.IFlightPointService;

/**
 * 航点Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class FlightPointServiceImpl extends ServiceImpl<FlightPointMapper, FlightPoint> implements IFlightPointService {

    private final FlightPointMapper flightPointMapper;

    /**
     * 查询航点
     *
     * @param id 航点主键
     * @return 航点
     */
    @Override
    public FlightPoint selectFlightPointById(Long id) {
        return flightPointMapper.selectFlightPointById(id);
    }

    /**
     * 查询航点列表
     *
     * @param flightPoint 航点
     * @return 航点
     */
    @Override
    public List<FlightPoint> selectFlightPointList(FlightPoint flightPoint) {
        return flightPointMapper.selectFlightPointList(flightPoint);
    }

    /**
     * 新增航点
     *
     * @param flightPoint 航点
     * @return 结果
     */
    @Override
    public int insertFlightPoint(FlightPoint flightPoint) {
        flightPoint.setCreateTime(DateUtils.getNowDate());
        return flightPointMapper.insertFlightPoint(flightPoint);
    }

    /**
     * 修改航点
     *
     * @param flightPoint 航点
     * @return 结果
     */
    @Override
    public int updateFlightPoint(FlightPoint flightPoint) {
        return flightPointMapper.updateFlightPoint(flightPoint);
    }

    /**
     * 批量删除航点
     *
     * @param ids 需要删除的航点主键
     * @return 结果
     */
    @Override
    public int deleteFlightPointByIds(Long[] ids) {
        return flightPointMapper.deleteFlightPointByIds(ids);
    }

    /**
     * 删除航点信息
     *
     * @param id 航点主键
     * @return 结果
     */
    @Override
    public int deleteFlightPointById(Long id) {
        return flightPointMapper.deleteFlightPointById(id);
    }
}
