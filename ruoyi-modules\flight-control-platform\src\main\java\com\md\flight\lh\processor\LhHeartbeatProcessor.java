package com.md.flight.lh.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.md.flight.lh.constant.LhTopicConstantReq;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseHeartbeatData;
import com.md.flight.lh.domain.dto.LhResponseHeartbeatDataOutput;
import com.md.flight.lh.domain.dto.LhResponseOnlineData;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 联合设备心跳消息处理
 */
@Component
@Slf4j
public class LhHeartbeatProcessor implements LhBaseProcessor{
    @Autowired
    private MqttMessageHandler messageHandler;

    @Override
    public void processMessage(String payload) {
        try {
            // 消息转换
            LhResponse<LhResponseHeartbeatData> lhResponse = JSON.parseObject(
                payload,
                new TypeReference<>() {});
            LhResponseHeartbeatData lhResponseHeartbeatData = new LhResponseHeartbeatData();
            lhResponseHeartbeatData.setOutput(new LhResponseHeartbeatDataOutput(50));
            lhResponse.setData(lhResponseHeartbeatData);
            // 构建主题
            String topic = String.format(LhTopicConstantReq.ONLINE, lhResponse.getGateway());
            // 响应消息
            messageHandler.sendToMqtt(topic, JSON.toJSONString(lhResponse));
        } catch (Exception e) {
            log.error("处理联合设备心跳消息响应失败: {}", e.getMessage(), e);
        }
    }
}
