package com.md.service;

import com.md.command.model.dto.CommandResult;
import com.md.domain.po.FlightTaskPoint;

import java.math.BigDecimal;
import java.util.List;

/**
 * 航线任务紧急处理服务接口
 * 
 * <AUTHOR>
 */
public interface IFlightTaskEmergencyService {
    
    /**
     * 查找最近的备降点
     *
     * @param taskId    任务ID
     * @param latitude  当前纬度
     * @param longitude 当前经度
     * @return 最近的备降点
     */
    FlightTaskPoint findNearestEmergencyLandingPoint(String taskId, BigDecimal latitude, BigDecimal longitude);

    /**
     * 获取任务中的所有备降点
     *
     * @param taskId 任务ID
     * @return 备降点列表
     */
    List<FlightTaskPoint> getAllEmergencyLandingPoints(String taskId);

    /**
     * 执行备降操作
     *
     * @param taskId           任务ID
     * @param droneId          无人机ID
     * @param currentLatitude  当前纬度
     * @param currentLongitude 当前经度
     * @param currentAltitude  当前高度
     * @return 命令执行结果
     */
    CommandResult executeEmergencyLanding(String taskId, String droneId, BigDecimal currentLatitude,
        BigDecimal currentLongitude, BigDecimal currentAltitude);
}