<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.FenceViolationLogMapper">
    <resultMap type="com.md.domain.po.FenceViolationLog" id="FenceViolationLogResult">
        <id property="id" column="id"/>
        <result property="fenceId" column="fence_id"/>
        <result property="taskId" column="task_id"/>
        <result property="uavCode" column="uav_code"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="height" column="height"/>
        <result property="actionTaken" column="action_taken"/>
        <result property="violationTime" column="violation_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectViolationLogVo">
        select id,
               fence_id,
               task_id,
               uav_code,
               longitude,
               latitude,
               height,
               action_taken,
               violation_time,
               remark
        from fence_violation_log
    </sql>

    <select id="selectViolationLogList" parameterType="com.md.domain.po.FenceViolationLog"
            resultMap="FenceViolationLogResult">
        <include refid="selectViolationLogVo"/>
        <where>
            <if test="fenceId != null and fenceId != ''">
                AND fence_id = #{fenceId}
            </if>
            <if test="taskId != null and taskId != ''">
                AND task_id = #{taskId}
            </if>
            <if test="uavCode != null and uavCode != ''">
                AND uav_code = #{uavCode}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND violation_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND violation_time &lt;= #{params.endTime}
            </if>
        </where>
        order by violation_time desc
    </select>

    <select id="selectViolationLogById" parameterType="String" resultMap="FenceViolationLogResult">
        <include refid="selectViolationLogVo"/>
        where id = #{id}
    </select>

    <select id="selectByTaskId" parameterType="String" resultMap="FenceViolationLogResult">
        <include refid="selectViolationLogVo"/>
        where task_id = #{taskId}
        order by violation_time desc
    </select>

    <select id="selectByUavCode" parameterType="String" resultMap="FenceViolationLogResult">
        <include refid="selectViolationLogVo"/>
        where uav_code = #{uavCode}
        order by violation_time desc
    </select>

    <select id="selectByFenceId" parameterType="String" resultMap="FenceViolationLogResult">
        <include refid="selectViolationLogVo"/>
        where fence_id = #{fenceId}
        order by violation_time desc
    </select>

    <select id="selectByTimeRange" resultMap="FenceViolationLogResult">
        <include refid="selectViolationLogVo"/>
        <where>
            <if test="beginTime != null">
                AND violation_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                AND violation_time &lt;= #{endTime}
            </if>
        </where>
        order by violation_time desc
    </select>

    <insert id="insertViolationLog" parameterType="com.md.domain.po.FenceViolationLog">
        insert into fence_violation_log (
        id,
        fence_id,
        task_id,
        uav_code,
        longitude,
        latitude,
        height,
        action_taken,
        violation_time,
        remark
        ) values (
        #{id},
        #{fenceId},
        #{taskId},
        #{uavCode},
        #{longitude},
        #{latitude},
        #{height},
        #{actionTaken},
        #{violationTime},
        #{remark}
        )
    </insert>

    <insert id="batchInsertViolationLogs" parameterType="java.util.List">
        insert into fence_violation_log (
        id,
        fence_id,
        task_id,
        uav_code,
        longitude,
        latitude,
        height,
        action_taken,
        violation_time,
        remark
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.fenceId},
            #{item.taskId},
            #{item.uavCode},
            #{item.longitude},
            #{item.latitude},
            #{item.height},
            #{item.actionTaken},
            #{item.violationTime},
            #{item.remark}
            )
        </foreach>
    </insert>

    <update id="updateViolationLog" parameterType="com.md.domain.po.FenceViolationLog">
        update fence_violation_log
        <set>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteViolationLogById" parameterType="String">
        delete from fence_violation_log where id = #{id}
    </delete>

    <delete id="deleteViolationLogByIds" parameterType="String">
        delete from fence_violation_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>