package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.md.domain.po.PlatformInfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 飞控平台视图对象
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PlatformInfo.class)
public class PlatformInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 飞控厂商名称
     */
    @ExcelProperty(value = "飞控厂商名称")
    private String manufacturerName;

    /**
     * 飞控编号
     */
    @ExcelProperty(value = "飞控编号")
    private String flightControlNo;

    /**
     * 飞控网络地址
     */
    @ExcelProperty(value = "飞控网络地址")
    private String networkAddress;

    /**
     * 账号
     */
    @ExcelProperty(value = "账号")
    private String account;

    /**
     * 密码
     */
    @ExcelProperty(value = "密码")
    private String password;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 秘钥
     */
    private String aesKey;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人账号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    @ExcelProperty(value = "创建人")
    private String createByName;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新人账号
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "updateBy")
    @ExcelProperty(value = "更新人")
    private String updateByName;
}