package com.md.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.domain.bo.FenceViolationBo;
import com.md.domain.po.FenceViolationLog;
import com.md.domain.vo.FenceViolationVo;
import com.md.mapper.FenceViolationLogMapper;
import com.md.service.IFenceViolationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 围栏违规记录服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FenceViolationServiceImpl extends ServiceImpl<FenceViolationLogMapper, FenceViolationLog>
    implements IFenceViolationService {

    private final FenceViolationLogMapper fenceViolationLogMapper;

    @Override
    public List<FenceViolationVo> selectViolationList(FenceViolationBo bo) {
        LambdaQueryWrapper<FenceViolationLog> lqw = buildQueryWrapper(bo);
        return fenceViolationLogMapper.selectVoList(lqw);
    }

    @Override
    public TableDataInfo<FenceViolationVo> selectViolationList(FenceViolationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FenceViolationLog> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(FenceViolationLog::getId);
        Page<FenceViolationVo> page = fenceViolationLogMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public FenceViolationVo selectViolationById(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        return fenceViolationLogMapper.selectVoById(id);
    }

    @Override
    public TableDataInfo<FenceViolationVo> selectViolationByTaskId(String taskId, PageQuery pageQuery) {
        if (StringUtils.isEmpty(taskId)) {
            return TableDataInfo.build(new ArrayList<>());
        }

        LambdaQueryWrapper<FenceViolationLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(FenceViolationLog::getTaskId, taskId);
        lqw.orderByDesc(FenceViolationLog::getViolationTime);

        Page<FenceViolationVo> page = fenceViolationLogMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public TableDataInfo<FenceViolationVo> selectViolationByUavCode(String uavCode, PageQuery pageQuery) {
        if (StringUtils.isEmpty(uavCode)) {
            return TableDataInfo.build(new ArrayList<>());
        }

        LambdaQueryWrapper<FenceViolationLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(FenceViolationLog::getUavCode, uavCode);
        lqw.orderByDesc(FenceViolationLog::getViolationTime);

        Page<FenceViolationVo> page = fenceViolationLogMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public TableDataInfo<FenceViolationVo> selectViolationByFenceId(String fenceId, PageQuery pageQuery) {
        if (StringUtils.isEmpty(fenceId)) {
            return TableDataInfo.build(new ArrayList<>());
        }

        LambdaQueryWrapper<FenceViolationLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(FenceViolationLog::getFenceId, fenceId);
        lqw.orderByDesc(FenceViolationLog::getViolationTime);

        Page<FenceViolationVo> page = fenceViolationLogMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public int deleteViolationByIds(String[] ids) {
        if (ids == null || ids.length == 0) {
            return 0;
        }
        return fenceViolationLogMapper.deleteViolationLogByIds(ids);
    }

    /**
     * 构建查询条件
     *
     * @param bo 业务查询对象
     * @return 查询条件
     */
    private LambdaQueryWrapper<FenceViolationLog> buildQueryWrapper(FenceViolationBo bo) {
        LambdaQueryWrapper<FenceViolationLog> lqw = Wrappers.lambdaQuery();

        lqw.eq(StringUtils.isNotEmpty(bo.getFenceId()), FenceViolationLog::getFenceId, bo.getFenceId());
        lqw.eq(StringUtils.isNotEmpty(bo.getTaskId()), FenceViolationLog::getTaskId, bo.getTaskId());
        lqw.eq(StringUtils.isNotEmpty(bo.getUavCode()), FenceViolationLog::getUavCode, bo.getUavCode());
        lqw.eq(bo.getActionTaken() != null, FenceViolationLog::getActionTaken, bo.getActionTaken());

        // 处理时间范围查询
        lqw.ge(bo.getBeginTime() != null, FenceViolationLog::getViolationTime, bo.getBeginTime());
        lqw.le(bo.getEndTime() != null, FenceViolationLog::getViolationTime, bo.getEndTime());

        return lqw;
    }
}