package com.md.mqtt.processor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.md.command.service.DroneCommandLogService;
import com.md.constant.TaskCallbackConstants;
import com.md.domain.po.DroneCommandLog;
import com.md.domain.po.FlightTask;
import com.md.enums.TaskStatusEnum;
import com.md.mqtt.event.DroneType;
import com.md.mqtt.event.TaskExecutionEvent;
import com.md.service.IFlightTaskService;
import com.md.service.IFlightTrackService;
import com.md.service.ITaskCallbackService;
import com.md.utils.DroneLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 无人机响应消息处理器 处理 dji/response 和 dji/error 主题的消息
 */
@Slf4j
@Component
public class DroneResponseMessageProcessor extends AbstractMqttMessageProcessor {

    @Autowired
    private DroneCommandLogService commandLogService;

    @Autowired
    private IFlightTaskService flightTaskService;

    @Autowired
    private IFlightTrackService flightTrackService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private DroneLockUtil droneLockUtil;

    @Autowired
    private ITaskCallbackService taskCallbackService;

    // 缓存命令日志，减少频繁的数据库查询
    private final Map<String, DroneCommandLog> commandLogCache = new ConcurrentHashMap<>();

    // 缓存上次任务状态更新时间，避免频繁更新相同状态
    private final Map<String, Map<String, Long>> taskStatusUpdateTimeMap = new ConcurrentHashMap<>();

    // 记录任务执行状态
    private final Map<String, Boolean> taskExecutionMap = new ConcurrentHashMap<>();

    @Value("${mqtt.response.batch-update-millis:2000}")  // 默认2秒
    private long batchUpdateMillis;

    private static final String DICT_TYPE = "flight_task_status";
    private static final String STATUS_EXECUTING = String.valueOf(TaskStatusEnum.EXECUTING.getCode());  // 执行中
    private static final String STATUS_FINISHED = String.valueOf(TaskStatusEnum.COMPLETED.getCode());   // 已完成

    @Override
    protected String getTopicPattern() {
        return "dji/response|dji/error";  // 使用正则表达式匹配两个主题
    }

    @Override
    protected void doProcessMessage(String topic, String payload) {
        try {
            log.info("收到{}消息: {}", topic, payload);
            JSONObject jsonPayload = JSON.parseObject(payload);
            String droneId = jsonPayload.getString("droneSN");
            String commandId = jsonPayload.getString("commandId");
            String message = jsonPayload.getString("message");

            // 处理航线任务状态更新
            if (topic.equals("dji/response") && message != null) {
                if (message.contains("EXECUTING") || message.contains("MISSION_COMPLETE_WITH_LANDING")) {
                    log.info("收到任务状态更新消息: droneId={}, message={}, commandId={}", droneId, message, commandId);

                    // 获取对应的任务状态值
                    String taskStatus = message.contains("EXECUTING") ? STATUS_EXECUTING : STATUS_FINISHED;
                    boolean isExecuting = message.contains("EXECUTING");
                    boolean isCompleted = message.contains("MISSION_COMPLETE_WITH_LANDING");

                    // 从缓存或数据库获取命令日志
                    DroneCommandLog commandLog = getCommandLogFromCacheOrDb(commandId);
                    log.info("获取到命令日志: commandId={}, commandLog={}", commandId, commandLog);

                    if (commandLog != null && commandLog.getParameters() != null) {
                        // 从命令参数中提取任务名称
                        JSONObject parameters = JSON.parseObject(commandLog.getParameters());
                        log.info("解析命令参数: parameters={}", parameters);

                        if (parameters != null && parameters.containsKey("missionFile")) {
                            String missionFile = parameters.getString("missionFile");
                            String taskName = extractTaskName(missionFile);
                            log.info("提取任务名称: missionFile={}, taskName={}", missionFile, taskName);

                            if (taskName != null) {
                                // 检查是否需要更新任务状态
                                if (shouldUpdateTaskStatus(taskName, taskStatus)) {
                                    log.info("开始更新任务状态: taskName={}, status={}, isExecuting={}", taskName,
                                        taskStatus, isExecuting);

                                    // 更新任务状态
                                    updateTaskStatus(taskName, taskStatus);

                                    // 如果是执行中状态，查找当前任务并发布事件
                                    if (isExecuting) {
                                        FlightTask currentTask =
                                            flightTaskService.lambdaQuery().eq(FlightTask::getTaskName, taskName)
                                                .last("limit 1").one();
                                        log.info("查询到当前任务: taskName={}, task={}", taskName, currentTask);

                                        if (currentTask != null) {
                                            log.info("准备清除历史轨迹点: taskId={}, droneId={}, taskName={}",
                                                currentTask.getId(), droneId, taskName);

                                            try {
                                                // 清除该任务和无人机的历史轨迹点，确保只保留最新执行的轨迹
                                                flightTrackService.deleteTrackPointsByTaskIdAndDroneId(
                                                    currentTask.getId(), droneId);
                                                log.info("历史轨迹点清除成功: taskId={}, droneId={}",
                                                    currentTask.getId(), droneId);
                                            } catch (Exception e) {
                                                log.error("清除历史轨迹点失败: taskId={}, droneId={}, error={}",
                                                    currentTask.getId(), droneId, e.getMessage(), e);
                                            }

                                            // 标记任务开始执行
                                            taskExecutionMap.put(droneId, true);
                                            log.info("任务执行标记已设置: droneId={}, taskId={}", droneId,
                                                currentTask.getId());

                                            // 发布任务开始执行事件
                                            try {
                                                eventPublisher.publishEvent(
                                                    new TaskExecutionEvent(droneId, currentTask.getId(), true,
                                                        DroneType.DJI));
                                                log.info("任务开始执行事件已发布: droneId={}, taskId={}", droneId,
                                                    currentTask.getId());
                                            } catch (Exception e) {
                                                log.error("发布任务开始执行事件失败: droneId={}, taskId={}, error={}",
                                                    droneId, currentTask.getId(), e.getMessage(), e);
                                            }
                                        } else {
                                            log.warn("未找到对应的任务记录: taskName={}", taskName);
                                        }
                                    } else {
                                        log.info("任务执行完成，清除执行标记: droneId={}", droneId);
                                        // 标记任务结束
                                        taskExecutionMap.remove(droneId);

                                        // 获取taskId用于回调和事件发布
                                        String taskId = getTaskIdByTaskName(taskName);

                                        // 发送任务完成回调
                                        if (taskId != null) {
                                            try {
                                                taskCallbackService.sendTaskCallback(taskId,
                                                    TaskCallbackConstants.CODE_SUCCESS, "大疆任务执行成功");
                                                log.info("大疆任务完成回调已发送: taskId={}, taskName={}", taskId,
                                                    taskName);
                                            } catch (Exception e) {
                                                log.error("发送大疆任务完成回调失败: taskId={}, taskName={}, error={}",
                                                    taskId, taskName, e.getMessage(), e);
                                            }
                                        } else {
                                            log.warn("无法获取任务ID，跳过发送任务完成回调: taskName={}", taskName);
                                        }

                                        // 发布任务结束事件
                                        try {
                                            eventPublisher.publishEvent(
                                                new TaskExecutionEvent(droneId, taskId, false, DroneType.DJI));
                                            log.info("任务结束事件已发布: droneId={}, taskId={}", droneId, taskId);
                                        } catch (Exception e) {
                                            log.error("发布任务结束事件失败: droneId={}, taskId={}, error={}", droneId,
                                                taskId, e.getMessage(), e);
                                        }
                                    }
                                } else {
                                    log.debug("任务状态更新被过滤（重复更新）: taskName={}, status={}", taskName,
                                        taskStatus);
                                }
                            } else {
                                log.warn("无法从航线文件名提取任务名称: missionFile={}", missionFile);
                            }
                        } else {
                            log.warn("命令参数中未包含missionFile字段: parameters={}", parameters);
                        }
                    } else {
                        log.warn("未找到对应的命令日志或参数为空: commandId={}", commandId);
                    }

                    // 如果是任务完成消息，释放无人机控制锁
                    if (isCompleted && droneId != null) {
                        try {
                            droneLockUtil.unlock(droneId);
                            log.info("任务完成，已释放无人机控制锁: droneId={}", droneId);
                        } catch (Exception e) {
                            log.error("释放无人机控制锁失败: droneId={}, error={}", droneId, e.getMessage(), e);
                        }
                    }
                }
            }

            // 处理命令日志更新
            if (commandId != null) {
                processCommandLog(topic, payload, jsonPayload, commandId);
            }

            log.debug("消息处理完成: topic={}, droneId={}, commandId={}", topic, droneId, commandId);

        } catch (Exception e) {
            log.error("处理消息失败: topic={}, payload={}, error={}", topic, payload, e.getMessage(), e);
        }
    }

    private DroneCommandLog getCommandLogFromCacheOrDb(String commandId) {
        // 先从缓存中查找
        DroneCommandLog cachedLog = commandLogCache.get(commandId);
        if (cachedLog != null) {
            return cachedLog;
        }

        // 缓存未命中，从数据库查询
        DroneCommandLog commandLog = commandLogService.getOne(
            new LambdaQueryWrapper<DroneCommandLog>().eq(DroneCommandLog::getCommandId, commandId));

        // 存入缓存
        if (commandLog != null) {
            commandLogCache.put(commandId, commandLog);
        }

        return commandLog;
    }

    private boolean shouldUpdateTaskStatus(String taskName, String status) {
        long currentTime = System.currentTimeMillis();

        // 获取或创建任务的状态更新时间映射
        Map<String, Long> statusUpdateTimes =
            taskStatusUpdateTimeMap.computeIfAbsent(taskName, k -> new ConcurrentHashMap<>());

        // 获取上次该状态的更新时间
        Long lastUpdateTime = statusUpdateTimes.get(status);

        // 如果是第一次更新，或者距离上次更新已经超过指定时间，则允许更新
        if (lastUpdateTime == null || currentTime - lastUpdateTime > batchUpdateMillis) {
            statusUpdateTimes.put(status, currentTime);
            return true;
        }

        return false;
    }

    private void processCommandLog(String topic, String payload, JSONObject jsonPayload, String commandId) {
        // 先查询现有的日志记录
        DroneCommandLog existingLog = getCommandLogFromCacheOrDb(commandId);

        if (existingLog == null) {
            log.warn("未找到对应的命令日志记录: commandId={}", commandId);
            return;
        }

        // 构建命令日志更新对象
        DroneCommandLog update = new DroneCommandLog();

        // 处理响应数据追加
        String existingResponseData = existingLog.getResponseData();
        String newResponseData;
        if (StringUtils.hasText(existingResponseData)) {
            newResponseData = existingResponseData + "\n" + payload;
        } else {
            newResponseData = payload;
        }
        update.setResponseData(newResponseData);

        if (topic.equals("dji/response")) {
            boolean success = jsonPayload.getBooleanValue("success");
            update.setExecutionStatus(success ? 1 : 2);  // 1:成功 2:失败

            if (!success) {
                String errorMessage = jsonPayload.getString("message");
                if (StringUtils.hasText(existingLog.getErrorMessage())) {
                    update.setErrorMessage(existingLog.getErrorMessage() + "\n" + errorMessage);
                } else {
                    update.setErrorMessage(errorMessage);
                }
            }
        } else if (topic.equals("dji/error")) {
            update.setExecutionStatus(2);  // 错误状态
            String errorMessage = jsonPayload.getString("error");
            if (StringUtils.hasText(existingLog.getErrorMessage())) {
                update.setErrorMessage(existingLog.getErrorMessage() + "\n" + errorMessage);
            } else {
                update.setErrorMessage(errorMessage);
            }
        }

        // 更新数据库
        commandLogService.update(update,
            new LambdaQueryWrapper<DroneCommandLog>().eq(DroneCommandLog::getCommandId, commandId));

        // 更新缓存
        DroneCommandLog cachedLog = commandLogCache.get(commandId);
        if (cachedLog != null) {
            cachedLog.setResponseData(update.getResponseData());
            cachedLog.setExecutionStatus(update.getExecutionStatus());
            cachedLog.setErrorMessage(update.getErrorMessage());
        }
    }

    private String extractTaskName(String missionFile) {
        // 从 "wayline/裕丰围地铁站测试10093.kmz" 提取任务名称
        if (missionFile != null && missionFile.contains("/")) {
            String fileName = missionFile.substring(missionFile.lastIndexOf("/") + 1);
            return fileName.substring(0, fileName.lastIndexOf("."));
        }
        return null;
    }

    private void updateTaskStatus(String taskName, String status) {
        try {
            // 根据任务名称查询任务
            FlightTask task =
                flightTaskService.lambdaQuery().eq(FlightTask::getTaskName, taskName).last("limit 1").one();

            if (task == null) {
                log.warn("未找到对应的任务: taskName={}", taskName);
                return;
            }

            // 检查当前任务状态是否允许更新
            Integer currentStatus = task.getTaskStatus();
            if (currentStatus == null) {
                log.warn("任务状态异常: taskName={}, currentStatus=null", taskName);
                return;
            }

            // 只允许待执行(0)和执行中(1)的任务更新状态
            if (!currentStatus.equals(TaskStatusEnum.PENDING.getCode()) &&
                !currentStatus.equals(TaskStatusEnum.EXECUTING.getCode())) {
                log.warn("当前任务状态不允许更新: taskName={}, currentStatus={}, targetStatus={}", taskName,
                    TaskStatusEnum.getInfo(currentStatus), TaskStatusEnum.getInfo(Integer.parseInt(status)));
                return;
            }

            // 更新任务状态
            FlightTask updateTask = getFlightTask(status, task);

            // 执行更新
            boolean success = flightTaskService.updateById(updateTask);

            if (success) {
                log.info("任务状态更新成功: taskName={}, oldStatus={}, newStatus={}", taskName,
                    TaskStatusEnum.getInfo(currentStatus), TaskStatusEnum.getInfo(Integer.parseInt(status)));
            } else {
                log.warn("任务状态更新失败: taskName={}, oldStatus={}, targetStatus={}", taskName,
                    TaskStatusEnum.getInfo(currentStatus), TaskStatusEnum.getInfo(Integer.parseInt(status)));
            }
        } catch (Exception e) {
            log.error("更新任务状态失败: taskName={}, status={}, error={}", taskName, status, e.getMessage(), e);
        }
    }

    private static FlightTask getFlightTask(String status, FlightTask task) {
        FlightTask updateTask = new FlightTask();
        updateTask.setId(task.getId());
        updateTask.setTaskStatus(Integer.parseInt(status));
        updateTask.setUpdateTime(DateUtils.getNowDate());
        updateTask.setUpdateBy(LoginHelper.getUserId()); // 系统自动更新

        // 如果是执行中状态，且计划执飞时间为空，才设置flightTime为当前时间
        if (STATUS_EXECUTING.equals(status) && task.getFlightTime() == null) {
            updateTask.setFlightTime(DateUtils.getNowDate());
        }

        // 如果是已完成状态，设置为已上传
        if (STATUS_FINISHED.equals(status)) {
            updateTask.setIsUploaded(1);
        }
        return updateTask;
    }

    /**
     * 检查任务是否在执行中
     *
     * @param droneId 无人机ID
     * @return 是否在执行任务
     */
    public boolean isTaskExecuting(String droneId) {
        return taskExecutionMap.getOrDefault(droneId, false);
    }

    /**
     * 根据任务名称获取任务ID
     *
     * @param taskName 任务名称
     * @return 任务ID，如果未找到则返回null
     */
    private String getTaskIdByTaskName(String taskName) {
        try {
            FlightTask task =
                flightTaskService.lambdaQuery().eq(FlightTask::getTaskName, taskName).last("limit 1").one();
            return task != null ? task.getId() : null;
        } catch (Exception e) {
            log.error("根据任务名称[{}]查询任务ID失败", taskName, e);
            return null;
        }
    }
}
