package com.md.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.md.domain.bo.OpenApiConfigBo;
import com.md.domain.po.OpenApiConfig;
import com.md.domain.vo.OpenApiConfigVo;
import com.md.mapper.OpenApiConfigMapper;
import com.md.service.IOpenApiConfigService;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 开放端接口管理Service实现
 */
@RequiredArgsConstructor
@Service
public class OpenApiConfigServiceImpl implements IOpenApiConfigService {

    private final OpenApiConfigMapper baseMapper;
    private final Converter converter;

    /**
     * 查询开放端接口管理列表
     *
     * @param bo 开放端接口管理查询对象
     * @return 开放端接口管理集合
     */
    @Override
    public List<OpenApiConfigVo> queryList(OpenApiConfigBo bo) {
        LambdaQueryWrapper<OpenApiConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询开放端接口管理分页列表
     *
     * @param bo 开放端接口管理查询对象
     * @param pageQuery 分页查询参数
     * @return 开放端接口管理分页列表
     */
    @Override
    public TableDataInfo<OpenApiConfigVo> queryPageList(OpenApiConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OpenApiConfig> lqw = buildQueryWrapper(bo);
        Page<OpenApiConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 根据ID查询开放端接口管理详情
     *
     * @param id 开放端接口管理ID
     * @return 开放端接口管理详情
     */
    @Override
    public OpenApiConfigVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增开放端接口管理
     *
     * @param bo 开放端接口管理业务对象
     * @return 结果
     */
    @Override
    public Boolean insertByBo(OpenApiConfigBo bo) {
        OpenApiConfig add = converter.convert(bo, OpenApiConfig.class);
        // 设置默认值
        if (StringUtils.isBlank(add.getStatus())) {
            add.setStatus("0"); // 默认为正常状态
            add.setDelFlag("0");
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改开放端接口管理
     *
     * @param bo 开放端接口管理业务对象
     * @return 结果
     */
    @Override
    public Boolean updateByBo(OpenApiConfigBo bo) {
        OpenApiConfig update = converter.convert(bo, OpenApiConfig.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 批量删除开放端接口管理
     *
     */
    @Override
    public Boolean deleteWithValidByIds(Long id) {
        try {
            OpenApiConfig openApiConfig = new OpenApiConfig();
            openApiConfig.setId(id);
            openApiConfig.setDelFlag("1");
            baseMapper.updateById(openApiConfig);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 构建查询条件
     *
     * @param bo 开放端接口管理查询对象
     * @return 查询条件
     */
    private LambdaQueryWrapper<OpenApiConfig> buildQueryWrapper(OpenApiConfigBo bo) {
        LambdaQueryWrapper<OpenApiConfig> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getOpenAddress()), OpenApiConfig::getOpenAddress, bo.getOpenAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OpenApiConfig::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getEncryptInfo()), OpenApiConfig::getEncryptInfo, bo.getEncryptInfo());
        lqw.eq(OpenApiConfig::getDelFlag, "0"); // 只查询未删除的记录
        lqw.orderByAsc(OpenApiConfig::getSort);
        return lqw;
    }

}
