package com.md.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.domain.bo.FlightLineBo;
import com.md.domain.bo.FlightPointActionBo;
import com.md.domain.bo.FlightPointBo;
import com.md.domain.dto.FlightLineDto;
import com.md.domain.po.FlightLine;
import com.md.domain.po.FlightPoint;
import com.md.domain.po.FlightPointAction;
import com.md.domain.po.PlatformInfo;
import com.md.domain.vo.FlightLineVo;
import com.md.enums.BaseInfoSyncEnum;
import com.md.flight.djauv.domain.ActionTriggerReq;
import com.md.flight.djauv.domain.PointActionReq;
import com.md.flight.djauv.domain.RoutePointReq;
import com.md.flight.djauv.domain.UavRouteReq;
import com.md.flight.djauv.domain.WaypointHeadingReq;
import com.md.flight.djauv.domain.WaypointTurnReq;
import com.md.flight.djauv.kml.ActionTriggerTypeEnums;
import com.md.flight.djauv.service.UavRouteService;
import com.md.mapper.FlightLineMapper;
import com.md.mapper.FlightPointMapper;
import com.md.service.IBaseInfoService;
import com.md.service.IFlightLineService;
import com.md.service.IFlightPointActionService;
import com.md.service.IPlatformInfoService;
import com.md.utils.MinioUtils;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.dromara.common.mybatis.core.utils.QueryUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 航线管理Service业务层处理
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FlightLineServiceImpl extends ServiceImpl<FlightLineMapper, FlightLine> implements IFlightLineService {

    private final UavRouteService uavRouteService;
    private final FlightLineMapper flightLineMapper;
    private final FlightPointMapper flightPointMapper;
    private final IFlightPointActionService flightPointActionService;
    private final MinioUtils minioUtils;
    private final IPlatformInfoService platformInfoService;

    /**
     * 查询航线管理
     *
     * @param id 航线管理主键
     * @return 航线管理
     */
    @Override
    public FlightLineVo selectFlightLineById(String id) {
        // 使用 BaseMapperPlus 的 selectVoById 方法
        FlightLineVo flightLineVO = flightLineMapper.selectVoById(id);

        if (flightLineVO != null) {
            // 查询航点信息及其动作
            List<FlightPoint> points = flightPointMapper.selectPointsByLineId(id);
            if (!points.isEmpty()) {
                // 获取所有航点ID
                List<String> pointIds = points.stream().map(FlightPoint::getId).collect(Collectors.toList());

                // 查询所有航点动作
                List<FlightPointAction> actions =
                    flightPointActionService.lambdaQuery().in(FlightPointAction::getPointId, pointIds).list();

                // 将动作分配给对应的航点
                Map<String, List<FlightPointAction>> actionMap =
                    actions.stream().collect(Collectors.groupingBy(FlightPointAction::getPointId));

                // 设置每个航点的动作列表
                points.forEach(point -> point.setActions(actionMap.getOrDefault(point.getId(), new ArrayList<>())));
            }
            flightLineVO.setPoints(points);
        }

        return flightLineVO;
    }

    /**
     * 查询航线管理列表（不分页）
     *
     * @param flightLineBo 航线管理查询条件
     * @return 航线管理集合
     */
    @Override
    public List<FlightLineVo> selectFlightLineList(FlightLineBo flightLineBo) {
        // 转换为 FlightLineDto 进行查询
        FlightLineDto flightLineDto = new FlightLineDto();
        BeanUtils.copyProperties(flightLineBo, flightLineDto);

        // 使用现有的查询方法
        List<FlightLineDto> dtoList = flightLineMapper.selectFlightLineList(flightLineDto);

        // 将结果转换为 FlightLineVO 列表
        List<FlightLineVo> voList = new ArrayList<>();
        for (FlightLineDto dto : dtoList) {
            FlightLineVo vo = new FlightLineVo();
            BeanUtils.copyProperties(dto, vo);

            // 设置航点列表
            vo.setPoints(dto.getPoints());
            voList.add(vo);
        }

        return voList;
    }

    /**
     * 查询航线管理列表（分页）
     *
     * @param flightLineBo 航线管理查询条件
     * @param pageQuery    分页参数
     * @return 分页航线管理集合
     */
    @Override
    public TableDataInfo<FlightLineVo> selectFlightLineList(FlightLineBo flightLineBo, PageQuery pageQuery) {
        // 转换为FlightLine实体对象
        FlightLine flightLine = MapstructUtils.convert(flightLineBo, FlightLine.class);

        // 构建查询条件
        assert flightLine != null;
        LambdaQueryWrapper<FlightLine> lqw = buildQueryWrapper(flightLine, flightLineBo);

        // 执行分页查询
        Page<FlightLineVo> page = flightLineMapper.selectVoPage(pageQuery.build(), lqw);

        // 获取分页后的结果
        List<FlightLineVo> records = page.getRecords();

        // 为每个航线设置航点和动作信息
        if (!records.isEmpty()) {
            for (FlightLineVo vo : records) {
                // 设置平台名称
                setFlightLinePlatformName(vo);

                List<FlightPoint> points = flightPointMapper.selectPointsByLineId(vo.getId());
                if (!points.isEmpty()) {
                    // 获取所有航点ID
                    List<String> pointIds = points.stream().map(FlightPoint::getId).collect(Collectors.toList());

                    // 查询所有航点动作
                    List<FlightPointAction> actions =
                        flightPointActionService.lambdaQuery().in(FlightPointAction::getPointId, pointIds).list();

                    // 将动作分配给对应的航点
                    Map<String, List<FlightPointAction>> actionMap =
                        actions.stream().collect(Collectors.groupingBy(FlightPointAction::getPointId));

                    // 设置每个航点的动作列表
                    points.forEach(point -> point.setActions(actionMap.getOrDefault(point.getId(), new ArrayList<>())));
                }
                vo.setPoints(points);
            }
        }

        return TableDataInfo.build(page);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<FlightLine> buildQueryWrapper(FlightLine flightLine, FlightLineBo flightLineBo) {
        LambdaQueryWrapper<FlightLine> lqw = Wrappers.lambdaQuery();

        lqw.like(StringUtils.isNotBlank(flightLine.getLineName()), FlightLine::getLineName, flightLine.getLineName());
        lqw.eq(StringUtils.isNotBlank(flightLine.getFlightControlNo()), FlightLine::getFlightControlNo,
            flightLine.getFlightControlNo());
        lqw.eq(StringUtils.isNotBlank(flightLine.getDataSource()), FlightLine::getDataSource,
            flightLine.getDataSource());
        lqw.eq(flightLine.getCreateBy() != null, FlightLine::getCreateBy, flightLine.getCreateBy());
        lqw.eq(flightLine.getUpdateBy() != null, FlightLine::getUpdateBy, flightLine.getUpdateBy());
        lqw.eq(flightLine.getTenantId() != null, FlightLine::getTenantId, flightLine.getTenantId());

        // 根据创建者名称查询
        String createByName = flightLineBo.getCreateByName();
        QueryUtils.buildCreateByQuery(lqw, FlightLine::getCreateBy, createByName);

        // 根据修改者名称查询
        String updateByName = flightLineBo.getUpdateByName();
        QueryUtils.buildUpdateByQuery(lqw, FlightLine::getUpdateBy, updateByName);

        // 创建时间范围查询
        if (flightLineBo.getStartTime() != null && flightLineBo.getEndTime() != null) {
            lqw.between(FlightLine::getCreateTime, flightLineBo.getStartTime(), flightLineBo.getEndTime());
        }

        // 修改时间范围查询
        if (flightLineBo.getStartUpdateTime() != null && flightLineBo.getEndUpdateTime() != null) {
            lqw.between(FlightLine::getUpdateTime, flightLineBo.getStartUpdateTime(), flightLineBo.getEndUpdateTime());
        }
        lqw.orderByDesc(FlightLine::getId);
        return lqw;
    }

    /**
     * 查询航线管理列表排除备降点
     */
    @Override
    public List<FlightLineVo> selectFlightLineListExclude(FlightLineBo flightLineBo) {
        // 转换为FlightLine实体对象
        FlightLine flightLine = MapstructUtils.convert(flightLineBo, FlightLine.class);

        // 构建查询条件
        LambdaQueryWrapper<FlightLine> lqw = buildQueryWrapper(flightLine, flightLineBo);

        // 查询不含备降点的航线
        List<FlightLineVo> flightLineVOList = flightLineMapper.selectVoList(lqw);

        for (FlightLineVo flightLineVO : flightLineVOList) {
            // 查询航线对应的非备降点航点
            List<FlightPoint> points = flightPointMapper.selectList(
                Wrappers.<FlightPoint>lambdaQuery().eq(FlightPoint::getLineId, flightLineVO.getId())
                    .eq(FlightPoint::getIsEmergencyLandingPoint, 0).orderByAsc(FlightPoint::getPointIndex));
            flightLineVO.setPoints(points);
        }

        return flightLineVOList;
    }

    /**
     * 新增航线管理
     *
     * @param flightLineBo 航线管理
     * @return 航线ID
     */
    @Transactional
    @Override
    public String insertFlightLine(FlightLineBo flightLineBo) {
        // 验证航线名称的唯一性
        validateFlightLineName(flightLineBo.getLineName(), flightLineBo.getFlightControlNo(), null);

        // 将Bo转换为实体
        FlightLine flightLine = MapstructUtils.convert(flightLineBo, FlightLine.class);

        // 设置默认值
        flightLine.setDataSource("HANDLE");
        flightLine.setId(IdWorker.getIdStr());
        flightLine.setFileGenerateStatus(0); // 未生成
        flightLine.setIsUploaded(0); // 未上传

        // 保存航线基本信息
        if (save(flightLine)) {
            // 获取航点列表
            List<FlightPointBo> pointBos = flightLineBo.getPoints();
            if (CollectionUtils.isNotEmpty(pointBos)) {
                // 转换并保存航点信息
                List<FlightPoint> points = MapstructUtils.convert(pointBos, FlightPoint.class);
                for (int i = 0; i < points.size(); i++) {
                    FlightPoint point = points.get(i);
                    point.setId(IdWorker.getIdStr());
                    point.setLineId(flightLine.getId());
                    point.setPointIndex((long)i);
                    point.setCreateTime(DateUtils.getNowDate());
                    point.setUpdateTime(DateUtils.getNowDate());
                    point.setCreateBy(LoginHelper.getUserId());
                    point.setUpdateBy(LoginHelper.getUserId());

                    flightPointMapper.insert(point);

                    // 处理航点动作
                    List<FlightPointActionBo> actionBos = pointBos.get(i).getActions();
                    if (CollectionUtils.isNotEmpty(actionBos)) {
                        List<FlightPointAction> actions = MapstructUtils.convert(actionBos, FlightPointAction.class);
                        for (FlightPointAction action : actions) {
                            action.setPointId(point.getId());
                            action.setCreateTime(DateUtils.getNowDate());
                            action.setUpdateTime(DateUtils.getNowDate());
                        }
                        flightPointActionService.saveBatch(actions);
                    }
                }
            }

            return flightLine.getId();
        } else {
            throw new ServiceException("新增航线失败");
        }
    }

    /**
     * 修改航线管理
     *
     * @param flightLineBo 航线管理
     * @return 航线ID
     */
    @Transactional
    @Override
    public String updateFlightLine(FlightLineBo flightLineBo) {
        // 验证航线名称的唯一性
        validateFlightLineName(flightLineBo.getLineName(), flightLineBo.getFlightControlNo(), flightLineBo.getId());

        // 将Bo转换为实体
        FlightLine flightLine = MapstructUtils.convert(flightLineBo, FlightLine.class);

        // 更新航线基本信息
        if (updateById(flightLine)) {
            String lineId = flightLine.getId();

            // 先删除原有的航点和动作
            List<FlightPoint> existingPoints = flightPointMapper.selectPointsByLineId(lineId);
            if (!existingPoints.isEmpty()) {
                // 收集所有航点ID
                List<String> pointIds = existingPoints.stream().map(FlightPoint::getId).collect(Collectors.toList());

                // 删除航点相关的动作
                flightPointActionService.lambdaUpdate().in(FlightPointAction::getPointId, pointIds).remove();

                // 删除航点
                flightPointMapper.delete(Wrappers.<FlightPoint>lambdaQuery().eq(FlightPoint::getLineId, lineId));
            }

            // 添加新的航点和动作
            List<FlightPointBo> pointBos = flightLineBo.getPoints();
            if (CollectionUtils.isNotEmpty(pointBos)) {
                // 转换并保存航点信息
                List<FlightPoint> points = MapstructUtils.convert(pointBos, FlightPoint.class);
                for (int i = 0; i < points.size(); i++) {
                    FlightPoint point = points.get(i);
                    point.setId(IdWorker.getIdStr());
                    point.setLineId(lineId);
                    point.setPointIndex((long)i);
                    point.setCreateTime(DateUtils.getNowDate());
                    point.setUpdateTime(DateUtils.getNowDate());
                    point.setCreateBy(LoginHelper.getUserId());
                    point.setUpdateBy(LoginHelper.getUserId());

                    flightPointMapper.insert(point);

                    // 处理航点动作
                    List<FlightPointActionBo> actionBos = pointBos.get(i).getActions();
                    if (CollectionUtils.isNotEmpty(actionBos)) {
                        List<FlightPointAction> actions = MapstructUtils.convert(actionBos, FlightPointAction.class);
                        for (FlightPointAction action : actions) {
                            action.setPointId(point.getId());
                            action.setCreateTime(DateUtils.getNowDate());
                            action.setUpdateTime(DateUtils.getNowDate());
                        }
                        flightPointActionService.saveBatch(actions);
                    }
                }
            }

            return lineId;
        } else {
            throw new ServiceException("修改航线失败");
        }
    }

    /**
     * 批量删除航线及其航点数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteFlightLineByIds(String[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return 0;
        }

        // 1. 查询所有相关航点ID
        List<String> pointIds = flightPointMapper.selectPointIdsByLineIds(Arrays.asList(ids));

        // 2. 删除航点动作
        if (!pointIds.isEmpty()) {
            flightPointActionService.lambdaUpdate().in(FlightPointAction::getPointId, pointIds).remove();
        }

        // 3. 删除航线下的所有航点
        flightPointMapper.deletePointsByLineIds(Arrays.asList(ids));

        // 4. 删除航线
        return flightLineMapper.deleteFlightLineByIds(ids);
    }

    /**
     * 删除航线及其航点数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteFlightLineById(String id) {
        // 1. 查询所有相关航点ID
        List<String> pointIds = flightPointMapper.selectPointIdsByLineIds(Collections.singletonList(id));

        // 2. 删除航点动作
        if (!pointIds.isEmpty()) {
            flightPointActionService.lambdaUpdate().in(FlightPointAction::getPointId, pointIds).remove();
        }

        // 3. 删除航线下的所有航点
        flightPointMapper.deletePointsByLineIds(Collections.singletonList(id));

        // 4. 删除航线
        return flightLineMapper.deleteFlightLineById(id);
    }

    @Override
    public boolean syncFlightLine(String code) {
        IBaseInfoService syncType = BaseInfoSyncEnum.getSyncType(code);
        syncType.syncFlightLine();
        return true;
    }

    public List<RoutePointReq> buildRoutePoints(List<FlightPoint> points, UavRouteReq uavRouteReq) {
        List<RoutePointReq> routePoints = new ArrayList<>();
        if (!points.isEmpty()) {
            for (int i = 0, routeIndex = 0; i < points.size(); i++) {
                FlightPoint point = points.get(i);
                // 跳过备降点
                if (point.getIsEmergencyLandingPoint() != null && point.getIsEmergencyLandingPoint() == 1) {
                    continue;
                }
                RoutePointReq routePoint = new RoutePointReq();

                routePoint.setRoutePointIndex(routeIndex++);

                // 直接使用原始坐标
                routePoint.setLongitude(point.getLongitude().doubleValue());
                routePoint.setLatitude(point.getLatitude().doubleValue());

                // 设置航点高度，如果航点高度为空则使用全局高度
                BigDecimal altitude = point.getAltitude();
                if (altitude != null && altitude.doubleValue() > 0) {
                    routePoint.setHeight(altitude.doubleValue());
                } else {
                    routePoint.setHeight(uavRouteReq.getGlobalHeight());
                }

                // 设置航点速度，如果航点速度为空或为0则使用全局速度
                Integer speed = point.getSpeed();
                if (speed != null && speed > 0) {
                    routePoint.setSpeed(speed.doubleValue());
                } else {
                    routePoint.setSpeed(uavRouteReq.getAutoFlightSpeed());
                }

                // 设置航点动作
                if (CollectionUtils.isNotEmpty(point.getActions())) {
                    List<PointActionReq> actions = point.getActions().stream().map(action -> {
                        PointActionReq actionReq = new PointActionReq();
                        // 设置基本属性...
                        actionReq.setActionIndex(action.getActionIndex());
                        actionReq.setHoverTime(action.getHoverTime());
                        actionReq.setAircraftHeading(action.getAircraftHeading());
                        actionReq.setUseGlobalImageFormat(action.getUseGlobalImageFormat());
                        actionReq.setImageFormat(action.getImageFormat());
                        actionReq.setGimbalYawRotateAngle(action.getGimbalYawRotateAngle());
                        actionReq.setGimbalPitchRotateAngle(action.getGimbalPitchRotateAngle());
                        actionReq.setZoom(action.getZoom());
                        actionReq.setRecordStatus(action.getRecordStatus());
                        return actionReq;
                    }).collect(Collectors.toList());
                    routePoint.setActions(actions);

                    // 创建默认的触发器（到达航点触发）
                    ActionTriggerReq triggerReq = new ActionTriggerReq();
                    triggerReq.setActionTriggerType(ActionTriggerTypeEnums.REACH_POINT.getValue());

                    // 检查航点的动作是否包含 multipleTiming 或 multipleDistance
                    List<FlightPointAction> pointActions = point.getActions();
                    if (CollectionUtils.isNotEmpty(pointActions)) {
                        Optional<FlightPointAction> actionWithTrigger = pointActions.stream().filter(
                            action -> ObjectUtil.isNotNull(action.getMultipleTiming()) ||
                                ObjectUtil.isNotNull(action.getMultipleDistance())).findFirst();

                        // 如果存在特殊触发条件，则覆盖默认触发器
                        actionWithTrigger.ifPresent(action -> {
                            if (ObjectUtil.isNotNull(action.getMultipleTiming()) && action.getMultipleTiming() != 0) {
                                triggerReq.setActionTriggerType(ActionTriggerTypeEnums.MULTIPLE_TIMING.getValue());
                                triggerReq.setActionTriggerParam(String.valueOf(action.getMultipleTiming()));
                            } else if (ObjectUtil.isNotNull(action.getMultipleDistance()) &&
                                action.getMultipleDistance() != 0) {
                                triggerReq.setActionTriggerType(ActionTriggerTypeEnums.MULTIPLE_DISTANCE.getValue());
                                triggerReq.setActionTriggerParam(String.valueOf(action.getMultipleDistance()));
                            }
                        });
                    }

                    // 设置触发器
                    routePoint.setActionTrigger(triggerReq);
                }
                routePoints.add(routePoint);
            }
        }
        return routePoints;
    }

    public void setDefaultValues(UavRouteReq uavRouteReq) {
        // todo 后续可以通过设备编码到上云api表manage_device_payload、manage_device获取数据
        uavRouteReq.setDroneType(77);
        uavRouteReq.setSubDroneType(0);
        uavRouteReq.setPayloadType(66);
        uavRouteReq.setPayloadPosition(0);
        uavRouteReq.setImageFormat("ir,zoom");
        uavRouteReq.setExitOnRcLostAction("goBack");
        uavRouteReq.setWaypointHeadingReq(new WaypointHeadingReq().setWaypointHeadingMode("followWayline"));
        uavRouteReq.setWaypointTurnReq(
            new WaypointTurnReq().setWaypointTurnMode("toPointAndStopWithDiscontinuityCurvature"));
        uavRouteReq.setGimbalPitchMode("usePointSetting");
    }

    /**
     * 校验航线名称唯一性
     *
     * @param lineName        航线名称
     * @param flightControlNo 飞控平台ID
     * @param lineId          航线ID（更新时使用，新增时为null）
     */
    private void validateFlightLineName(String lineName, String flightControlNo, String lineId) {
        if (StringUtils.isEmpty(lineName)) {
            throw new ServiceException("航线名称不能为空");
        }

        // 校验航线名称不能包含特殊字符和空格
        String regex = "^[a-zA-Z0-9\\u4e00-\\u9fa5\\-_]+$";
        if (!lineName.matches(regex)) {
            throw new ServiceException("航线名称只能包含字母、数字、中文、连字符和下划线，不允许包含空格和特殊字符");
        }

        LambdaQueryWrapper<FlightLine> queryWrapper =
            Wrappers.<FlightLine>lambdaQuery().eq(FlightLine::getLineName, lineName)
                .eq(FlightLine::getFlightControlNo, flightControlNo);

        // 如果是更新操作，排除自身
        if (StringUtils.isNotEmpty(lineId)) {
            queryWrapper.ne(FlightLine::getId, lineId);
        }

        if (flightLineMapper.selectCount(queryWrapper) > 0) {
            throw new ServiceException("航线名称'" + lineName + "'已存在");
        }
    }

    @Override
    public String exportKmz(String lineName) {
        try {
            // 1. 根据航线名称查询航线
            FlightLineBo query = new FlightLineBo();
            query.setLineName(lineName);
            List<FlightLineVo> lines = selectFlightLineList(query);

            if (CollectionUtils.isEmpty(lines)) {
                throw new ServiceException("航线不存在");
            }

            FlightLineVo line = lines.get(0);

            // 2. 检查文件生成状态
            if (line.getFileGenerateStatus() == null || line.getFileGenerateStatus() != 2) {
                throw new ServiceException("航线文件未生成，请先生成航线文件");
            }

            // 3. 检查数据库中的URL是否存在且未过期
            String storedUrl = line.getKmzFilePath();
            if (StringUtils.isNotEmpty(storedUrl) && !minioUtils.isUrlExpired(storedUrl)) {
                return storedUrl;
            }

            // 4. URL不存在或已过期，从MinIO重新获取
            try {
                String objectName = "flightline/" + line.getKmzFileName();
                String newFileUrl = minioUtils.getFileUrl(minioUtils.getDefaultBucketName(), objectName);

                // 更新数据库中的URL
                FlightLine updateLine = new FlightLine();
                updateLine.setId(line.getId());
                updateLine.setKmzFilePath(newFileUrl);
                updateLine.setUpdateTime(DateUtils.getNowDate());
                updateLine.setUpdateBy(LoginHelper.getUserId());
                this.updateById(updateLine);

                log.info("获取航线[{}]新的下载地址成功：{}", lineName, newFileUrl);
                return newFileUrl;

            } catch (Exception e) {
                log.error("从MinIO获取新的文件URL失败", e);
                if (StringUtils.isNotEmpty(storedUrl)) {
                    // 如果获取新URL失败，但存在旧URL，则返回旧URL
                    log.warn("使用数据库存储的URL：{}", storedUrl);
                    return storedUrl;
                }
                throw new ServiceException("获取文件访问地址失败");
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取航线[{}]下载地址失败", lineName, e);
            throw new ServiceException("获取下载地址失败：" + e.getMessage());
        }
    }

    @Override
    public boolean updateLineFences(String lineId, String fenceIds) {
        log.info("更新航线关联的围栏: lineId={}, fenceIds={}", lineId, fenceIds);

        try {
            if (StringUtils.isEmpty(lineId)) {
                log.warn("航线ID为空，无法更新围栏关联");
                return false;
            }

            // 检查航线是否存在
            FlightLine line = this.getById(lineId);
            if (line == null) {
                log.warn("航线不存在: lineId={}", lineId);
                return false;
            }

            // 更新航线表中的fence_id字段
            FlightLine updateLine = new FlightLine();
            updateLine.setId(lineId);
            updateLine.setFenceId(fenceIds);
            updateLine.setUpdateTime(DateUtils.getNowDate());
            updateLine.setUpdateBy(LoginHelper.getUserId());
            boolean updateResult = this.updateById(updateLine);

            if (updateResult) {
                log.info("成功更新航线关联的围栏: lineId={}, fenceIds={}", lineId, fenceIds);
                return true;
            } else {
                log.warn("更新航线关联的围栏失败: lineId={}", lineId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新航线关联的围栏异常: lineId={}, error={}", lineId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getLineFences(String lineId) {
        FlightLine line = this.getById(lineId);
        if (line == null) {
            log.warn("航线不存在: lineId={}", lineId);
            return "";
        }
        return line.getFenceId() != null ? line.getFenceId() : "";
    }

    /**
     * 根据平台ID设置平台名称
     *
     * @param flightLine 航线信息
     */
    private void setFlightLinePlatformName(FlightLineVo flightLine) {
        String platformId = flightLine.getFlightControlNo();
        if (platformId == null) {
            return;
        }

        PlatformInfo platformInfo = platformInfoService.getOne(
            Wrappers.<PlatformInfo>lambdaQuery().eq(PlatformInfo::getFlightControlNo, platformId));

        if (platformInfo != null) {
            flightLine.setManufacturerName(platformInfo.getManufacturerName());
        }
    }
}
