package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.VfrHud;
import lombok.extern.slf4j.Slf4j;

/**
 * 飞行数据处理器
 * 处理MAVLink VFR_HUD消息，提取空速、地速等信息
 */
@Slf4j
public class VfrHudHandler extends AbstractMavlinkHandler<VfrHud> {

    public VfrHudHandler(MavlinkCoreService coreService) {
        super(coreService);
    }

    @Override
    protected void doHandle(String droneId, VfrHud message) {
        // 保存最新的飞行数据
        coreService.setLastVfrHud(droneId, message);

        log.info("无人机[{}]飞行数据: 空速={}m/s, 地速={}m/s, 爬升速率={}m/s, 航向={}度, 油门={}%, 高度={}m",
            droneId,
            message.airspeed(),
            message.groundspeed(),
            message.climb(),
            message.heading(),
            message.throttle(),
            message.alt());
    }

    @Override
    public Class<VfrHud> getMessageType() {
        return VfrHud.class;
    }
}