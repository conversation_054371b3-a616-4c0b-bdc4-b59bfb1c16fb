package com.md.flight.lh.processor;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.md.flight.engine.impl.LhMqttExecutionEngine;
import com.md.flight.lh.constant.LhTopicConstantReq;
import com.md.flight.lh.domain.dto.LhBaseReq;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseRouteUpload;
import com.md.flight.lh.enums.LhStatusEnum;
import com.md.flight.lh.service.impl.LhDroneControlServiceImpl;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 联合航线执行消息处理
 */
@Component
@Slf4j
public class LhRouteExecuteProcessor implements LhBaseProcessor{
    @Autowired
    private LhDroneControlServiceImpl lhDroneControlService;

    @Override
    public void processMessage(String payload) {
        try {
            log.info("收到联合航线执行消息处理消息: {}", payload);

            // 消息转换
            LhResponse<LhResponseRouteUpload> lhResponse = JSON.parseObject(
                payload,
                new TypeReference<>() {});

            Integer status = lhResponse.getData().getOutput().getStatus();
            if (status == 1) {
                log.info("航线开始执行，释放阻塞锁");
                // 通知等待的线程继续执行
                lhDroneControlService.notifyResponse(lhResponse.getBid());
            } else if (status == 0) {
                log.info("航线执行完成");
            }
        } catch (Exception e) {
            log.error("收到联合航线执行消息处理失败: {}", e.getMessage(), e);
        }
    }
}
