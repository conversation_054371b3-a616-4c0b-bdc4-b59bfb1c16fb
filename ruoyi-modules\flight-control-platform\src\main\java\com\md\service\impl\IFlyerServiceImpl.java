package com.md.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.domain.bo.FlyerBo;
import com.md.domain.dto.FlyerDto;
import com.md.domain.po.Flyer;
import com.md.domain.po.PlatformInfo;
import com.md.domain.vo.FlyerVo;
import com.md.enums.BaseInfoSyncEnum;
import com.md.enums.DataSourceEnum;
import com.md.mapper.FlyerMapper;
import com.md.service.IBaseInfoService;
import com.md.service.IFlyerService;
import com.md.service.IPlatformInfoService;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class IFlyerServiceImpl extends ServiceImpl<FlyerMapper, Flyer> implements IFlyerService {

    private final IPlatformInfoService platformInfoService;
    private final Converter converter;

    /**
     * 查询飞手列表
     *
     * @return
     */
    @Override
    public List<FlyerVo> selectFlyerList(FlyerDto flyerDto) {
        List<Flyer> flyers = lambdaQuery().like(ObjectUtil.isNotEmpty(flyerDto.getPlateFormId()), Flyer::getPlateformId,
                flyerDto.getPlateFormId())
            .like(ObjectUtil.isNotEmpty(flyerDto.getFlyerPhone()), Flyer::getFlyerPhone, flyerDto.getFlyerPhone())
            .eq(ObjectUtil.isNotEmpty(flyerDto.getFlyerSex()), Flyer::getFlyerSex, flyerDto.getFlyerSex())
            .like(ObjectUtil.isNotEmpty(flyerDto.getPlateFormName()), Flyer::getPlateformName,
                flyerDto.getPlateFormName()).orderByDesc(Flyer::getCreateTime).list();
        // 转换为FlyerVo对象
        return converter.convert(flyers, FlyerVo.class);
    }

    /**
     * 分页查询飞手列表
     *
     * @param flyerDto  查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<FlyerVo> queryPageList(FlyerDto flyerDto, PageQuery pageQuery) {
        // 构建查询条件
        LambdaQueryWrapper<Flyer> lqw = Wrappers.lambdaQuery();
        lqw.like(ObjectUtil.isNotEmpty(flyerDto.getPlateFormId()), Flyer::getPlateformId, flyerDto.getPlateFormId())
            .like(ObjectUtil.isNotEmpty(flyerDto.getFlyerPhone()), Flyer::getFlyerPhone, flyerDto.getFlyerPhone())
            .eq(ObjectUtil.isNotEmpty(flyerDto.getFlyerSex()), Flyer::getFlyerSex, flyerDto.getFlyerSex())
            .like(ObjectUtil.isNotEmpty(flyerDto.getFlyerName()), Flyer::getFlyerName, flyerDto.getFlyerName())
            .like(ObjectUtil.isNotEmpty(flyerDto.getPlateFormName()), Flyer::getPlateformName,
                flyerDto.getPlateFormName()).orderByDesc(Flyer::getCreateTime);

        // 执行分页查询
        Page<Flyer> page = baseMapper.selectPage(pageQuery.build(), lqw);

        // 转换记录为FlyerVo列表
        List<FlyerVo> flyerVoList = converter.convert(page.getRecords(), FlyerVo.class);

        // 使用TableDataInfo的构造函数创建分页结果
        return new TableDataInfo<>(flyerVoList, page.getTotal());
    }

    /**
     * 同步飞手信息
     *
     * @param plateformId
     * @return
     */
    @Override
    public boolean syncFlyer(String plateformId) {
        IBaseInfoService syncType = BaseInfoSyncEnum.getSyncType(plateformId);
        if (syncType != null) {
            syncType.syncFlyer();
        }
        return true;
    }

    /**
     * 新增飞手信息
     *
     * @param flyerBo 飞手业务对象
     * @return 操作结果
     */
    @Override
    public boolean insertFlyer(FlyerBo flyerBo) {
        // 将FlyerBo转换为Flyer
        Flyer flyer = converter.convert(flyerBo, Flyer.class);
        return addFlyer(flyer);
    }

    /**
     * 修改飞手信息
     *
     * @param flyerBo 飞手业务对象
     * @return 操作结果
     */
    @Override
    public boolean updateFlyer(FlyerBo flyerBo) {
        // 将FlyerBo转换为Flyer
        Flyer flyer = converter.convert(flyerBo, Flyer.class);
        return updateFlyer(flyer);
    }

    /**
     * 更新飞手信息
     *
     * @param flyer 飞手信息
     * @return 更新结果
     */
    private boolean updateFlyer(Flyer flyer) {
        // 处理从同步接口获取的数据（只能修改remark）
        String dataSourceValue = this.getById(flyer.getId().toString()).getDataSourceValue();
        if (DataSourceEnum.SYNC.getValue().equals(dataSourceValue)) {
            return updateSyncFlyerRemark(flyer);
        }
        flyer.setUpdateBy(LoginHelper.getUserId());
        flyer.setUpdateTime(DateUtils.getNowDate());

        // 如果是手动添加的飞手信息才可以更新
        setFlyerPlatformName(flyer);

        // 执行更新操作
        return updateById(flyer);
    }

    /**
     * 只更新同步飞手的备注
     *
     * @param flyer 飞手信息
     * @return 更新结果
     */
    private boolean updateSyncFlyerRemark(Flyer flyer) {
        Flyer existingFlyer = baseMapper.selectById(flyer.getId());
        if (existingFlyer != null) {
            // 只更新remark属性，其他属性保持不变
            existingFlyer.setRemark(flyer.getRemark());
            return updateById(existingFlyer);
        }
        return false;
    }

    /**
     * 添加新飞手信息
     *
     * @param flyer 飞手信息
     * @return 添加结果
     */
    private boolean addFlyer(Flyer flyer) {
        // 设置平台名称
        setFlyerPlatformName(flyer);

        // 设置数据源和创建时间
        flyer.setDataSource(DataSourceEnum.HANDLE.getName());
        flyer.setDataSourceValue(DataSourceEnum.HANDLE.getValue());
        flyer.setCreateTime(DateUtils.getNowDate());
        flyer.setCreateBy(LoginHelper.getUserId());

        // 执行保存操作
        return save(flyer);
    }

    /**
     * 根据平台ID设置平台名称
     *
     * @param flyer 飞手信息
     */
    private void setFlyerPlatformName(Flyer flyer) {
        String platformId = flyer.getPlateformId();
        if (platformId == null) {
            return;
        }

        PlatformInfo platformInfo = platformInfoService.getOne(
            Wrappers.<PlatformInfo>lambdaQuery().eq(PlatformInfo::getFlightControlNo, platformId));

        if (platformInfo != null) {
            flyer.setPlateformName(platformInfo.getManufacturerName());
        }
    }

    /**
     * 根据ID获取飞手视图对象
     *
     * @param id 飞手ID
     * @return 飞手视图对象
     */
    @Override
    public FlyerVo getById(String id) {
        Flyer flyer = baseMapper.selectById(id);
        if (flyer != null) {
            return converter.convert(flyer, FlyerVo.class);
        }
        return null;
    }

    /**
     * 同步飞手详情
     * @param id
     * @return
     */
    //    @Override
    //    public boolean  syncFlyerDetail(Long id) {
    //        Flyer flyer = flyerMapper.selectById(id);
    //        IBaseInfoService syncType = BaseInfoSyncEnum.getSyncType(flyer.getPlateformId());
    //        if (syncType != null) {
    //            syncType.syncFlyerDetail(id);
    //        }
    //        return true;
    //    }

}
