package com.md.flight.px4.service.impl;

import com.alibaba.fastjson2.JSON;
import com.md.command.constant.CommandType;
import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.domain.po.FlightTaskPointAction;
import com.md.flight.px4.constant.Px4Constants;
import com.md.flight.px4.service.Px4TaskStatusService;
import com.md.flight.px4.service.Px4DroneControlService;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * PX4无人机控制服务实现类
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class Px4DroneControlServiceImpl implements Px4DroneControlService {

    // 使用Px4Constants中的常量
    private final String TOPIC_PREFIX = Px4Constants.TOPIC_PREFIX;
    private final String COMMAND_TOPIC_SUFFIX = Px4Constants.TopicSuffix.COMMAND;
    private final String VIRTUAL_STICK_COMMAND_TOPIC_SUFFIX = Px4Constants.TopicSuffix.VIRTUAL_STICK_COMMAND;
    private final String MISSION_COMMAND_TOPIC_SUFFIX = Px4Constants.TopicSuffix.MISSION_COMMAND;
    // 跟踪已开启虚拟摇杆的无人机
    private final String VIRTUAL_STICK_ENABLED_DRONES = "Px4_VirtualStickEnabledDrones";
    private final int MQTT_QOS = 1;

    private final MqttMessageHandler messageHandler;// MQTT消息处理器
    private final Px4TaskStatusService taskStatusService;// 任务状态服务

    /**
     * 发送MQTT命令
     *
     * @param droneId     无人机ID
     * @param commandType MQTT命令类型
     * @param payload     命令参数
     * @param topicSuffix 主题后缀
     * @return 命令ID
     */
    @Override
    public String sendCommand(String droneId, String commandType, Map<String, Object> payload, String topicSuffix) {
        try {
            // 生成命令ID
            String commandId = UUID.randomUUID().toString();

            // 构建主题
            String topic = TOPIC_PREFIX + droneId + topicSuffix;

            // 构建消息负载
            Map<String, Object> mqttMessage = new HashMap<>();
            mqttMessage.put("commandId", commandId);
            mqttMessage.put("timestamp", System.currentTimeMillis());
            mqttMessage.put("type", commandType);
            mqttMessage.put("droneId", droneId);
            mqttMessage.put("payload", payload);

            String messagePayload = JSON.toJSONString(mqttMessage);

            // 构建MQTT消息
            Message<String> message = MessageBuilder.withPayload(messagePayload).setHeader(MqttHeaders.TOPIC, topic)
                .setHeader(MqttHeaders.QOS, MQTT_QOS)  // 使用QoS 1确保消息至少送达一次
                .setHeader("commandId", commandId).build();

            log.info("正在发送PX4 MQTT指令: 主题={}, 消息内容={}", topic, messagePayload);
            messageHandler.sendToMqtt(message);

            return commandId;
        } catch (Exception e) {
            log.error("PX4 MQTT指令发送失败: {}", e.getMessage(), e);
            throw new RuntimeException("指令发送失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行解锁命令
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    @Override
    public boolean arm(String droneId) {
        try {
            log.info("执行PX4解锁命令: droneId={}", droneId);

            // ARM命令不需要额外参数
            Map<String, Object> params = new HashMap<>();

            // 发送MQTT命令
            sendCommand(droneId, CommandType.ARM.getCode(), params, COMMAND_TOPIC_SUFFIX);
            return true;
        } catch (Exception e) {
            log.error("PX4解锁命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行上锁命令
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    @Override
    public boolean disarm(String droneId) {
        try {
            log.info("执行PX4上锁命令: droneId={}", droneId);

            // DISARM命令不需要额外参数
            Map<String, Object> params = new HashMap<>();

            // 发送MQTT命令
            sendCommand(droneId, CommandType.DISARM.getCode(), params, COMMAND_TOPIC_SUFFIX);
            return true;
        } catch (Exception e) {
            log.error("PX4上锁命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行起飞命令
     *
     * @param droneId  无人机ID
     * @param altitude 起飞高度（米）
     * @return 命令执行结果
     */
    @Override
    public boolean takeoff(String droneId, float altitude) {
        try {
            log.info("执行PX4起飞命令: droneId={}, altitude={}", droneId, altitude);

            // 构建起飞参数
            Map<String, Object> takeoffParams = new HashMap<>();
            takeoffParams.put("altitude", altitude);

            // 发送MQTT命令
            sendCommand(droneId, CommandType.TAKEOFF.getCode(), takeoffParams, COMMAND_TOPIC_SUFFIX);
            return true;
        } catch (Exception e) {
            log.error("PX4起飞命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行降落命令
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    @Override
    public boolean land(String droneId) {
        try {
            log.info("执行PX4降落命令: droneId={}", droneId);

            // LAND命令不需要额外参数
            Map<String, Object> params = new HashMap<>();

            // 发送MQTT命令
            sendCommand(droneId, CommandType.LAND.getCode(), params, COMMAND_TOPIC_SUFFIX);
            return true;
        } catch (Exception e) {
            log.error("PX4降落命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 执行返航命令
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    @Override
    public boolean goHome(String droneId) {
        try {
            log.info("执行PX4返航命令: droneId={}", droneId);

            // GO_HOME命令不需要额外参数
            Map<String, Object> params = new HashMap<>();

            // 发送MQTT命令
            sendCommand(droneId, CommandType.GO_HOME.getCode(), params, COMMAND_TOPIC_SUFFIX);
            return true;
        } catch (Exception e) {
            log.error("PX4返航命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 开启虚拟摇杆控制
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    @Override
    public boolean startVirtualStick(String droneId) {
        try {
            log.info("执行PX4开启虚拟摇杆命令: droneId={}", droneId);

            // 标记无人机已开启虚拟摇杆，仅在Redis中记录状态，不发送MQTT命令
            Set<String> virtualStickEnabledDrones = getVirtualStick();
            virtualStickEnabledDrones.add(droneId);
            TenantHelper.ignore(() -> {
                RedisUtils.setCacheSet(VIRTUAL_STICK_ENABLED_DRONES, virtualStickEnabledDrones);
                return null;
            });
            log.info("已启用PX4虚拟摇杆控制模式: droneId={}", droneId);

            return true;
        } catch (Exception e) {
            log.error("PX4开启虚拟摇杆命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 停止虚拟摇杆控制
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    @Override
    public boolean stopVirtualStick(String droneId) {
        try {
            log.info("执行PX4停止虚拟摇杆命令: droneId={}", droneId);

            // VIRTUAL_STICK_STOP命令不需要额外参数
            Map<String, Object> params = new HashMap<>();

            // 发送MQTT命令（使用虚拟摇杆主题）
            sendCommand(droneId, CommandType.VIRTUAL_STICK_STOP.getCode(), params, VIRTUAL_STICK_COMMAND_TOPIC_SUFFIX);

            // 移除无人机的虚拟摇杆状态
            Set<String> virtualStickEnabledDrones = getVirtualStick();
            virtualStickEnabledDrones.remove(droneId);
            TenantHelper.ignore(() -> {
                RedisUtils.setCacheSet(VIRTUAL_STICK_ENABLED_DRONES, virtualStickEnabledDrones);
                return null;
            });
            log.info("已停用PX4虚拟摇杆控制模式: droneId={}", droneId);

            return true;
        } catch (Exception e) {
            log.error("PX4停止虚拟摇杆命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        } finally {
            Set<String> virtualStickEnabledDrones = getVirtualStick();
            // 确保在发生异常时也移除状态
            virtualStickEnabledDrones.remove(droneId);
            TenantHelper.ignore(() -> {
                RedisUtils.setCacheSet(VIRTUAL_STICK_ENABLED_DRONES, virtualStickEnabledDrones);
                return null;
            });
        }
    }

    /**
     * 发送虚拟摇杆控制命令
     *
     * @param droneId    无人机ID
     * @param x          前后移动，正值前进，负值后退，范围 -1.0 到 1.0
     * @param y          左右移动，正值右移，负值左移，范围 -1.0 到 1.0
     * @param z          上下移动，正值下降，负值上升，范围 -1.0 到 1.0
     * @param yaw        偏航旋转，正值顺时针，负值逆时针，范围 -1.0 到 1.0
     * @param coordinate 坐标系统，默认为"BODY"
     * @return 命令执行结果
     */
    @Override
    public boolean controlVirtualStick(String droneId, float x, float y, float z, float yaw, String coordinate) {
        try {
            // 检查是否已启用虚拟摇杆模式
            if (!getVirtualStick().contains(droneId)) {
                log.warn("无人机未启用虚拟摇杆模式，请先开启虚拟摇杆: droneId={}", droneId);
                return false;
            }

            log.info(
                "执行PX4虚拟摇杆控制命令: droneId={}, x(前后)={}, y(左右)={}, z(上下)={}, yaw(旋转)={}, coordinate={}",
                droneId, String.format("%.2f", x), String.format("%.2f", y), String.format("%.2f", z),
                String.format("%.2f", yaw), coordinate);

            // 构建虚拟摇杆控制参数
            Map<String, Object> controlParams = new HashMap<>();
            controlParams.put("x", x);
            controlParams.put("y", y);
            controlParams.put("z", z);
            controlParams.put("yaw", yaw);
            controlParams.put("coordinate", coordinate);

            // 发送MQTT命令（使用虚拟摇杆主题）
            sendCommand(droneId, CommandType.VIRTUAL_STICK_CONTROL.getCode(), controlParams,
                VIRTUAL_STICK_COMMAND_TOPIC_SUFFIX);
            return true;
        } catch (Exception e) {
            log.error("PX4虚拟摇杆控制命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查无人机是否已启用虚拟摇杆模式
     *
     * @param droneId 无人机ID
     * @return 是否已启用
     */
    @Override
    public boolean isVirtualStickEnabled(String droneId) {
        return getVirtualStick().contains(droneId);
    }

    /**
     * 执行航线任务
     *
     * @param droneId 无人机ID
     * @param task    飞行任务
     * @param points  航点列表
     * @return 异步执行结果
     */
    @Override
    public CompletableFuture<Boolean> executeMission(String droneId, FlightTask task, List<FlightTaskPoint> points) {
        CompletableFuture<Boolean> future = new CompletableFuture<>();

        try {
            log.info("执行PX4航线任务: droneId={}, taskId={}, taskName={}, 航点数量={}", droneId, task.getId(),
                task.getTaskName(), points.size());

            // 转换航点数据为PX4格式
            List<Map<String, Object>> missionData = convertToMissionData(task, points);

            // 构建任务参数
            Map<String, Object> missionParams = new HashMap<>();
            missionParams.put("missionData", missionData);

            // 添加任务配置参数
            if (task.getReturnHeight() != null) {
                missionParams.put("returnHeight", task.getReturnHeight());
                log.info("设置PX4任务返航高度: {}米", task.getReturnHeight());
            }
            if (task.getFinishedAction() != null) {
                missionParams.put("finishedAction", task.getFinishedAction());
                log.info("设置PX4任务结束动作: {}", task.getFinishedAction());
            }
            if (task.getMissingAction() != null) {
                missionParams.put("missingAction", task.getMissingAction());
                log.info("设置PX4任务网络失联动作: {}", task.getMissingAction());
            }

            // 发送MQTT命令
            String commandId = sendCommand(droneId, CommandType.EXECUTE_MINIO_MISSION.getCode(), missionParams,
                MISSION_COMMAND_TOPIC_SUFFIX);

            log.info("PX4航线任务命令已发送: droneId={}, taskId={}, commandId={}", droneId, task.getId(), commandId);

            // 在任务状态管理器中记录任务开始
            taskStatusService.startTask(droneId, task.getId(), task.getTaskName(), commandId);

            // 立即返回成功，实际执行结果通过状态监控获取
            future.complete(true);

        } catch (Exception e) {
            log.error("PX4航线任务执行失败: droneId={}, taskId={}, error={}", droneId, task.getId(), e.getMessage(), e);
            future.completeExceptionally(new ServiceException("航线任务执行失败: " + e.getMessage()));
        }

        return future;
    }

    /**
     * 暂停航线任务
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    @Override
    public boolean pauseMission(String droneId) {
        try {
            log.info("执行PX4暂停任务命令: droneId={}", droneId);

            // PAUSE_MISSION命令不需要额外参数
            Map<String, Object> params = new HashMap<>();

            // 发送MQTT命令
            sendCommand(droneId, CommandType.PAUSE_MISSION.getCode(), params, MISSION_COMMAND_TOPIC_SUFFIX);
            return true;
        } catch (Exception e) {
            log.error("PX4暂停任务命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 恢复航线任务
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    @Override
    public boolean resumeMission(String droneId) {
        try {
            log.info("执行PX4恢复任务命令: droneId={}", droneId);

            // RESUME_MISSION命令不需要额外参数
            Map<String, Object> params = new HashMap<>();

            // 发送MQTT命令
            sendCommand(droneId, CommandType.RESUME_MISSION.getCode(), params, MISSION_COMMAND_TOPIC_SUFFIX);
            return true;
        } catch (Exception e) {
            log.error("PX4恢复任务命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 停止航线任务
     *
     * @param droneId 无人机ID
     * @return 命令执行结果
     */
    @Override
    public boolean stopMission(String droneId) {
        try {
            log.info("执行PX4停止任务命令: droneId={}", droneId);

            // STOP_MISSION命令不需要额外参数
            Map<String, Object> params = new HashMap<>();

            // 发送MQTT命令
            sendCommand(droneId, CommandType.STOP_MISSION.getCode(), params, MISSION_COMMAND_TOPIC_SUFFIX);
            return true;
        } catch (Exception e) {
            log.error("PX4停止任务命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将航点列表转换为PX4 MQTT协议格式
     *
     * @param task 飞行任务（包含结束动作配置）
     * @param points 航点列表
     * @return PX4格式的航点数据
     */
    private List<Map<String, Object>> convertToMissionData(FlightTask task, List<FlightTaskPoint> points) {
        List<Map<String, Object>> missionData = new ArrayList<>();

        for (int i = 0; i < points.size(); i++) {
            FlightTaskPoint point = points.get(i);
            Map<String, Object> waypoint = new HashMap<>();

            // 基本位置信息
            waypoint.put("latitude", point.getLatitude());
            waypoint.put("longitude", point.getLongitude());
            waypoint.put("relative_altitude", point.getAltitude());

            // 飞行参数 - speed字段是Integer类型
            waypoint.put("speed", point.getSpeed() != null ? point.getSpeed().floatValue() : 5.0f);
            waypoint.put("is_fly_through", false); // 默认不飞越，到达航点后悬停

            // 设置飞行器动作 - 根据任务结束动作配置最后一个航点
            boolean isLastWaypoint = (i == points.size() - 1);
            if (isLastWaypoint) {
                setLastWaypointAction(waypoint, task, i + 1, points.size());
            } else {
                waypoint.put("vehicle_action", Px4Constants.VehicleAction.NONE);
            }

            // 从航点动作中获取航向角和悬停时间
            if (point.getActions() != null && !point.getActions().isEmpty()) {
                for (FlightTaskPointAction action : point.getActions()) {
                    // 获取航向角
                    if (action.getAircraftHeading() != null) {
                        waypoint.put("yaw", action.getAircraftHeading());
                    }

                    // 获取悬停时间
                    if (action.getHoverTime() != null && action.getHoverTime() > 0) {
                        waypoint.put("loiter_time", action.getHoverTime());
                        // 默认已经是不飞越，无需重复设置
                    }
                }
            }

            // 设置MAVSDK兼容的默认值
            setDefaultMavsdkValues(waypoint);

            missionData.add(waypoint);
        }

        log.info("航线任务转换完成: 总航点数={}", points.size());
        return missionData;
    }

    /**
     * 根据任务结束动作配置设置最后一个航点的动作
     *
     * @param waypoint 最后一个航点数据
     * @param task 飞行任务
     * @param waypointIndex 航点序号（从1开始）
     * @param totalWaypoints 总航点数
     */
    private void setLastWaypointAction(Map<String, Object> waypoint, FlightTask task, int waypointIndex, int totalWaypoints) {
        String finishedAction = task.getFinishedAction();

        if (finishedAction == null || finishedAction.isEmpty()) {
            // 默认动作：原地降落
            finishedAction = "autoLand";
        }

        switch (finishedAction) {
            case "autoLand":
                // 原地降落
                waypoint.put("vehicle_action", Px4Constants.VehicleAction.LAND);
                log.info("根据任务结束动作配置[{}]设置最后航点为[降落]: 航点{}/{}", finishedAction, waypointIndex, totalWaypoints);
                break;

            case "goHome":
                // 返回起点 - 需要添加返航航点
                waypoint.put("vehicle_action", Px4Constants.VehicleAction.NONE);
                log.info("根据任务结束动作配置[{}]设置最后航点为[普通航点，返航由机载程序处理]: 航点{}/{}", finishedAction, waypointIndex, totalWaypoints);
                break;

            case "noAction":
                // 无动作（悬停）
                waypoint.put("vehicle_action", Px4Constants.VehicleAction.NONE);
                log.info("根据任务结束动作配置[{}]设置最后航点为[悬停]: 航点{}/{}", finishedAction, waypointIndex, totalWaypoints);
                break;

            case "gotoFirstWaypoint":
                // 返回第一个航点
                waypoint.put("vehicle_action", Px4Constants.VehicleAction.NONE);
                log.info("根据任务结束动作配置[{}]设置最后航点为[普通航点，返回第一个航点由机载程序处理]: 航点{}/{}", finishedAction, waypointIndex, totalWaypoints);
                break;

            default:
                // 未知动作，默认降落
                waypoint.put("vehicle_action", Px4Constants.VehicleAction.LAND);
                log.warn("未知的任务结束动作[{}]，默认设置最后航点为[降落]: 航点{}/{}", finishedAction, waypointIndex, totalWaypoints);
                break;
        }
    }

    /**
     * 设置MAVSDK兼容的默认值
     *
     * @param waypoint 航点数据
     */
    private void setDefaultMavsdkValues(Map<String, Object> waypoint) {
        // 设置默认的云台角度（如果未设置）
        waypoint.putIfAbsent("gimbal_pitch", Float.NaN);
        waypoint.putIfAbsent("gimbal_yaw", Float.NaN);

        // 设置默认的相机动作（如果未设置）
        waypoint.putIfAbsent("camera_action", 0); // 0 = NONE

        // 设置默认的悬停时间（如果未设置）
        waypoint.putIfAbsent("loiter_time", Float.NaN);

        // 设置默认的相机参数（如果未设置）
        waypoint.putIfAbsent("camera_photo_interval", Float.NaN);
        waypoint.putIfAbsent("camera_photo_distance", Float.NaN);

        // 设置默认的接受半径（如果未设置）
        waypoint.putIfAbsent("acceptance_radius", Float.NaN);

        // 设置默认的航向角（如果未设置）
        waypoint.putIfAbsent("yaw", Float.NaN);
    }

    /**
     * 获取Redis中保存正在使用虚拟遥杆的无人机设备号集合
     *
     * @return 正在使用虚拟遥杆的无人机设备号集合
     */
    private Set<String> getVirtualStick() {
        return TenantHelper.ignore(() -> RedisUtils.getCacheSet(VIRTUAL_STICK_ENABLED_DRONES));
    }
}
