package com.md.utils;

import cn.hutool.crypto.symmetric.AES;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Component
public class AESUtil {

    /**
     * 加密
     *
     * @param content  需要加密的内容
     * @param password 加密密码
     * @return
     */
    public String encrypt(String content, String password) {
        try {
            return new AES(password.getBytes(StandardCharsets.UTF_8)).encryptBase64(content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解密
     *
     * @param content  待解密内容
     * @param password 解密密钥
     * @return
     */
    public String decrypt(String content, String password) {
        try {
            return new AES(password.getBytes(StandardCharsets.UTF_8)).decryptStr(content);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
