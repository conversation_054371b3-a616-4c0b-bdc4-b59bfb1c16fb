package com.md.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.md.domain.po.FlightTaskPointAction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年01月14日 11:08
 */
@Mapper
public interface FlightTaskPointActionMapper extends BaseMapper<FlightTaskPointAction> {

    /**
     * 批量插入航点动作
     *
     * @param actions
     * @return
     */
    int batchInsertActions(@Param("actions") List<FlightTaskPointAction> actions);
}