package com.md.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.md.domain.bo.FlightTaskBo;
import com.md.domain.po.FlightPoint;
import com.md.domain.po.FlightPointAction;
import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.domain.po.FlightTaskPointAction;
import com.md.domain.po.UavInfo;
import com.md.domain.vo.FlightLineVo;
import com.md.domain.vo.FlightTaskVo;
import com.md.enums.TaskStatusEnum;
import com.md.flight.engine.TaskExecutionEngine;
import com.md.flight.engine.TaskExecutionEngineFactory;
import com.md.mapper.FlightLineTaskMapper;
import com.md.mapper.FlightTaskPointActionMapper;
import com.md.mapper.FlightTaskPointMapper;
import com.md.mapper.UavInfoMapper;
import com.md.service.IFenceDetectionService;
import com.md.service.IFlightLineService;
import com.md.service.IFlightTaskExecutionService;
import com.md.service.IFlightTaskFileService;
import com.md.utils.BeanConvertUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 航线任务执行服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FlightTaskExecutionServiceImpl implements IFlightTaskExecutionService {

    private final IFlightLineService flightLineService;
    private final IFlightTaskFileService flightTaskFileService;
    private final IFenceDetectionService fenceDetectionService;
    private final TaskExecutionEngineFactory engineFactory;
    private final UavInfoMapper uavInfoMapper;
    private final FlightLineTaskMapper flightLineTaskMapper;
    private final FlightTaskPointMapper flightTaskPointMapper;
    private final FlightTaskPointActionMapper flightTaskPointActionMapper;

    @Override
    public Map<String, Object> getCurrentTask(String uavCode) {
        try {
            // 直接查询该无人机正在执行的任务（状态为1-执行中）
            LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlightTask::getUavCode, uavCode)
                .eq(FlightTask::getTaskStatus, TaskStatusEnum.EXECUTING.getCode()); // 1:执行中

            List<FlightTask> tasks = flightLineTaskMapper.selectList(queryWrapper);

            // 如果有正在执行的任务，返回第一个（正常情况下同一时间只应该有一个执行中的任务）
            if (!tasks.isEmpty()) {
                FlightTask task = tasks.get(0);
                if (tasks.size() > 1) {
                    log.warn("发现无人机[{}]有多个执行中的任务，任务ID: {}", uavCode,
                        tasks.stream().map(FlightTask::getId).collect(Collectors.joining(", ")));
                }

                Map<String, Object> result = new HashMap<>(4);
                result.put("id", task.getId());
                result.put("taskName", task.getTaskName());
                result.put("taskStatus", String.valueOf(task.getTaskStatus()));
                result.put("uavCode", task.getUavCode());
                return result;
            }
            return null;
        } catch (Exception e) {
            log.error("查询无人机[{}]当前任务状态失败", uavCode, e);
            return null;
        }
    }

    @Override
    public CompletableFuture<Boolean> executeFlightTask(String taskId) {
        log.info("开始执行航线任务: taskId={}", taskId);
        try {
            // 1. 获取任务信息
            FlightTaskVo taskVO = selectFlightTaskById(taskId);
            if (taskVO == null) {
                throw new ServiceException("任务不存在");
            }

            // 2. 检查任务状态
            if (taskVO.getTaskStatus() != null && !"0".equals(taskVO.getTaskStatus())) {
                throw new ServiceException("只能执行未执行状态的任务");
            }

            // 3. 获取航点信息
            List<FlightTaskPoint> points = taskVO.getPoints();
            if (points == null || points.isEmpty()) {
                throw new ServiceException("任务没有航点信息");
            }

            // 4. 检查无人机编码
            String uavCode = taskVO.getUavCode();
            if (StringUtils.isEmpty(uavCode)) {
                throw new ServiceException("任务未关联无人机");
            }

            // 5. 检查地理围栏
            FlightTask task = BeanConvertUtils.convert(taskVO, FlightTask.class);
            //暂时屏蔽电子围栏检测
            //if (StringUtils.isNotEmpty(task.getFenceId())) {
            //    log.info("开始检查航点是否违反地理围栏: fenceId={}", task.getFenceId());
            //    checkFenceViolation(task.getFenceId(), points);
            //}

            // 6. 根据厂商类型和协议类型获取对应的执行引擎
            String manufacturerType = taskVO.getFlightControlNo();
            if (StringUtils.isEmpty(manufacturerType)) {
                throw new ServiceException("任务未指定厂商类型");
            }

            TaskExecutionEngine engine;
            try {
                String protocolType = taskVO.getProtocolType();
                if (StringUtils.isNotEmpty(protocolType)) {
                    log.info("使用指定协议[{}]获取执行引擎", protocolType);
                    engine = engineFactory.getEngine(manufacturerType, protocolType);
                } else {
                    log.info("使用默认协议获取执行引擎");
                    engine = engineFactory.getEngine(manufacturerType);
                }

                // 获取该厂商支持的所有协议类型，用于日志记录
                Set<String> supportedProtocols = engineFactory.getSupportedProtocols(manufacturerType);
                log.info("厂商[{}]支持的协议类型: {}", manufacturerType, supportedProtocols);

                // 使用选定的引擎执行任务
                log.info("开始使用引擎[{}/{}]执行任务", engine.getSupportedManufacturer(),
                    engine.getSupportedProtocol());
                return engine.executeTask(task, points);

            } catch (IllegalArgumentException e) {
                log.error("获取任务执行引擎失败: {}", e.getMessage());
                throw new ServiceException("不支持的厂商类型或协议类型: " + e.getMessage());
            }

        } catch (Exception e) {
            log.error("执行航线任务失败: taskId=" + taskId, e);
            throw new ServiceException("执行航线失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTaskFromLine(String lineId, String uavCode, Boolean executeImmediately) {
        return createTaskFromLine(lineId, uavCode, executeImmediately, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTaskFromLine(String lineId, String uavCode, Boolean executeImmediately, String tenantId) {
        // 从IFlightLineService获取航线信息（包含航点和航点动作）
        FlightLineVo flightLine = flightLineService.selectFlightLineById(lineId);
        if (flightLine == null) {
            throw new ServiceException("未找到指定的航线");
        }

        // 创建航线任务Bo对象
        FlightTaskBo flightTaskBo = new FlightTaskBo();
        flightTaskBo.setLineId(lineId);
        flightTaskBo.setLineName(flightLine.getLineName());
        flightTaskBo.setUavCode(uavCode);

        // 生成包含日期时间的任务名称，格式：航线名称-yyyyMMddHHmmss
        String dateTimeStr = DateUtils.dateTimeNow(FormatsType.YYYYMMDDHHMMSS);
        flightTaskBo.setTaskName(flightLine.getLineName() + "-" + dateTimeStr);

        // 设置飞控平台ID
        flightTaskBo.setFlightControlNo(flightLine.getFlightControlNo());

        // 设置租户ID（如果指定了租户ID，则使用指定的；否则使用当前上下文的租户ID）
        if (StringUtils.isNotEmpty(tenantId)) {
            flightTaskBo.setTenantId(tenantId);
            log.info("使用指定的租户ID创建任务: {}", tenantId);
        } else {
            log.info("使用当前上下文的租户ID创建任务");
        }

        // 获取无人机协议类型并设置
        UavInfo uavInfo =
            uavInfoMapper.selectOne(new LambdaQueryWrapper<UavInfo>().eq(UavInfo::getUavCode, uavCode).last("limit 1"));
        if (uavInfo != null && StringUtils.isNotEmpty(uavInfo.getProtocolType())) {
            log.info("根据无人机编码[{}]获取到协议类型: {}", uavCode, uavInfo.getProtocolType());
            flightTaskBo.setProtocolType(uavInfo.getProtocolType());
        } else {
            log.info("无人机[{}]未设置协议类型或不存在，将使用默认协议", uavCode);
        }

        // 设置其他飞行参数
        flightTaskBo.setFlightSpeed(flightLine.getFlightSpeed());
        flightTaskBo.setFlightHeight(flightLine.getFlightHeight());
        flightTaskBo.setReturnHeight(flightLine.getReturnHeight());
        flightTaskBo.setFinishedAction(flightLine.getFinishedAction());
        flightTaskBo.setMissingAction(flightLine.getMissingAction());
        flightTaskBo.setFenceId(flightLine.getFenceId());

        // 复制航点信息
        List<FlightTaskPoint> taskPoints = new ArrayList<>();
        List<FlightPoint> flightPoints = flightLine.getPoints();
        if (flightPoints != null && !flightPoints.isEmpty()) {
            for (FlightPoint flightPoint : flightPoints) {
                FlightTaskPoint taskPoint = new FlightTaskPoint();
                taskPoint.setLineId(lineId);
                taskPoint.setPointIndex(flightPoint.getPointIndex().intValue());
                taskPoint.setLatitude(flightPoint.getLatitude());
                taskPoint.setLongitude(flightPoint.getLongitude());
                taskPoint.setAltitude(flightPoint.getAltitude());
                taskPoint.setSpeed(flightPoint.getSpeed());
                taskPoint.setIsEmergencyLandingPoint(flightPoint.getIsEmergencyLandingPoint());

                // 转换航点动作
                if (flightPoint.getActions() != null && !flightPoint.getActions().isEmpty()) {
                    List<FlightTaskPointAction> taskActions = new ArrayList<>();
                    for (FlightPointAction action : flightPoint.getActions()) {
                        FlightTaskPointAction taskAction = new FlightTaskPointAction();
                        // 设置动作属性
                        taskAction.setActionIndex(action.getActionIndex());
                        taskAction.setType(action.getType());
                        taskAction.setHoverTime(action.getHoverTime());
                        taskAction.setAircraftHeading(action.getAircraftHeading());
                        taskAction.setUseGlobalImageFormat(action.getUseGlobalImageFormat());
                        taskAction.setImageFormat(action.getImageFormat());
                        taskAction.setGimbalYawRotateAngle(action.getGimbalYawRotateAngle());
                        taskAction.setGimbalPitchRotateAngle(action.getGimbalPitchRotateAngle());
                        taskAction.setZoom(action.getZoom());
                        taskAction.setRecordStatus(action.getRecordStatus());
                        taskAction.setMultipleTiming(action.getMultipleTiming());
                        taskAction.setMultipleDistance(action.getMultipleDistance());
                        taskActions.add(taskAction);
                    }
                    taskPoint.setActions(taskActions);
                }

                taskPoints.add(taskPoint);
            }
        }

        // 设置航点列表
        flightTaskBo.setPoints(taskPoints);

        // 创建任务
        String taskId = insertFlightTask(flightTaskBo);

        // 对于DJI厂商，需要生成和上传KMZ文件
        if ("DJI".equals(flightLine.getFlightControlNo())) {
            try {
                log.info("开始为DJI任务生成KMZ文件，任务ID: {}", taskId);
                boolean kmzGenerated = flightTaskFileService.buildKmzByFlightLine(taskId);
                if (kmzGenerated) {
                    log.info("DJI任务KMZ文件生成成功，任务ID: {}", taskId);
                } else {
                    log.warn("DJI任务KMZ文件生成失败，任务ID: {}", taskId);
                }
            } catch (Exception e) {
                log.error("生成DJI任务KMZ文件时发生异常，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
                // 不抛出异常，允许任务创建成功，但记录错误
            }
        }

        // 根据参数决定是否立即执行任务
        if (Boolean.TRUE.equals(executeImmediately)) {
            // 存储taskId以在事务完成后使用
            final String finalTaskId = taskId;
            // 注册事务同步，确保事务提交后再执行异步任务
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 在事务提交后异步执行任务
                    new Thread(() -> {
                        try {
                            boolean taskExecuted = false;
                            int retryCount = 0;
                            int maxRetries = 3;
                            long waitTime = 2000; // 2秒等待时间

                            while (!taskExecuted && retryCount < maxRetries) {
                                try {
                                    log.info("等待{}毫秒后开始执行任务（重试次数: {}/{}），任务ID: {}", waitTime,
                                        retryCount + 1, maxRetries, finalTaskId);
                                    Thread.sleep(waitTime);

                                    // 执行任务
                                    log.info("开始执行航线任务，任务ID: {}", finalTaskId);
                                    CompletableFuture<Boolean> future = executeFlightTask(finalTaskId);
                                    Boolean result = future.get(30, TimeUnit.SECONDS);
                                    log.info("航线任务执行结果: {}, 任务ID: {}", result, finalTaskId);
                                    taskExecuted = true;
                                } catch (Exception e) {
                                    retryCount++;
                                    log.warn("执行任务失败，将重试 ({}/{}), 任务ID: {}, 错误: {}", retryCount,
                                        maxRetries, finalTaskId, e.getMessage());
                                    if (retryCount >= maxRetries) {
                                        log.error("多次重试后任务执行失败，任务ID: {}", finalTaskId, e);
                                    }
                                    // 继续重试，不抛出异常
                                }
                            }
                        } catch (Exception e) {
                            log.error("执行航线任务时发生异常，任务ID: {}", finalTaskId, e);
                        }
                    }).start();
                }
            });
            log.info("任务创建成功，将在事务提交后执行，任务ID: {}", taskId);
        } else {
            log.info("仅创建航线任务，不执行任务，任务ID: {}", taskId);
        }

        // 立即返回任务ID
        return taskId;
    }

    @Override
    public Set<String> getSupportedProtocols(String manufacturerType) {
        if (StringUtils.isEmpty(manufacturerType)) {
            throw new IllegalArgumentException("厂商类型不能为空");
        }
        try {
            return engineFactory.getSupportedProtocols(manufacturerType);
        } catch (Exception e) {
            log.error("获取厂商[{}]支持的协议类型失败", manufacturerType, e);
            throw new IllegalArgumentException("不支持的厂商类型: " + manufacturerType);
        }
    }

    /**
     * 检查航点是否违反地理围栏
     */
    private void checkFenceViolation(String fenceId, List<FlightTaskPoint> points) {
        for (FlightTaskPoint point : points) {
            boolean isInFence = fenceDetectionService.detectUavPosition(null, // 任务ID为空，因为这是任务执行前的检查
                null, // 无人机编码为空，因为这是任务执行前的检查
                point.getLongitude(), point.getLatitude(), point.getAltitude()).isViolated();

            if (!isInFence) {
                throw new ServiceException(String.format("航点[%d]超出地理围栏范围", point.getPointIndex()));
            }
        }
    }

    /**
     * 根据ID查询航线任务详情
     */
    private FlightTaskVo selectFlightTaskById(String id) {
        // 1. 查询基础数据
        FlightTask task = flightLineTaskMapper.selectById(id);
        if (task == null) {
            return null;
        }

        // 2. 转换并补充关联数据
        FlightTaskVo vo = new FlightTaskVo();
        BeanUtils.copyProperties(task, vo);
        // 手动处理taskStatus的类型转换
        if (task.getTaskStatus() != null) {
            vo.setTaskStatus(String.valueOf(task.getTaskStatus()));
        }

        // 3. 查询并设置航点数据
        List<FlightTaskPoint> points = flightTaskPointMapper.selectList(
            new LambdaQueryWrapper<FlightTaskPoint>().eq(FlightTaskPoint::getLineId, task.getId()));

        // 4. 查询并设置航点动作数据
        points.forEach(point -> point.setActions(flightTaskPointActionMapper.selectList(
            new LambdaQueryWrapper<FlightTaskPointAction>().eq(FlightTaskPointAction::getPointId, point.getId()))));

        vo.setPoints(points);
        return vo;
    }

    /**
     * 创建航线任务
     */
    @Transactional
    protected String insertFlightTask(FlightTaskBo bo) {
        log.info("开始创建航线任务，参数：{}", bo);
        try {
            // 转换Bo为Vo进行处理
            FlightTaskVo vo = BeanConvertUtils.convert(bo, FlightTaskVo.class);

            FlightTask flightTask = new FlightTask();
            // 1. 设置航线信息
            BeanUtils.copyProperties(vo, flightTask);
            flightTask.setId(IdWorker.getIdStr());
            flightTask.setCreateTime(DateUtils.getNowDate());
            flightTask.setCreateBy(LoginHelper.getUserId());
            flightTask.setTaskStatus(0);

            // 设置租户ID（如果Bo中有指定租户ID，则使用指定的）
            if (StringUtils.isNotEmpty(bo.getTenantId())) {
                flightTask.setTenantId(bo.getTenantId());
                log.info("为任务[{}]设置指定的租户ID: {}", flightTask.getId(), bo.getTenantId());
            } else {
                log.info("任务[{}]使用当前上下文的租户ID", flightTask.getId());
            }

            // 暂存围栏ID
            String fenceIds = flightTask.getFenceId();

            // 清空围栏ID，避免重复处理
            flightTask.setFenceId(null);

            // 插入任务数据
            flightLineTaskMapper.insert(flightTask);

            // 2. 设置航点信息和动作信息
            List<FlightTaskPoint> points = vo.getPoints();
            if (points != null && !points.isEmpty()) {
                List<FlightTaskPointAction> allActions = new ArrayList<>();
                for (FlightTaskPoint point : points) {
                    String pointId = IdWorker.getIdStr();
                    point.setId(pointId);
                    point.setLineId(flightTask.getId());

                    // 设置航点的租户ID（与任务保持一致）
                    if (StringUtils.isNotEmpty(bo.getTenantId())) {
                        point.setTenantId(bo.getTenantId());
                    }

                    // 处理航点动作
                    if (point.getActions() != null && !point.getActions().isEmpty()) {
                        for (FlightTaskPointAction action : point.getActions()) {
                            action.setPointId(pointId);

                            // 设置动作的租户ID（与任务保持一致）
                            if (StringUtils.isNotEmpty(bo.getTenantId())) {
                                action.setTenantId(bo.getTenantId());
                            }
                        }
                        allActions.addAll(point.getActions());
                    }
                }
                flightTaskPointMapper.batchInsertPoints(points);

                // 批量插入动作
                if (!allActions.isEmpty()) {
                    flightTaskPointActionMapper.batchInsertActions(allActions);
                }

                log.info("成功插入 {} 个航点和 {} 个动作，租户ID: {}", points.size(), allActions.size(),
                    bo.getTenantId());
            }

            // 3. 处理围栏关联 - 如果有围栏ID则更新
            if (StringUtils.isNotEmpty(fenceIds)) {
                FlightTask updateTask = new FlightTask();
                updateTask.setId(flightTask.getId());
                updateTask.setFenceId(fenceIds);
                flightLineTaskMapper.updateById(updateTask);
            }

            log.info("航线任务创建成功，ID：{}", flightTask.getId());
            return flightTask.getId();
        } catch (Exception e) {
            log.error("航线任务创建失败，参数：{}，异常：", bo, e);
            throw new ServiceException("创建任务失败：" + e.getMessage());
        }
    }
}