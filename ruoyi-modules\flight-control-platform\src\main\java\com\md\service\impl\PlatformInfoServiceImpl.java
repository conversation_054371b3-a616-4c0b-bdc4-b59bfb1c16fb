package com.md.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.domain.bo.PlatformInfoBo;
import com.md.domain.po.PlatformInfo;
import com.md.domain.vo.PlatformInfoVo;
import com.md.mapper.PlatformInfoMapper;
import com.md.service.IPlatformInfoService;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 飞控平台Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class PlatformInfoServiceImpl extends ServiceImpl<PlatformInfoMapper, PlatformInfo>
    implements IPlatformInfoService {

    private final PlatformInfoMapper platformInfoMapper;

    /**
     * 查询飞控平台
     *
     * @param id 飞控平台主键
     * @return 飞控平台
     */
    @Override
    public PlatformInfoVo selectFkPlatformInfoById(Long id) {
        PlatformInfo entity = platformInfoMapper.selectFkPlatformInfoById(id);
        return MapstructUtils.convert(entity, PlatformInfoVo.class);
    }

    /**
     * 查询飞控平台列表
     *
     * @param bo 飞控平台
     * @return 飞控平台
     */
    @Override
    public List<PlatformInfoVo> selectFkPlatformInfoList(PlatformInfoBo bo) {
        PlatformInfo platformInfo = MapstructUtils.convert(bo, PlatformInfo.class);
        List<PlatformInfo> list = platformInfoMapper.selectFkPlatformInfoList(platformInfo);
        return MapstructUtils.convert(list, PlatformInfoVo.class);
    }

    /**
     * 分页查询飞控平台列表
     *
     * @param bo        飞控平台
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<PlatformInfoVo> selectPlatformInfoPage(PlatformInfoBo bo, PageQuery pageQuery) {
        PlatformInfo platformInfo = MapstructUtils.convert(bo, PlatformInfo.class);

        LambdaQueryWrapper<PlatformInfo> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(platformInfo.getManufacturerName()), PlatformInfo::getManufacturerName,
            platformInfo.getManufacturerName());
        lqw.eq(StringUtils.isNotBlank(platformInfo.getFlightControlNo()), PlatformInfo::getFlightControlNo,
            platformInfo.getFlightControlNo());
        lqw.like(StringUtils.isNotBlank(platformInfo.getNetworkAddress()), PlatformInfo::getNetworkAddress,
            platformInfo.getNetworkAddress());
        lqw.like(StringUtils.isNotBlank(platformInfo.getAccount()), PlatformInfo::getAccount,
            platformInfo.getAccount());
        lqw.like(StringUtils.isNotBlank(platformInfo.getRemark()), PlatformInfo::getRemark, platformInfo.getRemark());

        Page<PlatformInfo> page = page(pageQuery.build(), lqw);
        List<PlatformInfoVo> listVo = MapstructUtils.convert(page.getRecords(), PlatformInfoVo.class);
        TableDataInfo<PlatformInfoVo> dataInfo = new TableDataInfo<>();
        dataInfo.setRows(listVo);
        dataInfo.setTotal(page.getTotal());
        return dataInfo;
    }

    /**
     * 新增飞控平台
     *
     * @param bo 飞控平台
     * @return 结果
     */
    @Override
    public int insertFkPlatformInfo(PlatformInfoBo bo) {
        PlatformInfo platformInfo = MapstructUtils.convert(bo, PlatformInfo.class);

        // 校验飞控编号是否已存在
        PlatformInfo existingPlatform =
            platformInfoMapper.checkFlightControlNoUnique(platformInfo.getFlightControlNo());
        if (existingPlatform != null) {
            throw new ServiceException("飞控编号'" + platformInfo.getFlightControlNo() + "'已存在");
        }

        platformInfo.setCreateBy(LoginHelper.getUserId());
        platformInfo.setCreateTime(DateUtils.getNowDate());
        return platformInfoMapper.insertFkPlatformInfo(platformInfo);
    }

    /**
     * 修改飞控平台
     *
     * @param bo 飞控平台
     * @return 结果
     */
    @Override
    public int updateFkPlatformInfo(PlatformInfoBo bo) {
        PlatformInfo platformInfo = MapstructUtils.convert(bo, PlatformInfo.class);

        // 校验飞控编号是否已存在（排除自身）
        PlatformInfo existingPlatform =
            platformInfoMapper.checkFlightControlNoUnique(platformInfo.getFlightControlNo());
        if (existingPlatform != null && !existingPlatform.getId().equals(platformInfo.getId())) {
            throw new ServiceException("飞控编号'" + platformInfo.getFlightControlNo() + "'已存在");
        }

        platformInfo.setUpdateBy(LoginHelper.getUserId());
        platformInfo.setUpdateTime(DateUtils.getNowDate());
        return platformInfoMapper.updateFkPlatformInfo(platformInfo);
    }

    /**
     * 批量删除飞控平台
     *
     * @param ids 需要删除的飞控平台主键
     * @return 结果
     */
    @Override
    public int deleteFkPlatformInfoByIds(Long[] ids) {
        return platformInfoMapper.deleteFkPlatformInfoByIds(ids);
    }

    /**
     * 删除飞控平台信息
     *
     * @param id 飞控平台主键
     * @return 结果
     */
    @Override
    public int deleteFkPlatformInfoById(Long id) {
        return platformInfoMapper.deleteFkPlatformInfoById(id);
    }

    /**
     * 通过飞控编号获取飞控实体
     *
     * @param code 飞控编号
     * @return 飞控平台
     */
    @Override
    public PlatformInfo selectPlatformByCode(String code) {
        return platformInfoMapper.selectPlatformtByCode(code);
    }
}
