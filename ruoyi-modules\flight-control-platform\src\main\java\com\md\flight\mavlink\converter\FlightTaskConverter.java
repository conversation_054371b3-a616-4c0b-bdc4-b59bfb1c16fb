package com.md.flight.mavlink.converter;

import com.md.domain.po.FlightTask;
import com.md.domain.po.FlightTaskPoint;
import com.md.domain.po.FlightTaskPointAction;
import com.md.flight.mavlink.model.Mission;
import com.md.utils.MissionUtils;
import com.md.utils.MissionUtils.WaypointParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 航线任务转换器
 * 负责将系统航线任务转换为MAVLink航线任务
 */
@Slf4j
@Component
public class FlightTaskConverter {

    /**
     * 将FlightTask转换为MAVLink Mission
     *
     * @param task   航线任务
     * @param points 航点列表
     * @return MAVLink任务
     */
    public Mission convertToMavlinkMission(FlightTask task, List<FlightTaskPoint> points) {
        if (task == null || points == null || points.isEmpty()) {
            throw new IllegalArgumentException("航线任务或航点列表不能为空");
        }

        log.info("开始转换航线任务: taskId={}, pointCount={}", task.getId(), points.size());

        try {
            // 转换航点列表为WaypointParams列表
            List<WaypointParams> waypointParams = new ArrayList<>();
            for (FlightTaskPoint point : points) {
                // 跳过备降点
                if (point.getIsEmergencyLandingPoint() != null && point.getIsEmergencyLandingPoint() == 1) {
                    continue;
                }

                float holdTime = 0.0f;
                float acceptRadius = 5.0f; // 默认接受半径

                // 从航点动作中获取悬停时间
                if (point.getActions() != null) {
                    for (FlightTaskPointAction action : point.getActions()) {
                        if (action.getHoverTime() != null && action.getHoverTime() > 0) {
                            holdTime = action.getHoverTime();
                            break;
                        }
                    }
                }

                // 获取航点高度 - 优先使用航点自身的高度，如果未设置则使用任务默认高度
                double altitude;
                if (point.getAltitude() != null && point.getAltitude().doubleValue() > 0) {
                    altitude = point.getAltitude().doubleValue();
                } else {
                    altitude = task.getFlightHeight().doubleValue();
                }

                waypointParams.add(
                    new WaypointParams(point.getLatitude().doubleValue(), point.getLongitude().doubleValue(),
                        acceptRadius, holdTime, (float)altitude  // 传递航点高度
                    ));
            }

            // 使用MissionUtils创建航线任务
            Mission mission =
                MissionUtils.createWaypointsMission(task.getTaskName(), task.getFlightHeight().floatValue(),
                    waypointParams);

            // 设置任务速度
            mission.setSpeed(task.getFlightSpeed().floatValue());

            log.info("航线任务转换完成: taskName={}, waypointCount={}", task.getTaskName(), mission.getItems().size());

            return mission;
        } catch (Exception e) {
            log.error("航线任务转换失败: taskId=" + task.getId(), e);
            throw new RuntimeException("航线任务转换失败", e);
        }
    }
}