package com.md.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 围栏顶点VO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FenceVertexVO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 围栏区域ID
     */
    private String fenceAreaId;

    /**
     * 顶点序号
     */
    private Integer sequence;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;
} 