package com.md.controller;

import com.md.service.IWebSocketTokenService;
import org.dromara.common.core.domain.R;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.NotBlank;

import java.util.Map;

/**
 * WebSocket相关接口
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ws")
public class WebSocketController {

    private final IWebSocketTokenService tokenService;

    /**
     * 获取WebSocket连接Token
     */
    @SaCheckPermission(value = {"system:websocket:token", "flight:task:execute"}, mode = SaMode.OR)
    @GetMapping("/token/{droneId}")
    public R<Map<String, Object>> getToken(@NotBlank(message = "无人机ID不能为空") @PathVariable String droneId) {
        log.info("获取WebSocket连接Token, droneId={}", droneId);
        Map<String, Object> tokenInfo = tokenService.generateToken(droneId);
        return R.ok(tokenInfo);
    }
}