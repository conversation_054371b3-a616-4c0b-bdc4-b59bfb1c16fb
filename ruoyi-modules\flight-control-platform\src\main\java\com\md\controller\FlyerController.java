package com.md.controller;

import com.md.domain.bo.FlyerBo;
import com.md.domain.dto.FlyerDto;
import com.md.domain.vo.FlyerVo;
import com.md.enums.DataSourceEnum;
import com.md.service.IFlyerService;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;

/**
 * 飞手管理
 */
@RestController
@RequiredArgsConstructor
@Validated
@RequestMapping("/md/fk/flyer")
public class FlyerController extends BaseController {

    private final IFlyerService flyerService;

    /**
     * 查询飞手列表
     *
     * @param flyerDto  查询参数
     * @param pageQuery 分页参数
     * @return 飞手列表
     */
    @SaCheckPermission("md:flyer:query")
    @GetMapping("/list")
    public TableDataInfo<FlyerVo> list(FlyerDto flyerDto, PageQuery pageQuery) {
        return flyerService.queryPageList(flyerDto, pageQuery);
    }

    /**
     * 同步飞手信息
     *
     * @param plateformId 平台ID
     * @return 结果
     */
    @SaCheckPermission("md:flyer:sync")
    @Log(title = "飞手管理", businessType = BusinessType.OTHER)
    @PostMapping("/sync/{plateformId}")
    public R<Void> syncFlyer(@PathVariable("plateformId") String plateformId) {
        return toAjax(flyerService.syncFlyer(plateformId));
    }

    /**
     * 新增飞手
     *
     * @param flyerBo 飞手业务对象
     * @return 结果
     */
    @SaCheckPermission("md:flyer:add")
    @Log(title = "飞手管理", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping
    public R<Void> add(@Validated @RequestBody FlyerBo flyerBo) {
        return toAjax(flyerService.insertFlyer(flyerBo));
    }

    /**
     * 修改飞手
     *
     * @param flyerBo 飞手业务对象
     * @return 结果
     */
    @SaCheckPermission("md:flyer:edit")
    @Log(title = "飞手管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping
    public R<Void> edit(@Validated @RequestBody FlyerBo flyerBo) {
        return toAjax(flyerService.updateFlyer(flyerBo));
    }

    /**
     * 根据id删除飞手
     *
     * @param id 飞手ID
     * @return 结果
     */
    @SaCheckPermission("md:flyer:remove")
    @Log(title = "飞手管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotNull(message = "主键不能为空") @PathVariable("id") String id) {
        FlyerVo flyer = flyerService.getById(id);
        if (flyer.getDataSourceValue().equals(DataSourceEnum.SYNC.getValue())) {
            return R.fail("同步飞手信息，不允许删除");
        }
        return toAjax(flyerService.removeById(id));
    }

    /**
     * 根据id查询飞手详情
     *
     * @param id 飞手ID
     * @return 飞手信息
     */
    @SaCheckPermission("md:flyer:query")
    @GetMapping("/{id}")
    public R<FlyerVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") String id) {
        return R.ok(flyerService.getById(id));
    }
}
