package com.md.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 无人机位置DTO
 */
@Data
public class UavPositionDto {
    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    /**
     * 无人机编码
     */
    @NotBlank(message = "无人机编码不能为空")
    private String uavCode;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;

    /**
     * 高度(米)
     */
    @NotNull(message = "高度不能为空")
    private BigDecimal height;
}
