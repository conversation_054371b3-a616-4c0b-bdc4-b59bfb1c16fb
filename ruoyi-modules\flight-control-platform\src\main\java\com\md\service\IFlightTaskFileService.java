package com.md.service;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 航线任务文件服务接口
 * 
 * <AUTHOR>
 */
public interface IFlightTaskFileService {
    
    /**
     * 根据航线ID生成kmz文件
     *
     * @param id 任务ID
     * @return 结果
     */
    boolean buildKmzByFlightLine(String id);

    /**
     * 导出航线任务对应的kmz文件
     *
     * @param taskName 任务名称
     * @param response HTTP响应对象
     * @return 文件URL
     */
    String exportKmz(String taskName, HttpServletResponse response);

    /**
     * 复制航线任务
     *
     * @param taskId 原任务ID
     * @return 新任务ID
     */
    String copyFlightTask(String taskId);
}