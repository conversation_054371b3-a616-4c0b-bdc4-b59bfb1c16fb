# 多厂商无人机指令执行框架接入指南

## 一、框架概述

飞行控制平台设计了统一的指令执行框架，支持多种厂商的无人机接入。目前已支持大疆(DJI)、联合飞机(LH)和Mavlink协议的无人机，通过统一的接口实现指令下发、状态监控和任务执行。本文档详细说明了指令执行框架的架构和新厂商接入流程，帮助开发人员快速接入新的无人机厂商。

### 1.1 架构设计

指令执行框架采用分层设计，主要包括以下几个层次：

1. **API层**：提供统一的HTTP接口，接收来自前端或其他系统的指令请求
2. **服务层**：处理指令请求，转发到合适的指令执行器
3. **执行器层**：根据无人机厂商和协议，将通用指令转换为厂商特定指令
4. **通信层**：负责与无人机建立通信连接，发送指令并接收响应
5. **处理器层**：处理来自无人机的状态数据和响应消息

### 1.2 核心组件

框架包含以下核心组件：

1. **DroneCommandController**：统一的HTTP接口入口，接收指令请求
2. **DroneCommandService**：指令服务，处理指令请求并分发
3. **DroneCommandFactory**：指令工厂，创建合适的指令执行器
4. **DroneCommandExecutor**：指令执行器接口，由各厂商实现
5. **TaskExecutionEngine**：任务执行引擎接口，由各厂商实现

### 1.3 指令流程

```mermaid
graph LR
    Client[客户端] -->|1.发送指令请求| Controller[DroneCommandController]
    Controller -->|2.转发请求| Service[DroneCommandService]
    Service -->|3.创建执行器| Factory[DroneCommandFactory]
    Factory -->|返回执行器| Service
    Service -->|4.转换指令| Executor[DroneCommandExecutor]
    Executor -->|5.发送厂商特定指令| Drone[无人机]
    Drone -->|6.执行指令并返回响应| Processor[处理器层]
    Processor -->|7.转换为统一格式| Executor
    Executor -->|8.返回执行结果| Service
    Service --> Controller
    Controller --> Client
```

1. 客户端发送指令请求到`DroneCommandController`
2. 控制器将请求转发给`DroneCommandService`
3. 服务层通过`DroneCommandFactory`创建合适的指令执行器
4. 执行器将通用指令转换为厂商特定指令
5. 执行器通过厂商特定的通信方式发送指令
6. 无人机执行指令并返回响应
7. 响应通过处理器层转换为统一格式
8. 执行器返回执行结果

## 二、统一接口规范

### 2.1 指令控制器

```java
@RestController
@RequestMapping("/api/drone/command")
public class DroneCommandController {
    
    @Autowired
    private DroneCommandService commandService;
    
    @PostMapping("/execute")
    public ResponseEntity<CommandResult> executeCommand(@Validated @RequestBody CommandRequest request) {
        CommandResult result = commandService.executeCommand(request.toCommand());
        return ResponseEntity.ok(result);
    }
}
```

### 2.2 指令执行器接口

```java
public interface DroneCommandExecutor {
    
    /**
     * 执行无人机命令
     * @param command 无人机命令
     * @return 命令执行结果
     */
    CommandResult executeCommand(DroneCommand command);
    
    /**
     * 获取支持的厂商列表
     * @return 支持的厂商列表
     */
    Set<String> getSupportedManufacturers();
    
    /**
     * 获取支持的协议
     * @return 支持的协议
     */
    String getSupportedProtocol();
    
    /**
     * 获取执行器优先级
     * @return 优先级，数值越大优先级越高
     */
    int getPriority();
}
```

### 2.3 任务执行引擎接口

```java
public interface TaskExecutionEngine {
    
    /**
     * 执行飞行任务
     * @param task 飞行任务
     * @param points 航点列表
     * @return 异步执行结果
     */
    CompletableFuture<Boolean> executeTask(FlightTask task, List<FlightTaskPoint> points);
    
    /**
     * 获取支持的厂商
     * @return 支持的厂商
     */
    String getSupportedManufacturer();
    
    /**
     * 获取支持的协议
     * @return 支持的协议
     */
    String getSupportedProtocol();
}
```

### 2.4 指令枚举

```java
public enum CommandType {
    // 基础控制命令
    ARM,                    // 解锁
    DISARM,                 // 上锁
    TAKEOFF,                // 起飞
    LAND,                   // 降落
    GO_HOME,                // 返航
    
    // 飞行控制命令
    SET_MODE,               // 设置模式
    FLY_TO,                 // 指点飞行
    MOVE,                   // 相对运动
    SET_YAW,                // 设置偏航角
    
    // 云台和相机控制命令
    GIMBAL_CONTROL,         // 云台控制
    TAKE_PHOTO,             // 拍照
    START_RECORD,           // 开始录像
    STOP_RECORD,            // 停止录像
    
    // 虚拟摇杆控制命令
    VIRTUAL_STICK_START,    // 开始虚拟摇杆控制
    VIRTUAL_STICK_STOP,     // 停止虚拟摇杆控制
    VIRTUAL_STICK_CONTROL,  // 虚拟摇杆控制
    VIRTUAL_STICK_TAKEOFF,  // 虚拟摇杆起飞
    VIRTUAL_STICK_LAND      // 虚拟摇杆降落
}
```

## 三、接入流程

### 3.1 准备工作

1. 了解目标厂商的通信协议和控制指令
2. 确定通信方式（MQTT、HTTP、Socket等）
3. 确定需要支持的指令类型
4. 规划代码结构和包命名

### 3.2 开发步骤

#### 3.2.1 创建基础结构

1. 创建厂商特定的包结构（示例：`com.md.flight.{vendor}`）
2. 创建必要的子包：
   - `service`：服务接口和实现
   - `processor`：消息处理器
   - `domain`：数据模型
   - `constant`：常量定义
   - `enums`：枚举类型

#### 3.2.2 实现指令执行器

1. 创建指令执行器实现类：

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class VendorCommandExecutor implements DroneCommandExecutor {
    
    private final VendorDroneControlService droneControlService;
    
    // 命令处理器映射
    private final Map<CommandType, CommandHandler> commandHandlers = new EnumMap<>(CommandType.class);
    
    /**
     * 命令处理器函数式接口
     */
    @FunctionalInterface
    private interface CommandHandler {
        CommandResult handle(String droneId, Map<String, Object> params) throws Exception;
    }
    
    /**
     * 初始化命令处理器映射
     */
    @PostConstruct
    private void initCommandHandlers() {
        // 基本飞行控制
        commandHandlers.put(CommandType.TAKEOFF, this::executeTakeoff);
        commandHandlers.put(CommandType.LAND, this::executeLand);
        commandHandlers.put(CommandType.GO_HOME, this::executeGoHome);
        // 添加更多命令处理器...
    }
    
    @Override
    public CommandResult executeCommand(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            CommandType commandType = command.getCommandType();
            Map<String, Object> params = command.getParameters();
            
            log.info("收到厂商无人机命令: droneId={}, commandType={}, params={}", 
                     droneId, commandType, params);
            
            // 查找对应的命令处理器
            CommandHandler handler = commandHandlers.get(commandType);
            if (handler != null) {
                // 执行命令
                return handler.handle(droneId, params);
            }
            
            return CommandResult.failed("不支持的命令类型: " + commandType);
        } catch (Exception e) {
            log.error("命令执行失败: command={}, error={}", command, e.getMessage(), e);
            return CommandResult.failed("命令执行失败: " + e.getMessage());
        }
    }
    
    @Override
    public Set<String> getSupportedManufacturers() {
        return Collections.singleton("VENDOR_NAME");
    }
    
    @Override
    public String getSupportedProtocol() {
        return "VENDOR_PROTOCOL";
    }
    
    @Override
    public int getPriority() {
        return 10;
    }
    
    // 实现各种命令处理方法...
    private CommandResult executeTakeoff(String droneId, Map<String, Object> params) {
        try {
            // 提取参数
            float altitude = getFloatParam(params, "altitude", 10.0f);
            
            // 调用服务执行起飞
            droneControlService.takeoff(droneId, altitude);
            
            return CommandResult.success("起飞命令已发送");
        } catch (Exception e) {
            log.error("起飞命令执行失败: droneId={}, error={}", droneId, e.getMessage(), e);
            return CommandResult.failed("起飞命令执行失败: " + e.getMessage());
        }
    }
    
    // 更多命令处理方法...
    
    // 参数提取辅助方法
    private float getFloatParam(Map<String, Object> params, String name, float defaultValue) {
        // 实现参数提取逻辑
        return defaultValue;
    }
}
```

#### 3.2.3 实现任务执行引擎

```java
@Component
@Slf4j
public class VendorExecutionEngine implements TaskExecutionEngine {
    
    @Autowired
    private VendorCommunicationClient client;
    
    @Override
    public CompletableFuture<Boolean> executeTask(FlightTask task, List<FlightTaskPoint> points) {
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        
        try {
            log.info("执行航线任务: taskId={}", task.getId());
            
            // 1. 转换航点为厂商特定格式
            VendorMission mission = convertToVendorMission(task, points);
            
            // 2. 上传航线
            String taskId = client.uploadMission(task.getUavCode(), mission);
            
            // 3. 执行航线
            client.startMission(task.getUavCode(), taskId, result -> {
                if (result.isSuccess()) {
                    future.complete(true);
                } else {
                    future.completeExceptionally(
                        new ServiceException("执行航线失败: " + result.getMessage()));
                }
            });
            
        } catch (Exception e) {
            log.error("执行航线任务失败: taskId={}", task.getId(), e);
            future.completeExceptionally(e);
        }
        
        return future;
    }
    
    @Override
    public String getSupportedManufacturer() {
        return "VENDOR_NAME";
    }
    
    @Override
    public String getSupportedProtocol() {
        return "VENDOR_PROTOCOL";
    }
    
    // 私有辅助方法
    private VendorMission convertToVendorMission(FlightTask task, List<FlightTaskPoint> points) {
        // 实现航点转换逻辑
        return new VendorMission();
    }
}
```

#### 3.2.4 实现无人机控制服务

```java
@Service
@RequiredArgsConstructor
@Slf4j
public class VendorDroneControlServiceImpl implements VendorDroneControlService {
    
    private final VendorCommunicationClient client;
    
    @Override
    public void takeoff(String droneId, float altitude) {
        log.info("发送起飞命令: droneId={}, altitude={}", droneId, altitude);
        
        // 实现起飞逻辑
        client.sendCommand(droneId, "takeoff", Map.of("altitude", altitude));
        
        log.info("起飞命令已发送: droneId={}", droneId);
    }
    
    @Override
    public void land(String droneId) {
        log.info("发送降落命令: droneId={}", droneId);
        
        // 实现降落逻辑
        client.sendCommand(droneId, "land", Collections.emptyMap());
        
        log.info("降落命令已发送: droneId={}", droneId);
    }
    
    // 实现更多控制方法...
}
```

#### 3.2.5 实现消息处理器

```java
@Component
@Slf4j
@RequiredArgsConstructor
public class VendorStatusProcessor {
    
    @EventListener
    public void onMessage(VendorStatusMessage message) {
        log.debug("收到状态消息: {}", message);
        
        try {
            // 1. 解析消息
            String droneId = message.getDroneId();
            
            // 2. 转换为统一的DroneStatus格式
            DroneStatus status = convertToDroneStatus(message);
            
            // 3. 存储到Redis
            String redisKey = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId;
            TenantHelper.ignore(() -> 
                RedisUtils.setCacheObject(redisKey, JSON.toJSONString(status)));
            
        } catch (Exception e) {
            log.error("处理状态消息失败: {}", e.getMessage(), e);
        }
    }
    
    private DroneStatus convertToDroneStatus(VendorStatusMessage message) {
        // 实现状态转换逻辑
        DroneStatus status = new DroneStatus();
        // 设置各种状态字段
        return status;
    }
}
```

### 3.3 注意事项

1. **统一数据模型**：确保将厂商特定的状态数据转换为统一的`DroneStatus`格式
2. **错误处理**：实现完善的错误处理和日志记录
3. **超时控制**：为所有异步操作设置合理的超时时间
4. **资源清理**：确保及时清理临时资源，避免内存泄漏
5. **多租户支持**：使用`TenantHelper.ignore`处理跨租户操作
6. **分布式环境**：使用分布式锁等机制确保在集群环境中正确工作

## 四、典型案例分析

### 4.1 大疆(DJI)实现

大疆实现使用MQTT协议与无人机通信，主要特点：

1. 使用MQTT主题订阅无人机状态和响应
2. 将通用指令转换为大疆特定的控制指令
3. 使用异步回调处理响应

### 4.2 联合飞机(LH)实现

联合飞机实现同样基于MQTT协议，主要特点：

1. 使用分布式锁等待指令响应
2. 支持指定返航点的返航功能
3. 将状态数据转换为统一格式

### 4.3 Mavlink实现

Mavlink实现基于MAVLink协议，主要特点：

1. 支持标准的MAVLink消息格式
2. 实现全面的飞行控制和任务执行功能
3. 支持各种飞行模式切换

## 五、故障排查指南

### 5.1 常见问题

1. **指令超时**：检查网络连接、指令格式和无人机状态
2. **状态数据异常**：检查数据转换逻辑和字段映射
3. **任务执行失败**：检查航点格式和任务参数

### 5.2 调试工具

1. **日志分析**：使用详细的日志记录跟踪指令执行流程
2. **MQTT调试工具**：如MQTTLens或MQTT.fx监控消息流
3. **Redis监控**：检查状态数据的存储和更新

### 5.3 最佳实践

1. **增量测试**：先测试基本命令（起飞、降落），再测试复杂功能
2. **模拟测试**：使用模拟器进行初步测试，避免硬件风险
3. **异常处理**：确保所有异常情况都有合理的处理逻辑

## 六、扩展功能

### 6.1 自定义指令

如果需要支持框架未定义的厂商特定指令，可以：

1. 在`CommandType`中添加新的指令类型
2. 在指令执行器中实现对应的处理方法
3. 更新前端界面支持新指令

### 6.2 高级功能

除了基本的飞行控制外，还可以实现：

1. **航线规划**：支持复杂的航线规划和优化
2. **智能避障**：接入厂商的避障功能
3. **视觉定位**：支持视觉或RTK精确定位
4. **多机协同**：实现多架无人机的协同作业

## 七、参考资料

1. [大疆开发者文档](https://developer.dji.com/doc)
2. [MAVLink开发者指南](https://mavlink.io/en/)
3. [MQTT协议规范](https://mqtt.org/)
4. [Spring Boot文档](https://spring.io/projects/spring-boot) 