package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.command.model.dto.CommandResult;
import com.md.domain.bo.EmergencyLandingBo;
import com.md.domain.po.FlightTaskPoint;
import com.md.service.IFlightTaskEmergencyService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 航线任务紧急处理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/flight/task/emergency")
@Validated
public class FlightTaskEmergencyController extends BaseController {

    private final IFlightTaskEmergencyService flightTaskEmergencyService;

    /**
     * 紧急备降操作
     */
    @SaCheckPermission("flight:task:emergency")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.UPDATE)
    @PostMapping("/landing")
    public R<String> executeEmergencyLanding(@Valid @RequestBody EmergencyLandingBo bo) {
        CommandResult result = flightTaskEmergencyService.executeEmergencyLanding(
            bo.getTaskId(), 
            bo.getDroneId(), 
            bo.getCurrentLatitude(),
            bo.getCurrentLongitude(), 
            bo.getCurrentAltitude());

        if (result.isSuccess()) {
            return R.ok("紧急备降指令已发送");
        } else {
            return R.fail("紧急备降指令发送失败: " + result.getMessage());
        }
    }

    /**
     * 获取任务的所有备降点
     */
    @SaCheckPermission("flight:task:query")
    @GetMapping("/points/{taskId}")
    public R<List<FlightTaskPoint>> getEmergencyPoints(
        @PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId) {
        List<FlightTaskPoint> points = flightTaskEmergencyService.getAllEmergencyLandingPoints(taskId);
        return R.ok(points);
    }
}