package com.md.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

@Data
@Configuration
@ConfigurationProperties(prefix = "agora")
public class AgoraConfig {

    /**
     * Agora App ID
     */
    private String appId;

    /**
     * Agora App Certificate
     */
    private String appCertificate;

    /**
     * Token有效期(秒)，默认1小时
     */
    private Integer tokenExpireSeconds = 3600;
}