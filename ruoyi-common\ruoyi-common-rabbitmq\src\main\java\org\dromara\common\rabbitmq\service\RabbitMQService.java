package org.dromara.common.rabbitmq.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * RabbitMQ服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RabbitMQService {

    private final RabbitTemplate rabbitTemplate;

    /**
     * 发送消息到RabbitMQ
     *
     * @param exchange   交换机
     * @param routingKey 路由键
     * @param message    消息内容
     */
    public void sendMessage(String exchange, String routingKey, Object message) {
        try {
            String correlationId = UUID.randomUUID().toString();
            CorrelationData correlationData = new CorrelationData(correlationId);

            log.info("发送消息到RabbitMQ，exchange={}, routingKey={}, message={}, correlationId={}",
                exchange, routingKey, message, correlationId);

            rabbitTemplate.convertAndSend(exchange, routingKey, message, correlationData);
        } catch (Exception e) {
            log.error("发送消息到RabbitMQ失败：exchange={}, routingKey={}, message={}, error={}",
                exchange, routingKey, message, e.getMessage(), e);
        }
    }
}
