package com.md.flight.lh.mqtt;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.md.flight.lh.domain.dto.LhBaseReq;
import com.md.flight.lh.enums.LhReqMassageEnum;
import com.md.flight.lh.processor.LhBaseProcessor;
import com.md.flight.lh.utils.LhUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.Duration;
import java.util.Objects;

@Component
@Slf4j
public class LhCustomMqttMessageReceiver {

    private static final String TOPIC_PREFIX = "lh:topic:prefix";

    @Autowired
    private LhUtils lhUtils;

    /**
     * 处理联合MQTT消息 接收消息后异步分发到对应的处理器进行处理
     *
     */
    public void handleCustomMessage(String topic,String payload){
        try {
            //获取主题方法消息
            int lastSlashIndex = topic.lastIndexOf('/');
            String topicInfo = (lastSlashIndex != -1)
                ? topic.substring(lastSlashIndex + 1)
                : null;

            int index = topicInfo.indexOf('_');
            String topicMethod = topicInfo.substring(index + 1);

            // 消息转换
            LhBaseReq lhBaseReq = JSONObject.parseObject(payload, LhBaseReq.class);
            if (ObjectUtil.isEmpty(lhBaseReq)){
                log.error("消息转换失败, 消息:{}",payload);
                return;
            }

            //获取当前ip地址
            InetAddress addr = InetAddress.getLocalHost();
            String hostAddress = addr.getHostAddress();

            //检查是否重复消费
            if(!RedisUtils.setObjectIfAbsent(TOPIC_PREFIX+lhBaseReq.getBid()+topicMethod+lhBaseReq.getMethod()
                ,"topic_prefix->"+lhBaseReq.getMethod()+",ip->"+hostAddress,Duration.ofMinutes(3))){
                log.error("消息处理失败, 重复消费, 消息:{}",payload);
                return;
            }

            //判断消息并执行处理
            LhBaseProcessor lhBaseProcessor = lhUtils.judgmentMessage(payload, topic);
            lhBaseProcessor.processMessage(payload);

            // 删除redis缓存
            RedisUtils.deleteObject(TOPIC_PREFIX+lhBaseReq.getBid()+topicMethod+lhBaseReq.getMethod());
        } catch (UnknownHostException e) {
            log.error("LhCustomMqttMessageReceiver:"+e.getMessage());
        } catch (Exception e) {
            log.error("处理联合飞机消息失败: {}", e.getMessage(), e);
        }
    }
}

