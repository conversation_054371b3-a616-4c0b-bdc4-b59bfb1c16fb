package com.md.flight.yz.openfeign;

import com.md.flight.yz.domain.dto.YzBaseReqDto;
import com.md.flight.yz.openfeign.fallback.YzGetTokenClientFallback;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 获取token
 */
@FeignClient(
        name = "getTokenClient",
        url = "${yz-remote-call.url}",
        fallbackFactory = YzGetTokenClientFallback.class)
public interface YzGetTokenClient {
    @PostMapping(value = "${yz-remote-call.getToken}")
    @Headers({"Content-Type: application/json;charset=UTF-8"})
    String getTokenClient(@RequestBody YzBaseReqDto baseReqDto);
}
