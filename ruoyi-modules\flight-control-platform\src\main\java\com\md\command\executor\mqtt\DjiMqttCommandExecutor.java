package com.md.command.executor.mqtt;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.command.constant.CommandType;
import com.md.command.core.DroneCommand;
import com.md.command.core.DroneCommandExecutor;
import com.md.command.model.dto.CommandResult;
import com.md.domain.po.FlightTask;
import com.md.enums.TaskStatusEnum;
import com.md.mapper.FlightLineTaskMapper;
import com.md.mqtt.dispatcher.MqttMessageHandler;
import com.md.service.IFlightTaskStatusService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

/**
 * DJI MQTT命令执行器
 * 负责将通用命令转发给DJI无人机执行
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DjiMqttCommandExecutor implements DroneCommandExecutor {

    private static final String COMMAND_TOPIC = "dji/command";
    private static final String VIRTUAL_STICK_TOPIC = "dji/virtual_stick/command";
    private static final String LIVESTREAM_TOPIC = "dji/livestream/command";

    private final FlightLineTaskMapper flightLineTaskMapper;
    private final IFlightTaskStatusService flightTaskStatusService;
    private final MqttMessageHandler messageHandler;

    // 命令处理器映射
    private final Map<CommandType, CommandHandler> commandHandlers = new EnumMap<>(CommandType.class);

    /**
     * 命令处理器函数式接口
     */
    @FunctionalInterface
    private interface CommandHandler {
        CommandResult handle(DroneCommand command) throws Exception;
    }

    /**
     * 初始化命令处理器映射
     */
    @PostConstruct
    private void initCommandHandlers() {
        // 航线任务相关
        commandHandlers.put(CommandType.EXECUTE_MINIO_MISSION, this::executeFlightTask);

        // 基本飞行控制
        commandHandlers.put(CommandType.GO_HOME, this::executeGoHome);
        commandHandlers.put(CommandType.LAND, this::executeLand);

        // 虚拟摇杆控制
        commandHandlers.put(CommandType.VIRTUAL_STICK_START, this::executeVirtualStickStart);
        commandHandlers.put(CommandType.VIRTUAL_STICK_STOP, this::executeVirtualStickStop);
        commandHandlers.put(CommandType.VIRTUAL_STICK_CONTROL, this::executeVirtualStickControl);
        commandHandlers.put(CommandType.VIRTUAL_STICK_TAKEOFF, this::executeVirtualStickTakeoff);
        commandHandlers.put(CommandType.VIRTUAL_STICK_LAND, this::executeVirtualStickLand);

        // 直播控制
        commandHandlers.put(CommandType.START_LIVE_STREAM, this::executeStartLiveStream);
        commandHandlers.put(CommandType.STOP_LIVE_STREAM, this::executeStopLiveStream);
        commandHandlers.put(CommandType.LIVE_STREAM_CONFIG, this::executeLiveStreamConfig);

        // Agora直播控制
        commandHandlers.put(CommandType.AGORA_CONFIG, this::executeAgoraConfig);
        commandHandlers.put(CommandType.AGORA_START, this::executeAgoraStart);
        commandHandlers.put(CommandType.AGORA_STOP, this::executeAgoraStop);
    }

    @Override
    public CommandResult executeCommand(DroneCommand command) {
        try {
            String droneId = command.getDroneId();
            CommandType commandType = command.getCommandType();

            log.info("收到DJI MQTT命令: droneId={}, commandType={}, params={}", droneId, commandType,
                command.getParameters());

            // 查找对应的命令处理器
            CommandHandler handler = commandHandlers.get(commandType);
            if (handler != null) {
                log.debug("开始执行DJI MQTT命令: droneId={}, commandType={}", droneId, commandType);
                CommandResult result = handler.handle(command);
                log.info("DJI MQTT命令执行完成: droneId={}, commandType={}, 结果={}", droneId, commandType,
                    result.isSuccess() ? "成功" : "失败");
                return result;
            }

            // 如果没有找到特定处理器，使用默认处理方式
            log.debug("使用默认处理器处理命令: droneId={}, commandType={}", droneId, commandType);
            return executeDefaultCommand(command);
        } catch (Exception e) {
            log.error("DJI MQTT命令执行失败: command={}, error={}", command, e.getMessage(), e);
            return CommandResult.failed("命令执行失败: " + e.getMessage());
        }
    }

    /**
     * 执行航线任务命令
     * DJI通过MQTT发送航线任务命令
     */
    private CommandResult executeFlightTask(DroneCommand command) {
        try {
            String droneId = command.getDroneId();

            // 获取任务名称
            String taskName = getTaskNameFromCommand(command);
            if (taskName == null) {
                log.error("未找到任务名称: droneId={}", droneId);
                return CommandResult.failed("未找到任务名称");
            }

            // 查询任务状态
            FlightTask task = flightLineTaskMapper.selectOne(
                Wrappers.<FlightTask>lambdaQuery().eq(FlightTask::getTaskName, taskName));

            if (ObjectUtil.isNull(task)) {
                log.error("任务不存在: taskName={}", taskName);
                return CommandResult.failed("任务不存在: " + taskName);
            }

            // 检查任务状态 - 只有待执行状态的任务才能执行
            if (!Objects.equals(TaskStatusEnum.PENDING.getCode(), task.getTaskStatus())) {
                log.error("任务状态不是未执行状态，不能执行: taskName={}, currentStatus={}, expectedStatus={}", taskName,
                    task.getTaskStatus(), TaskStatusEnum.PENDING.getCode());
                return CommandResult.failed(
                    "任务状态不是未执行状态，不能执行。当前状态：" + TaskStatusEnum.getInfo(task.getTaskStatus()));
            }

            // 更新任务执飞人（如果operatorId不为空）
            Long operatorId = LoginHelper.getUserId();
            if (operatorId != null) {
                boolean updateResult = flightTaskStatusService.updateTaskOperator(taskName, operatorId);
                if (!updateResult) {
                    log.error("更新任务执飞人失败: taskName={}, operatorId={}", taskName, operatorId);
                    return CommandResult.failed("更新任务执飞人失败");
                }
                log.info("已更新任务[{}]的执飞人ID为: {}", taskName, operatorId);
            } else {
                log.info("执飞人ID为空，跳过更新任务[{}]的执飞人", taskName);
            }

            // 发送MQTT命令
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行DJI航线任务命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行DJI航线任务命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行返航命令
     */
    private CommandResult executeGoHome(DroneCommand command) {
        try {
            // 发送MQTT命令
            CommandResult result = sendMqttCommand(command);

            // 如果命令发送成功，更新任务状态
            if (result.isSuccess()) {
                try {
                    String taskName = getTaskNameFromCommand(command);
                    if (taskName != null) {
                        // 更新任务状态为已取消
                        flightTaskStatusService.updateTaskStatus(taskName, TaskStatusEnum.CANCELLED.getCode(),
                            LoginHelper.getUsername());
                        log.info("已将任务[{}]状态更新为已取消，触发命令: {}", taskName, command.getCommandType());
                    }
                } catch (Exception ex) {
                    log.error("更新任务状态失败", ex);
                    // 不影响主流程，继续返回成功
                }
            }

            return result;
        } catch (Exception e) {
            log.error("执行返航命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行返航命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行降落命令
     */
    private CommandResult executeLand(DroneCommand command) {
        try {
            // 发送MQTT命令
            CommandResult result = sendMqttCommand(command);

            // 如果命令发送成功，更新任务状态
            if (result.isSuccess()) {
                try {
                    String taskName = getTaskNameFromCommand(command);
                    if (taskName != null) {
                        // 更新任务状态为已取消
                        flightTaskStatusService.updateTaskStatus(taskName, TaskStatusEnum.CANCELLED.getCode(),
                            LoginHelper.getUsername());
                        log.info("已将任务[{}]状态更新为已取消，触发命令: {}", taskName, command.getCommandType());
                    }
                } catch (Exception ex) {
                    log.error("更新任务状态失败", ex);
                    // 不影响主流程，继续返回成功
                }
            }

            return result;
        } catch (Exception e) {
            log.error("执行降落命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行降落命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行虚拟摇杆起飞命令
     */
    private CommandResult executeVirtualStickTakeoff(DroneCommand command) {
        try {
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行虚拟摇杆起飞命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行虚拟摇杆起飞命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行虚拟摇杆降落命令
     */
    private CommandResult executeVirtualStickLand(DroneCommand command) {
        try {
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行虚拟摇杆降落命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行虚拟摇杆降落命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行开启虚拟摇杆控制命令
     */
    private CommandResult executeVirtualStickStart(DroneCommand command) {
        try {
            log.info("执行开启虚拟摇杆控制命令: droneId={}, params={}", command.getDroneId(), command.getParameters());
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行开启虚拟摇杆控制命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行开启虚拟摇杆控制命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行停止虚拟摇杆控制命令
     */
    private CommandResult executeVirtualStickStop(DroneCommand command) {
        try {
            log.info("执行停止虚拟摇杆控制命令: droneId={}, params={}", command.getDroneId(), command.getParameters());
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行停止虚拟摇杆控制命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行停止虚拟摇杆控制命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行虚拟摇杆控制命令
     */
    private CommandResult executeVirtualStickControl(DroneCommand command) {
        try {
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行虚拟摇杆控制命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行虚拟摇杆控制命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行开始直播命令
     */
    private CommandResult executeStartLiveStream(DroneCommand command) {
        try {
            log.info("执行开始直播命令: droneId={}, params={}", command.getDroneId(), command.getParameters());
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行开始直播命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行开始直播命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行停止直播命令
     */
    private CommandResult executeStopLiveStream(DroneCommand command) {
        try {
            log.info("执行停止直播命令: droneId={}, params={}", command.getDroneId(), command.getParameters());
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行停止直播命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行停止直播命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行配置直播参数命令
     */
    private CommandResult executeLiveStreamConfig(DroneCommand command) {
        try {
            log.info("执行配置直播参数命令: droneId={}, params={}", command.getDroneId(), command.getParameters());
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行配置直播参数命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行配置直播参数命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行配置Agora参数命令
     */
    private CommandResult executeAgoraConfig(DroneCommand command) {
        try {
            log.info("执行配置Agora参数命令: droneId={}, params={}", command.getDroneId(), command.getParameters());
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行配置Agora参数命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行配置Agora参数命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行开始Agora直播命令
     */
    private CommandResult executeAgoraStart(DroneCommand command) {
        try {
            log.info("执行开始Agora直播命令: droneId={}, params={}", command.getDroneId(), command.getParameters());
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行开始Agora直播命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行开始Agora直播命令失败: " + e.getMessage());
        }
    }

    /**
     * 执行停止Agora直播命令
     */
    private CommandResult executeAgoraStop(DroneCommand command) {
        try {
            log.info("执行停止Agora直播命令: droneId={}, params={}", command.getDroneId(), command.getParameters());
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("执行停止Agora直播命令失败: droneId={}, error={}", command.getDroneId(), e.getMessage(), e);
            return CommandResult.failed("执行停止Agora直播命令失败: " + e.getMessage());
        }
    }

    /**
     * 处理默认命令（未特别定义处理器的命令类型）
     */
    private CommandResult executeDefaultCommand(DroneCommand command) {
        try {
            log.debug("使用默认处理器处理命令: droneId={}, commandType={}", command.getDroneId(),
                command.getCommandType());
            return sendMqttCommand(command);
        } catch (Exception e) {
            log.error("默认命令处理失败: droneId={}, commandType={}, error={}", command.getDroneId(),
                command.getCommandType(), e.getMessage(), e);
            return CommandResult.failed("命令处理失败: " + e.getMessage());
        }
    }

    /**
     * 发送MQTT命令
     */
    private CommandResult sendMqttCommand(DroneCommand command) {
        try {
            // 使用传入的commandId，如果没有则生成新的
            String commandId = command.getCommandId() != null ? command.getCommandId() : UUID.randomUUID().toString();

            // 构建消息负载
            String payload = buildPayload(command, commandId);

            // 根据命令类型选择不同的主题
            String topic = selectCommandTopic(command.getCommandType());

            // 构建MQTT消息
            Message<String> message = MessageBuilder.withPayload(payload).setHeader(MqttHeaders.TOPIC, topic)
                .setHeader("commandId", commandId).build();

            log.info("正在发送MQTT指令: 主题={}, 消息内容={}", topic, payload);
            messageHandler.sendToMqtt(message);

            return CommandResult.success().setCommandId(commandId).setMessage("指令发送成功");
        } catch (Exception e) {
            log.error("MQTT指令发送失败: {}", e.getMessage(), e);
            return CommandResult.failed("指令发送失败: " + e.getMessage());
        }
    }

    /**
     * 从命令参数中获取任务名称
     *
     * @param command 无人机命令
     * @return 任务名称，如果未找到则返回null
     */
    private String getTaskNameFromCommand(DroneCommand command) {
        if (command.getParameters() == null) {
            return null;
        }

        Object missionFileObj = command.getParameters().get("missionFile");
        if (missionFileObj == null) {
            return null;
        }

        return extractTaskNameFromPath(missionFileObj.toString());
    }

    /**
     * 从路径中提取任务名称 例如: "wayline/task123.kmz" -> "task123"
     */
    private String extractTaskNameFromPath(String path) {
        if (path == null || path.isEmpty()) {
            return "";
        }

        // 先获取文件名
        String fileName = path;
        if (path.contains("/")) {
            fileName = path.substring(path.lastIndexOf("/") + 1);
        }

        // 移除扩展名
        if (fileName.contains(".")) {
            fileName = fileName.substring(0, fileName.lastIndexOf("."));
        }

        return fileName;
    }

    /**
     * 根据命令类型选择对应的MQTT主题
     */
    private String selectCommandTopic(CommandType commandType) {
        if (commandType == null) {
            return COMMAND_TOPIC;
        }

        return switch (commandType) {
            case VIRTUAL_STICK_START, VIRTUAL_STICK_STOP, VIRTUAL_STICK_CONTROL, VIRTUAL_STICK_TAKEOFF,
                 VIRTUAL_STICK_LAND, VIRTUAL_STICK_EMERGENCY_STOP, VIRTUAL_STICK_NAVIGATE_TO_POINT ->
                VIRTUAL_STICK_TOPIC;
            case START_LIVE_STREAM, STOP_LIVE_STREAM, LIVE_STREAM_CONFIG, AGORA_CONFIG, AGORA_START, AGORA_STOP ->
                LIVESTREAM_TOPIC;
            default -> COMMAND_TOPIC;
        };
    }

    /**
     * 构建MQTT消息负载
     */
    private String buildPayload(DroneCommand command, String commandId) {
        Map<String, Object> mqttMessage = new HashMap<>();
        mqttMessage.put("commandId", commandId);
        mqttMessage.put("timestamp", System.currentTimeMillis());
        mqttMessage.put("type", command.getCommandType().name());
        mqttMessage.put("droneId", command.getDroneId());

        if (command.getParameters() != null && !command.getParameters().isEmpty()) {
            mqttMessage.put("payload", command.getParameters());
        }

        return JSON.toJSONString(mqttMessage);
    }

    @Override
    public Set<String> getSupportedManufacturers() {
        return Set.of("DJI");
    }

    @Override
    public String getSupportedProtocol() {
        return "MQTT";
    }

    @Override
    public int getPriority() {
        return 10; // 默认优先级
    }
}
