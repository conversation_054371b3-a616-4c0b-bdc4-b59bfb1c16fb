package com.md.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.po.FenceInfo;
import com.md.domain.vo.FenceAreaVO;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.Date;
import java.util.List;

/**
 * 电子围栏业务对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FenceInfo.class, reverseConvertGenerate = false)
public class FenceBo extends BaseEntity {
    /**
     * 主键ID
     */
    @NotBlank(message = "围栏ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 围栏名称
     */
    @NotBlank(message = "围栏名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 100, message = "围栏名称长度不能超过100个字符",
        groups = {AddGroup.class, EditGroup.class, QueryGroup.class})
    private String fenceName;

    /**
     * 围栏类型(1:包含 2:排除 3:高度 4:复合)
     */
    @NotNull(message = "围栏类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer fenceType;

    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符",
        groups = {AddGroup.class, EditGroup.class, QueryGroup.class})
    private String description;

    /**
     * 状态(0:禁用 1:启用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符",
        groups = {AddGroup.class, EditGroup.class, QueryGroup.class})
    private String remark;

    /**
     * 区域列表
     */
    @Valid
    @NotEmpty(message = "围栏区域不能为空", groups = {AddGroup.class, EditGroup.class})
    private List<FenceAreaVO> areas;

    /**
     * 关联任务ID列表
     */
    private List<String> taskIds;

    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}