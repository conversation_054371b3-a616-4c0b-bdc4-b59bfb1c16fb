package com.md.flight.lh.domain.dto;

import lombok.Data;
import java.util.List;

/**
 * OSD 数据实体类，用于接收和解析无人机状态信息。
 */
@Data
public class LhResponseOsdData {

    /** 飞行记录号 */
    private String order_id;

    /** 制造商 ID，标识设备厂商 */
    private String manufacturer_id;

    /** 无人机 ID，标识具体设备型号 */
    private String uas_id;

    /** 时间戳，表示该数据生成的时间（毫秒） */
    private Long timestamp;

    /** 数据来源
     device：本体4g/5g网络
     rc/station/hangar：地面站
     module：模组
     */
    private String source;

    /** 无人机型号，如 TA-Q20 */
    private String uas_model;

    /** 坐标系统类型，必填项 默认为1
     1：WGS-84
     2：CGCS2000
     3：GLONASS-PZ90 4
     4：CGJ-02
     5：BD09 */
    private Integer coordinate;

    /** 经度，单位为度 */
    private Float longitude;

    /** 纬度，单位为度 */
    private Float latitude;

    /** 相对高度，单位为米 */
    private Float height;

    /** 海拔高度，单位为米 */
    private Float altitude;

    /** 垂直速度，单位为 m/s */
    private Float vs;

    /** 水平飞行速度值，单位为 m/s */
    private Float gs;

    /** 航向角，单位为度 */
    private Float heading;

    /** 俯仰角，单位为度 */
    private Float pitch;

    /** 滚动角，单位为度 */
    private Double roll;

    /** 当前飞行模式
     * MANUAL：手动模式
     * ALTCTL：增稳模式
     * POSCTL：定点模式
     * OFFBOARD：板外控制
     * AUTO.MISSION：任务模式
     * AUTO.LOITER：悬停
     * AUTO.RTL：自主返航
     * AUTO.LAND：自主降落
     * AUTO.READY：就绪
     * AUTO.TAKEOFF：自主起飞
     * 其他值
     * UNKNOWN：未知模式
     * {CUSTOM}:{用户自定义}
     * 支持用户输出自定义模式
     */
    private String mode;

    /** 是否正在飞行，0 表示未飞行，1 表示飞行中 */
    private Integer flying;

    /** 飞行时间，精确到毫秒 */
    private Long fly_time;

    /** 飞行里程 */
    private Float fly_distance;

    /** 是否设置了返航点，0 表示未设置，1 表示已设置 */
    private Integer home_set;

    /** 到返航点的距离，单位为米 */
    private Float home_distance;

    /** 返航点位置信息 */
    private LhResponseOsdDataHomeLocation home_location;

    /** 无人机状态信息 */
    private LhResponseOsdDataState state;
}

