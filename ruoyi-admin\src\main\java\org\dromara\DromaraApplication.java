package org.dromara;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 启动程序
 *
 * <AUTHOR>
 */

@SpringBootApplication(scanBasePackages = {"com.md.*", "org.dromara.*"})
@EnableFeignClients(basePackages = "com.md.*")
@MapperScan({"com.md.**.mapper", "org.dromara.**.mapper"})
public class DromaraApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(DromaraApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("✈ Flying Service Plus 飞行控制平台就绪 - 系统已成功初始化 ✈");
    }

}
