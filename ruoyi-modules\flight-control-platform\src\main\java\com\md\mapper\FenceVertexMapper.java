package com.md.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.md.domain.po.FenceVertex;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 围栏顶点Mapper接口
 */
public interface FenceVertexMapper extends BaseMapper<FenceVertex> {
    /**
     * 查询围栏顶点
     *
     * @param id 顶点ID
     * @return 围栏顶点
     */
    FenceVertex selectFenceVertexById(String id);

    /**
     * 根据区域ID查询顶点列表
     *
     * @param areaId 区域ID
     * @return 顶点列表
     */
    List<FenceVertex> selectByAreaId(@Param("areaId") String areaId);

    /**
     * 查询围栏顶点列表
     *
     * @param fenceVertex 围栏顶点
     * @return 围栏顶点集合
     */
    List<FenceVertex> selectFenceVertexList(FenceVertex fenceVertex);

    /**
     * 新增围栏顶点
     *
     * @param fenceVertex 围栏顶点
     * @return 结果
     */
    int insertFenceVertex(FenceVertex fenceVertex);

    /**
     * 批量新增围栏顶点
     *
     * @param vertices 顶点列表
     * @return 结果
     */
    int batchInsertVertices(@Param("vertices") List<FenceVertex> vertices);

    /**
     * 修改围栏顶点
     *
     * @param fenceVertex 围栏顶点
     * @return 结果
     */
    int updateFenceVertex(FenceVertex fenceVertex);

    /**
     * 删除围栏顶点
     *
     * @param id 顶点ID
     * @return 结果
     */
    int deleteFenceVertexById(String id);

    /**
     * 批量删除围栏顶点
     *
     * @param ids 需要删除的顶点ID数组
     * @return 结果
     */
    int deleteFenceVertexByIds(String[] ids);

    /**
     * 根据区域ID删除顶点
     *
     * @param areaId 区域ID
     * @return 结果
     */
    int deleteByAreaId(@Param("areaId") String areaId);
} 