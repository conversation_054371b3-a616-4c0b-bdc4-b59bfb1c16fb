package com.md.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.md.domain.bo.FlightLineBo;
import com.md.domain.po.FlightLine;
import com.md.domain.vo.FlightLineVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 航线管理Service接口
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public interface IFlightLineService extends IService<FlightLine> {
    /**
     * 查询航线管理
     *
     * @param id 航线管理主键
     * @return 航线管理
     */
    public FlightLineVo selectFlightLineById(String id);

    /**
     * 查询航线管理列表（不分页）
     *
     * @param flightLineBo 航线管理查询条件
     * @return 航线管理集合
     */
    public List<FlightLineVo> selectFlightLineList(FlightLineBo flightLineBo);

    /**
     * 查询航线管理列表（分页）
     *
     * @param flightLineBo 航线管理查询条件
     * @param pageQuery    分页参数
     * @return 分页航线管理集合
     */
    public TableDataInfo<FlightLineVo> selectFlightLineList(FlightLineBo flightLineBo, PageQuery pageQuery);

    /**
     * 查询航线管理列表排除备降点
     *
     * @param flightLineBo 航线管理
     * @return 航线管理
     */
    List<FlightLineVo> selectFlightLineListExclude(FlightLineBo flightLineBo);

    /**
     * 新增航线管理
     *
     * @param flightLineBo 航线管理
     * @return 航线ID
     */
    public String insertFlightLine(FlightLineBo flightLineBo);

    /**
     * 修改航线管理
     *
     * @param flightLineBo 航线管理
     * @return 航线ID
     */
    public String updateFlightLine(FlightLineBo flightLineBo);

    /**
     * 批量删除航线管理
     *
     * @param ids 需要删除的航线管理主键集合
     * @return 结果
     */
    public int deleteFlightLineByIds(String[] ids);

    /**
     * 删除航线管理信息
     *
     * @param id 航线管理主键
     * @return 结果
     */
    public int deleteFlightLineById(String id);

    /**
     * 同步航线
     *
     * @param code
     * @return
     */
    boolean syncFlightLine(String code);

    /**
     * 导出航线KMZ文件
     *
     * @param lineName 航线名称
     * @return 文件访问URL
     */
    String exportKmz(String lineName);

    /**
     * 更新航线关联的围栏
     *
     * @param lineId   航线ID
     * @param fenceIds 围栏ID列表，以逗号分隔的字符串
     * @return 是否更新成功
     */
    boolean updateLineFences(String lineId, String fenceIds);

    /**
     * 获取航线关联的围栏ID
     *
     * @param lineId 航线ID
     * @return 以逗号分隔的围栏ID字符串
     */
    String getLineFences(String lineId);
}
