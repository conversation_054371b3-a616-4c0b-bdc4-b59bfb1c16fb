package com.md.mapper;

import com.md.domain.po.FenceInfo;
import com.md.domain.vo.FenceVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 电子围栏信息Mapper接口
 */
public interface FenceInfoMapper extends BaseMapperPlus<FenceInfo, FenceVo> {
    /**
     * 查询围栏信息
     *
     * @param id 围栏ID
     * @return 围栏信息
     */
    FenceInfo selectFenceInfoById(String id);

    /**
     * 查询围栏信息列表
     *
     * @param fenceInfo 围栏信息
     * @return 围栏信息集合
     */
    List<FenceInfo> selectFenceInfoList(FenceInfo fenceInfo);

    /**
     * 查询所有启用的围栏
     *
     * @return 启用的围栏信息集合
     */
    List<FenceInfo> selectActiveFences();

    /**
     * 新增围栏信息
     *
     * @param fenceInfo 围栏信息
     * @return 结果
     */
    int insertFenceInfo(FenceInfo fenceInfo);

    /**
     * 修改围栏信息
     *
     * @param fenceInfo 围栏信息
     * @return 结果
     */
    int updateFenceInfo(FenceInfo fenceInfo);

    /**
     * 删除围栏信息
     *
     * @param id 围栏ID
     * @return 结果
     */
    int deleteFenceInfoById(String id);

    /**
     * 批量删除围栏信息
     *
     * @param ids 需要删除的围栏ID数组
     * @return 结果
     */
    int deleteFenceInfoByIds(String[] ids);

    /**
     * 根据任务ID查询关联的围栏ID
     *
     * @param taskId 任务ID
     * @return 围栏ID
     */
    @Select("SELECT fence_id FROM flight_task WHERE id = #{taskId}")
    String selectFenceIdByTaskId(@Param("taskId") String taskId);
}