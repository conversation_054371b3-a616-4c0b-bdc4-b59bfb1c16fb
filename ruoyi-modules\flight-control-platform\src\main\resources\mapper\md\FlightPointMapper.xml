<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.FlightPointMapper">
    <resultMap type="com.md.domain.po.FlightPoint" id="FlightPointResult">
        <result property="id" column="id"/>
        <result property="lineId" column="line_id"/>
        <result property="pointIndex" column="point_index"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="altitude" column="altitude"/>
        <result property="createTime" column="create_time"/>
        <result property="speed" column="speed"/>
        <result property="isEmergencyLandingPoint" column="is_emergency_landing_point"/>
    </resultMap>

    <sql id="selectFlightPointVo">
        select id,
               line_id,
               point_index,
               latitude,
               longitude,
               altitude,
               create_time,
               speed,
               is_emergency_landing_point
        from flight_point
    </sql>

    <select id="selectFlightPointList" parameterType="FlightPoint" resultMap="FlightPointResult">
        <include refid="selectFlightPointVo"/>
        <where>
            <if test="lineId != null">
                and line_id = #{lineId}
            </if>
            <if test="pointIndex != null">
                and point_index = #{pointIndex}
            </if>
            <if test="latitude != null">
                and latitude = #{latitude}
            </if>
            <if test="longitude != null">
                and longitude = #{longitude}
            </if>
            <if test="altitude != null">
                and altitude = #{altitude}
            </if>
        </where>
    </select>

    <select id="selectFlightPointById" parameterType="Long" resultMap="FlightPointResult">
        <include refid="selectFlightPointVo"/>
        where id = #{id}
    </select>

    <insert id="insertFlightPoint" parameterType="FlightPoint" useGeneratedKeys="true" keyProperty="id">
        insert into flight_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="lineId != null">
                line_id,
            </if>
            <if test="pointIndex != null">
                point_index,
            </if>
            <if test="latitude != null">
                latitude,
            </if>
            <if test="longitude != null">
                longitude,
            </if>
            <if test="altitude != null">
                altitude,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="speed != null">
                speed,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="lineId != null">
                #{lineId},
            </if>
            <if test="pointIndex != null">
                #{pointIndex},
            </if>
            <if test="latitude != null">
                #{latitude},
            </if>
            <if test="longitude != null">
                #{longitude},
            </if>
            <if test="altitude != null">
                #{altitude},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="speed != null">
                #{speed},
            </if>
        </trim>
    </insert>

    <update id="updateFlightPoint" parameterType="FlightPoint">
        update flight_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="lineId != null">
                line_id = #{lineId},
            </if>
            <if test="pointIndex != null">
                point_index = #{pointIndex},
            </if>
            <if test="latitude != null">
                latitude = #{latitude},
            </if>
            <if test="longitude != null">
                longitude = #{longitude},
            </if>
            <if test="altitude != null">
                altitude = #{altitude},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="speed != null">
                speed = #{speed},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFlightPointById" parameterType="Long">
        delete
        from flight_point
        where id = #{id}
    </delete>

    <delete id="deleteFlightPointByIds" parameterType="String">
        delete
        from flight_point where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsertPoints">
        insert into flight_point(id, line_id, point_index, latitude, longitude,
        altitude, speed, create_time, is_emergency_landing_point) values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.lineId}, #{item.pointIndex}, #{item.latitude},
            #{item.longitude}, #{item.altitude}, #{item.speed}, sysdate(), #{item.isEmergencyLandingPoint})
        </foreach>
    </insert>

    <delete id="deletePointsByLineId">
        delete
        from flight_point
        where line_id = #{lineId}
    </delete>

    <select id="selectPointsByLineId" resultType="com.md.domain.po.FlightPoint">
        SELECT *
        FROM flight_point
        WHERE line_id = #{lineId}
        ORDER BY point_index
    </select>

    <delete id="deletePointsByLineIds">
        DELETE
        FROM flight_point
        WHERE line_id IN
        <foreach collection="lineIds" item="lineId" open="(" separator="," close=")">
            #{lineId}
        </foreach>
    </delete>

    <select id="selectPointIdsByLineIds" resultType="java.lang.String">
        select id
        from flight_point
        where line_id in
        <foreach collection="singletonList" item="lineId" open="(" separator="," close=")">
            #{lineId}
        </foreach>
        order by id
    </select>
</mapper>