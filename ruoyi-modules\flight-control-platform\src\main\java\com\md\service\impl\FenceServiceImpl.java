package com.md.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.domain.bo.FenceBo;
import com.md.domain.po.FenceArea;
import com.md.domain.po.FenceInfo;
import com.md.domain.po.FenceVertex;
import com.md.domain.po.FlightTask;
import com.md.domain.vo.FenceAreaVO;
import com.md.domain.vo.FenceVo;
import com.md.domain.vo.FenceVertexVO;
import com.md.mapper.FenceAreaMapper;
import com.md.mapper.FenceInfoMapper;
import com.md.mapper.FenceVertexMapper;
import com.md.mapper.FlightLineTaskMapper;
import com.md.service.IFenceDetectionService;
import com.md.service.IFenceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电子围栏服务实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FenceServiceImpl extends ServiceImpl<FenceInfoMapper, FenceInfo> implements IFenceService {

    private final FenceInfoMapper fenceInfoMapper;
    private final FenceAreaMapper fenceAreaMapper;
    private final FenceVertexMapper fenceVertexMapper;
    private final FlightLineTaskMapper flightLineTaskMapper;
    private final IFenceDetectionService fenceDetectionService;

    @Override
    public TableDataInfo<FenceVo> selectFenceList(FenceBo bo, PageQuery pageQuery) {
        // 创建查询条件对象
        LambdaQueryWrapper<FenceInfo> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StringUtils.isNotEmpty(bo.getFenceName())) {
            queryWrapper.like(FenceInfo::getFenceName, bo.getFenceName());
        }
        if (bo.getFenceType() != null) {
            queryWrapper.eq(FenceInfo::getFenceType, bo.getFenceType());
        }
        if (bo.getStatus() != null) {
            queryWrapper.eq(FenceInfo::getStatus, bo.getStatus());
        }
        if (bo.getBeginTime() != null && bo.getEndTime() != null) {
            queryWrapper.between(FenceInfo::getCreateTime, bo.getBeginTime(), bo.getEndTime());
        }

        // 设置排序
        queryWrapper.orderByDesc(FenceInfo::getId);

        // 执行分页查询
        Page<FenceVo> page = this.baseMapper.selectVoPage(pageQuery.build(), queryWrapper);

        // 构建TableDataInfo对象并返回
        return TableDataInfo.build(page);
    }

    @Override
    public FenceVo selectFenceById(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        // 查询围栏基本信息
        FenceInfo fenceInfo = fenceInfoMapper.selectFenceInfoById(id);
        if (fenceInfo == null) {
            return null;
        }

        // 转换为VO对象
        FenceVo fenceVO = new FenceVo();
        BeanUtils.copyProperties(fenceInfo, fenceVO);

        // 查询围栏区域
        List<FenceArea> areas = fenceAreaMapper.selectByFenceId(id);
        if (areas != null && !areas.isEmpty()) {
            List<FenceAreaVO> areaVOs = new ArrayList<>();

            // 处理每个区域
            for (FenceArea area : areas) {
                FenceAreaVO areaVO = new FenceAreaVO();
                BeanUtils.copyProperties(area, areaVO);

                // 如果是多边形或矩形，查询顶点信息
                if (area.getAreaType() == 2 || area.getAreaType() == 3) {
                    List<FenceVertex> vertices = fenceVertexMapper.selectByAreaId(area.getId());
                    if (vertices != null && !vertices.isEmpty()) {
                        List<FenceVertexVO> vertexVOs = vertices.stream().map(vertex -> {
                            FenceVertexVO vertexVO = new FenceVertexVO();
                            BeanUtils.copyProperties(vertex, vertexVO);
                            return vertexVO;
                        }).collect(Collectors.toList());
                        areaVO.setVertices(vertexVOs);
                    }
                }

                areaVOs.add(areaVO);
            }

            fenceVO.setAreas(areaVOs);
        }

        // 查询关联此围栏的任务ID列表
        List<String> taskIds = selectTaskIdsByFenceId(id);
        fenceVO.setTaskIds(taskIds);

        return fenceVO;
    }

    /**
     * 根据围栏ID查询关联的任务ID列表
     *
     * @param fenceId 围栏ID
     * @return 任务ID列表
     */
    public List<String> selectTaskIdsByFenceId(String fenceId) {
        if (StringUtils.isEmpty(fenceId)) {
            return new ArrayList<>();
        }

        try {
            // 使用FlightLineTaskMapper查询直接关联此围栏的任务
            LambdaQueryWrapper<FlightTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlightTask::getFenceId, fenceId).select(FlightTask::getId);

            return flightLineTaskMapper.selectList(queryWrapper).stream().map(FlightTask::getId)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询围栏关联任务异常: fenceId={}", fenceId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertFence(FenceBo bo) {
        // 参数验证
        validFenceData(bo);

        // 生成围栏ID
        String fenceId = IdWorker.getIdStr();

        // 设置围栏基本信息并转换为实体
        FenceInfo fenceInfo = MapstructUtils.convert(bo, FenceInfo.class);
        fenceInfo.setId(fenceId);
        fenceInfo.setCreateTime(DateUtils.getNowDate());

        // 插入围栏基本信息
        fenceInfoMapper.insertFenceInfo(fenceInfo);

        // 处理围栏区域
        insertFenceAreas(fenceId, bo.getAreas());

        // 任务关联关系现在由任务侧维护，这里不再处理任务关联

        // 如果围栏状态为启用，则更新缓存
        if (fenceInfo.getStatus() != null && fenceInfo.getStatus() == 1) {
            fenceDetectionService.updateFenceCache(fenceId);
        }

        log.info("新增围栏成功, id: {}, name: {}", fenceId, fenceInfo.getFenceName());
        return fenceId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateFence(FenceBo bo) {
        // 参数验证
        if (StringUtils.isEmpty(bo.getId())) {
            throw new ServiceException("围栏ID不能为空");
        }
        validFenceData(bo);

        // 查询围栏是否存在
        FenceInfo existingFence = fenceInfoMapper.selectFenceInfoById(bo.getId());
        if (existingFence == null) {
            throw new ServiceException("围栏不存在: " + bo.getId());
        }

        // 更新围栏基本信息
        FenceInfo fenceInfo = MapstructUtils.convert(bo, FenceInfo.class);
        fenceInfo.setUpdateTime(DateUtils.getNowDate());

        fenceInfoMapper.updateFenceInfo(fenceInfo);

        String fenceId = bo.getId();

        // 更新围栏区域（先删除后新增）
        fenceAreaMapper.deleteByFenceId(fenceId);
        insertFenceAreas(fenceId, bo.getAreas());

        // 任务关联关系现在由任务侧维护，这里不再处理任务关联

        // 更新缓存
        fenceDetectionService.updateFenceCache(fenceId);

        log.info("更新围栏成功, id: {}, name: {}", fenceId, fenceInfo.getFenceName());
        return fenceId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteFenceByIds(String[] ids) {
        if (ids == null || ids.length == 0) {
            return 0;
        }

        int count = 0;
        for (String id : ids) {
            // 检查是否有任务关联此围栏
            List<String> relatedTaskIds = selectTaskIdsByFenceId(id);
            if (!relatedTaskIds.isEmpty()) {
                log.warn("围栏[{}]有{}个关联任务，无法删除", id, relatedTaskIds.size());
                continue;
            }

            // 删除围栏（级联删除区域、顶点）
            int result = fenceInfoMapper.deleteFenceInfoById(id);
            if (result > 0) {
                count++;
                log.info("删除围栏成功, id: {}", id);

                // 从缓存中移除
                fenceDetectionService.removeFenceFromCache(id);
            }
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableFence(String id) {
        if (StringUtils.isEmpty(id)) {
            return false;
        }

        // 查询围栏是否存在
        FenceInfo fenceInfo = fenceInfoMapper.selectFenceInfoById(id);
        if (fenceInfo == null) {
            throw new ServiceException("围栏不存在: " + id);
        }

        // 如果已经是启用状态，无需处理
        if (fenceInfo.getStatus() != null && fenceInfo.getStatus() == 1) {
            return true;
        }

        // 更新状态为启用
        FenceInfo updateInfo = new FenceInfo();
        updateInfo.setId(id);
        updateInfo.setStatus(1);
        updateInfo.setUpdateTime(DateUtils.getNowDate());
        int result = fenceInfoMapper.updateFenceInfo(updateInfo);

        if (result > 0) {
            // 更新缓存
            fenceDetectionService.updateFenceCache(id);
            log.info("启用围栏成功, id: {}", id);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableFence(String id) {
        if (StringUtils.isEmpty(id)) {
            return false;
        }

        // 查询围栏是否存在
        FenceInfo fenceInfo = fenceInfoMapper.selectFenceInfoById(id);
        if (fenceInfo == null) {
            throw new ServiceException("围栏不存在: " + id);
        }

        // 如果已经是禁用状态，无需处理
        if (fenceInfo.getStatus() != null && fenceInfo.getStatus() == 0) {
            return true;
        }

        // 更新状态为禁用
        FenceInfo updateInfo = new FenceInfo();
        updateInfo.setId(id);
        updateInfo.setStatus(0);
        updateInfo.setUpdateTime(DateUtils.getNowDate());
        int result = fenceInfoMapper.updateFenceInfo(updateInfo);

        if (result > 0) {
            // 从缓存中移除
            fenceDetectionService.removeFenceFromCache(id);
            log.info("禁用围栏成功, id: {}", id);
            return true;
        }
        return false;
    }

    /**
     * 验证围栏数据
     *
     * @param bo 围栏业务对象
     */
    private void validFenceData(FenceBo bo) {
        if (bo == null) {
            throw new ServiceException("围栏数据不能为空");
        }

        if (StringUtils.isEmpty(bo.getFenceName())) {
            throw new ServiceException("围栏名称不能为空");
        }

        if (bo.getFenceType() == null) {
            throw new ServiceException("围栏类型不能为空");
        }

        if (bo.getAreas() == null || bo.getAreas().isEmpty()) {
            throw new ServiceException("围栏区域不能为空");
        }

        for (FenceAreaVO area : bo.getAreas()) {
            if (area.getAreaType() == null) {
                throw new ServiceException("围栏区域类型不能为空");
            }

            if (area.getActionType() == null) {
                throw new ServiceException("围栏触发动作不能为空");
            }

            // 圆形区域必须有中心点和半径
            if (area.getAreaType() == 1) {
                if (area.getCenterLongitude() == null || area.getCenterLatitude() == null) {
                    throw new ServiceException("圆形区域必须设置中心点坐标");
                }
                if (area.getRadius() == null || area.getRadius().doubleValue() <= 0) {
                    throw new ServiceException("圆形区域半径必须大于0");
                }
            }
            // 多边形区域必须有顶点
            else if (area.getAreaType() == 2) {
                if (area.getVertices() == null || area.getVertices().size() < 3) {
                    throw new ServiceException("多边形区域至少需要3个顶点");
                }
            }
            // 矩形区域必须有顶点
            else if (area.getAreaType() == 3) {
                if (area.getVertices() == null || area.getVertices().size() != 4) {
                    throw new ServiceException("矩形区域必须有4个顶点");
                }
            }
            // 高度区域必须有最小高度和最大高度
            else if (area.getAreaType() == 4) {
                if (area.getMinHeight() == null || area.getMaxHeight() == null) {
                    throw new ServiceException("高度区域必须设置最小高度和最大高度");
                }
                if (area.getMinHeight().compareTo(area.getMaxHeight()) >= 0) {
                    throw new ServiceException("最小高度必须小于最大高度");
                }
            } else {
                throw new ServiceException("不支持的围栏区域类型: " + area.getAreaType());
            }
        }
    }

    /**
     * 插入围栏区域和顶点
     *
     * @param fenceId 围栏ID
     * @param areas   区域列表
     */
    private void insertFenceAreas(String fenceId, List<FenceAreaVO> areas) {
        if (StringUtils.isEmpty(fenceId) || areas == null || areas.isEmpty()) {
            return;
        }

        for (FenceAreaVO areaVO : areas) {
            // 创建区域对象
            FenceArea area = new FenceArea();
            BeanUtils.copyProperties(areaVO, area);
            area.setId(IdWorker.getIdStr());
            area.setFenceId(fenceId);
            area.setCreateTime(DateUtils.getNowDate());

            // 插入区域
            fenceAreaMapper.insertFenceArea(area);

            // 如果是多边形或矩形，保存顶点信息
            if ((area.getAreaType() == 2 || area.getAreaType() == 3) && areaVO.getVertices() != null) {
                int sequence = 0;
                for (FenceVertexVO vertexVO : areaVO.getVertices()) {
                    FenceVertex vertex = new FenceVertex();
                    BeanUtils.copyProperties(vertexVO, vertex);
                    vertex.setId(IdWorker.getIdStr());
                    vertex.setFenceAreaId(area.getId());
                    vertex.setSequence(sequence++);
                    fenceVertexMapper.insertFenceVertex(vertex);
                }
            }
        }
    }
}
