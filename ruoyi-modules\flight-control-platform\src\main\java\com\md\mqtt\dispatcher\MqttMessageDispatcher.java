package com.md.mqtt.dispatcher;

import com.md.mqtt.processor.MqttMessageProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class MqttMessageDispatcher {

    @Autowired
    private List<MqttMessageProcessor> processors;

    /**
     * 处理MQTT消息 接收消息后异步分发到对应的处理器进行处理
     *
     * @param message MQTT消息对象，包含主题和负载数据
     */
    public void handleMessage(Message<?> message) {
        String topic = Objects.requireNonNull(message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC)).toString();
        String payload = message.getPayload().toString();

        log.debug("收到MQTT消息，主题: {}", topic);

        // 其他消息交给标准处理器处理
        processors.stream().filter(processor -> processor.canProcess(topic)).forEach(processor -> {
            long startTime = System.currentTimeMillis();
            try {
                processor.processMessage(topic, payload);
                long processingTime = System.currentTimeMillis() - startTime;
                log.debug("MQTT消息处理完成，主题: {}，处理器: {}，耗时: {}ms", topic,
                    processor.getClass().getSimpleName(), processingTime);
            } catch (Exception e) {
                log.error("处理MQTT消息失败, 主题: {}, 处理器: {}", topic, processor.getClass().getSimpleName(), e);
            }
        });
    }
}
