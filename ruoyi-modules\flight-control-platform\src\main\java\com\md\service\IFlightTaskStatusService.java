package com.md.service;

/**
 * 飞行任务状态管理服务接口
 * 专门负责任务状态的更新和管理，从IFlightTaskService中拆分出来
 * 
 * <AUTHOR>
 */
public interface IFlightTaskStatusService {
    
    /**
     * 更新任务状态
     *
     * @param taskName 任务名称
     * @param status   任务状态
     * @param message  状态消息
     * @return 更新结果
     */
    boolean updateTaskStatus(String taskName, Integer status, String message);
    
    /**
     * 更新任务操作人
     *
     * @param taskName   任务名称
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateTaskOperator(String taskName, Long operatorId);
    
    /**
     * 根据任务ID更新任务状态
     *
     * @param taskId 任务ID
     * @param status 任务状态
     * @param message 状态消息
     * @return 更新结果
     */
    boolean updateTaskStatusById(String taskId, Integer status, String message);
    
    /**
     * 根据任务ID更新任务操作人
     *
     * @param taskId     任务ID
     * @param operatorId 操作人ID
     * @return 更新结果
     */
    boolean updateTaskOperatorById(String taskId, Long operatorId);
}
