package com.md.websocket.converter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.md.flight.lh.domain.dto.LhBaseReq;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.domain.dto.LhResponseOsdData;
import com.md.flight.lh.domain.dto.LhResponseRtkData;
import com.md.flight.lh.domain.dto.LhResponseBatteryData;
import com.md.flight.lh.processor.LhOsdProcessor;
import com.md.flight.lh.processor.LhRtkProcessor;
import com.md.flight.lh.processor.LhBatteryProcessor;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.websocket.message.DroneMessage;
import com.md.websocket.message.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 联合飞机消息转换器
 */
@Slf4j
@Component
public class LhMessageConverter implements DroneMessageConverter {

    private static final String MANUFACTURER = "LH";

    // 复用现有的MQTT频率限制配置
    @Value("${mqtt.state.min-update-interval:1000}")
    private long minUpdateInterval;

    // WebSocket推送频率限制器
    private final ConcurrentHashMap<String, Long> lastWebSocketUpdateTimeMap = new ConcurrentHashMap<>();

    // 内存缓存，按droneId存储最新的三种数据
    private final ConcurrentHashMap<String, DroneDataCache> droneDataMap = new ConcurrentHashMap<>();

    // 定时清理过期数据的调度器
    private final ScheduledExecutorService cleanupScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "LhMessageConverter-Cleanup");
        t.setDaemon(true);
        return t;
    });

    public LhMessageConverter() {
        // 每5分钟清理一次过期数据（超过30秒未更新的设备数据）
        cleanupScheduler.scheduleAtFixedRate(this::cleanupExpiredData, 5, 5, TimeUnit.MINUTES);
    }

    @Override
    public DroneMessage convert(String topic, String payload, MessageType messageType) {
        try {
            String droneId = null;
            String method = null;

            // 先解析基本信息获取method字段
            try {
                LhResponse<?> basicResponse = JSON.parseObject(payload, LhResponse.class);
                if (basicResponse != null) {
                    droneId = basicResponse.getGateway();
                    method = basicResponse.getMethod();
                }
            } catch (Exception e) {
                try {
                    LhBaseReq<?> basicRequest = JSON.parseObject(payload, LhBaseReq.class);
                    if (basicRequest != null) {
                        droneId = basicRequest.getGateway();
                        method = basicRequest.getMethod();
                    }
                } catch (Exception ex) {
                    log.debug("无法解析联合飞机消息基本信息: {}", ex.getMessage());
                }
            }

            // 如果无法获取droneId，尝试从主题中提取
            if (droneId == null) {
                droneId = extractDroneIdFromTopic(topic);
            }

            // 获取或创建设备数据缓存
            DroneDataCache cache = droneDataMap.computeIfAbsent(droneId, k -> new DroneDataCache());

            // 根据method字段更新对应的缓存数据
            if ("osd".equals(method)) {
                // 处理OSD消息
                LhResponseOsdData osdData = parseOsdData(payload);
                if (osdData == null) {
                    log.warn("联合飞机OSD数据为空: topic={}", topic);
                    return null;
                }
                cache.updateOsdData(osdData);

            } else if ("rtk".equals(method)) {
                // 处理RTK消息
                LhResponseRtkData rtkData = parseRtkData(payload);
                if (rtkData == null) {
                    log.warn("联合飞机RTK数据为空: topic={}", topic);
                    return null;
                }
                cache.updateRtkData(rtkData);

            } else if ("battery".equals(method)) {
                // 处理电池消息
                LhResponseBatteryData batteryData = parseBatteryData(payload);
                if (batteryData == null) {
                    log.warn("联合飞机电池数据为空: topic={}", topic);
                    return null;
                }
                cache.updateBatteryData(batteryData);

            } else {
                log.debug("联合飞机消息method不支持WebSocket推送: topic={}, method={}", topic, method);
                return null;
            }

            // 添加WebSocket推送频率限制，与其他无人机保持一致
            long currentTime = System.currentTimeMillis();
            Long lastTime = lastWebSocketUpdateTimeMap.get(droneId);
            if (lastTime != null && currentTime - lastTime < minUpdateInterval) {
                log.debug("联合飞机WebSocket推送过于频繁，跳过处理: droneId={}, interval={}ms", droneId,
                    currentTime - lastTime);
                return null;
            }
            lastWebSocketUpdateTimeMap.put(droneId, currentTime);

            // 合并所有可用数据生成完整的DroneStatus
            DroneStatus droneStatus = cache.mergeToStatus(droneId);

            return DroneMessage.builder().type(messageType.name()).droneId(droneId).data(droneStatus)  // 使用转换后的统一格式
                .timestamp(System.currentTimeMillis()).topic(topic).manufacturer(MANUFACTURER).build();

        } catch (Exception e) {
            log.error("联合飞机消息转换失败: topic={}, error={}", topic, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean supports(String manufacturer) {
        return MANUFACTURER.equals(manufacturer);
    }

    @Override
    public String getManufacturer() {
        return MANUFACTURER;
    }

    /**
     * 解析OSD数据
     */
    private LhResponseOsdData parseOsdData(String payload) {
        try {
            // 尝试解析为LhResponse格式
            LhResponse<LhResponseOsdData> lhResponse =
                JSON.parseObject(payload, new TypeReference<LhResponse<LhResponseOsdData>>() {
                });
            if (lhResponse != null && lhResponse.getData() != null) {
                return lhResponse.getData();
            }
        } catch (Exception e) {
            log.debug("尝试解析OSD LhResponse格式失败: {}", e.getMessage());
        }

        try {
            // 尝试解析为LhBaseReq格式
            LhBaseReq<LhResponseOsdData> lhBaseReq =
                JSON.parseObject(payload, new TypeReference<LhBaseReq<LhResponseOsdData>>() {
                });
            if (lhBaseReq != null && lhBaseReq.getData() != null) {
                return lhBaseReq.getData();
            }
        } catch (Exception e) {
            log.debug("尝试解析OSD LhBaseReq格式失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 解析RTK数据
     */
    private LhResponseRtkData parseRtkData(String payload) {
        try {
            // 尝试解析为LhResponse格式
            LhResponse<LhResponseRtkData> lhResponse =
                JSON.parseObject(payload, new TypeReference<LhResponse<LhResponseRtkData>>() {
                });
            if (lhResponse != null && lhResponse.getData() != null) {
                return lhResponse.getData();
            }
        } catch (Exception e) {
            log.debug("尝试解析RTK LhResponse格式失败: {}", e.getMessage());
        }

        try {
            // 尝试解析为LhBaseReq格式
            LhBaseReq<LhResponseRtkData> lhBaseReq =
                JSON.parseObject(payload, new TypeReference<LhBaseReq<LhResponseRtkData>>() {
                });
            if (lhBaseReq != null && lhBaseReq.getData() != null) {
                return lhBaseReq.getData();
            }
        } catch (Exception e) {
            log.debug("尝试解析RTK LhBaseReq格式失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从主题中提取无人机ID
     * 主题格式: thing/device/{droneId}/osd
     */
    private String extractDroneIdFromTopic(String topic) {
        if (topic == null || !topic.contains("/")) {
            return null;
        }

        String[] parts = topic.split("/");
        if (parts.length >= 3 && "thing".equals(parts[0]) && "device".equals(parts[1])) {
            return parts[2];  // 返回 droneId 部分
        }

        return null;
    }

    /**
     * 解析电池数据
     */
    private LhResponseBatteryData parseBatteryData(String payload) {
        try {
            // 尝试解析为LhResponse格式
            LhResponse<LhResponseBatteryData> lhResponse =
                JSON.parseObject(payload, new TypeReference<LhResponse<LhResponseBatteryData>>() {
                });
            if (lhResponse != null && lhResponse.getData() != null) {
                return lhResponse.getData();
            }
        } catch (Exception e) {
            log.debug("尝试解析电池 LhResponse格式失败: {}", e.getMessage());
        }

        try {
            // 尝试解析为LhBaseReq格式
            LhBaseReq<LhResponseBatteryData> lhBaseReq =
                JSON.parseObject(payload, new TypeReference<LhBaseReq<LhResponseBatteryData>>() {
                });
            if (lhBaseReq != null && lhBaseReq.getData() != null) {
                return lhBaseReq.getData();
            }
        } catch (Exception e) {
            log.debug("尝试解析电池 LhBaseReq格式失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 无人机数据缓存，用于合并不同类型的消息
     */
    private static class DroneDataCache {
        private LhResponseOsdData osdData;
        private LhResponseRtkData rtkData;
        private LhResponseBatteryData batteryData;
        private long lastUpdateTime;

        /**
         * 更新OSD数据
         */
        void updateOsdData(LhResponseOsdData data) {
            this.osdData = data;
            this.lastUpdateTime = System.currentTimeMillis();
        }

        /**
         * 更新RTK数据
         */
        void updateRtkData(LhResponseRtkData data) {
            this.rtkData = data;
            this.lastUpdateTime = System.currentTimeMillis();
        }

        /**
         * 更新电池数据
         */
        void updateBatteryData(LhResponseBatteryData data) {
            this.batteryData = data;
            this.lastUpdateTime = System.currentTimeMillis();
        }

        /**
         * 合并所有可用数据到DroneStatus
         */
        DroneStatus mergeToStatus(String droneId) {
            DroneStatus status = new DroneStatus();

            // 按优先级合并数据：OSD -> RTK -> 电池
            if (osdData != null) {
                LhOsdProcessor.droneStatusSet(status, osdData, droneId);
            }
            if (rtkData != null) {
                LhRtkProcessor.droneStatusSet(status, rtkData, droneId);
            }
            if (batteryData != null) {
                LhBatteryProcessor.droneStatusSet(status, batteryData);
            }

            return status;
        }

        /**
         * 检查数据是否过期（超过30秒未更新）
         */
        boolean isExpired() {
            return System.currentTimeMillis() - lastUpdateTime > 30_000;
        }
    }

    /**
     * 清理过期的设备数据
     */
    private void cleanupExpiredData() {
        try {
            int removedCount = 0;
            for (String droneId : droneDataMap.keySet()) {
                DroneDataCache cache = droneDataMap.get(droneId);
                if (cache != null && cache.isExpired()) {
                    droneDataMap.remove(droneId);
                    removedCount++;
                }
            }
            if (removedCount > 0) {
                log.debug("清理了{}个过期的联合飞机设备缓存", removedCount);
            }
        } catch (Exception e) {
            log.warn("清理联合飞机设备缓存时发生异常", e);
        }
    }
}
