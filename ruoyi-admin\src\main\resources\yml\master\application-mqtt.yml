mqtt:
  broker: tcp://43.139.159.93:1883
  client-id: flying-${random.uuid}
  username: admin
  password: Expressfly@Mai*de!}
  topics:
    - dji/status
    - thing/product/+/osd
    - dji/response
    - dji/error
    - dji/virtual_stick/response
    - dji/virtual_stick/error
    - dji/livestream/response
    - dji/livestream/status
    # MAVLink主题
    - mavlink/status
    # PX4主题
    - px4/+/status
    - px4/+/response
    - px4/+/error
    - px4/+/mission/status
#    # 联合飞机主题
    - thing/device/+/osd
    - thing/device/+/events
    - thing/device/+/services_reply
    - sys/device/+/status
  redis:
    expire-seconds: 20   # 消息过期时间，单位秒
  # 状态消息处理配置
  state:
    min-update-interval: 1000  # 状态消息最小更新间隔(毫秒)，过滤高频状态消息
  # 响应消息处理配置
  response:
    batch-update-millis: 2000  # 命令响应最小批处理间隔(毫秒)，避免频繁更新状态
  # 虚拟摇杆配置
  virtual-stick:
    expire-seconds: 300  # 虚拟摇杆状态过期时间，单位秒
    min-update-interval: 500  # 虚拟摇杆状态最小更新间隔(毫秒)
