package com.md.flight.djauv.openfeign.fallback;

import com.md.flight.djauv.common.HttpResultResponse;
import com.md.flight.djauv.openfeign.DjaUavApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Component
public class DjaUavApiFallback implements FallbackFactory<DjaUavApiClient> {

    @Override
    public DjaUavApiClient create(Throwable cause) {
        return new DjaUavApiClient() {
            @Override
            public HttpResultResponse importKmzFile(MultipartFile file, String workspaceId, String creator) {
                log.error("导入航线文件失败，workspaceId: {}, creator: {}", workspaceId, creator, cause);
                return HttpResultResponse.error("导入航线文件失败：" + cause.getMessage());
            }

            @Override
            public String exportKmzFile(String lineName) {
                log.error("导出航线文件失败，lineName: {}", lineName, cause);
                return null;
            }
        };
    }
}