package com.md.flight.px4.converter;

import com.alibaba.fastjson2.JSONObject;
import com.md.flight.mavlink.model.DroneStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * PX4状态数据转换器
 * 将PX4原始状态数据转换为统一的DroneStatus格式
 */
@Component
@Slf4j
public class Px4StatusConverter {

    /**
     * 将PX4原始状态数据转换为统一的DroneStatus格式
     *
     * @param px4StatusJson PX4原始状态JSON数据
     * @return 转换后的DroneStatus对象
     */
    public DroneStatus convertToUnifiedFormat(JSONObject px4StatusJson) {
        try {
            DroneStatus status = new DroneStatus();

            // 基础设备信息
            status.setDroneSN(px4StatusJson.getString("droneId"));
            status.setOnline(true); // 能收到消息说明在线
            status.setArmed(px4StatusJson.getBooleanValue("armed"));
            status.setFlightMode(px4StatusJson.getString("flight_mode"));
            status.setTimestamp(System.currentTimeMillis());
            status.setLastUpdateTime(System.currentTimeMillis());

            // 位置信息转换
            convertPositionInfo(px4StatusJson, status);

            // 姿态信息转换
            convertAttitudeInfo(px4StatusJson, status);

            // 电池信息转换
            convertBatteryInfo(px4StatusJson, status);

            // 速度信息转换
            convertSpeedInfo(px4StatusJson, status);

            // GPS/卫星信息转换
            convertGpsInfo(px4StatusJson, status);

            // RTK信息转换
            convertRtkInfo(px4StatusJson, status);

            // 连接状态信息
            convertConnectionInfo(px4StatusJson, status);

            // 任务信息转换（如果存在）
            convertMissionInfo(px4StatusJson, status);

            log.debug("PX4状态数据转换完成: droneId={}", status.getDroneSN());
            return status;

        } catch (Exception e) {
            log.error("PX4状态数据转换失败: {}", e.getMessage(), e);
            throw new RuntimeException("PX4状态数据转换失败", e);
        }
    }

    /**
     * 转换位置信息
     */
    private void convertPositionInfo(JSONObject px4StatusJson, DroneStatus status) {
        JSONObject position = px4StatusJson.getJSONObject("position");
        if (position != null) {
            // 位置坐标
            if (position.containsKey("latitude")) {
                status.setLatitude(new BigDecimal(position.getDoubleValue("latitude")));
            }
            if (position.containsKey("longitude")) {
                status.setLongitude(new BigDecimal(position.getDoubleValue("longitude")));
            }
            if (position.containsKey("absolute_altitude")) {
                status.setAbsoluteAltitude(position.getFloatValue("absolute_altitude"));
                // 将绝对高度作为RTK海拔使用
                status.setRtkAltitude(position.getDoubleValue("absolute_altitude"));
            }
            if (position.containsKey("relative_altitude")) {
                status.setRelativeAltitude(position.getFloatValue("relative_altitude"));
            }
        }
    }

    /**
     * 转换姿态信息
     */
    private void convertAttitudeInfo(JSONObject px4StatusJson, DroneStatus status) {
        JSONObject attitude = px4StatusJson.getJSONObject("attitude");
        if (attitude != null) {
            if (attitude.containsKey("roll")) {
                status.setRoll(attitude.getFloatValue("roll"));
            }
            if (attitude.containsKey("pitch")) {
                status.setPitch(attitude.getFloatValue("pitch"));
            }
            if (attitude.containsKey("yaw")) {
                float yawDegrees = attitude.getFloatValue("yaw");
                // 确保航向角在0-360度范围内
                if (yawDegrees < 0) {
                    yawDegrees += 360;
                }
                status.setHeading(yawDegrees);
            }
        }
    }

    /**
     * 转换电池信息
     */
    private void convertBatteryInfo(JSONObject px4StatusJson, DroneStatus status) {
        JSONObject battery = px4StatusJson.getJSONObject("battery");
        if (battery != null) {
            DroneStatus.BatteryInfo batteryInfo = new DroneStatus.BatteryInfo();

            if (battery.containsKey("remaining")) {
                batteryInfo.setChargeRemainingInPercent(battery.getIntValue("remaining"));
            }
            if (battery.containsKey("voltage")) {
                batteryInfo.setBatteryVoltage(battery.getFloatValue("voltage"));
            }

            // 设置默认值（如果PX4没有提供这些信息）
            Integer remainingPercent = batteryInfo.getChargeRemainingInPercent();
            if (remainingPercent != null && remainingPercent > 0) {
                // 假设标准电池容量为5000mAh（可根据实际情况调整）
                int defaultCapacity = 5000;
                batteryInfo.setTotalCapacity(defaultCapacity);
                batteryInfo.setChargeRemaining((int)(defaultCapacity * remainingPercent / 100.0));
            }

            status.setBatteryInfo(batteryInfo);
        }
    }

    /**
     * 转换速度信息
     */
    private void convertSpeedInfo(JSONObject px4StatusJson, DroneStatus status) {
        JSONObject speed = px4StatusJson.getJSONObject("speed");
        if (speed != null) {
            // 地速
            if (speed.containsKey("ground_speed")) {
                status.setGroundSpeed(speed.getFloatValue("ground_speed"));
            }

            // 空速
            if (speed.containsKey("air_speed")) {
                status.setAirSpeed(speed.getDoubleValue("air_speed"));
            }

            // 垂直速度
            if (speed.containsKey("vertical_speed")) {
                status.setVerticalSpeed(speed.getFloatValue("vertical_speed"));
            }

            // 设置综合速度（使用地速作为主要速度指标）
            if (speed.containsKey("ground_speed")) {
                status.setSpeed(speed.getDoubleValue("ground_speed"));
            }
        }
    }

    /**
     * 转换GPS/卫星信息
     */
    private void convertGpsInfo(JSONObject px4StatusJson, DroneStatus status) {
        JSONObject gpsInfo = px4StatusJson.getJSONObject("gps_info");
        if (gpsInfo != null) {
            if (gpsInfo.containsKey("num_satellites")) {
                status.setGpsSatellites(gpsInfo.getIntValue("num_satellites"));
            }
            if (gpsInfo.containsKey("fix_type")) {
                String fixType = gpsInfo.getString("fix_type");
                // 将PX4的fix_type转换为数字格式
                switch (fixType) {
                    case "NO_FIX":
                        status.setGpsFixType(0);
                        break;
                    case "FIX_2D":
                        status.setGpsFixType(2);
                        break;
                    case "FIX_3D":
                        status.setGpsFixType(3);
                        break;
                    case "FIX_DGPS":
                        status.setGpsFixType(4);
                        break;
                    case "FIX_RTK":
                        status.setGpsFixType(5);
                        break;
                    default:
                        status.setGpsFixType(0);
                        break;
                }
            }
        }
    }

    /**
     * 转换RTK信息
     */
    private void convertRtkInfo(JSONObject px4StatusJson, DroneStatus status) {
        JSONObject gpsInfo = px4StatusJson.getJSONObject("gps_info");
        if (gpsInfo != null && gpsInfo.containsKey("fix_type")) {
            String fixType = gpsInfo.getString("fix_type");

            // 根据GPS定位类型判断RTK状态
            if ("FIX_RTK".equals(fixType)) {
                // RTK固定解，表示RTK正常工作
                status.setRTKEnabled(true);
                status.setRTKConnected(true);
                status.setRTKHealthy(true);

                // 设置RTK卫星数量
                if (gpsInfo.containsKey("num_satellites")) {
                    status.setRtkSatelliteCount(gpsInfo.getIntValue("num_satellites"));
                }
            } else if ("FIX_DGPS".equals(fixType)) {
                // DGPS，可能是RTK浮点解
                status.setRTKEnabled(true);
                status.setRTKConnected(true);
                status.setRTKHealthy(false); // 浮点解，健康状态为false

                if (gpsInfo.containsKey("num_satellites")) {
                    status.setRtkSatelliteCount(gpsInfo.getIntValue("num_satellites"));
                }
            } else {
                // 普通GPS定位，RTK未启用
                status.setRTKEnabled(false);
                status.setRTKConnected(false);
                status.setRTKHealthy(false);
                status.setRtkSatelliteCount(0);
            }
        } else {
            // 没有GPS信息，默认RTK未启用
            status.setRTKEnabled(false);
            status.setRTKConnected(false);
            status.setRTKHealthy(false);
            status.setRtkSatelliteCount(0);
        }
    }

    /**
     * 转换连接状态信息
     */
    private void convertConnectionInfo(JSONObject px4StatusJson, DroneStatus status) {
        String connectionStatus = px4StatusJson.getString("connection_status");
        if ("connected".equals(connectionStatus)) {
            status.setOnline(true);
        } else {
            status.setOnline(false);
        }

        // 设置offboard模式状态
        Boolean offboardActive = px4StatusJson.getBoolean("offboard_active");
        if (offboardActive != null && offboardActive) {
            // 如果offboard模式激活，可以在飞行模式中体现
            if (status.getFlightMode() == null) {
                status.setFlightMode("OFFBOARD");
            }
        }
    }

    /**
     * 转换任务信息（如果存在mission字段）
     */
    private void convertMissionInfo(JSONObject px4StatusJson, DroneStatus status) {
        JSONObject mission = px4StatusJson.getJSONObject("mission");
        if (mission != null) {
            if (mission.containsKey("current_waypoint")) {
                status.setCurrentWaypoint(mission.getIntValue("current_waypoint"));
            }
            if (mission.containsKey("total_waypoints")) {
                status.setTotalWaypoints(mission.getIntValue("total_waypoints"));
            }
            if (mission.containsKey("mission_status")) {
                status.setMissionStatus(mission.getString("mission_status"));
            }
        }
    }

    /**
     * 验证转换后的数据是否有效
     */
    public boolean validateConvertedData(DroneStatus status) {
        if (status == null) {
            return false;
        }

        // 检查必要字段
        if (status.getDroneSN() == null || status.getDroneSN().trim().isEmpty()) {
            log.warn("转换后的DroneStatus缺少droneId");
            return false;
        }

        return true;
    }
}
