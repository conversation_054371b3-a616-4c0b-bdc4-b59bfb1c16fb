package com.md.domain.bo;

import com.md.domain.po.FlightTaskPoint;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 航点业务对象
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = FlightTaskPoint.class, reverseConvertGenerate = false)
public class FlightTaskPointBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 航点ID
     */
    @NotBlank(message = "航点ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 任务ID
     */
    @NotBlank(message = "任务ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String taskId;

    /**
     * 航点编号(序号)
     */
    @NotNull(message = "航点编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer pointIndex;

    /**
     * 航点类型(0:普通航点 1:起飞点 2:降落点)
     */
    @NotNull(message = "航点类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer pointType;

    /**
     * 纬度
     */
    @NotNull(message = "纬度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal lat;

    /**
     * 经度
     */
    @NotNull(message = "经度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal lng;

    /**
     * 高度(m)
     */
    @NotNull(message = "高度不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal alt;

    /**
     * 高度类型(0:相对高度 1:绝对高度)
     */
    private Integer altitudeType;

    /**
     * 航点动作列表
     */
    @Valid
    private List<FlightTaskPointActionBo> actions;

    /**
     * 停留时间(s)
     */
    private Integer stayTime;

    /**
     * 到达速度(m/s)
     */
    private BigDecimal arrivalSpeed;

    /**
     * 转弯半径(m)
     */
    private BigDecimal turnRadius;

    /**
     * 转弯模式(0:顺时针 1:逆时针)
     */
    private Integer turnMode;

    /**
     * 云台俯仰角度(度)
     */
    private BigDecimal gimbalPitch;

    /**
     * 云台航向角度(度)
     */
    private BigDecimal gimbalYaw;
} 