<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.flight.yz.mapper.YzFlightRouteMapper">

    <resultMap type="com.md.flight.yz.domain.po.YzFlightRoute" id="YhFlightRouteResult">
        <result property="id"    column="id"    />
        <result property="airLineId"    column="air_line_id"    />
        <result property="takeOffApronLongitude"    column="take_off_apron_longitude"    />
        <result property="takeoffApronCode"    column="take_off_apron_code"    />
        <result property="takeOffApronLatitude"    column="take_off_apron_latitude"    />
        <result property="takeoffApronName"    column="take_off_apron_name"    />
        <result property="takeOffApronAltitude"    column="take_off_apron_altitude"    />
        <result property="landApronCode"    column="land_apron_code"    />
        <result property="landApronName"    column="land_apron_name"    />
        <result property="landApronLatitude"    column="land_apron_latitude"    />
        <result property="landApronLongitude"    column="land_apron_longitude"    />
        <result property="landApronAltitude"    column="land_apron_altitude"    />
        <result property="status"    column="status"    />
        <result property="batch"    column="batch"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="syncTime"    column="sync_time"    />
    </resultMap>

    <sql id="selectYhFlightRouteVo">
        select id, air_line_id, take_off_apron_longitude, take_off_apron_code, take_off_apron_latitude, take_off_apron_name, take_off_apron_altitude, land_apron_code, land_apron_name, land_apron_latitude, land_apron_longitude, land_apron_altitude, status, update_time, create_time, sync_time,batch from yz_flight_route
    </sql>

    <select id="selectYhFlightRouteList" parameterType="com.md.flight.yz.domain.po.YzFlightRoute" resultMap="YhFlightRouteResult">
        <include refid="selectYhFlightRouteVo"/>
        <where>
            <if test="airLineId != null  and airLineId != ''"> and air_line_id = #{airLineId}</if>
            <if test="takeOffApronLongitude != null "> and take_off_apron_longitude = #{takeOffApronLongitude}</if>
            <if test="takeoffApronCode != null  and takeoffApronCode != ''"> and take_off_apron_code = #{takeoffApronCode}</if>
            <if test="takeOffApronLatitude != null "> and take_off_apron_latitude = #{takeOffApronLatitude}</if>
            <if test="takeoffApronName != null  and takeoffApronName != ''"> and take_off_apron_name like concat('%', #{takeoffApronName}, '%')</if>
            <if test="takeOffApronAltitude != null "> and take_off_apron_altitude = #{takeOffApronAltitude}</if>
            <if test="landApronCode != null  and landApronCode != ''"> and land_apron_code = #{landApronCode}</if>
            <if test="landApronName != null  and landApronName != ''"> and land_apron_name like concat('%', #{landApronName}, '%')</if>
            <if test="landApronLatitude != null "> and land_apron_latitude = #{landApronLatitude}</if>
            <if test="landApronLongitude != null "> and land_apron_longitude = #{landApronLongitude}</if>
            <if test="landApronAltitude != null "> and land_apron_altitude = #{landApronAltitude}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="syncTime != null "> and sync_time = #{syncTime}</if>
        </where>
    </select>

    <select id="selectYhFlightRouteById" parameterType="Long" resultMap="YhFlightRouteResult">
        <include refid="selectYhFlightRouteVo"/>
        where id = #{id}
    </select>

    <insert id="insertYhFlightRoute" parameterType="com.md.flight.yz.domain.po.YzFlightRoute" useGeneratedKeys="true" keyProperty="id">
        insert into yz_flight_route
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="airLineId != null">air_line_id,</if>
            <if test="takeOffApronLongitude != null">take_off_apron_longitude,</if>
            <if test="takeoffApronCode != null">take_off_apron_code,</if>
            <if test="takeOffApronLatitude != null">take_off_apron_latitude,</if>
            <if test="takeoffApronName != null">take_off_apron_name,</if>
            <if test="takeOffApronAltitude != null">take_off_apron_altitude,</if>
            <if test="landApronCode != null">land_apron_code,</if>
            <if test="landApronName != null">land_apron_name,</if>
            <if test="landApronLatitude != null">land_apron_latitude,</if>
            <if test="landApronLongitude != null">land_apron_longitude,</if>
            <if test="landApronAltitude != null">land_apron_altitude,</if>
            <if test="batch != null">batch,</if>
            <if test="status != null">status,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="syncTime != null">sync_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="airLineId != null">#{airLineId},</if>
            <if test="takeOffApronLongitude != null">#{takeOffApronLongitude},</if>
            <if test="takeoffApronCode != null">#{takeoffApronCode},</if>
            <if test="takeOffApronLatitude != null">#{takeOffApronLatitude},</if>
            <if test="takeoffApronName != null">#{takeoffApronName},</if>
            <if test="takeOffApronAltitude != null">#{takeOffApronAltitude},</if>
            <if test="landApronCode != null">#{landApronCode},</if>
            <if test="landApronName != null">#{landApronName},</if>
            <if test="landApronLatitude != null">#{landApronLatitude},</if>
            <if test="landApronLongitude != null">#{landApronLongitude},</if>
            <if test="landApronAltitude != null">#{landApronAltitude},</if>
            <if test="batch != null">#{batch},</if>
            <if test="status != null">#{status},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="syncTime != null">#{syncTime},</if>
        </trim>
    </insert>


    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into yz_flight_route (
        air_line_id, take_off_apron_longitude, take_off_apron_code, take_off_apron_latitude,
        take_off_apron_name, take_off_apron_altitude, land_apron_code, land_apron_name,
        land_apron_latitude, land_apron_longitude, land_apron_altitude, status, batch, update_time,
        create_time, sync_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.airLineId,jdbcType=VARCHAR}, #{item.takeOffApronLongitude,jdbcType=DOUBLE},
            #{item.takeoffApronCode,jdbcType=VARCHAR}, #{item.takeOffApronLatitude,jdbcType=DOUBLE},
            #{item.takeoffApronName,jdbcType=VARCHAR}, #{item.takeOffApronAltitude,jdbcType=INTEGER},
            #{item.landApronCode,jdbcType=VARCHAR}, #{item.landApronName,jdbcType=VARCHAR},
            #{item.landApronLatitude,jdbcType=DOUBLE}, #{item.landApronLongitude,jdbcType=DOUBLE},
            #{item.landApronAltitude,jdbcType=INTEGER}, #{item.status,jdbcType=TINYINT}, #{item.batch,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.syncTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>


</mapper>