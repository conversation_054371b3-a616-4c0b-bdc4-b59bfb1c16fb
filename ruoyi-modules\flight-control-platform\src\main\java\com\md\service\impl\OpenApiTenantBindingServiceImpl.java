package com.md.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.md.domain.bo.OpenApiTenantBindingBo;
import com.md.domain.po.OpenApiTenantBinding;
import com.md.domain.vo.OpenApiTenantBindingVo;
import com.md.mapper.OpenApiTenantBindingMapper;
import com.md.service.IOpenApiTenantBindingService;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.domain.bo.SysTenantBo;
import org.dromara.system.domain.vo.SysTenantVo;
import org.dromara.system.service.ISysTenantService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 开放端接口租户绑定关系服务实现
 */
@RequiredArgsConstructor
@Service
public class OpenApiTenantBindingServiceImpl implements IOpenApiTenantBindingService {

    private final OpenApiTenantBindingMapper baseMapper;
    private final Converter converter;
    private final ISysTenantService sysTenantService;


    /**
     * 查询租户绑定分页列表
     */
    @Override
    public TableDataInfo<OpenApiTenantBindingVo> queryPageList(OpenApiTenantBindingBo bo, PageQuery pageQuery) {
        // 获取当前租户ID
        String currentTenantId = TenantHelper.getTenantId();
        // 判断当前用户是否为超级管理员
        boolean isAdmin = LoginHelper.isTenantAdmin() || LoginHelper.isSuperAdmin();

        List<SysTenantVo> tenantList = new ArrayList<>();

        // 如果是管理员，获取所有租户信息
        if (isAdmin) {
            tenantList = sysTenantService.queryList(new SysTenantBo());
        } else {
            // 如果不是管理员，只获取当前租户信息
            SysTenantBo queryBo = new SysTenantBo();
            queryBo.setTenantId(currentTenantId);
            tenantList = sysTenantService.queryList(queryBo);
        }

        // 对每个租户，检查是否已有绑定记录，没有则创建
        for (SysTenantVo tenant : tenantList) {
            String tenantId = tenant.getTenantId();
            String companyName = tenant.getCompanyName();

            // 构建查询条件，查找与当前租户和API的绑定关系
            LambdaQueryWrapper<OpenApiTenantBinding> existQuery = Wrappers.lambdaQuery();
            existQuery.eq(OpenApiTenantBinding::getBindTenantId, tenantId)
                .eq(OpenApiTenantBinding::getApiId, bo.getApiId());

            // 判断是否存在记录，不存在则新增
            List<OpenApiTenantBinding> existingBindings = baseMapper.selectList(existQuery);
            if (existingBindings.isEmpty()) {
                OpenApiTenantBindingBo bindingBo = new OpenApiTenantBindingBo();
                bindingBo.setBindTenantId(tenantId);
                bindingBo.setCompanyName(companyName);
                bindingBo.setApiId(bo.getApiId());
                // 插入新记录（默认字段由insertByBo方法处理）
                insertByBo(bindingBo);
            }
        }

        // 构建查询条件，根据API ID
        LambdaQueryWrapper<OpenApiTenantBinding> lqw = buildQueryWrapper(bo);
        Page<OpenApiTenantBindingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询租户绑定列表
     */
    @Override
    public List<OpenApiTenantBindingVo> queryList(OpenApiTenantBindingBo bo) {
        LambdaQueryWrapper<OpenApiTenantBinding> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增租户绑定
     */
    @Override
    public Boolean insertByBo(OpenApiTenantBindingBo bo) {
        OpenApiTenantBinding add = converter.convert(bo, OpenApiTenantBinding.class);
        // 设置默认值
        if (StringUtils.isBlank(add.getStatus())) {
            add.setStatus("0"); // 默认为生效状态
        }
        if (StringUtils.isBlank(add.getTargetTenantId())) {
            add.setTargetTenantId(add.getTenantId()); // 默认目标租户ID与源租户ID相同
        }
        if (StringUtils.isBlank(add.getConfigInfo())) {
            add.setConfigInfo(""); // 默认为空字符串
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改租户绑定
     */
    @Override
    public Boolean updateByBo(OpenApiTenantBindingBo bo) {
        OpenApiTenantBinding update = converter.convert(bo, OpenApiTenantBinding.class);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<OpenApiTenantBinding> buildQueryWrapper(OpenApiTenantBindingBo bo) {
        LambdaQueryWrapper<OpenApiTenantBinding> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getApiId() != null, OpenApiTenantBinding::getApiId, bo.getApiId());
        return lqw;
    }

}
