package com.md.flight.lh.enums;

import com.md.flight.lh.processor.*;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.utils.SpringUtils;

/**
 * 联合消息枚举
 */
@Getter
public enum LhReqMassageEnum {
    //设备状态指令
    ONLINE("status_online", SpringUtils.getBean(LhOnlineProcessor.class)), //设备下线
    OFFLINE("status_offline", SpringUtils.getBean(LhOfflineProcessor.class)), //设备心跳
    HEARTBEAT("status_heartbeat", SpringUtils.getBean(LhHeartbeatProcessor.class)), //航线上传响应

    //航线指令响应
    ROUTE_UPLOAD_SERVICES_REPLY("reply_route_upload", SpringUtils.getBean(LhRouteUploadProcessor.class)), //航线上传响应
    ROUTE_EXECUTE("reply_route_execute", SpringUtils.getBean(LhRouteExecuteProcessor.class)),//航线执行响应
    REPLY_ROUTE_PAUSE("reply_route_pause", SpringUtils.getBean(LhRoutePauseProcessor.class)),//航线暂停命令响应
    REPLY_ROUTE_RESUME("reply_route_resume", SpringUtils.getBean(LhRouteResumeProcessor.class)),//航线继续命令响应

    //起飞指令响应
    TAKEOFF("reply_takeoff", SpringUtils.getBean(LhTakeoffProcessor.class)), //起飞指令响应
    LAND("reply_land", SpringUtils.getBean(LhLandProcessor.class)), //降落指令响应
    GO_HOME("reply_go_home", SpringUtils.getBean(LhGoHomeProcessor.class)),//返航指令响应

    //基础数据响应
    OSD("osd_osd", SpringUtils.getBean(LhOsdProcessor.class)), //无人机基础数据
    BATTERY("osd_battery", SpringUtils.getBean(LhBatteryProcessor.class)),//无人机电池数据

    //执行命令响应
    ARRIVAL("events_arrival", SpringUtils.getBean(LhArrivalProcessor.class)),//无人机电池数据

    //数据上报响应
    MOUNT("osd_mount", SpringUtils.getBean(LhMountProcessor.class)),//无人机负载数据
    RTK("osd_rtk", SpringUtils.getBean(LhRtkProcessor.class)),//无人机rtk数据
    ;

    /**
     * 方法名
     */
    private String name;

    /**
     * 飞控同步实现类
     */
    private LhBaseProcessor baseInfoService;

    LhReqMassageEnum(String name, LhBaseProcessor iBaseInfoService) {
        this.name = name;
        this.baseInfoService = iBaseInfoService;
    }

    /**
     * 根据方法名获取消息实现类
     *
     * @param name
     * @return
     */
    public static LhBaseProcessor getProcessor(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        for (LhReqMassageEnum baseInfoSyncEnum : LhReqMassageEnum.values()) {
            if (baseInfoSyncEnum.name.equals(name)) {
                return baseInfoSyncEnum.getBaseInfoService();
            }
        }
        return null;
    }
}
