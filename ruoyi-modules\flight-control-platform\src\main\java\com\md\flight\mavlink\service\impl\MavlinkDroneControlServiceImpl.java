package com.md.flight.mavlink.service.impl;

import com.md.flight.mavlink.service.MavlinkDroneControlService;
import io.dronefleet.mavlink.common.GlobalPositionInt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * MAVLink无人机控制服务实现
 */
@Slf4j
@Service
public class MavlinkDroneControlServiceImpl implements MavlinkDroneControlService {

    @Autowired
    private MavlinkCoreService coreService;

    @Override
    public void arm(String droneId, boolean force) {
        coreService.arm(droneId, force);
    }

    @Override
    public void disarm(String droneId) {
        coreService.disarm(droneId);
    }

    @Override
    public void takeoff(String droneId, float altitude) {
        coreService.takeoff(droneId, altitude);
    }

    @Override
    public void land(String droneId) {
        coreService.land(droneId);
    }

    @Override
    public void returnToHome(String droneId) {
        coreService.returnToHome(droneId);
    }

    @Override
    public void setMode(String droneId, int mode) {
        coreService.setMode(droneId, mode);
    }

    @Override
    public void flyTo(String droneId, BigDecimal latitude, BigDecimal longitude, BigDecimal altitude, float speed) {
        coreService.flyTo(droneId, latitude, longitude, altitude, speed);
    }

    @Override
    public GlobalPositionInt getCurrentPosition(String droneId) {
        return coreService.getCurrentPosition(droneId);
    }

    @Override
    public Map<String, Object> getFormattedPositionData(String droneId) {
        return coreService.getFormattedPositionData(droneId);
    }

    @Override
    public void moveRelativeToHeading(String droneId, float vForward, float vRight, float vUp, float duration) {
        coreService.moveRelativeToHeading(droneId, vForward, vRight, vUp, duration);
    }

    @Override
    public void setYaw(String droneId, float yawAngle, float angularSpeed, boolean isRelative) {
        coreService.setYaw(droneId, yawAngle, angularSpeed, isRelative);
    }

    @Override
    public void controlGimbal(String droneId, float pitch, float yaw, boolean isAbsolute) {
        coreService.controlGimbal(droneId, pitch, yaw, isAbsolute);
    }

    @Override
    public void takePhoto(String droneId) {
        coreService.takePhoto(droneId);
    }

    @Override
    public void startContinuousPhotos(String droneId, float interval, int count) {
        coreService.startContinuousPhotos(droneId, interval, count);
    }

    @Override
    public void stopContinuousPhotos(String droneId) {
        coreService.stopContinuousPhotos(droneId);
    }

    @Override
    public void startVideoRecording(String droneId) {
        coreService.startVideoRecording(droneId);
    }

    @Override
    public void stopVideoRecording(String droneId) {
        coreService.stopVideoRecording(droneId);
    }
}
