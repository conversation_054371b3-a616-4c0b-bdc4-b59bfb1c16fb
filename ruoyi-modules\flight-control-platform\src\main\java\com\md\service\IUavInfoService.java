package com.md.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.md.domain.bo.UavInfoBo;
import com.md.domain.po.UavInfo;
import com.md.domain.vo.UavInfoVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 无人机Service接口
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
public interface IUavInfoService extends IService<UavInfo> {
    /**
     * 查询无人机
     *
     * @param id 无人机主键
     * @return 无人机
     */
    public UavInfoVo selectFkUavInfoById(Long id);

    /**
     * 查询无人机列表
     *
     * @param bo 无人机
     * @return 无人机集合
     */
    public List<UavInfoVo> selectFkUavInfoList(UavInfoBo bo);

    /**
     * 分页查询无人机列表
     *
     * @param bo 无人机
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<UavInfoVo> selectUavInfoPage(UavInfoBo bo, PageQuery pageQuery);

    /**
     * 新增无人机
     *
     * @param bo 无人机
     * @return 结果
     */
    public int insertFkUavInfo(UavInfoBo bo);

    /**
     * 修改无人机
     *
     * @param bo 无人机
     * @return 结果
     */
    public int updateFkUavInfo(UavInfoBo bo);

    /**
     * 批量删除无人机
     *
     * @param ids 需要删除的无人机主键集合
     * @return 结果
     */
    public int deleteFkUavInfoByIds(Long[] ids);

    /**
     * 删除无人机信息
     *
     * @param id 无人机主键
     * @return 结果
     */
    public int deleteFkUavInfoById(Long id);

    /**
     * 同步无人机信息
     *
     * @param code
     * @return
     */
    boolean syncUAV(String code);
}