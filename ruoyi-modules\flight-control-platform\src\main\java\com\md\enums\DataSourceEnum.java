package com.md.enums;

import lombok.Getter;


@Getter
public enum DataSourceEnum {
    HANDLE("手动添加", "HANDLE","0"), SYNC("同步接口获取", "SYNC","1");

    // 成员变量
    private String name;
    private String code;
    private String value;

    DataSourceEnum(String name, String code , String value) {
        this.name = name;
        this.code = code;
        this.value = value;
    }

    public static String getName(String code) {
        for (DataSourceEnum c : DataSourceEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    public static DataSourceEnum parseEnum(String code) {
        for (DataSourceEnum dataSourceEnum : DataSourceEnum.values()) {
            if (dataSourceEnum.getCode().equals(code)) {
                return dataSourceEnum;
            }
        }
        return null;
    }
}
