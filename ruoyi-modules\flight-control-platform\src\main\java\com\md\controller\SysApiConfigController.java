package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.domain.bo.SysApiConfigBo;
import com.md.domain.bo.SysApiTenantBindingBo;
import com.md.domain.dto.ConfigInfo;
import com.md.domain.dto.EncryInfo;
import com.md.domain.po.SysApiConfig;
import com.md.domain.vo.SysApiConfigVo;
import com.md.domain.vo.SysApiTenantBindingVo;
import com.md.service.ISysApiConfigService;
import com.md.service.ISysApiTenantBindingService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 调用端接口管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/apiConfig")
public class SysApiConfigController extends BaseController {

    private final ISysApiConfigService apiConfigService;
    private final ISysApiTenantBindingService apiTenantBindingService;

    /**
     * 查询接口管理列表
     */
    @GetMapping("/list")
    @SaCheckPermission("system:apiConfig:list")
    public TableDataInfo<SysApiConfigVo> list(SysApiConfigBo bo, PageQuery pageQuery) {
        return apiConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取接口管理详细信息
     */
    @GetMapping("/{id}")
    @SaCheckPermission("system:apiConfig:query")
    public R<SysApiConfigVo> getInfo(@PathVariable("id") Long id) {
        return R.ok(apiConfigService.queryById(id));
    }

    /**
     * 新增接口管理
     */
    @Log(title = "接口管理", businessType = BusinessType.INSERT)
    @PostMapping
    @SaCheckPermission("system:apiConfig:add")
    public R<Void> add(@Validated @RequestBody SysApiConfigBo bo) {
        handleEncryptInfo(bo);
        // 检查目标平台是否重复（排除当前ID）
        SysApiConfig existConfig = apiConfigService.checkTargetPlatformExists(bo.getTargetPlatform(), bo.getId());
        if (existConfig != null) {
            return R.fail("目标平台 [" + bo.getTargetPlatform() + "] 已存在，请使用其他名称");
        }

        return toAjax(apiConfigService.insertByBo(bo));
    }

    /**
     * 修改接口管理
     */
    @Log(title = "接口管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @SaCheckPermission("system:apiConfig:edit")
    public R<Void> edit(@Validated @RequestBody SysApiConfigBo bo) {
        handleEncryptInfo(bo);
        // 检查目标平台是否重复（排除当前ID）
        SysApiConfig existConfig = apiConfigService.checkTargetPlatformExists(bo.getTargetPlatform(), bo.getId());
        if (existConfig != null) {
            return R.fail("目标平台 [" + bo.getTargetPlatform() + "] 已存在，请使用其他名称");
        }
        return toAjax(apiConfigService.updateByBo(bo));
    }

    /**
     * 删除接口管理（将状态改为停用）
     */
    @Log(title = "接口管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @SaCheckPermission("system:apiConfig:remove")
    public R<Void> remove(@PathVariable Long id) {
        return toAjax(apiConfigService.deleteWithValidByIds(id));
    }

    /**
     * 根据接口Id获取绑定租户信息列表
     */
    @GetMapping("/binding/list/{apiId}")
    @SaCheckPermission("system:apiConfig:binding:list")
    public TableDataInfo<SysApiTenantBindingVo> bindingList(@PathVariable("apiId") @NotNull Long apiId,
        PageQuery pageQuery) {
        SysApiTenantBindingBo bo = new SysApiTenantBindingBo();
        bo.setApiId(apiId);
        return apiTenantBindingService.queryPageList(bo, pageQuery);
    }

    /**
     * 修改租户绑定
     */
    @Log(title = "租户绑定", businessType = BusinessType.UPDATE)
    @PutMapping("/binding")
    @SaCheckPermission("system:apiConfig:binding:edit")
    public R<Void> editBinding(@Validated @RequestBody SysApiTenantBindingBo bo) {
        handleConfigInfo(bo);
        // 处理密钥配置信息转换
        R<Void> result = handleConfigInfo(bo);
        if (result != null) {
            return result;
        }
        return toAjax(apiTenantBindingService.updateByBo(bo));
    }

    /**
     * 处理密钥配置信息转换
     */
    private R<Void> handleEncryptInfo(SysApiConfigBo bo) {
        String encryptInfoStr = bo.getEncryptInfo();
        if (StringUtils.isNotBlank(encryptInfoStr)) {
            try {
                EncryInfo encryInfo = JsonUtils.parseObject(encryptInfoStr, EncryInfo.class);
                if (encryInfo != null) {
                    String formattedEncryptInfo = JsonUtils.toJsonString(encryInfo);
                    bo.setEncryptInfo(formattedEncryptInfo);
                }
            } catch (Exception e) {
                return R.fail("密钥配置信息格式不正确");
            }
        }
        return null;
    }

    /**
     * 处理密钥配置信息转换
     */
    private R<Void> handleConfigInfo(SysApiTenantBindingBo bo) {
        String encryptInfoStr = bo.getConfigInfo();
        if (StringUtils.isNotBlank(encryptInfoStr)) {
            try {
                // 将String转换为ConfigInfo对象
                ConfigInfo configInfo = JsonUtils.parseObject(encryptInfoStr, ConfigInfo.class);
                if (configInfo != null) {
                    // 将ConfigInfo对象转换回标准格式的JSON字符串
                    String formattedEncryptInfo = JsonUtils.toJsonString(configInfo);
                    bo.setConfigInfo(formattedEncryptInfo);
                }
            } catch (Exception e) {
                return R.fail("密钥配置信息格式不正确");
            }
        }
        return null;
    }

}
