package org.dromara.common.mybatis.core.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import org.dromara.common.core.utils.StringUtils;

/**
 * 查询辅助工具类
 * 主要用于构建常用的查询条件
 *
 * <AUTHOR>
 */
public class QueryUtils {

    /**
     * 根据用户名称构建查询用户ID的SQL条件
     *
     * @param wrapper         查询包装器
     * @param fieldGetterFunc 实体类字段获取函数
     * @param username        用户名称
     * @param <T>             实体类类型
     * @param <R>             字段类型
     * @return 更新后的查询包装器
     */
    public static <T, R> LambdaQueryWrapper<T> buildUserNameQuery(
        LambdaQueryWrapper<T> wrapper,
        SFunction<T, R> fieldGetterFunc,
        String username) {

        if (StringUtils.isNotEmpty(username)) {
            wrapper.inSql(fieldGetterFunc,
                "SELECT user_id FROM sys_user WHERE user_name LIKE CONCAT('%', '" + username + "', '%')");
        }
        return wrapper;
    }

    /**
     * 根据用户名称构建查询用户ID的SQL条件 (普通QueryWrapper版本)
     *
     * @param wrapper      查询包装器
     * @param fieldName    字段名称
     * @param username     用户名称
     * @param <T>          实体类类型
     * @return 更新后的查询包装器
     */
    public static <T> QueryWrapper<T> buildUserNameQuery(
        QueryWrapper<T> wrapper,
        String fieldName,
        String username) {

        if (StringUtils.isNotEmpty(username)) {
            wrapper.inSql(fieldName,
                "SELECT user_id FROM sys_user WHERE user_name LIKE CONCAT('%', '" + username + "', '%')");
        }
        return wrapper;
    }

    /**
     * 构建创建者查询条件
     *
     * @param wrapper      查询包装器
     * @param createByFunc 创建者字段获取函数
     * @param createByName 创建者名称
     * @param <T>          实体类类型
     * @return 更新后的查询包装器
     */
    public static <T> LambdaQueryWrapper<T> buildCreateByQuery(
        LambdaQueryWrapper<T> wrapper,
        SFunction<T, ?> createByFunc,
        String createByName) {

        return buildUserNameQuery(wrapper, createByFunc, createByName);
    }

    /**
     * 构建更新者查询条件
     *
     * @param wrapper      查询包装器
     * @param updateByFunc 更新者字段获取函数
     * @param updateByName 更新者名称
     * @param <T>          实体类类型
     * @return 更新后的查询包装器
     */
    public static <T> LambdaQueryWrapper<T> buildUpdateByQuery(
        LambdaQueryWrapper<T> wrapper,
        SFunction<T, ?> updateByFunc,
        String updateByName) {

        return buildUserNameQuery(wrapper, updateByFunc, updateByName);
    }
}