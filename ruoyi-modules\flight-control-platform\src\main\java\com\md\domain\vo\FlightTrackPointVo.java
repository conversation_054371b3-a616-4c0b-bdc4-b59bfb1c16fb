package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.md.mongodb.document.FlightTrackPoint;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 飞行轨迹点视图对象
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FlightTrackPoint.class)
public class FlightTrackPointVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 任务ID
     */
    @ExcelProperty(value = "任务ID")
    private String taskId;
    
    /**
     * 无人机ID
     */
    @ExcelProperty(value = "无人机ID")
    private String droneId;
    
    /**
     * 时间戳，记录数据采集时间
     */
    @ExcelProperty(value = "时间戳")
    private Long timestamp;
    
    /**
     * 纬度，单位：度
     */
    @ExcelProperty(value = "纬度")
    private Double latitude;
    
    /**
     * 经度，单位：度
     */
    @ExcelProperty(value = "经度")
    private Double longitude;
    
    /**
     * 海拔高度，单位：米
     */
    @ExcelProperty(value = "海拔高度")
    private Double altitude;
    
    /**
     * 相对高度（相对起飞点），单位：米
     */
    @ExcelProperty(value = "相对高度")
    private Double relativeHeight;
    
    /**
     * 速度，单位：米/秒
     */
    @ExcelProperty(value = "速度")
    private Double speed;
    
    /**
     * 空速，单位：米/秒
     */
    @ExcelProperty(value = "空速")
    private Double airSpeed;
    
    /**
     * 地速，单位：米/秒
     */
    @ExcelProperty(value = "地速")
    private Double groundSpeed;
    
    /**
     * 飞行距离，单位：米
     */
    @ExcelProperty(value = "飞行距离")
    private Double flightDistance;
    
    /**
     * 航向角，单位：度，范围0-360
     */
    @ExcelProperty(value = "航向角")
    private Double heading;
    
    /**
     * 俯仰角，单位：度
     */
    @ExcelProperty(value = "俯仰角")
    private Double pitch;
    
    /**
     * 横滚角，单位：度
     */
    @ExcelProperty(value = "横滚角")
    private Double roll;
    
    /**
     * 飞行模式，如：定点模式、航线模式等
     */
    @ExcelProperty(value = "飞行模式")
    private String flightMode;
    
    /**
     * 电池信息
     */
    private BatteryInfo batteryInfo;
    
    /**
     * 任务状态，如：执行中、已完成等
     */
    @ExcelProperty(value = "任务状态")
    private String missionStatus;
    
    /**
     * RTK是否连接
     */
    @ExcelProperty(value = "RTK是否连接")
    private Boolean isRTKConnected;
    
    /**
     * RTK是否启用
     */
    @ExcelProperty(value = "RTK是否启用")
    private Boolean isRTKEnabled;
    
    /**
     * RTK是否健康
     */
    @ExcelProperty(value = "RTK是否健康")
    private Boolean isRTKHealthy;
    
    /**
     * RTK高度
     */
    @ExcelProperty(value = "RTK高度")
    private Double rtkAltitude;
    
    /**
     * RTK卫星数量
     */
    @ExcelProperty(value = "RTK卫星数量")
    private Integer rtkSatelliteCount;
    
    /**
     * 卫星信息
     */
    private SatelliteInfo satelliteInfo;

    /**
     * 电池信息内部类 记录无人机电池的详细状态
     */
    @Data
    public static class BatteryInfo {
        /**
         * 剩余电量，单位：毫安时
         */
        @ExcelProperty(value = "剩余电量(毫安时)")
        private Integer chargeRemaining;
        
        /**
         * 剩余电量百分比，范围：0-100
         */
        @ExcelProperty(value = "剩余电量百分比")
        private Integer chargeRemainingInPercent;
        
        /**
         * 电池总容量，单位：毫安时
         */
        @ExcelProperty(value = "电池总容量")
        private Integer totalCapacity;
    }
    
    /**
     * 卫星信息内部类 记录无人机卫星接收状态
     */
    @Data
    public static class SatelliteInfo {
        /**
         * 基站卫星数量
         */
        @ExcelProperty(value = "基站卫星数量")
        private Integer baseStationCount;
        
        /**
         * GPS卫星数量
         */
        @ExcelProperty(value = "GPS卫星数量")
        private Integer gpsCount;
        
        /**
         * 移动站1卫星数量
         */
        @ExcelProperty(value = "移动站1卫星数量")
        private Integer mobileStation1Count;
        
        /**
         * 移动站2卫星数量
         */
        @ExcelProperty(value = "移动站2卫星数量")
        private Integer mobileStation2Count;
    }
} 