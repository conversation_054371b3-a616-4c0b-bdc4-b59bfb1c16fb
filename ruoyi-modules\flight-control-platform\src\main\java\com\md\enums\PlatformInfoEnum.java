package com.md.enums;

import lombok.Getter;

/**
 * 飞控平台枚举
 */
@Getter
public enum PlatformInfoEnum {
    YZ("邮政", "YZ"),
    YX("亿迅", "YX"),
    MD("迈得", "MD"),
    LH("联合", "LH"),
    DJI("大疆", "DJI"),
    PX4("PX4", "PX4"),
    MAVLINK("MAVLink", "MAVLINK"),
    ;

    // 成员变量
    private String name;
    private String code;

    // 构造方法
    private PlatformInfoEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    // 普通方法
    public static String getName(String code) {
        for (PlatformInfoEnum c : PlatformInfoEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    //获取枚举
    public static PlatformInfoEnum parseEnum(String code) {
        for (PlatformInfoEnum platformInfoEnum : PlatformInfoEnum.values()) {
            if (platformInfoEnum.getCode().equals(code)) {
                return platformInfoEnum;
            }
        }
        return null;
    }
}
