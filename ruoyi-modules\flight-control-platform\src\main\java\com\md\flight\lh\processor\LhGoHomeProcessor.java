package com.md.flight.lh.processor;

import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.md.flight.lh.domain.dto.LhResponse;
import com.md.flight.lh.service.impl.LhDroneControlServiceImpl;

/**
 * 联合返航指令响应处理器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LhGoHomeProcessor implements LhBaseProcessor {
    
    private final LhDroneControlServiceImpl lhDroneControlService;

    @Override
    public void processMessage(String payload) {
        log.info("收到联合返航指令响应: {}", payload);
        try {
            // 解析响应
            LhResponse response = JSON.parseObject(payload, LhResponse.class);
            // 获取bid
            String bid = response.getBid();
            // 通知等待的线程继续执行
            lhDroneControlService.notifyResponse(bid);
        } catch (Exception e) {
            log.error("处理联合返航指令响应失败: {}", e.getMessage(), e);
        }
    }
} 