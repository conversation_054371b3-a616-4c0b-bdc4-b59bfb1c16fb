package com.md.flight.yx.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.md.domain.po.PlatformInfo;
import com.md.enums.PlatformInfoEnum;
import com.md.flight.yx.domain.dto.YxRequsetResult;
import com.md.flight.yx.domain.po.YxFlightRoute;
import com.md.flight.yx.mapper.YxFlightRouteMapper;
import com.md.flight.yx.model.task.RoutePoineTaskRequest;
import com.md.flight.yx.openfeign.UavApiClient;
import com.md.flight.yx.service.IYxFlightRouteService;
import com.md.flight.yx.util.SignUtil;
import com.md.flight.yx.util.YxAESUtil;
import com.md.service.IPlatformInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 亿讯航线Service
 */
@Service
@RequiredArgsConstructor
public class YxFlightRouteServiceImpl extends ServiceImpl<YxFlightRouteMapper, YxFlightRoute> implements IYxFlightRouteService {

    private final UavApiClient uavApiClient;

    private final IPlatformInfoService platformInfoService;

    /**
     * 获取航线列表
     *
     * @return
     */
    @Override
    public List<YxFlightRoute> getLineList() {
        String timestamp = SignUtil.getTimeStampStr();
        PlatformInfo platform = platformInfoService.getOne(Wrappers.<PlatformInfo>lambdaQuery().eq(PlatformInfo::getFlightControlNo, PlatformInfoEnum.YX.getCode()));
        String sign = SignUtil.sign(platform.getPassword(), timestamp, "");

        String encrypt = YxAESUtil.encrypt("", platform.getPassword());
        String interfaceResult = uavApiClient.getLineList(encrypt, timestamp, sign, platform.getAccount());
        YxRequsetResult requsetResult = JSON.parseObject(interfaceResult, YxRequsetResult.class);
        String decrypt = YxAESUtil.decrypt(requsetResult.getResult(), platform.getPassword());
        List<YxFlightRoute> yxFlightRoutes = JSON.parseArray(decrypt, YxFlightRoute.class);
        if (yxFlightRoutes == null || yxFlightRoutes.size() == 0) {
            throw new RuntimeException("获取航线列表失败");
        }
        return yxFlightRoutes;
    }

    @Override
    public YxFlightRoute getLineDetail(RoutePoineTaskRequest routePoineTaskRequest) {
        PlatformInfo platform = platformInfoService.getOne(Wrappers.<PlatformInfo>lambdaQuery().eq(PlatformInfo::getFlightControlNo, PlatformInfoEnum.YX.getCode()));
        String timestamp = String.valueOf(System.currentTimeMillis());
        String secretParam = YxAESUtil.encrypt(JSON.toJSONString(routePoineTaskRequest), platform.getPassword());
        String sign = SignUtil.sign(platform.getPassword(), timestamp, JSON.toJSONString(routePoineTaskRequest));

        String interfaceResult = uavApiClient.getLineDatail(secretParam, timestamp, sign, platform.getAccount());
        if (interfaceResult == null) {
            throw new RuntimeException("获取航线详情失败");
        }

        YxRequsetResult requsetResult = JSON.parseObject(interfaceResult, YxRequsetResult.class);
        String decrypt = YxAESUtil.decrypt(requsetResult.getResult(), platform.getPassword());
        YxFlightRoute flightRoute = JSON.parseObject(decrypt, YxFlightRoute.class);

        return flightRoute;
    }
}
