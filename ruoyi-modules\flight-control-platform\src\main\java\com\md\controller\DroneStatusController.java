package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.md.domain.bo.DroneStatusBo;
import com.md.domain.vo.DroneStatusVo;
import com.md.service.IDroneStatusService;
import org.dromara.common.core.domain.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * 无人机状态Controller
 */
@RestController
@RequestMapping("/drone/status")
@Validated
public class DroneStatusController {

    @Autowired
    private IDroneStatusService droneStatusService;

    /**
     * 查询无人机在线状态
     */
    @GetMapping("/online/{droneId}")
    @SaCheckPermission(value = {"drone:status:query", "flight:task:query", "flight:task:execute"}, mode = SaMode.OR)
    public R<Boolean> checkOnline(@PathVariable @NotBlank(message = "无人机ID不能为空") String droneId) {
        return R.ok(droneStatusService.isDroneOnline(droneId));
    }

    /**
     * 获取所有在线无人机
     */
    @GetMapping("/online/list")
    @SaCheckPermission(value = {"drone:status:query", "flight:task:query", "flight:task:execute"}, mode = SaMode.OR)
    public R<List<DroneStatusVo>> getOnlineDrones() {
        return R.ok(droneStatusService.getOnlineDrones());
    }

    /**
     * 批量查询无人机在线状态
     */
    @PostMapping("/online/batch")
    @SaCheckPermission(value = {"drone:status:query", "flight:task:query", "flight:task:execute"}, mode = SaMode.OR)
    public R<Map<String, Boolean>> batchCheckOnline(@Valid @RequestBody DroneStatusBo bo) {
        return R.ok(droneStatusService.getDronesOnlineStatus(bo));
    }

    /**
     * 获取无人机状态数据
     */
    @GetMapping("/{droneId}")
    @SaCheckPermission(value = {"drone:status:query", "flight:task:query", "flight:task:execute"}, mode = SaMode.OR)
    public R<DroneStatusVo> getDroneStatus(@PathVariable @NotBlank(message = "无人机ID不能为空") String droneId) {
        return R.ok(droneStatusService.getDroneStatus(droneId));
    }

    /**
     * 批量获取无人机状态数据
     */
    @PostMapping("/batch")
    @SaCheckPermission(value = {"drone:status:query", "flight:task:query", "flight:task:execute"}, mode = SaMode.OR)
    public R<Map<String, String>> batchGetStatus(@Valid @RequestBody DroneStatusBo bo) {
        return R.ok(droneStatusService.getDronesStatus(bo));
    }

    /**
     * 查询无人机虚拟摇杆状态
     */
    @GetMapping("/virtual-stick/{droneId}")
    @SaCheckPermission(value = {"drone:status:query", "flight:task:query", "flight:task:execute"}, mode = SaMode.OR)
    public R<DroneStatusVo> getVirtualStickStatus(
        @PathVariable @NotBlank(message = "无人机ID不能为空") String droneId) {
        return droneStatusService.getVirtualStickStatus(droneId);
    }
}