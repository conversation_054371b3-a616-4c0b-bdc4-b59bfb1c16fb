package com.md.flight.yx.model.drone;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 无人机信息实体类 包含无人机的基本信息和性能参数
 */
@Data
@TableName("yx_uav_info")
public class DroneInfo {
    /** 无人机ID */
    private Long id;
    /** 无人机名称 */
    private String name;
    /** 品牌机型 */
    private String model;
    /** 序列号 */
    private String sn;
    /** 备案号 */
    private String recordNo;
    /** 负责人 */
    private String owner;
    /** 生产厂家 */
    private String manufacturer;
    /** 生产日期 */
    private Date manufactureDate;
    /** 续航时间(分钟) */
    private Integer endurance;
    /** 最大起飞海拔高度(米) */
    private Integer maxTakeOffAltitude;
    /** 最大工作距离(米) */
    private Integer maxWorkingDistance;
    /** 最大图传距离(米) */
    private Integer maxTransDistance;
    /**批次号*/
    @TableField("batch_id")
    private String batchId;
    /**同步时间*/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("sync_time")
    private Date syncTime;
}