<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.md.mapper.FenceVertexMapper">
    
    <resultMap type="com.md.domain.po.FenceVertex" id="FenceVertexResult">
        <id property="id" column="id"/>
        <result property="fenceAreaId" column="fence_area_id"/>
        <result property="sequence" column="sequence"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
    </resultMap>
    
    <sql id="selectFenceVertexVo">
        select id, fence_area_id, sequence, longitude, latitude
        from fence_vertex
    </sql>
    
    <select id="selectFenceVertexList" parameterType="com.md.domain.po.FenceVertex" resultMap="FenceVertexResult">
        <include refid="selectFenceVertexVo"/>
        <where>
            <if test="fenceAreaId != null and fenceAreaId != ''">
                AND fence_area_id = #{fenceAreaId}
            </if>
        </where>
        order by sequence asc
    </select>
    
    <select id="selectFenceVertexById" parameterType="String" resultMap="FenceVertexResult">
        <include refid="selectFenceVertexVo"/>
        where id = #{id}
    </select>
    
    <select id="selectByAreaId" parameterType="String" resultMap="FenceVertexResult">
        <include refid="selectFenceVertexVo"/>
        where fence_area_id = #{areaId}
        order by sequence asc
    </select>
    
    <insert id="insertFenceVertex" parameterType="com.md.domain.po.FenceVertex">
        insert into fence_vertex (
            id,
            fence_area_id,
            sequence,
            longitude,
            latitude
        ) values (
            #{id},
            #{fenceAreaId},
            #{sequence},
            #{longitude},
            #{latitude}
        )
    </insert>
    
    <insert id="batchInsertVertices" parameterType="java.util.List">
        insert into fence_vertex (
            id,
            fence_area_id,
            sequence,
            longitude,
            latitude
        ) values
        <foreach collection="vertices" item="item" separator=",">
            (
            #{item.id},
            #{item.fenceAreaId},
            #{item.sequence},
            #{item.longitude},
            #{item.latitude}
            )
        </foreach>
    </insert>
    
    <update id="updateFenceVertex" parameterType="com.md.domain.po.FenceVertex">
        update fence_vertex
        <set>
            <if test="sequence != null">sequence = #{sequence},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
        </set>
        where id = #{id}
    </update>
    
    <delete id="deleteFenceVertexById" parameterType="String">
        delete from fence_vertex where id = #{id}
    </delete>
    
    <delete id="deleteFenceVertexByIds" parameterType="String">
        delete from fence_vertex where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteByAreaId" parameterType="String">
        delete from fence_vertex where fence_area_id = #{areaId}
    </delete>
    
</mapper> 