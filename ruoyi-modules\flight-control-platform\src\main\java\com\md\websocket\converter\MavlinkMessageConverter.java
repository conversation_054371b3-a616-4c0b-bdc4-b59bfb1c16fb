package com.md.websocket.converter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.websocket.message.DroneMessage;
import com.md.websocket.message.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * MAVLink无人机消息转换器
 */
@Slf4j
@Component
public class MavlinkMessageConverter implements DroneMessageConverter {

    private static final String MANUFACTURER = "MAVLINK";

    @Override
    public DroneMessage convert(String topic, String payload, MessageType messageType) {
        try {
            JSONObject data = JSON.parseObject(payload);
            String droneId = extractDroneId(data);

            // MAVLink数据已经是统一的DroneStatus格式，直接使用
            // 如果需要额外处理，可以在这里添加

            return DroneMessage.builder().type(messageType.name()).droneId(droneId).data(data)  // MAVLink数据已经是统一格式
                .timestamp(System.currentTimeMillis()).topic(topic).manufacturer(MANUFACTURER).build();

        } catch (Exception e) {
            log.error("MAVLink消息转换失败: topic={}, error={}", topic, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean supports(String manufacturer) {
        return MANUFACTURER.equals(manufacturer);
    }

    @Override
    public String getManufacturer() {
        return MANUFACTURER;
    }

    /**
     * 从消息数据中提取无人机ID
     *
     * @param data 消息数据
     * @return 无人机ID
     */
    private String extractDroneId(JSONObject data) {
        // MAVLink优先使用droneSN字段
        String droneId = data.getString("droneSN");
        if (droneId == null || droneId.isEmpty()) {
            // 备用字段
            droneId = data.getString("droneId");
        }
        return droneId;
    }
}
