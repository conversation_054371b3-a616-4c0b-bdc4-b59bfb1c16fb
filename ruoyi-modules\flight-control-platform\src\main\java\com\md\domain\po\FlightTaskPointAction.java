package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年01月14日 11:08
 */

/**
 * 航点动作表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "flight_task_point_action")
public class FlightTaskPointAction extends TenantEntity {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 航点ID
     */
    @TableField(value = "point_id")
    private String pointId;

    /**
     * 动作编号
     */
    @TableField(value = "action_index")
    private Integer actionIndex;

    /**
     * 飞行器悬停等待时间
     */
    @TableField(value = "hover_time")
    private Integer hoverTime;

    /**
     * 飞行器目标偏航角
     */
    @TableField(value = "aircraft_heading")
    private Double aircraftHeading;

    /**
     * 是否使用全局拍照模式(0：不使用 1：使用)
     */
    @TableField(value = "use_global_image_format")
    private Integer useGlobalImageFormat;

    /**
     * 拍照模式
     */
    @TableField(value = "image_format")
    private String imageFormat;

    /**
     * 云台偏航角
     */
    @TableField(value = "gimbal_yaw_rotate_angle")
    private Double gimbalYawRotateAngle;

    /**
     * 云台俯仰角
     */
    @TableField(value = "gimbal_pitch_rotate_angle")
    private Double gimbalPitchRotateAngle;

    /**
     * 等时触发
     */
    @TableField(value = "multiple_timing")
    private Integer multipleTiming;

    /**
     * 等距触发
     */
    @TableField(value = "multiple_distance")
    private Integer multipleDistance;

    /**
     * 变焦焦距
     */
    @TableField(value = "zoom")
    private Double zoom;

    /**
     * 录像状态：1：开始录像 0：结束录像
     */
    @TableField(value = "record_status")
    private Integer recordStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "`type`")
    private String type;

    private static final long serialVersionUID = 1L;

    // 重写父类字段，排除数据库中不存在的字段
    @TableField(exist = false)
    private Long createBy;

    @TableField(exist = false)
    private Long updateBy;

    @TableField(exist = false)
    private Long createDept;
}