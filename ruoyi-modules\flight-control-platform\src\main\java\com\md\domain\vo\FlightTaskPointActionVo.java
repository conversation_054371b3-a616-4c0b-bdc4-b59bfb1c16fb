package com.md.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.md.domain.po.FlightTaskPointAction;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 航点动作视图对象
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FlightTaskPointAction.class)
public class FlightTaskPointActionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "动作ID")
    private Long id;

    /**
     * 航点ID
     */
    @ExcelProperty(value = "航点ID")
    private String pointId;

    /**
     * 动作编号
     */
    @ExcelProperty(value = "动作编号")
    private Integer actionIndex;

    /**
     * 飞行器悬停等待时间
     */
    @ExcelProperty(value = "悬停等待时间")
    private Integer hoverTime;

    /**
     * 飞行器目标偏航角
     */
    @ExcelProperty(value = "目标偏航角")
    private Double aircraftHeading;

    /**
     * 是否使用全局拍照模式(0：不使用 1：使用)
     */
    @ExcelProperty(value = "是否使用全局拍照模式")
    private Integer useGlobalImageFormat;

    /**
     * 拍照模式
     */
    @ExcelProperty(value = "拍照模式")
    private String imageFormat;

    /**
     * 云台偏航角
     */
    @ExcelProperty(value = "云台偏航角")
    private Double gimbalYawRotateAngle;

    /**
     * 云台俯仰角
     */
    @ExcelProperty(value = "云台俯仰角")
    private Double gimbalPitchRotateAngle;

    /**
     * 等时触发
     */
    @ExcelProperty(value = "等时触发")
    private Integer multipleTiming;

    /**
     * 等距触发
     */
    @ExcelProperty(value = "等距触发")
    private Integer multipleDistance;

    /**
     * 变焦焦距
     */
    @ExcelProperty(value = "变焦焦距")
    private Double zoom;

    /**
     * 录像状态：1：开始录像 0：结束录像
     */
    @ExcelProperty(value = "录像状态")
    private Integer recordStatus;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 动作类型
     */
    @ExcelProperty(value = "动作类型")
    private String type;
} 