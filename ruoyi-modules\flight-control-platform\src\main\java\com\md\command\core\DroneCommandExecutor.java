package com.md.command.core;

import com.md.command.model.dto.CommandResult;

import java.util.Set;

/**
 * 无人机指令执行器接口
 */
public interface DroneCommandExecutor {
    /**
     * 执行无人机指令
     *
     * @param command 指令内容
     * @return 执行结果
     */
    CommandResult executeCommand(DroneCommand command);

    /**
     * 获取支持的厂商类型集合
     *
     * @return 厂商类型集合
     */
    Set<String> getSupportedManufacturers();

    /**
     * 获取支持的协议类型
     *
     * @return 协议类型
     */
    String getSupportedProtocol();

    /**
     * 获取优先级，数值越大优先级越高
     *
     * @return 优先级
     */
    default int getPriority() {
        return 0;
    }
}
