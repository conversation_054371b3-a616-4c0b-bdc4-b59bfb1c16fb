package com.md.websocket.converter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.flight.mavlink.model.DroneStatus;
import com.md.websocket.message.DroneMessage;
import com.md.websocket.message.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 大疆无人机消息转换器
 */
@Slf4j
@Component
public class DjiMessageConverter implements DroneMessageConverter {

    private static final String MANUFACTURER = "DJI";

    @Override
    public DroneMessage convert(String topic, String payload, MessageType messageType) {
        try {
            JSONObject originalData = JSON.parseObject(payload);
            String droneId = extractDroneId(originalData);

            // 将大疆原始数据转换为统一的DroneStatus格式
            DroneStatus unifiedStatus = convertToUnifiedFormat(originalData);

            return DroneMessage.builder().type(messageType.name()).droneId(droneId).data(unifiedStatus)
                .timestamp(System.currentTimeMillis()).topic(topic).manufacturer(MANUFACTURER).build();

        } catch (Exception e) {
            log.error("DJI消息转换失败: topic={}, error={}", topic, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean supports(String manufacturer) {
        return MANUFACTURER.equals(manufacturer);
    }

    @Override
    public String getManufacturer() {
        return MANUFACTURER;
    }

    /**
     * 从消息数据中提取无人机ID
     *
     * @param data 消息数据
     * @return 无人机ID
     */
    private String extractDroneId(JSONObject data) {
        // DJI使用droneSN字段作为无人机ID
        String droneId = data.getString("droneSN");
        if (droneId == null || droneId.isEmpty()) {
            // 备用字段
            droneId = data.getString("droneId");
        }
        return droneId;
    }

    /**
     * 将大疆原始数据转换为统一的DroneStatus格式
     *
     * @param originalData 大疆原始数据
     * @return 统一的DroneStatus对象
     */
    private DroneStatus convertToUnifiedFormat(JSONObject originalData) {
        DroneStatus status = new DroneStatus();

        // 基础设备信息
        status.setDroneSN(originalData.getString("droneSN"));
        status.setDeviceModel(originalData.getString("deviceModel"));
        status.setDroneName(originalData.getString("droneName"));
        status.setBatterySN(originalData.getString("batterySN"));
        status.setTotalFlightTime(originalData.getIntValue("totalFlightTime"));
        status.setFlightCount(originalData.getIntValue("flightCount"));
        status.setOnline(originalData.getBooleanValue("online"));
        status.setArmed(originalData.getBooleanValue("armed"));

        // 位置信息
        if (originalData.containsKey("latitude")) {
            status.setLatitude(new BigDecimal(originalData.getString("latitude")));
        }
        if (originalData.containsKey("longitude")) {
            status.setLongitude(new BigDecimal(originalData.getString("longitude")));
        }
        status.setAbsoluteAltitude(originalData.getFloatValue("absoluteAltitude"));
        status.setRelativeAltitude(originalData.getFloatValue("relativeAltitude"));

        // 飞行信息
        status.setSpeed(originalData.getDoubleValue("speed"));
        status.setHeading(originalData.getFloatValue("heading"));
        status.setFlightMode(originalData.getString("flightMode"));
        status.setDistanceToHome(originalData.getDoubleValue("distanceToHome"));
        status.setGroundSpeed(originalData.getFloatValue("groundSpeed"));
        status.setAirSpeed(originalData.getDoubleValue("airSpeed"));
        status.setFlightDistance(originalData.getDoubleValue("flightDistance"));
        status.setPitch(originalData.getFloatValue("pitch"));
        status.setRoll(originalData.getFloatValue("roll"));
        status.setVerticalSpeed(originalData.getFloatValue("verticalSpeed"));

        // 电池信息转换
        convertBatteryInfo(originalData, status);

        // 任务信息
        status.setCurrentWaypoint(originalData.getIntValue("currentWaypoint"));
        status.setTotalWaypoints(originalData.getIntValue("totalWaypoints"));
        status.setMissionStatus(originalData.getString("missionStatus"));

        // 卫星信息
        status.setRtkSatelliteCount(originalData.getIntValue("rtkSatelliteCount"));
        status.setGpsFixType(originalData.getIntValue("gpsFixType"));
        status.setGpsSatellites(originalData.getIntValue("gpsSatellites"));

        // RTK信息
        status.setRTKEnabled(originalData.getBooleanValue("isRTKEnabled"));
        status.setRTKHealthy(originalData.getBooleanValue("isRTKHealthy"));
        status.setRTKConnected(originalData.getBooleanValue("isRTKConnected"));
        status.setRtkAltitude(originalData.getDoubleValue("rtkAltitude"));

        // 时间戳
        status.setLastUpdateTime(originalData.getLongValue("lastUpdateTime"));
        status.setTimestamp(System.currentTimeMillis());

        return status;
    }

    /**
     * 转换电池信息
     */
    private void convertBatteryInfo(JSONObject originalData, DroneStatus status) {
        JSONObject batteryInfo = originalData.getJSONObject("batteryInfo");
        if (batteryInfo != null) {
            DroneStatus.BatteryInfo battery = new DroneStatus.BatteryInfo();
            battery.setChargeRemainingInPercent(batteryInfo.getIntValue("percentage"));
            battery.setBatteryVoltage(batteryInfo.getFloatValue("voltage"));
            // 大疆可能没有这些字段，设置默认值或从其他字段推算
            battery.setTotalCapacity(batteryInfo.getIntValue("totalCapacity"));
            battery.setChargeRemaining(batteryInfo.getIntValue("chargeRemaining"));
            battery.setBatteryLife(batteryInfo.getIntValue("batteryLife"));
            status.setBatteryInfo(battery);
        }
    }
}
