package com.md.flight.lh.domain.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 航线上传信息dto
 */
@Data
public class LhRouteUploadFolderDto {
    //模板id 在航线文件中该ID唯一
    private Long template_id;

    //航线id 在一条航线中该ID唯一
    private Long wayline_id;

    //全局航线飞行速度 m/s (必填项)
    private float auto_flight_speed;

    //执行高度模式 (必填项)
    private String execute_height_mode;

    //航点信息 (必填项)
    private List<LhRouteUploadPlacemarkDto> placemark;
}
