package com.md.domain.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.tenant.core.TenantEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年01月14日 11:00
 */

/**
 * 航点表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "flight_task_point")
public class FlightTaskPoint extends TenantEntity {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 所属航线ID
     */
    @TableField(value = "line_id")
    private String lineId;

    /**
     * 航点序号
     */
    @TableField(value = "point_index")
    private Integer pointIndex;

    /**
     * 纬度
     */
    @TableField(value = "latitude")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @TableField(value = "longitude")
    private BigDecimal longitude;

    /**
     * 高度(m)
     */
    @TableField(value = "altitude")
    private BigDecimal altitude;

    /**
     * 飞行速度
     */
    @TableField(value = "speed")
    private Integer speed;

    /**
     * 航点动作列表
     */
    @TableField(exist = false)
    private List<FlightTaskPointAction> actions;

    /**
     * 是否为备降点(0:否 1:是)
     */
    @TableField(value = "is_emergency_landing_point")
    private Integer isEmergencyLandingPoint;

    // 重写父类字段，排除数据库中不存在的字段
    @TableField(exist = false)
    private Long createBy;

    @TableField(exist = false)
    private Long updateBy;

    @TableField(exist = false)
    private Long createDept;

    @TableField(exist = false)
    private Date updateTime;
}