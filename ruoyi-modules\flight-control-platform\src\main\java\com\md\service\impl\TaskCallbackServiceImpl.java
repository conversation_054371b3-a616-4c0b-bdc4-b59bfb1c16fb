package com.md.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.md.constant.TaskCallbackConstants;
import com.md.domain.po.SysApiConfig;
import com.md.domain.po.SysApiTenantBinding;
import com.md.mapper.SysApiConfigMapper;
import com.md.mapper.SysApiTenantBindingMapper;
import com.md.openfeign.TaskCallbackClient;
import com.md.service.ITaskCallbackService;
import com.md.utils.EncryptUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务回调服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskCallbackServiceImpl implements ITaskCallbackService {

    private final TaskCallbackClient taskCallbackClient;
    private final EncryptUtils encryptUtils;
    private final SysApiConfigMapper sysApiConfigMapper;
    private final SysApiTenantBindingMapper sysApiTenantBindingMapper;

    @Value("${task-callback.tenant-id:'000000'}")
    private String defaultTenantId;

    @Override
    public boolean sendTaskCallback(String taskId, Integer code, String msg) {
        return sendTaskCallback(taskId, code, msg, null);
    }

    @Override
    public boolean sendTaskCallback(String taskId, Integer code, String msg, Object extraData) {
        try {
            log.info("发送任务回调: taskId={}, code={}, msg={}", taskId, code, msg);

            // 构建回调数据
            Map<String, Object> callbackData = new HashMap<>();
            callbackData.put("taskId", taskId);
            callbackData.put("code", code);
            callbackData.put("msg", msg);

            // 添加额外数据
            if (extraData != null) {
                if (extraData instanceof Map) {
                    callbackData.putAll((Map<String, Object>)extraData);
                } else {
                    callbackData.put("data", extraData);
                }
            }

            // 从Redis获取platformCode
            String platformCode = TenantHelper.ignore(
                () -> RedisUtils.getCacheObject(TaskCallbackConstants.TASK_PLATFORM_CODE_KEY_PREFIX + taskId));
            if (StringUtils.isEmpty(platformCode)) {
                log.warn("未找到任务[{}]对应的平台编码，无法发送回调", taskId);
                return false;
            } else {
                log.info("使用Redis中存储的平台编码[{}]进行回调", platformCode);
            }

            // 从Redis获取tenantId
            String taskTenantId = TenantHelper.ignore(
                () -> RedisUtils.getCacheObject(TaskCallbackConstants.TASK_TENANT_ID_KEY_PREFIX + taskId));
            if (StringUtils.isEmpty(taskTenantId)) {
                log.warn("未找到任务[{}]对应的租户ID，使用默认值[{}]", taskId, defaultTenantId);
                taskTenantId = defaultTenantId;
            } else {
                log.info("使用Redis中存储的租户ID[{}]进行回调", taskTenantId);
            }

            // 将回调数据转为JSON并加密
            String requestData =
                encryptUtils.parseApiAESEncrypt(platformCode, JSON.toJSONString(callbackData), taskTenantId);

            // 查找目标平台API配置
            SysApiConfig sysApiConfig = sysApiConfigMapper.selectOne(
                Wrappers.<SysApiConfig>lambdaQuery().eq(SysApiConfig::getPlateformCode, platformCode));

            if (sysApiConfig == null) {
                log.error("未找到平台编码[{}]对应的API配置", platformCode);
                return false;
            }

            // 查找租户绑定关系
            SysApiTenantBinding sysApiTenantBinding = sysApiTenantBindingMapper.selectOne(
                Wrappers.<SysApiTenantBinding>lambdaQuery().eq(SysApiTenantBinding::getApiId, sysApiConfig.getId())
                    .eq(SysApiTenantBinding::getBindTenantId, taskTenantId));

            if (sysApiTenantBinding == null) {
                log.error("未找到平台编码[{}]与租户[{}]的绑定关系", platformCode, taskTenantId);
                return false;
            }

            // 调用远程接口发送回调
            String response = taskCallbackClient.taskCallback(new URI(sysApiConfig.getUrl()), requestData,
                sysApiTenantBinding.getTargetTenantId(), taskTenantId);

            log.info("任务回调结果: {}", response);
            return true;
        } catch (Exception e) {
            log.error("发送任务回调失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }
}
