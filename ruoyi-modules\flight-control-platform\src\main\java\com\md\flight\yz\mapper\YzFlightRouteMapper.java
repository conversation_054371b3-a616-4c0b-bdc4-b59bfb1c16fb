package com.md.flight.yz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.md.flight.yz.domain.po.YzFlightRoute;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 亿航航线Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-12
 */
@Mapper
public interface YzFlightRouteMapper extends BaseMapper<YzFlightRoute> {
    /**
     * 查询亿航航线
     *
     * @param id 亿航航线主键
     * @return 亿航航线
     */
    public YzFlightRoute selectYhFlightRouteById(Long id);

    /**
     * 查询亿航航线列表
     *
     * @param yzFlightRoute 亿航航线
     * @return 亿航航线集合
     */
    public List<YzFlightRoute> selectYhFlightRouteList(YzFlightRoute yzFlightRoute);

    /**
     * 新增亿航航线
     *
     * @param yzFlightRoute 亿航航线
     * @return 结果
     */
    public int insertYhFlightRoute(YzFlightRoute yzFlightRoute);

    /**
     * 批量新增
     *
     * @param entityList
     * @return
     */
    int batchInsert(List<YzFlightRoute> entityList);

}