package com.md.flight.mavlink.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * MAVLink配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mavlink")
public class MavlinkConfig {
    /**
     * 系统ID (通常地面站为255)
     */
    private int systemId = 255;

    /**
     * 组件ID (通常地面站为190)
     */
    private int componentId = 190;

    /**
     * 目标系统ID (通常无人机为1)
     */
    private int targetSystemId = 1;

    /**
     * 目标组件ID (通常无人机为1)
     */
    private int targetComponentId = 1;

    /**
     * 心跳包发送间隔(毫秒)
     */
    private int heartbeatInterval = 1000;

    /**
     * 连接超时时间(毫秒)
     */
    private int connectionTimeout = 5000;

    /**
     * 重连间隔时间(毫秒)
     */
    private int reconnectInterval = 5000;

    /**
     * 最大重连次数
     */
    private int maxReconnectAttempts = 3;

    /**
     * 数据流请求频率(Hz)
     */
    private int streamRate = 10;
} 