package com.md.service;

import com.md.domain.bo.SysApiConfigBo;
import com.md.domain.po.SysApiConfig;
import com.md.domain.vo.SysApiConfigVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * 接口管理 服务接口
 *
 *
 */
public interface ISysApiConfigService {

    /**
     * 查询单个接口管理
     *
     * @param id 接口ID
     * @return 接口管理对象
     */
    SysApiConfigVo queryById(Long id);

    /**
     * 查询接口管理列表
     *
     * @param bo 接口管理查询条件
     * @return 接口管理集合
     */
    TableDataInfo<SysApiConfigVo> queryPageList(SysApiConfigBo bo, PageQuery pageQuery);

    /**
     * 查询接口管理列表
     *
     * @param bo 接口管理查询条件
     * @return 接口管理集合
     */
    List<SysApiConfigVo> queryList(SysApiConfigBo bo);

    /**
     * 新增接口管理
     *
     * @param bo 接口管理对象
     * @return 结果
     */
    Boolean insertByBo(SysApiConfigBo bo);

    /**
     * 修改接口管理
     *
     * @param bo 接口管理对象
     * @return 结果
     */
    Boolean updateByBo(SysApiConfigBo bo);

    /**
     * 校验并删除接口管理信息
     *
     */
    Boolean deleteWithValidByIds(Long id);

    /**
     * 检查目标平台是否已存在
     *
     * @param targetPlatform 目标平台
     * @param excludeId 排除的ID（修改时用）
     * @return 如果存在则返回对应的SysApiConfig对象，不存在返回null
     */
    SysApiConfig checkTargetPlatformExists(String targetPlatform, Long excludeId);
}
