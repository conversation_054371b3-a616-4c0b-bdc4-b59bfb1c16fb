package com.md.flight.mavlink.handler;

import com.md.flight.mavlink.service.impl.MavlinkCoreService;
import io.dronefleet.mavlink.common.Attitude;
import lombok.extern.slf4j.Slf4j;

/**
 * 姿态信息处理器
 * 处理MAVLink姿态消息，提取俯仰角、横滚角等信息
 */
@Slf4j
public class AttitudeHandler extends AbstractMavlinkHandler<Attitude> {

    public AttitudeHandler(MavlinkCoreService coreService) {
        super(coreService);
    }

    @Override
    protected void doHandle(String droneId, Attitude message) {
        // 保存最新的姿态信息
        coreService.setLastAttitude(droneId, message);

        // 将弧度转换为角度 (弧度 * 180 / PI = 角度)
        float pitchDeg = (float) Math.toDegrees(message.pitch());
        float rollDeg = (float) Math.toDegrees(message.roll());
        float yawDeg = (float) Math.toDegrees(message.yaw());

        log.info("无人机[{}]姿态信息: 俯仰角={}度, 横滚角={}度, 偏航角={}度",
            droneId, pitchDeg, rollDeg, yawDeg);
    }

    @Override
    public Class<Attitude> getMessageType() {
        return Attitude.class;
    }
}