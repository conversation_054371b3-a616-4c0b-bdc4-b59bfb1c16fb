package com.md.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.md.service.IFlightTaskFenceService;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 航线任务围栏管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/flight/task/fence")
@Validated
public class FlightTaskFenceController extends BaseController {

    private final IFlightTaskFenceService flightTaskFenceService;

    /**
     * 更新任务关联的围栏
     *
     * @param taskId   任务ID
     * @param fenceIds 围栏ID列表，以逗号分隔
     * @return 操作结果
     */
    @SaCheckPermission("flight:task:edit")
    @RepeatSubmit
    @Log(title = "航线任务管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{taskId}")
    public R<Void> updateTaskFences(@PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId,
        @RequestParam @NotBlank(message = "围栏ID不能为空") String fenceIds) {
        boolean result = flightTaskFenceService.updateTaskFences(taskId, fenceIds);
        return result ? R.ok() : R.fail("更新任务围栏关联失败");
    }

    /**
     * 获取任务关联的围栏ID
     *
     * @param taskId 任务ID
     * @return 围栏ID列表，以逗号分隔
     */
    @SaCheckPermission("flight:task:query")
    @GetMapping("/{taskId}")
    public R<String> getTaskFences(@PathVariable("taskId") @NotBlank(message = "任务ID不能为空") String taskId) {
        String fenceIds = flightTaskFenceService.getTaskFences(taskId);
        return R.ok(fenceIds);
    }
}