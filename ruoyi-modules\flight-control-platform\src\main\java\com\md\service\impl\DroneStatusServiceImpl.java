package com.md.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.md.constant.MqttConstants;
import com.md.domain.bo.DroneStatusBo;
import com.md.domain.dto.DroneStatusDto;
import com.md.domain.po.UavInfo;
import com.md.domain.vo.DroneStatusVo;
import com.md.mapper.UavInfoMapper;
import com.md.service.IDroneStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 无人机状态服务实现类
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DroneStatusServiceImpl implements IDroneStatusService {

    private final UavInfoMapper uavInfoMapper;

    @Override
    public boolean isDroneOnline(String droneId) {
        if (droneId == null) {
            return false;
        }
        String key = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId;
        return TenantHelper.ignore(() -> RedisUtils.hasKey(key));
    }

    @Override
    public List<DroneStatusVo> getOnlineDrones() {
        Collection<String> keys = TenantHelper.ignore(() ->
            RedisUtils.keys(MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + "*")
        );

        if (keys == null || keys.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 提取无人机ID列表
        List<String> droneIds =
            keys.stream().map(key -> key.substring(MqttConstants.REDIS_UAV_STATE_KEY_PREFIX.length()))
                .collect(Collectors.toList());

        // 3. 查询无人机信息
        List<UavInfo> uavInfoList = uavInfoMapper.selectByUavCodes(droneIds);
        Map<String, String> droneNameMap = uavInfoList.stream().collect(
            Collectors.toMap(UavInfo::getUavCode, uav -> uav.getUavName() != null ? uav.getUavName() : uav.getUavCode(),
                (v1, v2) -> v1));

        // 4. 组装返回结果，先创建DTO然后转换为VO
        List<DroneStatusDto> dtoList = droneIds.stream().map(droneId -> new DroneStatusDto().setDroneId(droneId)
            .setDroneName(droneNameMap.getOrDefault(droneId, droneId)).setOnline(true)).collect(Collectors.toList());

        // 5. 转换为VO对象
        return MapstructUtils.convert(dtoList, DroneStatusVo.class);
    }

    @Override
    public Map<String, Boolean> getDronesOnlineStatus(DroneStatusBo bo) {
        List<String> droneIds = bo.getDroneIds();
        if (droneIds == null || droneIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return droneIds.stream().collect(Collectors.toMap(droneId -> droneId, this::isDroneOnline, (v1, v2) -> v1));
    }

    @Override
    public DroneStatusVo getDroneStatus(String droneId) {
        if (droneId == null) {
            return null;
        }

        String key = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId;
        String statusData = TenantHelper.ignore(() -> RedisUtils.getCacheObject(key));

        if (statusData == null) {
            return null;
        }

        DroneStatusVo vo = new DroneStatusVo();
        vo.setDroneId(droneId);
        vo.setStatusData(statusData);
        vo.setOnline(true);

        // 尝试从UavInfo获取名称
        try {
            List<UavInfo> uavInfoList = uavInfoMapper.selectByUavCodes(Collections.singletonList(droneId));
            if (!uavInfoList.isEmpty()) {
                vo.setDroneName(uavInfoList.get(0).getUavName());
            }
        } catch (Exception e) {
            log.warn("获取无人机信息失败: droneId={}", droneId, e);
        }

        return vo;
    }

    @Override
    public Map<String, String> getDronesStatus(DroneStatusBo bo) {
        List<String> droneIds = bo.getDroneIds();
        if (droneIds == null || droneIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, String> result = new HashMap<>(droneIds.size());

        TenantHelper.ignore(() -> {
            for (String droneId : droneIds) {
                String key = MqttConstants.REDIS_UAV_STATE_KEY_PREFIX + droneId;
                String status = RedisUtils.getCacheObject(key);
                if (status != null) {
                    result.put(droneId, status);
                }
            }
        });

        return result;
    }

    @Override
    public R<DroneStatusVo> getVirtualStickStatus(String droneId) {
        try {
            String cacheKey = MqttConstants.REDIS_VIRTUAL_STICK_STATUS_PREFIX + droneId;
            String statusJson = TenantHelper.ignore(() -> RedisUtils.getCacheObject(cacheKey));

            DroneStatusVo vo = new DroneStatusVo();
            vo.setDroneId(droneId);

            Map<String, Object> statusMap = new HashMap<>();
            if (statusJson != null) {
                JSONObject statusObj = JSON.parseObject(statusJson);
                statusMap.put("enabled", true);
                statusMap.put("message", statusObj.getString("message"));
                statusMap.put("timestamp", statusObj.getLongValue("timestamp"));
                statusMap.put("commandId", statusObj.getString("commandId"));
            } else {
                statusMap.put("enabled", false);
                statusMap.put("message", "虚拟摇杆未启用");
            }

            vo.setVirtualStickStatus(statusMap);
            return R.ok(vo);
        } catch (Exception e) {
            log.error("查询虚拟摇杆状态失败：droneId={}", droneId, e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }
}