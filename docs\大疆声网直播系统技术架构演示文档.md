# 大疆声网直播系统技术架构演示文档

## 📋 目录

- [1. 系统概述](#1-系统概述)
- [2. 技术架构对比](#2-技术架构对比)
- [3. 大疆设备直播流程](#3-大疆设备直播流程)
- [4. 联合飞机直播流程](#4-联合飞机直播流程)
- [5. 核心技术组件](#5-核心技术组件)
- [6. 关键代码实现](#6-关键代码实现)
- [7. 技术优势总结](#7-技术优势总结)

---

## 1. 系统概述

### 1.1 业务背景

本系统实现了**多厂商无人机设备的统一直播解决方案**，支持：
- **大疆无人机**：原生Agora SDK直播
- **联合飞机**：RTMP推流 + Media Gateway转换
- **统一前端**：用户无感知的一致体验

### 1.2 核心挑战

```mermaid
graph LR
    A[技术挑战] --> B[大疆: 支持Agora SDK]
    A --> C[联合: 仅支持RTMP]
    A --> D[需求: 统一用户体验]
    A --> E[目标: 前端零改动]
```

---

## 2. 技术架构对比

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        FE[Vue.js前端应用<br/>统一的Live组件]
    end

    subgraph "后端服务层"
        API[AgoraTokenService<br/>智能路由服务]
        LH[LhMediaGatewayService<br/>联合飞机专用服务]
        CMD[DroneCommandService<br/>设备命令服务]
    end

    subgraph "数据存储层"
        DB[(MySQL数据库<br/>流配置存储)]
        REDIS[(Redis缓存<br/>Token缓存)]
    end

    subgraph "设备层"
        DJI[大疆无人机<br/>内置Agora SDK]
        LH_DRONE[联合飞机<br/>RTMP推流]
    end

    subgraph "Agora云服务"
        RTC[Agora RTC<br/>实时音视频]
        MGW[Media Gateway<br/>协议转换]
    end

    FE -->|统一API调用| API
    API -->|厂商识别| LH
    API -->|发送命令| CMD
    LH -->|存储配置| DB
    API -->|缓存Token| REDIS
    CMD -->|AGORA_START| DJI
    CMD -->|live_start| LH_DRONE
    DJI -->|原生RTC| RTC
    LH_DRONE -->|RTMP推流| MGW
    MGW -->|转换为RTC| RTC
    RTC -->|统一播放| FE

    style DJI fill:#e1f5fe
    style LH_DRONE fill:#fff3e0
    style MGW fill:#f3e5f5
    style RTC fill:#e8f5e8
```

### 2.2 技术路径对比

```mermaid
graph LR
    subgraph "大疆路径"
        DJI1[大疆设备] -->|内置SDK| RTC1[Agora RTC]
        RTC1 -->|直接连接| FRONT1[前端播放]
    end
    
    subgraph "联合路径"
        LH1[联合飞机] -->|RTMP推流| MGW1[Media Gateway]
        MGW1 -->|协议转换| RTC2[Agora RTC]
        RTC2 -->|统一接口| FRONT2[前端播放]
    end
    
    style DJI1 fill:#e1f5fe
    style LH1 fill:#fff3e0
    style MGW1 fill:#f3e5f5
```

---

## 3. 大疆设备直播流程

### 3.1 完整时序图

```mermaid
sequenceDiagram
    participant F as 前端Live组件
    participant A as AgoraTokenService
    participant C as CommandService
    participant D as 大疆无人机
    participant RTC as Agora RTC

    F->>A: GET /api/agora/channel/token?droneSN=DJI001
    A->>A: 识别DJI设备
    A->>A: 生成/获取缓存Token
    A->>C: 发送AGORA_CONFIG命令
    Note over A,C: 配置参数：channelId, token, uid, quality
    A->>C: 发送AGORA_START命令
    C->>D: MQTT推送直播配置
    C->>D: MQTT推送开始直播
    A-->>F: 返回Token信息
    Note over A,F: {token, channelId, appId, droneSN}
    
    F->>F: 创建Agora客户端
    F->>RTC: client.join(appId, channelId, token)
    F->>RTC: setClientRole("audience")
    
    D->>RTC: 推送H.264视频流
    RTC->>F: user-published事件
    F->>RTC: subscribe(user, "video")
    F->>F: videoTrack.play(container)
    
    Note over F: 显示直播画面
```

### 3.2 关键配置参数

```mermaid
graph TD
    A[AGORA_CONFIG命令] --> B[channelId: drone_DJI001_timestamp]
    A --> C[token: 动态生成的RTC Token]
    A --> D[uid: 用户唯一标识]
    A --> E[quality: SD/HD画质]
    A --> F[camera: LEFT_MAIN主摄像头]
    A --> G[bitRateMode: AUTO自动码率]
```

---

## 4. 联合飞机直播流程

### 4.1 完整时序图

```mermaid
sequenceDiagram
    participant F as 前端Live组件
    participant A as AgoraTokenService
    participant L as LhMediaGatewayService
    participant D as 数据库
    participant C as CommandService
    participant LH as 联合飞机
    participant MGW as Media Gateway
    participant RTC as Agora RTC

    F->>A: GET /api/agora/channel/token?droneSN=LH001
    A->>A: 识别LH前缀 → 联合飞机
    A->>L: 获取流配置
    L->>D: 查询lh_drone_stream_config
    
    alt 配置不存在
        L->>MGW: 调用API创建永久StreamKey
        Note over L,MGW: POST /v1/projects/{appId}/rtls/ingress/streamkeys
        MGW-->>L: 返回streamKey
        L->>D: 保存流配置
        Note over L,D: channelId, streamKey, rtmpUrl, uid
    end
    
    L-->>A: 返回流配置
    A->>C: 发送live_start命令
    C->>LH: MQTT推送开始直播
    A->>A: 生成观众Token (与大疆相同逻辑)
    A-->>F: 返回Token信息 (格式与大疆一致)
    
    F->>F: 创建Agora客户端 (相同代码)
    F->>RTC: client.join(appId, channelId, token)
    
    LH->>MGW: RTMP推流到固定地址
    Note over LH,MGW: rtls-ingress-prod-cn.agoramdn.com/live/{streamKey}
    MGW->>RTC: 转换为RTC流
    RTC->>F: user-published事件
    F->>F: 显示直播画面 (相同逻辑)
```

### 4.2 Media Gateway配置流程

```mermaid
graph TD
    A[系统启动] --> B[扫描联合飞机列表]
    B --> C{配置是否存在?}
    C -->|否| D[调用Agora API]
    D --> E[创建永久StreamKey]
    E --> F[生成RTMP推流地址]
    F --> G[保存到数据库]
    C -->|是| H[使用现有配置]
    G --> I[配置完成]
    H --> I
    
    style D fill:#f3e5f5
    style E fill:#f3e5f5
    style F fill:#fff3e0
```

---

## 5. 核心技术组件

### 5.1 前端Agora SDK集成

```mermaid
graph TD
    A[AgoraStream类] --> B[createClient配置]
    B --> C[mode: 'live' 直播模式]
    B --> D[codec: 'h264' 编码格式]
    
    A --> E[事件监听]
    E --> F[user-published: 用户发布流]
    E --> G[user-unpublished: 用户停止发布]
    E --> H[error: 错误处理]
    
    A --> I[播放控制]
    I --> J[join: 加入频道]
    I --> K[setClientRole: 设置观众角色]
    I --> L[subscribe: 订阅视频流]
    I --> M[play: 播放到容器]
```

### 5.2 后端智能路由

```mermaid
graph TD
    A[getLiveToken请求] --> B{设备类型识别}
    B -->|DJI前缀| C[handleDjiLiveStream]
    B -->|LH前缀| D[handleLhMediaGatewayStream]
    B -->|其他| E[默认处理]
    
    C --> F[生成RTC Token]
    C --> G[发送设备命令]
    C --> H[返回统一格式]
    
    D --> I[获取流配置]
    D --> J[生成观众Token]
    D --> K[返回统一格式]
    
    style C fill:#e1f5fe
    style D fill:#fff3e0
    style H fill:#e8f5e8
    style K fill:#e8f5e8
```

### 5.3 Token缓存机制

```mermaid
graph TD
    A[Token请求] --> B{Redis缓存检查}
    B -->|存在且有效| C[返回缓存Token]
    B -->|不存在或过期| D[生成新Token]
    D --> E[保存到Redis]
    E --> F[设置过期时间]
    F --> G[返回新Token]
    
    style C fill:#e8f5e8
    style D fill:#fff3e0

---

## 6. 关键代码实现

### 6.1 前端AgoraStream类

```javascript
export default class AgoraStream {
  constructor(container) {
    this.container = container; // 视频容器ID
  }

  async play() {
    if (!this.client) {
      // 创建声网客户端：直播模式 + H.264编码
      this.client = createClient({ mode: "live", codec: "h264" });

      // 监听用户发布事件
      this.client.on("user-published", async (user, mediaType) => {
        await this.client.subscribe(user, mediaType);
        if (mediaType === "video") {
          user.videoTrack.play(this.container); // 播放到指定容器
          this.onPlay?.(user);
        }
      });

      // 监听用户停止发布
      this.client.on("user-unpublished", (user) => {
        this.onUnPublished?.(user);
      });

      // 错误处理
      this.client.on("error", (error) => {
        this.stop();
        this.onError?.(error);
      });
    }

    // 加入房间并设置为观众角色
    await this.client.join(this.data.appId, this.data.channelId, this.data.token);
    await this.client.setClientRole("audience");
  }
}
```

### 6.2 后端智能路由逻辑

```java
@Override
public R<Object> getLiveToken(String droneSN, String uid) {
    try {
        // 🎯 关键：根据设备编号前缀判断厂商
        if (isLhDrone(droneSN)) {  // LH开头 = 联合飞机
            return handleLhMediaGatewayStream(droneSN, uid);
        }

        // 原有的DJI处理逻辑
        return handleDjiLiveStream(droneSN, uid);
    } catch (Exception e) {
        return R.fail("获取Token失败：" + e.getMessage());
    }
}

private boolean isLhDrone(String droneSN) {
    return droneSN != null && droneSN.toUpperCase().startsWith("LH");
}
```

### 6.3 联合飞机Media Gateway配置

```java
@Override
public LhDroneStreamConfig createPermanentStreamConfig(String droneSn) {
    // 1. 生成频道ID和UID
    String channelId = "lh_drone_" + droneSn;  // 例: lh_drone_LH001
    String uid = "1000";

    // 2. 🔑 调用Agora API创建永久StreamKey
    String streamKey = createAgoraStreamKey(channelId, uid, 0); // 0=永久有效

    // 3. 🌟 构建RTMP推流地址 (这是关键!)
    String rtmpUrl = "rtls-ingress-prod-cn.agoramdn.com/live/" + streamKey;

    // 4. 保存配置到数据库
    LhDroneStreamConfig config = new LhDroneStreamConfig();
    config.setDroneSn(droneSn);
    config.setChannelId(channelId);
    config.setStreamKey(streamKey);
    config.setRtmpUrl(rtmpUrl);  // 联合飞机推流到这个地址
    config.setIsPermanent(true);

    lhDroneStreamConfigMapper.insert(config);
    return config;
}
```

### 6.4 大疆设备命令配置

```java
private CommandRequest buildConfigCommand(String droneSN, String channelId, String uid, String token) {
    Map<String, Object> configParams = new HashMap<>();
    configParams.put("channelId", channelId);
    configParams.put("token", token);
    configParams.put("uid", uid);
    configParams.put("quality", "SD");           // 标清画质
    configParams.put("camera", "LEFT_MAIN");     // 主摄像头
    configParams.put("bitRateMode", "AUTO");     // 自动码率

    CommandRequest configRequest = new CommandRequest();
    configRequest.setCommandType(CommandType.AGORA_CONFIG);
    configRequest.setDroneId(droneSN);
    configRequest.setParameters(configParams);
    return configRequest;
}
```

---

## 7. 技术优势总结

### 7.1 系统特点

```mermaid
mindmap
  root((声网直播系统))
    统一体验
      前端零改动
      API格式一致
      用户无感知
    高性能
      Token缓存机制
      低延迟RTC
      自动重连
    易扩展
      厂商识别路由
      插件化架构
      配置化管理
    高可靠
      错误处理
      状态监控
      自动恢复
```

### 7.2 核心优势

| 特性 | 大疆方案 | 联合飞机方案 | 统一效果 |
|------|----------|--------------|----------|
| **协议支持** | 原生Agora SDK | RTMP + Media Gateway | RTC统一播放 |
| **延迟表现** | 100-300ms | 200-500ms | 低延迟体验 |
| **前端代码** | AgoraStream类 | 相同AgoraStream类 | 完全一致 |
| **API接口** | /api/agora/channel/token | 相同API | 格式统一 |
| **用户体验** | 直播播放 | 相同直播播放 | 无差异感知 |

### 7.3 技术创新点

1. **🔄 协议转换**：RTMP → RTC，让传统设备接入现代直播
2. **🎭 透明代理**：后端智能路由，前端完全无感知
3. **⚡ 永久配置**：StreamKey永久有效，减少API调用
4. **🏗️ 统一架构**：一套代码支持多厂商设备

### 7.4 部署架构

```mermaid
graph TB
    subgraph "生产环境"
        LB[负载均衡器]
        WEB1[Web服务器1]
        WEB2[Web服务器2]
        API1[API服务器1]
        API2[API服务器2]
    end

    subgraph "数据层"
        MYSQL[(MySQL主从)]
        REDIS_CLUSTER[(Redis集群)]
    end

    subgraph "外部服务"
        AGORA[Agora云服务]
        MQTT[MQTT消息队列]
    end

    LB --> WEB1
    LB --> WEB2
    WEB1 --> API1
    WEB2 --> API2
    API1 --> MYSQL
    API2 --> MYSQL
    API1 --> REDIS_CLUSTER
    API2 --> REDIS_CLUSTER
    API1 --> AGORA
    API2 --> AGORA
    API1 --> MQTT
    API2 --> MQTT
```

---

## 8. 演示要点

### 8.1 关键演示场景

1. **设备切换演示**：从大疆设备切换到联合飞机，前端界面无变化
2. **API调用对比**：展示相同的API返回相同格式的数据
3. **实时播放**：两种设备的视频都能正常播放
4. **性能监控**：展示延迟、画质等指标

### 8.2 技术亮点说明

- **Media Gateway的作用**：协议转换的关键技术
- **智能路由机制**：根据设备编号自动选择处理方式
- **缓存优化策略**：Token缓存提升性能
- **统一前端体验**：一套代码适配多种设备

---

*本文档展示了声网直播系统如何通过技术创新实现多厂商设备的统一接入，为用户提供一致的直播体验。*
```
